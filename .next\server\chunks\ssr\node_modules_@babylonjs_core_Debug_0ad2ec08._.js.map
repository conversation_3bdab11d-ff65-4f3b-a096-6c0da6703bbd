{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Debug/axesViewer.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Debug/axesViewer.ts"], "sourcesContent": ["import { Vector3 } from \"../Maths/math.vector\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { Scene } from \"../scene\";\r\nimport type { TransformNode } from \"../Meshes/transformNode\";\r\nimport { StandardMaterial } from \"../Materials/standardMaterial\";\r\nimport { AxisDragGizmo } from \"../Gizmos/axisDragGizmo\";\r\nimport { Color3 } from \"../Maths/math.color\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\n\r\n/**\r\n * The Axes viewer will show 3 axes in a specific point in space\r\n * @see https://doc.babylonjs.com/toolsAndResources/utilities/World_Axes\r\n */\r\nexport class AxesViewer {\r\n    private _xAxis: TransformNode;\r\n    private _yAxis: TransformNode;\r\n    private _zAxis: TransformNode;\r\n    private _scaleLinesFactor = 4;\r\n    private _instanced = false;\r\n\r\n    /**\r\n     * Gets the hosting scene\r\n     */\r\n    public scene: Nullable<Scene> = null;\r\n\r\n    private _scaleLines = 1;\r\n    /**\r\n     * Gets or sets a number used to scale line length\r\n     */\r\n    public get scaleLines() {\r\n        return this._scaleLines;\r\n    }\r\n\r\n    public set scaleLines(value: number) {\r\n        this._scaleLines = value;\r\n        this._xAxis.scaling.setAll(this._scaleLines * this._scaleLinesFactor);\r\n        this._yAxis.scaling.setAll(this._scaleLines * this._scaleLinesFactor);\r\n        this._zAxis.scaling.setAll(this._scaleLines * this._scaleLinesFactor);\r\n    }\r\n\r\n    /** Gets the node hierarchy used to render x-axis */\r\n    public get xAxis(): TransformNode {\r\n        return this._xAxis;\r\n    }\r\n\r\n    /** Gets the node hierarchy used to render y-axis */\r\n    public get yAxis(): TransformNode {\r\n        return this._yAxis;\r\n    }\r\n\r\n    /** Gets the node hierarchy used to render z-axis */\r\n    public get zAxis(): TransformNode {\r\n        return this._zAxis;\r\n    }\r\n\r\n    /**\r\n     * Creates a new AxesViewer\r\n     * @param scene defines the hosting scene\r\n     * @param scaleLines defines a number used to scale line length (1 by default)\r\n     * @param renderingGroupId defines a number used to set the renderingGroupId of the meshes (2 by default)\r\n     * @param xAxis defines the node hierarchy used to render the x-axis\r\n     * @param yAxis defines the node hierarchy used to render the y-axis\r\n     * @param zAxis defines the node hierarchy used to render the z-axis\r\n     * @param lineThickness The line thickness to use when creating the arrow. defaults to 1.\r\n     */\r\n    constructor(scene?: Scene, scaleLines = 1, renderingGroupId: Nullable<number> = 2, xAxis?: TransformNode, yAxis?: TransformNode, zAxis?: TransformNode, lineThickness = 1) {\r\n        scene = scene || <Scene>EngineStore.LastCreatedScene;\r\n        if (!scene) {\r\n            return;\r\n        }\r\n\r\n        if (!xAxis) {\r\n            const redColoredMaterial = new StandardMaterial(\"xAxisMaterial\", scene);\r\n            redColoredMaterial.disableLighting = true;\r\n            redColoredMaterial.emissiveColor = Color3.Red().scale(0.5);\r\n            xAxis = AxisDragGizmo._CreateArrow(scene, redColoredMaterial, lineThickness);\r\n        }\r\n\r\n        if (!yAxis) {\r\n            const greenColoredMaterial = new StandardMaterial(\"yAxisMaterial\", scene);\r\n            greenColoredMaterial.disableLighting = true;\r\n            greenColoredMaterial.emissiveColor = Color3.Green().scale(0.5);\r\n            yAxis = AxisDragGizmo._CreateArrow(scene, greenColoredMaterial, lineThickness);\r\n        }\r\n\r\n        if (!zAxis) {\r\n            const blueColoredMaterial = new StandardMaterial(\"zAxisMaterial\", scene);\r\n            blueColoredMaterial.disableLighting = true;\r\n            blueColoredMaterial.emissiveColor = Color3.Blue().scale(0.5);\r\n            zAxis = AxisDragGizmo._CreateArrow(scene, blueColoredMaterial, lineThickness);\r\n        }\r\n\r\n        this._xAxis = xAxis;\r\n        this._yAxis = yAxis;\r\n        this._zAxis = zAxis;\r\n\r\n        this.scaleLines = scaleLines;\r\n\r\n        if (renderingGroupId != null) {\r\n            AxesViewer._SetRenderingGroupId(this._xAxis, renderingGroupId);\r\n            AxesViewer._SetRenderingGroupId(this._yAxis, renderingGroupId);\r\n            AxesViewer._SetRenderingGroupId(this._zAxis, renderingGroupId);\r\n        }\r\n\r\n        this.scene = scene;\r\n        this.update(new Vector3(), Vector3.Right(), Vector3.Up(), Vector3.Forward());\r\n    }\r\n\r\n    /**\r\n     * Force the viewer to update\r\n     * @param position defines the position of the viewer\r\n     * @param xaxis defines the x axis of the viewer\r\n     * @param yaxis defines the y axis of the viewer\r\n     * @param zaxis defines the z axis of the viewer\r\n     */\r\n    public update(position: Vector3, xaxis: Vector3, yaxis: Vector3, zaxis: Vector3): void {\r\n        this._xAxis.position.copyFrom(position);\r\n        this._xAxis.setDirection(xaxis);\r\n\r\n        this._yAxis.position.copyFrom(position);\r\n        this._yAxis.setDirection(yaxis);\r\n\r\n        this._zAxis.position.copyFrom(position);\r\n        this._zAxis.setDirection(zaxis);\r\n    }\r\n\r\n    /**\r\n     * Creates an instance of this axes viewer.\r\n     * @returns a new axes viewer with instanced meshes\r\n     */\r\n    public createInstance(): AxesViewer {\r\n        const xAxis = AxisDragGizmo._CreateArrowInstance(this.scene!, this._xAxis);\r\n        const yAxis = AxisDragGizmo._CreateArrowInstance(this.scene!, this._yAxis);\r\n        const zAxis = AxisDragGizmo._CreateArrowInstance(this.scene!, this._zAxis);\r\n        const axesViewer = new AxesViewer(this.scene!, this.scaleLines, null, xAxis, yAxis, zAxis);\r\n        axesViewer._instanced = true;\r\n        return axesViewer;\r\n    }\r\n\r\n    /** Releases resources */\r\n    public dispose() {\r\n        if (this._xAxis) {\r\n            this._xAxis.dispose(false, !this._instanced);\r\n        }\r\n\r\n        if (this._yAxis) {\r\n            this._yAxis.dispose(false, !this._instanced);\r\n        }\r\n\r\n        if (this._zAxis) {\r\n            this._zAxis.dispose(false, !this._instanced);\r\n        }\r\n\r\n        this.scene = null;\r\n    }\r\n\r\n    private static _SetRenderingGroupId(node: TransformNode, id: number) {\r\n        const meshes = node.getChildMeshes();\r\n        for (const mesh of meshes) {\r\n            mesh.renderingGroupId = id;\r\n        }\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAI/C,OAAO,EAAE,gBAAgB,EAAE,MAAM,+BAA+B,CAAC;AACjE,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AACxD,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;;;;;;AAM/C,MAAO,UAAU;IAanB;;OAEG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,IAAW,UAAU,CAAC,KAAa,EAAA;QAC/B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACtE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACtE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC;IAC1E,CAAC;IAED,kDAAA,EAAoD,CACpD,IAAW,KAAK,GAAA;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED,kDAAA,EAAoD,CACpD,IAAW,KAAK,GAAA;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED,kDAAA,EAAoD,CACpD,IAAW,KAAK,GAAA;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;;;;;;;;OASG,CACH,YAAY,KAAa,EAAE,UAAU,GAAG,CAAC,EAAE,mBAAqC,CAAC,EAAE,KAAqB,EAAE,KAAqB,EAAE,KAAqB,EAAE,aAAa,GAAG,CAAC,CAAA;QAhDjK,IAAA,CAAA,iBAAiB,GAAG,CAAC,CAAC;QACtB,IAAA,CAAA,UAAU,GAAG,KAAK,CAAC;QAE3B;;WAEG,CACI,IAAA,CAAA,KAAK,GAAoB,IAAI,CAAC;QAE7B,IAAA,CAAA,WAAW,GAAG,CAAC,CAAC;QAyCpB,KAAK,GAAG,KAAK,kKAAW,cAAW,CAAC,gBAAgB,CAAC;QACrD,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,OAAO;QACX,CAAC;QAED,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,MAAM,kBAAkB,GAAG,yKAAI,mBAAgB,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;YACxE,kBAAkB,CAAC,eAAe,GAAG,IAAI,CAAC;YAC1C,kBAAkB,CAAC,aAAa,iKAAG,SAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC3D,KAAK,kKAAG,gBAAa,CAAC,YAAY,CAAC,KAAK,EAAE,kBAAkB,EAAE,aAAa,CAAC,CAAC;QACjF,CAAC;QAED,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,MAAM,oBAAoB,GAAG,yKAAI,mBAAgB,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;YAC1E,oBAAoB,CAAC,eAAe,GAAG,IAAI,CAAC;YAC5C,oBAAoB,CAAC,aAAa,iKAAG,SAAM,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC/D,KAAK,kKAAG,gBAAa,CAAC,YAAY,CAAC,KAAK,EAAE,oBAAoB,EAAE,aAAa,CAAC,CAAC;QACnF,CAAC;QAED,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,MAAM,mBAAmB,GAAG,yKAAI,mBAAgB,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;YACzE,mBAAmB,CAAC,eAAe,GAAG,IAAI,CAAC;YAC3C,mBAAmB,CAAC,aAAa,iKAAG,SAAM,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC7D,KAAK,kKAAG,gBAAa,CAAC,YAAY,CAAC,KAAK,EAAE,mBAAmB,EAAE,aAAa,CAAC,CAAC;QAClF,CAAC;QAED,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QAEpB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAE7B,IAAI,gBAAgB,IAAI,IAAI,EAAE,CAAC;YAC3B,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;YAC/D,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;YAC/D,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;QACnE,CAAC;QAED,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,CAAC,mKAAI,UAAO,EAAE,iKAAE,UAAO,CAAC,KAAK,EAAE,iKAAE,UAAO,CAAC,EAAE,EAAE,iKAAE,UAAO,CAAC,OAAO,EAAE,CAAC,CAAC;IACjF,CAAC;IAED;;;;;;OAMG,CACI,MAAM,CAAC,QAAiB,EAAE,KAAc,EAAE,KAAc,EAAE,KAAc,EAAA;QAC3E,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACxC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAEhC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACxC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAEhC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACxC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG,CACI,cAAc,GAAA;QACjB,MAAM,KAAK,kKAAG,gBAAa,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAC3E,MAAM,KAAK,kKAAG,gBAAa,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAC3E,MAAM,KAAK,kKAAG,gBAAa,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAC3E,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,KAAM,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QAC3F,UAAU,CAAC,UAAU,GAAG,IAAI,CAAC;QAC7B,OAAO,UAAU,CAAC;IACtB,CAAC;IAED,uBAAA,EAAyB,CAClB,OAAO,GAAA;QACV,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IACtB,CAAC;IAEO,MAAM,CAAC,oBAAoB,CAAC,IAAmB,EAAE,EAAU,EAAA;QAC/D,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QACrC,KAAK,MAAM,IAAI,IAAI,MAAM,CAAE,CAAC;YACxB,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC/B,CAAC;IACL,CAAC;CACJ", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Debug/boneAxesViewer.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Debug/boneAxesViewer.ts"], "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport { <PERSON><PERSON><PERSON>ie<PERSON> } from \"../Debug/axesViewer\";\r\nimport { Vector3 } from \"../Maths/math.vector\";\r\nimport type { Mesh } from \"../Meshes/mesh\";\r\nimport type { Bone } from \"../Bones/bone\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Axis } from \"../Maths/math.axis\";\r\n\r\n/**\r\n * The BoneAxesViewer will attach 3 axes to a specific bone of a specific mesh\r\n * @see demo here: https://www.babylonjs-playground.com/#0DE8F4#8\r\n */\r\nexport class BoneAxesViewer extends AxesViewer {\r\n    /**\r\n     * Gets or sets the target mesh where to display the axes viewer\r\n     */\r\n    public mesh: Nullable<Mesh>;\r\n    /**\r\n     * Gets or sets the target bone where to display the axes viewer\r\n     */\r\n    public bone: Nullable<Bone>;\r\n\r\n    /** Gets current position */\r\n    public pos = Vector3.Zero();\r\n    /** Gets direction of X axis */\r\n    public xaxis = Vector3.Zero();\r\n    /** Gets direction of Y axis */\r\n    public yaxis = Vector3.Zero();\r\n    /** Gets direction of Z axis */\r\n    public zaxis = Vector3.Zero();\r\n\r\n    /**\r\n     * Creates a new BoneAxesViewer\r\n     * @param scene defines the hosting scene\r\n     * @param bone defines the target bone\r\n     * @param mesh defines the target mesh\r\n     * @param scaleLines defines a scaling factor for line length (1 by default)\r\n     */\r\n    constructor(scene: Scene, bone: Bone, mesh: Mesh, scaleLines = 1) {\r\n        super(scene, scaleLines);\r\n\r\n        this.mesh = mesh;\r\n        this.bone = bone;\r\n    }\r\n\r\n    /**\r\n     * Force the viewer to update\r\n     */\r\n    public override update(): void {\r\n        if (!this.mesh || !this.bone) {\r\n            return;\r\n        }\r\n\r\n        const bone = this.bone;\r\n        bone.getAbsolutePositionToRef(this.mesh, this.pos);\r\n        bone.getDirectionToRef(Axis.X, this.mesh, this.xaxis);\r\n        bone.getDirectionToRef(Axis.Y, this.mesh, this.yaxis);\r\n        bone.getDirectionToRef(Axis.Z, this.mesh, this.zaxis);\r\n\r\n        super.update(this.pos, this.xaxis, this.yaxis, this.zaxis);\r\n    }\r\n\r\n    /** Releases resources */\r\n    public override dispose() {\r\n        if (this.mesh) {\r\n            this.mesh = null;\r\n            this.bone = null;\r\n\r\n            super.dispose();\r\n        }\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AACA,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AACjD,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAI/C,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;;;;AAMpC,MAAO,cAAe,oKAAQ,aAAU;IAmB1C;;;;;;OAMG,CACH,YAAY,KAAY,EAAE,IAAU,EAAE,IAAU,EAAE,UAAU,GAAG,CAAC,CAAA;QAC5D,KAAK,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;QAjB7B,0BAAA,EAA4B,CACrB,IAAA,CAAA,GAAG,kKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;QAC5B,6BAAA,EAA+B,CACxB,IAAA,CAAA,KAAK,kKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;QAC9B,6BAAA,EAA+B,CACxB,IAAA,CAAA,KAAK,kKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;QAC9B,6BAAA,EAA+B,CACxB,IAAA,CAAA,KAAK,kKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;QAY1B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACrB,CAAC;IAED;;OAEG,CACa,MAAM,GAAA;QAClB,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YAC3B,OAAO;QACX,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QACnD,IAAI,CAAC,iBAAiB,8JAAC,OAAI,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QACtD,IAAI,CAAC,iBAAiB,8JAAC,OAAI,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QACtD,IAAI,CAAC,iBAAiB,8JAAC,OAAI,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAEtD,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;IAC/D,CAAC;IAED,uBAAA,EAAyB,CACT,OAAO,GAAA;QACnB,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;YACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;YAEjB,KAAK,CAAC,OAAO,EAAE,CAAC;QACpB,CAAC;IACL,CAAC;CACJ", "debugId": null}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Debug/debugLayer.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Debug/debugLayer.ts"], "sourcesContent": ["import { Tools } from \"../Misc/tools\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport { Scene } from \"../scene\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\nimport type { IInspectable } from \"../Misc/iInspectable\";\r\nimport type { Camera } from \"../Cameras/camera\";\r\nimport { AbstractEngine } from \"core/Engines/abstractEngine\";\r\n\r\n// declare INSPECTOR namespace for compilation issue\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\ndeclare let INSPECTOR: any;\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\ndeclare let BABYLON: any;\r\n// load the inspector using require, if not present in the global namespace.\r\n\r\n/**\r\n * Interface used to define scene explorer extensibility option\r\n */\r\nexport interface IExplorerExtensibilityOption {\r\n    /**\r\n     * Define the option label\r\n     */\r\n    label: string;\r\n    /**\r\n     * Defines the action to execute on click\r\n     */\r\n    action: (entity: any) => void;\r\n    /**\r\n     * Keep popup open after click\r\n     */\r\n    keepOpenAfterClick?: boolean;\r\n}\r\n\r\n/**\r\n * Defines a group of actions associated with a predicate to use when extending the Inspector scene explorer\r\n */\r\nexport interface IExplorerExtensibilityGroup {\r\n    /**\r\n     * Defines a predicate to test if a given type mut be extended\r\n     */\r\n    predicate: (entity: any) => boolean;\r\n    /**\r\n     * Gets the list of options added to a type\r\n     */\r\n    entries: IExplorerExtensibilityOption[];\r\n}\r\n\r\n/**\r\n * Defines a new node that will be displayed as top level node in the explorer\r\n */\r\nexport interface IExplorerAdditionalChild {\r\n    /**\r\n     * Gets the name of the additional node\r\n     */\r\n    name: string;\r\n    /**\r\n     * Function used to return the class name of the child node\r\n     */\r\n    getClassName(): string;\r\n    /**\r\n     * List of inspectable custom properties (used by the Inspector)\r\n     * @see https://doc.babylonjs.com/toolsAndResources/inspector#extensibility\r\n     */\r\n    inspectableCustomProperties: IInspectable[];\r\n}\r\n\r\n/**\r\n * Defines a new node that will be displayed as top level node in the explorer\r\n */\r\nexport interface IExplorerAdditionalNode {\r\n    /**\r\n     * Gets the name of the additional node\r\n     */\r\n    name: string;\r\n    /**\r\n     * Function used to return the list of child entries\r\n     */\r\n    getContent(): IExplorerAdditionalChild[];\r\n}\r\n\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport type IInspectorContextMenuType = \"pipeline\" | \"node\" | \"materials\" | \"spriteManagers\" | \"particleSystems\" | \"frameGraphs\";\r\n\r\n/**\r\n * Context menu item\r\n */\r\nexport interface IInspectorContextMenuItem {\r\n    /**\r\n     * Display label - menu item\r\n     */\r\n    label: string;\r\n    /**\r\n     * Callback function that will be called when the menu item is selected\r\n     * @param entity the entity that is currently selected in the scene explorer\r\n     */\r\n    action: (entity?: unknown) => void;\r\n}\r\n\r\n/**\r\n * Interface used to define the options to use to create the Inspector\r\n */\r\nexport interface IInspectorOptions {\r\n    /**\r\n     * Display in overlay mode (default: false)\r\n     */\r\n    overlay?: boolean;\r\n    /**\r\n     * HTML element to use as root (the parent of the rendering canvas will be used as default value)\r\n     */\r\n    globalRoot?: HTMLElement;\r\n    /**\r\n     * Display the Scene explorer\r\n     */\r\n    showExplorer?: boolean;\r\n    /**\r\n     * Display the property inspector\r\n     */\r\n    showInspector?: boolean;\r\n    /**\r\n     * Display in embed mode (both panes on the right)\r\n     */\r\n    embedMode?: boolean;\r\n    /**\r\n     * let the Inspector handles resize of the canvas when panes are resized (default to true)\r\n     */\r\n    handleResize?: boolean;\r\n    /**\r\n     * Allow the panes to popup (default: true)\r\n     */\r\n    enablePopup?: boolean;\r\n    /**\r\n     * Allow the panes to be closed by users (default: true)\r\n     */\r\n    enableClose?: boolean;\r\n    /**\r\n     * Optional list of extensibility entries\r\n     */\r\n    explorerExtensibility?: IExplorerExtensibilityGroup[];\r\n    /**\r\n     * Optional list of additional top level nodes\r\n     */\r\n    additionalNodes?: IExplorerAdditionalNode[];\r\n    /**\r\n     * Optional URL to get the inspector script from (by default it uses the babylonjs CDN).\r\n     */\r\n    inspectorURL?: string;\r\n    /**\r\n     * Optional initial tab (default to DebugLayerTab.Properties)\r\n     */\r\n    initialTab?: DebugLayerTab;\r\n    /**\r\n     * Optional camera to use to render the gizmos from the inspector (default to the scene.activeCamera or the latest from scene.activeCameras)\r\n     */\r\n    gizmoCamera?: Camera;\r\n    /**\r\n     * Context menu for inspector tools such as \"Post Process\", \"Nodes\", \"Materials\", etc.\r\n     */\r\n    contextMenu?: Partial<Record<IInspectorContextMenuType, IInspectorContextMenuItem[]>>;\r\n    /**\r\n     * List of context menu items that should be completely overridden by custom items from the contextMenu property.\r\n     */\r\n    contextMenuOverride?: IInspectorContextMenuType[];\r\n\r\n    /**\r\n     * Should the default font loading be skipped\r\n     */\r\n    skipDefaultFontLoading?: boolean;\r\n}\r\n\r\ndeclare module \"../scene\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface Scene {\r\n        /**\r\n         * @internal\r\n         * Backing field\r\n         */\r\n        _debugLayer: DebugLayer;\r\n\r\n        /**\r\n         * Gets the debug layer (aka Inspector) associated with the scene\r\n         * @see https://doc.babylonjs.com/toolsAndResources/inspector\r\n         */\r\n        debugLayer: DebugLayer;\r\n    }\r\n}\r\nObject.defineProperty(Scene.prototype, \"debugLayer\", {\r\n    get: function (this: Scene) {\r\n        if (!this._debugLayer) {\r\n            this._debugLayer = new DebugLayer(this);\r\n        }\r\n        return this._debugLayer;\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n\r\n/**\r\n * Enum of inspector action tab\r\n */\r\nexport const enum DebugLayerTab {\r\n    /**\r\n     * Properties tag (default)\r\n     */\r\n    Properties = 0,\r\n    /**\r\n     * Debug tab\r\n     */\r\n    Debug = 1,\r\n    /**\r\n     * Statistics tab\r\n     */\r\n    Statistics = 2,\r\n    /**\r\n     * Tools tab\r\n     */\r\n    Tools = 3,\r\n    /**\r\n     * Settings tab\r\n     */\r\n    Settings = 4,\r\n}\r\n\r\n/**\r\n * The debug layer (aka Inspector) is the go to tool in order to better understand\r\n * what is happening in your scene\r\n * @see https://doc.babylonjs.com/toolsAndResources/inspector\r\n */\r\nexport class DebugLayer {\r\n    /**\r\n     * Define the url to get the inspector script from.\r\n     * By default it uses the babylonjs CDN.\r\n     * @ignoreNaming\r\n     */\r\n    public static InspectorURL = `${Tools._DefaultCdnUrl}/v${AbstractEngine.Version}/inspector/babylon.inspector.bundle.js`;\r\n\r\n    /**\r\n     * The default configuration of the inspector\r\n     */\r\n    public static Config: IInspectorOptions = {\r\n        overlay: false,\r\n        showExplorer: true,\r\n        showInspector: true,\r\n        embedMode: false,\r\n        handleResize: true,\r\n        enablePopup: true,\r\n    };\r\n\r\n    private _scene: Scene;\r\n\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private BJSINSPECTOR = this._getGlobalInspector();\r\n\r\n    private _onPropertyChangedObservable?: Observable<{ object: any; property: string; value: any; initialValue: any }>;\r\n    /**\r\n     * Observable triggered when a property is changed through the inspector.\r\n     */\r\n    public get onPropertyChangedObservable() {\r\n        if (this.BJSINSPECTOR && this.BJSINSPECTOR.Inspector) {\r\n            return this.BJSINSPECTOR.Inspector.OnPropertyChangedObservable;\r\n        }\r\n\r\n        if (!this._onPropertyChangedObservable) {\r\n            this._onPropertyChangedObservable = new Observable<{ object: any; property: string; value: any; initialValue: any }>();\r\n        }\r\n\r\n        return this._onPropertyChangedObservable;\r\n    }\r\n\r\n    private _onSelectionChangedObservable?: Observable<any>;\r\n    /**\r\n     * Observable triggered when the selection is changed through the inspector.\r\n     */\r\n    public get onSelectionChangedObservable() {\r\n        if (this.BJSINSPECTOR && this.BJSINSPECTOR.Inspector) {\r\n            return this.BJSINSPECTOR.Inspector.OnSelectionChangeObservable;\r\n        }\r\n\r\n        if (!this._onSelectionChangedObservable) {\r\n            this._onSelectionChangedObservable = new Observable<any>();\r\n        }\r\n\r\n        return this._onSelectionChangedObservable;\r\n    }\r\n\r\n    /**\r\n     * Instantiates a new debug layer.\r\n     * The debug layer (aka Inspector) is the go to tool in order to better understand\r\n     * what is happening in your scene\r\n     * @see https://doc.babylonjs.com/toolsAndResources/inspector\r\n     * @param scene Defines the scene to inspect\r\n     */\r\n    constructor(scene?: Scene) {\r\n        this._scene = scene || <Scene>EngineStore.LastCreatedScene;\r\n        if (!this._scene) {\r\n            return;\r\n        }\r\n        this._scene.onDisposeObservable.add(() => {\r\n            // Debug layer\r\n            if (this._scene._debugLayer) {\r\n                this._scene._debugLayer.hide();\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Creates the inspector window.\r\n     * @param config\r\n     */\r\n    private _createInspector(config?: Partial<IInspectorOptions>) {\r\n        if (this.isVisible()) {\r\n            return;\r\n        }\r\n\r\n        if (this._onPropertyChangedObservable) {\r\n            for (const observer of this._onPropertyChangedObservable.observers) {\r\n                this.BJSINSPECTOR.Inspector.OnPropertyChangedObservable.add(observer);\r\n            }\r\n            this._onPropertyChangedObservable.clear();\r\n            this._onPropertyChangedObservable = undefined;\r\n        }\r\n\r\n        if (this._onSelectionChangedObservable) {\r\n            for (const observer of this._onSelectionChangedObservable.observers) {\r\n                this.BJSINSPECTOR.Inspector.OnSelectionChangedObservable.add(observer);\r\n            }\r\n            this._onSelectionChangedObservable.clear();\r\n            this._onSelectionChangedObservable = undefined;\r\n        }\r\n\r\n        const userOptions: IInspectorOptions = {\r\n            ...DebugLayer.Config,\r\n            ...config,\r\n        };\r\n\r\n        this.BJSINSPECTOR = this.BJSINSPECTOR || this._getGlobalInspector();\r\n\r\n        this.BJSINSPECTOR.Inspector.Show(this._scene, userOptions);\r\n    }\r\n\r\n    /**\r\n     * Select a specific entity in the scene explorer and highlight a specific block in that entity property grid\r\n     * @param entity defines the entity to select\r\n     * @param lineContainerTitles defines the specific blocks to highlight (could be a string or an array of strings)\r\n     */\r\n    public select(entity: any, lineContainerTitles?: string | string[]) {\r\n        if (this.BJSINSPECTOR) {\r\n            if (lineContainerTitles) {\r\n                if (Object.prototype.toString.call(lineContainerTitles) == \"[object String]\") {\r\n                    this.BJSINSPECTOR.Inspector.MarkLineContainerTitleForHighlighting(lineContainerTitles);\r\n                } else {\r\n                    this.BJSINSPECTOR.Inspector.MarkMultipleLineContainerTitlesForHighlighting(lineContainerTitles);\r\n                }\r\n            }\r\n            if (!this.BJSINSPECTOR.Inspector.IsVisible) {\r\n                setTimeout(() => {\r\n                    this.select(entity, lineContainerTitles);\r\n                }, 100);\r\n            } else {\r\n                this.BJSINSPECTOR.Inspector.OnSelectionChangeObservable.notifyObservers(entity);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get the inspector from bundle or global\r\n     * @returns the inspector instance if found otherwise, null\r\n     */\r\n    private _getGlobalInspector(): any {\r\n        // UMD Global name detection from Webpack Bundle UMD Name.\r\n        if (typeof INSPECTOR !== \"undefined\") {\r\n            return INSPECTOR;\r\n        }\r\n\r\n        // In case of module let s check the global emitted from the Inspector entry point.\r\n        if (typeof BABYLON !== \"undefined\" && typeof BABYLON.Inspector !== \"undefined\") {\r\n            return BABYLON;\r\n        }\r\n\r\n        return undefined;\r\n    }\r\n\r\n    /**\r\n     * Get if the inspector is visible or not.\r\n     * @returns true if visible otherwise, false\r\n     */\r\n    public isVisible(): boolean {\r\n        return this.BJSINSPECTOR && this.BJSINSPECTOR.Inspector.IsVisible;\r\n    }\r\n\r\n    /**\r\n     * Hide the inspector and close its window.\r\n     */\r\n    public hide() {\r\n        if (this.BJSINSPECTOR) {\r\n            this.BJSINSPECTOR.Inspector.Hide();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get the number of opened panes in the inspector\r\n     */\r\n    public get openedPanes() {\r\n        if (this.BJSINSPECTOR) {\r\n            return this.BJSINSPECTOR.Inspector._OpenedPane;\r\n        }\r\n\r\n        return 0;\r\n    }\r\n\r\n    /**\r\n     * Update the scene in the inspector\r\n     */\r\n    public setAsActiveScene() {\r\n        if (this.BJSINSPECTOR) {\r\n            this.BJSINSPECTOR.Inspector._SetNewScene(this._scene);\r\n        }\r\n    }\r\n\r\n    public popupSceneExplorer() {\r\n        if (this.BJSINSPECTOR) {\r\n            this.BJSINSPECTOR.Inspector.PopupSceneExplorer();\r\n        }\r\n    }\r\n\r\n    public popupInspector() {\r\n        if (this.BJSINSPECTOR) {\r\n            this.BJSINSPECTOR.Inspector.PopupInspector();\r\n        }\r\n    }\r\n\r\n    public popupEmbed() {\r\n        if (this.BJSINSPECTOR) {\r\n            this.BJSINSPECTOR.Inspector.PopupEmbed();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Launch the debugLayer.\r\n     * @param config Define the configuration of the inspector\r\n     * @returns a promise fulfilled when the debug layer is visible\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public async show(config?: IInspectorOptions): Promise<DebugLayer> {\r\n        return await new Promise((resolve) => {\r\n            if (typeof this.BJSINSPECTOR == \"undefined\") {\r\n                const inspectorUrl = config && config.inspectorURL ? config.inspectorURL : DebugLayer.InspectorURL;\r\n\r\n                // Load inspector and add it to the DOM\r\n                Tools.LoadBabylonScript(inspectorUrl, () => {\r\n                    this._createInspector(config);\r\n                    resolve(this);\r\n                });\r\n            } else {\r\n                // Otherwise creates the inspector\r\n                this._createInspector(config);\r\n                resolve(this);\r\n            }\r\n        });\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AACtC,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,KAAK,EAAE,MAAM,UAAU,CAAC;AACjC,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AAGrD,OAAO,EAAE,cAAc,EAAE,qCAAoC;;;;;;AAmL7D,MAAM,CAAC,cAAc,8IAAC,QAAK,CAAC,SAAS,EAAE,YAAY,EAAE;IACjD,GAAG,EAAE;QACD,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACpB,IAAI,CAAC,WAAW,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;QAC5C,CAAC;QACD,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;AAKH,IAAkB,aAqBjB;AArBD,CAAA,SAAkB,aAAa;IAC3B;;OAEG,CACH,aAAA,CAAA,aAAA,CAAA,aAAA,GAAA,EAAA,GAAA,YAAc,CAAA;IACd;;OAEG,CACH,aAAA,CAAA,aAAA,CAAA,QAAA,GAAA,EAAA,GAAA,OAAS,CAAA;IACT;;OAEG,CACH,aAAA,CAAA,aAAA,CAAA,aAAA,GAAA,EAAA,GAAA,YAAc,CAAA;IACd;;OAEG,CACH,aAAA,CAAA,aAAA,CAAA,QAAA,GAAA,EAAA,GAAA,OAAS,CAAA;IACT;;OAEG,CACH,aAAA,CAAA,aAAA,CAAA,WAAA,GAAA,EAAA,GAAA,UAAY,CAAA;AAChB,CAAC,EArBiB,aAAa,IAAA,CAAb,aAAa,GAAA,CAAA,CAAA,GAqB9B;AAOK,MAAO,UAAU;IA0BnB;;OAEG,CACH,IAAW,2BAA2B,GAAA;QAClC,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC;YACnD,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,2BAA2B,CAAC;QACnE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,4BAA4B,EAAE,CAAC;YACrC,IAAI,CAAC,4BAA4B,GAAG,8JAAI,aAAU,EAAoE,CAAC;QAC3H,CAAC;QAED,OAAO,IAAI,CAAC,4BAA4B,CAAC;IAC7C,CAAC;IAGD;;OAEG,CACH,IAAW,4BAA4B,GAAA;QACnC,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC;YACnD,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,2BAA2B,CAAC;QACnE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,6BAA6B,EAAE,CAAC;YACtC,IAAI,CAAC,6BAA6B,GAAG,8JAAI,aAAU,EAAO,CAAC;QAC/D,CAAC;QAED,OAAO,IAAI,CAAC,6BAA6B,CAAC;IAC9C,CAAC;IAED;;;;;;OAMG,CACH,YAAY,KAAa,CAAA;QA1CzB,gEAAgE;QACxD,IAAA,CAAA,YAAY,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QA0C9C,IAAI,CAAC,MAAM,GAAG,KAAK,kKAAW,cAAW,CAAC,gBAAgB,CAAC;QAC3D,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACf,OAAO;QACX,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE;YACrC,cAAc;YACd,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;gBAC1B,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;YACnC,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG,CACK,gBAAgB,CAAC,MAAmC,EAAA;QACxD,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC;YACnB,OAAO;QACX,CAAC;QAED,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;YACpC,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,4BAA4B,CAAC,SAAS,CAAE,CAAC;gBACjE,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,2BAA2B,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC1E,CAAC;YACD,IAAI,CAAC,4BAA4B,CAAC,KAAK,EAAE,CAAC;YAC1C,IAAI,CAAC,4BAA4B,GAAG,SAAS,CAAC;QAClD,CAAC;QAED,IAAI,IAAI,CAAC,6BAA6B,EAAE,CAAC;YACrC,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,6BAA6B,CAAC,SAAS,CAAE,CAAC;gBAClE,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,4BAA4B,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC3E,CAAC;YACD,IAAI,CAAC,6BAA6B,CAAC,KAAK,EAAE,CAAC;YAC3C,IAAI,CAAC,6BAA6B,GAAG,SAAS,CAAC;QACnD,CAAC;QAED,MAAM,WAAW,GAAsB;YACnC,GAAG,UAAU,CAAC,MAAM;YACpB,GAAG,MAAM;SACZ,CAAC;QAEF,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAEpE,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;IAC/D,CAAC;IAED;;;;OAIG,CACI,MAAM,CAAC,MAAW,EAAE,mBAAuC,EAAA;QAC9D,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,mBAAmB,EAAE,CAAC;gBACtB,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,iBAAiB,EAAE,CAAC;oBAC3E,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,qCAAqC,CAAC,mBAAmB,CAAC,CAAC;gBAC3F,CAAC,MAAM,CAAC;oBACJ,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,8CAA8C,CAAC,mBAAmB,CAAC,CAAC;gBACpG,CAAC;YACL,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;gBACzC,UAAU,CAAC,GAAG,EAAE;oBACZ,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,mBAAmB,CAAC,CAAC;gBAC7C,CAAC,EAAE,GAAG,CAAC,CAAC;YACZ,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,2BAA2B,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YACpF,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;OAGG,CACK,mBAAmB,GAAA;QACvB,0DAA0D;QAC1D,IAAI,OAAO,SAAS,KAAK,WAAW,EAAE,CAAC;YACnC,OAAO,SAAS,CAAC;QACrB,CAAC;QAED,mFAAmF;QACnF,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,OAAO,CAAC,SAAS,KAAK,WAAW,EAAE,CAAC;YAC7E,OAAO,OAAO,CAAC;QACnB,CAAC;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;OAGG,CACI,SAAS,GAAA;QACZ,OAAO,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC;IACtE,CAAC;IAED;;OAEG,CACI,IAAI,GAAA;QACP,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACvC,CAAC;IACL,CAAC;IAED;;OAEG,CACH,IAAW,WAAW,GAAA;QAClB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,WAAW,CAAC;QACnD,CAAC;QAED,OAAO,CAAC,CAAC;IACb,CAAC;IAED;;OAEG,CACI,gBAAgB,GAAA;QACnB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC1D,CAAC;IACL,CAAC;IAEM,kBAAkB,GAAA;QACrB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,kBAAkB,EAAE,CAAC;QACrD,CAAC;IACL,CAAC;IAEM,cAAc,GAAA;QACjB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC;QACjD,CAAC;IACL,CAAC;IAEM,UAAU,GAAA;QACb,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;QAC7C,CAAC;IACL,CAAC;IAED;;;;OAIG,CACH,gEAAgE;IACzD,KAAK,CAAC,IAAI,CAAC,MAA0B,EAAA;QACxC,OAAO,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YACjC,IAAI,OAAO,IAAI,CAAC,YAAY,IAAI,WAAW,EAAE,CAAC;gBAC1C,MAAM,YAAY,GAAG,MAAM,IAAI,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,UAAU,CAAC,YAAY,CAAC;gBAEnG,uCAAuC;qKACvC,QAAK,CAAC,iBAAiB,CAAC,YAAY,EAAE,GAAG,EAAE;oBACvC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;oBAC9B,OAAO,CAAC,IAAI,CAAC,CAAC;gBAClB,CAAC,CAAC,CAAC;YACP,CAAC,MAAM,CAAC;gBACJ,kCAAkC;gBAClC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;gBAC9B,OAAO,CAAC,IAAI,CAAC,CAAC;YAClB,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;;AAtOD;;;;GAIG,CACW,WAAA,YAAY,GAAG,wJAAG,QAAK,CAAC,cAAc,CAAA,EAAA,mKAAK,iBAAc,CAAC,OAAO,CAAA,sCAAA,CAArD,CAA8F;AAExH;;GAEG,CACW,WAAA,MAAM,GAAsB;IACtC,OAAO,EAAE,KAAK;IACd,YAAY,EAAE,IAAI;IAClB,aAAa,EAAE,IAAI;IACnB,SAAS,EAAE,KAAK;IAChB,YAAY,EAAE,IAAI;IAClB,WAAW,EAAE,IAAI;CACpB,AAPmB,CAOlB", "debugId": null}}, {"offset": {"line": 420, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Debug/physicsViewer.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Debug/physicsViewer.ts"], "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport type { Scene } from \"../scene\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport { Mesh } from \"../Meshes/mesh\";\r\nimport { CreateBox } from \"../Meshes/Builders/boxBuilder\";\r\nimport { CreateSphere } from \"../Meshes/Builders/sphereBuilder\";\r\nimport { Matrix, Quaternion, TmpVectors, Vector3 } from \"../Maths/math.vector\";\r\nimport { Color3, Color4 } from \"../Maths/math.color\";\r\nimport type { Material } from \"../Materials/material\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\nimport { StandardMaterial } from \"../Materials/standardMaterial\";\r\nimport type { IPhysicsEnginePlugin as IPhysicsEnginePluginV1 } from \"../Physics/v1/IPhysicsEnginePlugin\";\r\nimport { PhysicsConstraintAxis, PhysicsConstraintAxisLimitMode, type IPhysicsEnginePluginV2, type PhysicsMassProperties } from \"../Physics/v2/IPhysicsEnginePlugin\";\r\nimport { PhysicsImpostor } from \"../Physics/v1/physicsImpostor\";\r\nimport { UtilityLayerRenderer } from \"../Rendering/utilityLayerRenderer\";\r\nimport { CreateCylinder } from \"../Meshes/Builders/cylinderBuilder\";\r\nimport type { ICreateCapsuleOptions } from \"../Meshes/Builders/capsuleBuilder\";\r\nimport { CreateCapsule } from \"../Meshes/Builders/capsuleBuilder\";\r\nimport { Logger } from \"../Misc/logger\";\r\nimport type { PhysicsBody } from \"../Physics/v2/physicsBody\";\r\nimport { VertexData } from \"../Meshes/mesh.vertexData\";\r\nimport { MeshBuilder } from \"../Meshes/meshBuilder\";\r\nimport type { PhysicsConstraint } from \"../Physics/v2/physicsConstraint\";\r\nimport { AxesViewer } from \"./axesViewer\";\r\nimport { TransformNode } from \"../Meshes/transformNode\";\r\nimport { Epsilon } from \"../Maths/math.constants\";\r\n\r\n/**\r\n * Used to show the physics impostor around the specific mesh\r\n */\r\nexport class PhysicsViewer {\r\n    /** @internal */\r\n    protected _impostors: Array<Nullable<PhysicsImpostor>> = [];\r\n    /** @internal */\r\n    protected _meshes: Array<Nullable<AbstractMesh>> = [];\r\n    /** @internal */\r\n    protected _bodies: Array<Nullable<PhysicsBody>> = [];\r\n    /** @internal */\r\n    protected _inertiaBodies: Array<Nullable<PhysicsBody>> = [];\r\n    /** @internal */\r\n    protected _constraints: Array<Nullable<PhysicsConstraint>> = [];\r\n    /** @internal */\r\n    protected _bodyMeshes: Array<Nullable<AbstractMesh>> = [];\r\n    /** @internal */\r\n    protected _inertiaMeshes: Array<Nullable<AbstractMesh>> = [];\r\n    /** @internal */\r\n    protected _constraintMeshes: Array<Nullable<Array<AbstractMesh>>> = [];\r\n    /** @internal */\r\n    protected _scene: Nullable<Scene>;\r\n    /** @internal */\r\n    protected _numMeshes = 0;\r\n    /** @internal */\r\n    protected _numBodies = 0;\r\n    /** @internal */\r\n    protected _numInertiaBodies = 0;\r\n    /** @internal */\r\n    protected _numConstraints = 0;\r\n    /** @internal */\r\n    protected _physicsEnginePlugin: IPhysicsEnginePluginV1 | IPhysicsEnginePluginV2 | null;\r\n    private _renderFunction: () => void;\r\n    private _inertiaRenderFunction: () => void;\r\n    private _constraintRenderFunction: () => void;\r\n    private _utilityLayer: Nullable<UtilityLayerRenderer>;\r\n    private _ownUtilityLayer = false;\r\n\r\n    private _debugBoxMesh: Mesh;\r\n    private _debugSphereMesh: Mesh;\r\n    private _debugCapsuleMesh: Mesh;\r\n    private _debugCylinderMesh: Mesh;\r\n    private _debugMaterial: StandardMaterial;\r\n    private _debugInertiaMaterial: StandardMaterial;\r\n    private _debugMeshMeshes = new Array<Mesh>();\r\n\r\n    private _constraintAxesSize = 0.4;\r\n    private _constraintAngularSize = 0.4;\r\n\r\n    /**\r\n     * Creates a new PhysicsViewer\r\n     * @param scene defines the hosting scene\r\n     * @param size Physics V2 size scalar\r\n     * @param utilityLayer The utility layer the viewer will be added to\r\n     */\r\n    constructor(scene?: Scene, size?: number, utilityLayer: UtilityLayerRenderer = UtilityLayerRenderer.DefaultUtilityLayer) {\r\n        this._scene = scene || EngineStore.LastCreatedScene;\r\n        if (!this._scene) {\r\n            return;\r\n        }\r\n        const physicEngine = this._scene.getPhysicsEngine();\r\n\r\n        if (physicEngine) {\r\n            this._physicsEnginePlugin = physicEngine.getPhysicsPlugin();\r\n        }\r\n\r\n        if (utilityLayer) {\r\n            this._utilityLayer = utilityLayer;\r\n        } else {\r\n            this._utilityLayer = new UtilityLayerRenderer(this._scene, false);\r\n            this._utilityLayer.pickUtilitySceneFirst = false;\r\n            this._utilityLayer.utilityLayerScene.autoClearDepthAndStencil = true;\r\n            this._ownUtilityLayer = true;\r\n        }\r\n        if (size) {\r\n            this._constraintAxesSize = 0.4 * size;\r\n            this._constraintAngularSize = 0.4 * size;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Updates the debug meshes of the physics engine.\r\n     *\r\n     * This code is useful for synchronizing the debug meshes of the physics engine with the physics impostor and mesh.\r\n     * It checks if the impostor is disposed and if the plugin version is 1, then it syncs the mesh with the impostor.\r\n     * This ensures that the debug meshes are up to date with the physics engine.\r\n     */\r\n    protected _updateDebugMeshes(): void {\r\n        const plugin = this._physicsEnginePlugin;\r\n\r\n        if (plugin?.getPluginVersion() === 1) {\r\n            this._updateDebugMeshesV1();\r\n        } else {\r\n            this._updateDebugMeshesV2();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Updates the debug meshes of the physics engine.\r\n     *\r\n     * This method is useful for synchronizing the debug meshes with the physics impostors.\r\n     * It iterates through the impostors and meshes, and if the plugin version is 1, it syncs the mesh with the impostor.\r\n     * This ensures that the debug meshes accurately reflect the physics impostors, which is important for debugging the physics engine.\r\n     */\r\n    protected _updateDebugMeshesV1(): void {\r\n        const plugin = this._physicsEnginePlugin as IPhysicsEnginePluginV1;\r\n        for (let i = 0; i < this._numMeshes; i++) {\r\n            const impostor = this._impostors[i];\r\n\r\n            if (!impostor) {\r\n                continue;\r\n            }\r\n\r\n            if (impostor.isDisposed) {\r\n                this.hideImpostor(this._impostors[i--]);\r\n            } else {\r\n                if (impostor.type === PhysicsImpostor.MeshImpostor) {\r\n                    continue;\r\n                }\r\n                const mesh = this._meshes[i];\r\n\r\n                if (mesh && plugin) {\r\n                    plugin.syncMeshWithImpostor(mesh, impostor);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Updates the debug meshes of the physics engine for V2 plugin.\r\n     *\r\n     * This method is useful for synchronizing the debug meshes of the physics engine with the current state of the bodies.\r\n     * It iterates through the bodies array and updates the debug meshes with the current transform of each body.\r\n     * This ensures that the debug meshes accurately reflect the current state of the physics engine.\r\n     */\r\n    protected _updateDebugMeshesV2(): void {\r\n        const plugin = this._physicsEnginePlugin as IPhysicsEnginePluginV2;\r\n        for (let i = 0; i < this._numBodies; ) {\r\n            const body = this._bodies[i];\r\n            if (body && body.isDisposed && this.hideBody(body)) {\r\n                continue;\r\n            }\r\n            const transform = this._bodyMeshes[i];\r\n            if (body && transform) {\r\n                plugin.syncTransform(body, transform);\r\n            }\r\n            i++;\r\n        }\r\n    }\r\n\r\n    protected _updateInertiaMeshes(): void {\r\n        for (let i = 0; i < this._numInertiaBodies; ) {\r\n            const body = this._inertiaBodies[i];\r\n            if (body && body.isDisposed && this.hideInertia(body)) {\r\n                continue;\r\n            }\r\n            const mesh = this._inertiaMeshes[i];\r\n            if (body && mesh) {\r\n                this._updateDebugInertia(body, mesh);\r\n            }\r\n            i++;\r\n        }\r\n    }\r\n\r\n    protected _updateDebugInertia(body: PhysicsBody, inertiaMesh: AbstractMesh): void {\r\n        const inertiaMatrixRef = Matrix.Identity();\r\n        const transformMatrixRef = Matrix.Identity();\r\n        const finalMatrixRef = Matrix.Identity();\r\n        if (body._pluginDataInstances.length) {\r\n            const inertiaAsMesh = inertiaMesh as Mesh;\r\n            const inertiaMeshMatrixData = inertiaAsMesh._thinInstanceDataStorage.matrixData!;\r\n            const bodyTransformMatrixData = (body.transformNode as Mesh)._thinInstanceDataStorage.matrixData!;\r\n            for (let i = 0; i < body._pluginDataInstances.length; i++) {\r\n                const props = body.getMassProperties(i);\r\n                this._getMeshDebugInertiaMatrixToRef(props, inertiaMatrixRef);\r\n                Matrix.FromArrayToRef(bodyTransformMatrixData, i * 16, transformMatrixRef);\r\n                inertiaMatrixRef.multiplyToRef(transformMatrixRef, finalMatrixRef);\r\n                finalMatrixRef.copyToArray(inertiaMeshMatrixData, i * 16);\r\n            }\r\n            inertiaAsMesh.thinInstanceBufferUpdated(\"matrix\");\r\n        } else {\r\n            const props = body.getMassProperties();\r\n            this._getMeshDebugInertiaMatrixToRef(props, inertiaMatrixRef);\r\n            body.transformNode.rotationQuaternion?.toRotationMatrix(transformMatrixRef);\r\n            transformMatrixRef.setTranslation(body.transformNode.position);\r\n            if (body.transformNode.parent) {\r\n                const parentTransform = body.transformNode.parent.computeWorldMatrix(true);\r\n                transformMatrixRef.multiplyToRef(parentTransform, transformMatrixRef);\r\n            }\r\n            inertiaMatrixRef.multiplyToRef(transformMatrixRef, inertiaMatrixRef);\r\n            inertiaMatrixRef.decomposeToTransformNode(inertiaMesh);\r\n        }\r\n    }\r\n\r\n    protected _updateDebugConstraints() {\r\n        for (let i = 0; i < this._numConstraints; i++) {\r\n            const constraint = this._constraints[i];\r\n            const mesh = this._constraintMeshes[i];\r\n            if (constraint && mesh) {\r\n                this._updateDebugConstraint(constraint, mesh[0]);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Given a scaling vector, make all of its components\r\n     * 1, preserving the sign\r\n     * @param scaling\r\n     */\r\n    protected _makeScalingUnitInPlace(scaling: Vector3) {\r\n        if (Math.abs(scaling.x - 1) > Epsilon) {\r\n            scaling.x = 1 * Math.sign(scaling.x);\r\n        }\r\n        if (Math.abs(scaling.y - 1) > Epsilon) {\r\n            scaling.y = 1 * Math.sign(scaling.y);\r\n        }\r\n        if (Math.abs(scaling.z - 1) > Epsilon) {\r\n            scaling.z = 1 * Math.sign(scaling.z);\r\n        }\r\n    }\r\n\r\n    protected _updateDebugConstraint(constraint: PhysicsConstraint, parentingMesh: AbstractMesh) {\r\n        if (!constraint._initOptions) {\r\n            return;\r\n        }\r\n\r\n        // Get constraint pivot and axes\r\n        const { pivotA, pivotB, axisA, axisB, perpAxisA, perpAxisB } = constraint._initOptions;\r\n\r\n        if (!pivotA || !pivotB || !axisA || !axisB || !perpAxisA || !perpAxisB) {\r\n            return;\r\n        }\r\n\r\n        const descendants = parentingMesh.getDescendants(true);\r\n        for (const parentConstraintMesh of descendants) {\r\n            // Get the parent transform\r\n            const parentCoordSystemNode = parentConstraintMesh.getDescendants(true)[0] as TransformNode;\r\n            const childCoordSystemNode = parentConstraintMesh.getDescendants(true)[1] as TransformNode;\r\n\r\n            const { parentBody, parentBodyIndex } = parentCoordSystemNode.metadata;\r\n            const { childBody, childBodyIndex } = childCoordSystemNode.metadata;\r\n\r\n            const parentTransform = this._getTransformFromBodyToRef(parentBody, TmpVectors.Matrix[0], parentBodyIndex);\r\n            const childTransform = this._getTransformFromBodyToRef(childBody, TmpVectors.Matrix[1], childBodyIndex);\r\n\r\n            parentTransform.decomposeToTransformNode(parentCoordSystemNode);\r\n            this._makeScalingUnitInPlace(parentCoordSystemNode.scaling);\r\n\r\n            childTransform.decomposeToTransformNode(childCoordSystemNode);\r\n            this._makeScalingUnitInPlace(childCoordSystemNode.scaling);\r\n\r\n            // Create a transform node and set its matrix\r\n            const parentTransformNode = parentCoordSystemNode.getDescendants(true)[0] as TransformNode;\r\n            parentTransformNode.position.copyFrom(pivotA);\r\n\r\n            const childTransformNode = childCoordSystemNode.getDescendants(true)[0] as TransformNode;\r\n            childTransformNode.position.copyFrom(pivotB);\r\n\r\n            // Get the transform to align the XYZ axes to the constraint axes\r\n            Quaternion.FromRotationMatrixToRef(\r\n                Matrix.FromXYZAxesToRef(axisA, perpAxisA, Vector3.CrossToRef(axisA, perpAxisA, TmpVectors.Vector3[0]), TmpVectors.Matrix[0]),\r\n                parentTransformNode.rotationQuaternion!\r\n            );\r\n            Quaternion.FromRotationMatrixToRef(\r\n                Matrix.FromXYZAxesToRef(axisB, perpAxisB, Vector3.CrossToRef(axisB, perpAxisB, TmpVectors.Vector3[1]), TmpVectors.Matrix[1]),\r\n                childTransformNode.rotationQuaternion!\r\n            );\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Renders a specified physic impostor\r\n     * @param impostor defines the impostor to render\r\n     * @param targetMesh defines the mesh represented by the impostor\r\n     * @returns the new debug mesh used to render the impostor\r\n     */\r\n    public showImpostor(impostor: PhysicsImpostor, targetMesh?: Mesh): Nullable<AbstractMesh> {\r\n        if (!this._scene) {\r\n            return null;\r\n        }\r\n\r\n        for (let i = 0; i < this._numMeshes; i++) {\r\n            if (this._impostors[i] == impostor) {\r\n                return null;\r\n            }\r\n        }\r\n\r\n        const debugMesh = this._getDebugMesh(impostor, targetMesh);\r\n\r\n        if (debugMesh) {\r\n            this._impostors[this._numMeshes] = impostor;\r\n            this._meshes[this._numMeshes] = debugMesh;\r\n\r\n            if (this._numMeshes === 0) {\r\n                this._renderFunction = () => this._updateDebugMeshes();\r\n                this._scene.registerBeforeRender(this._renderFunction);\r\n            }\r\n\r\n            this._numMeshes++;\r\n        }\r\n\r\n        return debugMesh;\r\n    }\r\n\r\n    /**\r\n     * Shows a debug mesh for a given physics body.\r\n     * @param body The physics body to show.\r\n     * @returns The debug mesh, or null if the body is already shown.\r\n     *\r\n     * This function is useful for visualizing the physics body in the scene.\r\n     * It creates a debug mesh for the given body and adds it to the scene.\r\n     * It also registers a before render function to update the debug mesh position and rotation.\r\n     */\r\n    public showBody(body: PhysicsBody): Nullable<AbstractMesh> {\r\n        if (!this._scene) {\r\n            return null;\r\n        }\r\n\r\n        for (let i = 0; i < this._numBodies; i++) {\r\n            if (this._bodies[i] == body) {\r\n                return null;\r\n            }\r\n        }\r\n\r\n        const debugMesh = this._getDebugBodyMesh(body);\r\n\r\n        if (debugMesh) {\r\n            this._bodies[this._numBodies] = body;\r\n            this._bodyMeshes[this._numBodies] = debugMesh;\r\n\r\n            if (this._numBodies === 0) {\r\n                this._renderFunction = () => this._updateDebugMeshes();\r\n                this._scene.registerBeforeRender(this._renderFunction);\r\n            }\r\n\r\n            this._numBodies++;\r\n        }\r\n\r\n        return debugMesh;\r\n    }\r\n\r\n    /**\r\n     * Shows a debug box corresponding to the inertia of a given body\r\n     * @param body the physics body used to get the inertia\r\n     * @returns the debug mesh used to show the inertia, or null if the body is already shown\r\n     */\r\n    public showInertia(body: PhysicsBody): Nullable<AbstractMesh> {\r\n        if (!this._scene) {\r\n            return null;\r\n        }\r\n\r\n        for (let i = 0; i < this._numInertiaBodies; i++) {\r\n            if (this._inertiaBodies[i] == body) {\r\n                return null;\r\n            }\r\n        }\r\n\r\n        const debugMesh = this._getDebugInertiaMesh(body);\r\n        if (debugMesh) {\r\n            this._inertiaBodies[this._numInertiaBodies] = body;\r\n            this._inertiaMeshes[this._numInertiaBodies] = debugMesh;\r\n\r\n            if (this._numInertiaBodies === 0) {\r\n                this._inertiaRenderFunction = () => this._updateInertiaMeshes();\r\n                this._scene.registerBeforeRender(this._inertiaRenderFunction);\r\n            }\r\n\r\n            this._numInertiaBodies++;\r\n        }\r\n\r\n        return debugMesh;\r\n    }\r\n\r\n    /**\r\n     * Shows a debug mesh for a given physics constraint.\r\n     * @param constraint the physics constraint to show\r\n     * @returns the debug mesh, or null if the constraint is already shown\r\n     */\r\n    public showConstraint(constraint: PhysicsConstraint): Nullable<AbstractMesh> {\r\n        if (!this._scene) {\r\n            return null;\r\n        }\r\n\r\n        for (let i = 0; i < this._numConstraints; i++) {\r\n            if (this._constraints[i] == constraint) {\r\n                return null;\r\n            }\r\n        }\r\n\r\n        const debugMesh = this._getDebugConstraintMesh(constraint);\r\n        if (debugMesh) {\r\n            this._constraints[this._numConstraints] = constraint;\r\n            this._constraintMeshes[this._numConstraints] = debugMesh;\r\n\r\n            if (this._numConstraints === 0) {\r\n                this._constraintRenderFunction = () => this._updateDebugConstraints();\r\n                this._scene.registerBeforeRender(this._constraintRenderFunction);\r\n            }\r\n\r\n            this._numConstraints++;\r\n        }\r\n\r\n        return debugMesh ? debugMesh[0] : null;\r\n    }\r\n\r\n    /**\r\n     * Hides an impostor from the scene.\r\n     * @param impostor - The impostor to hide.\r\n     *\r\n     * This method is useful for hiding an impostor from the scene. It removes the\r\n     * impostor from the utility layer scene, disposes the mesh, and removes the\r\n     * impostor from the list of impostors. If the impostor is the last one in the\r\n     * list, it also unregisters the render function.\r\n     */\r\n    public hideImpostor(impostor: Nullable<PhysicsImpostor>) {\r\n        if (!impostor || !this._scene || !this._utilityLayer) {\r\n            return;\r\n        }\r\n\r\n        let removed = false;\r\n        const utilityLayerScene = this._utilityLayer.utilityLayerScene;\r\n\r\n        for (let i = 0; i < this._numMeshes; i++) {\r\n            if (this._impostors[i] == impostor) {\r\n                const mesh = this._meshes[i];\r\n\r\n                if (!mesh) {\r\n                    continue;\r\n                }\r\n\r\n                utilityLayerScene.removeMesh(mesh);\r\n                mesh.dispose();\r\n\r\n                const index = this._debugMeshMeshes.indexOf(mesh as Mesh);\r\n                if (index > -1) {\r\n                    this._debugMeshMeshes.splice(index, 1);\r\n                }\r\n\r\n                this._numMeshes--;\r\n                if (this._numMeshes > 0) {\r\n                    this._meshes[i] = this._meshes[this._numMeshes];\r\n                    this._impostors[i] = this._impostors[this._numMeshes];\r\n                    this._meshes[this._numMeshes] = null;\r\n                    this._impostors[this._numMeshes] = null;\r\n                } else {\r\n                    this._meshes[0] = null;\r\n                    this._impostors[0] = null;\r\n                }\r\n                removed = true;\r\n                break;\r\n            }\r\n        }\r\n\r\n        if (removed && this._numMeshes === 0) {\r\n            this._scene.unregisterBeforeRender(this._renderFunction);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Hides a body from the physics engine.\r\n     * @param body - The body to hide.\r\n     * @returns true if body actually removed\r\n     *\r\n     * This function is useful for hiding a body from the physics engine.\r\n     * It removes the body from the utility layer scene and disposes the mesh associated with it.\r\n     * It also unregisters the render function if the number of bodies is 0.\r\n     * This is useful for hiding a body from the physics engine without deleting it.\r\n     */\r\n    public hideBody(body: Nullable<PhysicsBody>): boolean {\r\n        if (!body || !this._scene || !this._utilityLayer) {\r\n            return false;\r\n        }\r\n\r\n        let removed = false;\r\n        const utilityLayerScene = this._utilityLayer.utilityLayerScene;\r\n\r\n        for (let i = 0; i < this._numBodies; i++) {\r\n            if (this._bodies[i] === body) {\r\n                const mesh = this._bodyMeshes[i];\r\n\r\n                if (!mesh) {\r\n                    continue;\r\n                }\r\n\r\n                utilityLayerScene.removeMesh(mesh);\r\n                mesh.dispose();\r\n\r\n                this._numBodies--;\r\n                if (this._numBodies > 0) {\r\n                    this._bodyMeshes[i] = this._bodyMeshes[this._numBodies];\r\n                    this._bodies[i] = this._bodies[this._numBodies];\r\n                    this._bodyMeshes[this._numBodies] = null;\r\n                    this._bodies[this._numBodies] = null;\r\n                } else {\r\n                    this._bodyMeshes[0] = null;\r\n                    this._bodies[0] = null;\r\n                }\r\n                removed = true;\r\n                break;\r\n            }\r\n        }\r\n\r\n        if (removed && this._numBodies === 0) {\r\n            this._scene.unregisterBeforeRender(this._renderFunction);\r\n        }\r\n        return removed;\r\n    }\r\n\r\n    /**\r\n     * Hides a body's inertia from the viewer utility layer\r\n     * @param body the body to hide\r\n     * @returns true if inertia actually removed\r\n     */\r\n    public hideInertia(body: Nullable<PhysicsBody>): boolean {\r\n        if (!body || !this._scene || !this._utilityLayer) {\r\n            return false;\r\n        }\r\n        let removed = false;\r\n        const utilityLayerScene = this._utilityLayer.utilityLayerScene;\r\n\r\n        for (let i = 0; i < this._numInertiaBodies; i++) {\r\n            if (this._inertiaBodies[i] === body) {\r\n                const mesh = this._inertiaMeshes[i];\r\n\r\n                if (!mesh) {\r\n                    continue;\r\n                }\r\n\r\n                utilityLayerScene.removeMesh(mesh);\r\n                mesh.dispose();\r\n\r\n                this._inertiaBodies.splice(i, 1);\r\n                this._inertiaMeshes.splice(i, 1);\r\n\r\n                this._numInertiaBodies--;\r\n\r\n                removed = true;\r\n                break;\r\n            }\r\n        }\r\n\r\n        if (removed && this._numInertiaBodies === 0) {\r\n            this._scene.unregisterBeforeRender(this._inertiaRenderFunction);\r\n        }\r\n        return removed;\r\n    }\r\n\r\n    /**\r\n     * Hide a physics constraint from the viewer utility layer\r\n     * @param constraint the constraint to hide\r\n     */\r\n    public hideConstraint(constraint: Nullable<PhysicsConstraint>) {\r\n        if (!constraint || !this._scene || !this._utilityLayer) {\r\n            return;\r\n        }\r\n        let removed = false;\r\n        const utilityLayerScene = this._utilityLayer.utilityLayerScene;\r\n\r\n        for (let i = 0; i < this._numConstraints; i++) {\r\n            if (this._constraints[i] === constraint) {\r\n                const meshes = this._constraintMeshes[i];\r\n\r\n                if (!meshes) {\r\n                    continue;\r\n                }\r\n\r\n                for (const mesh of meshes) {\r\n                    utilityLayerScene.removeMesh(mesh);\r\n                    mesh.dispose();\r\n                }\r\n\r\n                this._constraints.splice(i, 1);\r\n                this._constraintMeshes.splice(i, 1);\r\n\r\n                this._numConstraints--;\r\n\r\n                if (this._numConstraints > 0) {\r\n                    this._constraints[i] = this._constraints[this._numConstraints];\r\n                    this._constraintMeshes[i] = this._constraintMeshes[this._numConstraints];\r\n                    this._constraints[this._numConstraints] = null;\r\n                    this._constraintMeshes[this._numConstraints] = null;\r\n                } else {\r\n                    this._constraints[0] = null;\r\n                    this._constraintMeshes[0] = null;\r\n                }\r\n\r\n                removed = true;\r\n                break;\r\n            }\r\n        }\r\n\r\n        if (removed && this._numConstraints === 0) {\r\n            this._scene.unregisterBeforeRender(this._constraintRenderFunction);\r\n        }\r\n    }\r\n\r\n    private _getDebugMaterial(scene: Scene): Material {\r\n        if (!this._debugMaterial) {\r\n            this._debugMaterial = new StandardMaterial(\"\", scene);\r\n            this._debugMaterial.wireframe = true;\r\n            this._debugMaterial.emissiveColor = Color3.White();\r\n            this._debugMaterial.disableLighting = true;\r\n        }\r\n\r\n        return this._debugMaterial;\r\n    }\r\n\r\n    private _getDebugInertiaMaterial(scene: Scene): Material {\r\n        if (!this._debugInertiaMaterial) {\r\n            this._debugInertiaMaterial = new StandardMaterial(\"\", scene);\r\n            this._debugInertiaMaterial.disableLighting = true;\r\n            this._debugInertiaMaterial.alpha = 0.0;\r\n        }\r\n\r\n        return this._debugInertiaMaterial;\r\n    }\r\n\r\n    private _getDebugAxisColoredMaterial(axisNumber: number, scene: Scene): Material {\r\n        const material = new StandardMaterial(\"\", scene);\r\n        material.emissiveColor = axisNumber == 0 ? Color3.Red() : axisNumber == 1 ? Color3.Green() : Color3.Blue();\r\n        material.disableLighting = true;\r\n        return material;\r\n    }\r\n\r\n    private _getDebugBoxMesh(scene: Scene): AbstractMesh {\r\n        if (!this._debugBoxMesh) {\r\n            this._debugBoxMesh = CreateBox(\"physicsBodyBoxViewMesh\", { size: 1 }, scene);\r\n            this._debugBoxMesh.rotationQuaternion = Quaternion.Identity();\r\n            this._debugBoxMesh.material = this._getDebugMaterial(scene);\r\n            this._debugBoxMesh.setEnabled(false);\r\n        }\r\n\r\n        return this._debugBoxMesh.createInstance(\"physicsBodyBoxViewInstance\");\r\n    }\r\n\r\n    private _getDebugSphereMesh(scene: Scene): AbstractMesh {\r\n        if (!this._debugSphereMesh) {\r\n            this._debugSphereMesh = CreateSphere(\"physicsBodySphereViewMesh\", { diameter: 1 }, scene);\r\n            this._debugSphereMesh.rotationQuaternion = Quaternion.Identity();\r\n            this._debugSphereMesh.material = this._getDebugMaterial(scene);\r\n            this._debugSphereMesh.setEnabled(false);\r\n        }\r\n\r\n        return this._debugSphereMesh.createInstance(\"physicsBodySphereViewInstance\");\r\n    }\r\n\r\n    private _getDebugCapsuleMesh(scene: Scene): AbstractMesh {\r\n        if (!this._debugCapsuleMesh) {\r\n            this._debugCapsuleMesh = CreateCapsule(\"physicsBodyCapsuleViewMesh\", { height: 1 } as ICreateCapsuleOptions, scene);\r\n            this._debugCapsuleMesh.rotationQuaternion = Quaternion.Identity();\r\n            this._debugCapsuleMesh.material = this._getDebugMaterial(scene);\r\n            this._debugCapsuleMesh.setEnabled(false);\r\n        }\r\n\r\n        return this._debugCapsuleMesh.createInstance(\"physicsBodyCapsuleViewInstance\");\r\n    }\r\n\r\n    private _getDebugCylinderMesh(scene: Scene): AbstractMesh {\r\n        if (!this._debugCylinderMesh) {\r\n            this._debugCylinderMesh = CreateCylinder(\"physicsBodyCylinderViewMesh\", { diameterTop: 1, diameterBottom: 1, height: 1 }, scene);\r\n            this._debugCylinderMesh.rotationQuaternion = Quaternion.Identity();\r\n            this._debugCylinderMesh.material = this._getDebugMaterial(scene);\r\n            this._debugCylinderMesh.setEnabled(false);\r\n        }\r\n\r\n        return this._debugCylinderMesh.createInstance(\"physicsBodyCylinderViewInstance\");\r\n    }\r\n\r\n    private _getDebugMeshMesh(mesh: Mesh, scene: Scene): AbstractMesh {\r\n        const wireframeOver = new Mesh(mesh.name, scene, null, mesh);\r\n        wireframeOver.setParent(mesh);\r\n        wireframeOver.position = Vector3.Zero();\r\n        wireframeOver.material = this._getDebugMaterial(scene);\r\n\r\n        this._debugMeshMeshes.push(wireframeOver);\r\n\r\n        return wireframeOver;\r\n    }\r\n\r\n    private _getDebugMesh(impostor: PhysicsImpostor, targetMesh?: Mesh): Nullable<AbstractMesh> {\r\n        if (!this._utilityLayer) {\r\n            return null;\r\n        }\r\n\r\n        // Only create child impostor debug meshes when evaluating the parent\r\n        if (targetMesh && targetMesh.parent && (targetMesh.parent as Mesh).physicsImpostor) {\r\n            return null;\r\n        }\r\n\r\n        let mesh: Nullable<AbstractMesh> = null;\r\n        const utilityLayerScene = this._utilityLayer.utilityLayerScene;\r\n        if (!impostor.physicsBody) {\r\n            Logger.Warn(\"Unable to get physicsBody of impostor. It might be initialized later by its parent's impostor.\");\r\n            return null;\r\n        }\r\n        switch (impostor.type) {\r\n            case PhysicsImpostor.BoxImpostor:\r\n                mesh = this._getDebugBoxMesh(utilityLayerScene);\r\n                impostor.getBoxSizeToRef(mesh.scaling);\r\n                break;\r\n            case PhysicsImpostor.SphereImpostor: {\r\n                mesh = this._getDebugSphereMesh(utilityLayerScene);\r\n                const radius = impostor.getRadius();\r\n                mesh.scaling.x = radius * 2;\r\n                mesh.scaling.y = radius * 2;\r\n                mesh.scaling.z = radius * 2;\r\n                break;\r\n            }\r\n            case PhysicsImpostor.CapsuleImpostor: {\r\n                mesh = this._getDebugCapsuleMesh(utilityLayerScene);\r\n                const bi = impostor.object.getBoundingInfo();\r\n                mesh.scaling.x = (bi.boundingBox.maximum.x - bi.boundingBox.minimum.x) * 2 * impostor.object.scaling.x;\r\n                mesh.scaling.y = (bi.boundingBox.maximum.y - bi.boundingBox.minimum.y) * impostor.object.scaling.y;\r\n                mesh.scaling.z = (bi.boundingBox.maximum.z - bi.boundingBox.minimum.z) * 2 * impostor.object.scaling.z;\r\n                break;\r\n            }\r\n            case PhysicsImpostor.MeshImpostor:\r\n                if (targetMesh) {\r\n                    mesh = this._getDebugMeshMesh(targetMesh, utilityLayerScene);\r\n                }\r\n                break;\r\n            case PhysicsImpostor.NoImpostor:\r\n                if (targetMesh) {\r\n                    // Handle compound impostors\r\n                    const childMeshes = targetMesh.getChildMeshes().filter((c) => {\r\n                        return c.physicsImpostor ? 1 : 0;\r\n                    });\r\n                    for (const m of childMeshes) {\r\n                        if (m.physicsImpostor && m.getClassName() === \"Mesh\") {\r\n                            const boundingInfo = m.getBoundingInfo();\r\n                            const min = boundingInfo.boundingBox.minimum;\r\n                            const max = boundingInfo.boundingBox.maximum;\r\n                            switch (m.physicsImpostor.type) {\r\n                                case PhysicsImpostor.BoxImpostor:\r\n                                    mesh = this._getDebugBoxMesh(utilityLayerScene);\r\n                                    mesh.position.copyFrom(min);\r\n                                    mesh.position.addInPlace(max);\r\n                                    mesh.position.scaleInPlace(0.5);\r\n                                    break;\r\n                                case PhysicsImpostor.SphereImpostor:\r\n                                    mesh = this._getDebugSphereMesh(utilityLayerScene);\r\n                                    break;\r\n                                case PhysicsImpostor.CylinderImpostor:\r\n                                    mesh = this._getDebugCylinderMesh(utilityLayerScene);\r\n                                    break;\r\n                                default:\r\n                                    mesh = null;\r\n                                    break;\r\n                            }\r\n                            if (mesh) {\r\n                                mesh.scaling.x = max.x - min.x;\r\n                                mesh.scaling.y = max.y - min.y;\r\n                                mesh.scaling.z = max.z - min.z;\r\n                                mesh.parent = m;\r\n                            }\r\n                        }\r\n                    }\r\n                } else {\r\n                    Logger.Warn(\"No target mesh parameter provided for NoImpostor. Skipping.\");\r\n                }\r\n                mesh = null;\r\n                break;\r\n            case PhysicsImpostor.CylinderImpostor: {\r\n                mesh = this._getDebugCylinderMesh(utilityLayerScene);\r\n                const bi = impostor.object.getBoundingInfo();\r\n                mesh.scaling.x = (bi.boundingBox.maximum.x - bi.boundingBox.minimum.x) * impostor.object.scaling.x;\r\n                mesh.scaling.y = (bi.boundingBox.maximum.y - bi.boundingBox.minimum.y) * impostor.object.scaling.y;\r\n                mesh.scaling.z = (bi.boundingBox.maximum.z - bi.boundingBox.minimum.z) * impostor.object.scaling.z;\r\n                break;\r\n            }\r\n        }\r\n        return mesh;\r\n    }\r\n\r\n    /**\r\n     * Creates a debug mesh for a given physics body\r\n     * @param body The physics body to create the debug mesh for\r\n     * @returns The created debug mesh or null if the utility layer is not available\r\n     *\r\n     * This code is useful for creating a debug mesh for a given physics body.\r\n     * It creates a Mesh object with a VertexData object containing the positions and indices\r\n     * of the geometry of the body. The mesh is then assigned a debug material from the utility layer scene.\r\n     * This allows for visualizing the physics body in the scene.\r\n     */\r\n    private _getDebugBodyMesh(body: PhysicsBody): Nullable<AbstractMesh> {\r\n        if (!this._utilityLayer) {\r\n            return null;\r\n        }\r\n\r\n        const utilityLayerScene = this._utilityLayer.utilityLayerScene;\r\n\r\n        const mesh = new Mesh(\"custom\", utilityLayerScene);\r\n        const vertexData = new VertexData();\r\n        const geometry = body.getGeometry() as any;\r\n        vertexData.positions = geometry.positions;\r\n        vertexData.indices = geometry.indices;\r\n        vertexData.applyToMesh(mesh);\r\n        if (body._pluginDataInstances) {\r\n            const instanceBuffer = new Float32Array(body._pluginDataInstances.length * 16);\r\n            mesh.thinInstanceSetBuffer(\"matrix\", instanceBuffer, 16, false);\r\n        }\r\n        mesh.material = this._getDebugMaterial(utilityLayerScene);\r\n        return mesh;\r\n    }\r\n\r\n    private _getMeshDebugInertiaMatrixToRef(massProps: PhysicsMassProperties, matrix: Matrix) {\r\n        const orientation = massProps.inertiaOrientation ?? Quaternion.Identity();\r\n        const inertiaLocal = massProps.inertia ?? Vector3.Zero();\r\n        const center = massProps.centerOfMass ?? Vector3.Zero();\r\n\r\n        const betaSqrd = (inertiaLocal.x - inertiaLocal.y + inertiaLocal.z) * 6;\r\n        const beta = Math.sqrt(Math.max(betaSqrd, 0)); // Safety check for zeroed elements!\r\n\r\n        const gammaSqrd = inertiaLocal.x * 12 - betaSqrd;\r\n        const gamma = Math.sqrt(Math.max(gammaSqrd, 0)); // Safety check for zeroed elements!\r\n\r\n        const alphaSqrd = inertiaLocal.z * 12 - betaSqrd;\r\n        const alpha = Math.sqrt(Math.max(alphaSqrd, 0)); // Safety check for zeroed elements!\r\n\r\n        const extents = TmpVectors.Vector3[0];\r\n        extents.set(alpha, beta, gamma);\r\n\r\n        const scaling = Matrix.ScalingToRef(extents.x, extents.y, extents.z, TmpVectors.Matrix[0]);\r\n        const rotation = orientation.toRotationMatrix(TmpVectors.Matrix[1]);\r\n        const translation = Matrix.TranslationToRef(center.x, center.y, center.z, TmpVectors.Matrix[2]);\r\n\r\n        scaling.multiplyToRef(rotation, matrix);\r\n        matrix.multiplyToRef(translation, matrix);\r\n\r\n        return matrix;\r\n    }\r\n\r\n    private _getDebugInertiaMesh(body: PhysicsBody): Nullable<AbstractMesh> {\r\n        if (!this._utilityLayer) {\r\n            return null;\r\n        }\r\n        const utilityLayerScene = this._utilityLayer.utilityLayerScene;\r\n\r\n        // The base inertia mesh is going to be a 1x1 cube that's scaled and rotated according to the inertia\r\n        const inertiaBoxMesh = MeshBuilder.CreateBox(\"custom\", { size: 1 }, utilityLayerScene);\r\n        const matrixRef = Matrix.Identity();\r\n        if (body._pluginDataInstances.length) {\r\n            const instanceBuffer = new Float32Array(body._pluginDataInstances.length * 16);\r\n            for (let i = 0; i < body._pluginDataInstances.length; ++i) {\r\n                const props = body.getMassProperties(i);\r\n                this._getMeshDebugInertiaMatrixToRef(props, matrixRef);\r\n                matrixRef.copyToArray(instanceBuffer, i * 16);\r\n            }\r\n            inertiaBoxMesh.thinInstanceSetBuffer(\"matrix\", instanceBuffer, 16, false);\r\n        } else {\r\n            const props = body.getMassProperties();\r\n            this._getMeshDebugInertiaMatrixToRef(props, matrixRef);\r\n            matrixRef.decomposeToTransformNode(inertiaBoxMesh);\r\n        }\r\n        inertiaBoxMesh.enableEdgesRendering();\r\n        inertiaBoxMesh.edgesWidth = 2.0;\r\n        inertiaBoxMesh.edgesColor = new Color4(1, 0, 1, 1);\r\n        inertiaBoxMesh.material = this._getDebugInertiaMaterial(utilityLayerScene);\r\n\r\n        return inertiaBoxMesh;\r\n    }\r\n\r\n    private _getTransformFromBodyToRef(body: PhysicsBody, matrix: Matrix, instanceIndex?: number) {\r\n        const tnode = body.transformNode;\r\n        if (instanceIndex && instanceIndex >= 0) {\r\n            return Matrix.FromArrayToRef((tnode as Mesh)._thinInstanceDataStorage.matrixData!, instanceIndex, matrix);\r\n        } else {\r\n            return matrix.copyFrom(tnode.getWorldMatrix());\r\n        }\r\n    }\r\n\r\n    private _createAngularConstraintMesh(minLimit: number, maxLimit: number, axisNumber: number, parent: TransformNode, scene: Scene): AbstractMesh {\r\n        const arcAngle = (maxLimit - minLimit) / (Math.PI * 2);\r\n        const mesh = MeshBuilder.CreateCylinder(\"ConstraintCylinder\", { height: 0.0001, diameter: 3 * this._constraintAngularSize, arc: arcAngle }, scene);\r\n        mesh.material = this._getDebugAxisColoredMaterial(axisNumber, scene);\r\n        mesh.parent = parent;\r\n        const parentScaling = parent.absoluteScaling;\r\n        switch (axisNumber) {\r\n            case 0:\r\n                mesh.rotation.z = Math.PI * 0.5;\r\n                mesh.rotation.x = -minLimit + Math.PI * 0.5;\r\n                // scaling on y,z\r\n                mesh.scaling.x = 1 / parentScaling.x;\r\n                mesh.scaling.y = 1 / parentScaling.z;\r\n                mesh.scaling.z = 1 / parentScaling.y;\r\n                break;\r\n            case 1:\r\n                mesh.rotation.y = Math.PI * 1.5 + minLimit;\r\n                // flip x,z\r\n                mesh.scaling.x = 1 / parentScaling.z;\r\n                mesh.scaling.y = 1 / parentScaling.y;\r\n                mesh.scaling.z = 1 / parentScaling.x;\r\n                break;\r\n            case 2:\r\n                mesh.rotation.x = Math.PI * 0.5;\r\n                // flip z,y\r\n                mesh.scaling.x = 1 / parentScaling.x;\r\n                mesh.scaling.y = 1 / parentScaling.z;\r\n                mesh.scaling.z = 1 / parentScaling.y;\r\n                break;\r\n        }\r\n        return mesh;\r\n    }\r\n\r\n    private _createCage(parent: TransformNode, scene: Scene): AbstractMesh {\r\n        const cage = MeshBuilder.CreateBox(\"cage\", { size: 1 }, scene);\r\n        cage.setPivotPoint(new Vector3(-0.5, -0.5, -0.5));\r\n        const transparentMaterial = new StandardMaterial(\"cage_material\", scene);\r\n        transparentMaterial.alpha = 0; // Fully transparent\r\n        cage.material = transparentMaterial;\r\n\r\n        cage.enableEdgesRendering();\r\n        cage.edgesWidth = 4.0;\r\n        cage.edgesColor = new Color4(1, 1, 1, 1);\r\n        cage.parent = parent;\r\n        return cage;\r\n    }\r\n\r\n    private _getDebugConstraintMesh(constraint: PhysicsConstraint): Nullable<Array<AbstractMesh>> {\r\n        if (!this._utilityLayer) {\r\n            return null;\r\n        }\r\n        const utilityLayerScene = this._utilityLayer.utilityLayerScene;\r\n\r\n        if (!constraint._initOptions) {\r\n            return null;\r\n        }\r\n\r\n        // Get constraint pivot and axes\r\n        const { pivotA, pivotB, axisA, axisB, perpAxisA, perpAxisB } = constraint._initOptions;\r\n\r\n        if (!pivotA || !pivotB || !axisA || !axisB || !perpAxisA || !perpAxisB) {\r\n            return null;\r\n        }\r\n\r\n        // Create a mesh to parent all the constraint debug meshes to\r\n        const parentingMesh = new Mesh(\"parentingDebugConstraint\", utilityLayerScene);\r\n\r\n        // First, get a reference to all physic bodies that are using this constraint\r\n        const bodiesUsingConstraint = constraint.getBodiesUsingConstraint();\r\n\r\n        const parentedConstraintMeshes = [];\r\n        parentedConstraintMeshes.push(parentingMesh);\r\n\r\n        for (const bodyPairInfo of bodiesUsingConstraint) {\r\n            // Create a mesh to keep the pair of constraint axes\r\n            const parentOfPair = new TransformNode(\"parentOfPair\", utilityLayerScene);\r\n            parentOfPair.parent = parentingMesh;\r\n\r\n            const { parentBody, parentBodyIndex, childBody, childBodyIndex } = bodyPairInfo;\r\n            // Get the parent transform\r\n\r\n            const parentTransform = this._getTransformFromBodyToRef(parentBody, TmpVectors.Matrix[0], parentBodyIndex);\r\n            const childTransform = this._getTransformFromBodyToRef(childBody, TmpVectors.Matrix[1], childBodyIndex);\r\n\r\n            const parentCoordSystemNode = new TransformNode(\"parentCoordSystem\", utilityLayerScene);\r\n            // parentCoordSystemNode.parent = parentingMesh;\r\n            parentCoordSystemNode.parent = parentOfPair;\r\n            // Save parent and index here to be able to get the transform on update\r\n            parentCoordSystemNode.metadata = { parentBody, parentBodyIndex };\r\n            parentTransform.decomposeToTransformNode(parentCoordSystemNode);\r\n\r\n            const childCoordSystemNode = new TransformNode(\"childCoordSystem\", utilityLayerScene);\r\n            // childCoordSystemNode.parent = parentingMesh;\r\n            childCoordSystemNode.parent = parentOfPair;\r\n            // Save child and index here to be able to get the transform on update\r\n            childCoordSystemNode.metadata = { childBody, childBodyIndex };\r\n            childTransform.decomposeToTransformNode(childCoordSystemNode);\r\n\r\n            // Get the transform to align the XYZ axes to the constraint axes\r\n            const rotTransformParent = Quaternion.FromRotationMatrix(Matrix.FromXYZAxesToRef(axisA, perpAxisA, axisA.cross(perpAxisA), TmpVectors.Matrix[0]));\r\n            const rotTransformChild = Quaternion.FromRotationMatrix(Matrix.FromXYZAxesToRef(axisB, perpAxisB, axisB.cross(perpAxisB), TmpVectors.Matrix[0]));\r\n\r\n            const translateTransformParent = pivotA;\r\n            const translateTransformChild = pivotB;\r\n\r\n            // Create a transform node and set its matrix\r\n            const parentTransformNode = new TransformNode(\"constraint_parent\", utilityLayerScene);\r\n            parentTransformNode.position.copyFrom(translateTransformParent);\r\n            parentTransformNode.rotationQuaternion = rotTransformParent;\r\n            parentTransformNode.parent = parentCoordSystemNode;\r\n\r\n            const childTransformNode = new TransformNode(\"constraint_child\", utilityLayerScene);\r\n            childTransformNode.parent = childCoordSystemNode;\r\n            childTransformNode.position.copyFrom(translateTransformChild);\r\n            childTransformNode.rotationQuaternion = rotTransformChild;\r\n\r\n            // Create axes for the constraint\r\n            const parentAxes = new AxesViewer(utilityLayerScene, this._constraintAxesSize);\r\n            parentAxes.xAxis.parent = parentTransformNode;\r\n            parentAxes.yAxis.parent = parentTransformNode;\r\n            parentAxes.zAxis.parent = parentTransformNode;\r\n\r\n            const childAxes = new AxesViewer(utilityLayerScene, this._constraintAxesSize);\r\n            childAxes.xAxis.parent = childTransformNode;\r\n            childAxes.yAxis.parent = childTransformNode;\r\n            childAxes.zAxis.parent = childTransformNode;\r\n\r\n            // constrain vis\r\n            const engine = this._physicsEnginePlugin! as IPhysicsEnginePluginV2;\r\n\r\n            const constraintAxisAngular = [PhysicsConstraintAxis.ANGULAR_X, PhysicsConstraintAxis.ANGULAR_Y, PhysicsConstraintAxis.ANGULAR_Z];\r\n            const constraintAxisLinear = [PhysicsConstraintAxis.LINEAR_X, PhysicsConstraintAxis.LINEAR_Y, PhysicsConstraintAxis.LINEAR_Z];\r\n            const constraintAxis = [constraintAxisAngular, constraintAxisLinear];\r\n\r\n            // count axis. Angular and Linear\r\n            const lockCount = [0, 0];\r\n            for (let angularLinear = 0; angularLinear < 2; angularLinear++) {\r\n                for (let axis = 0; axis < 3; axis++) {\r\n                    const constraintAxisValue = constraintAxis[angularLinear][axis];\r\n                    const axisMode = engine.getAxisMode(constraint, constraintAxisValue);\r\n                    if (axisMode == PhysicsConstraintAxisLimitMode.LOCKED) {\r\n                        lockCount[angularLinear]++;\r\n                    }\r\n                }\r\n            }\r\n\r\n            // Any free/limited Linear axis\r\n            if (lockCount[1] != 3) {\r\n                const cage = this._createCage(parentTransformNode, utilityLayerScene);\r\n\r\n                const min = TmpVectors.Vector3[0];\r\n                const max = TmpVectors.Vector3[1];\r\n                const limited = [false, false, false];\r\n\r\n                limited[0] = engine.getAxisMode(constraint, PhysicsConstraintAxis.LINEAR_X) == PhysicsConstraintAxisLimitMode.LIMITED;\r\n                limited[1] = engine.getAxisMode(constraint, PhysicsConstraintAxis.LINEAR_Y) == PhysicsConstraintAxisLimitMode.LIMITED;\r\n                limited[2] = engine.getAxisMode(constraint, PhysicsConstraintAxis.LINEAR_Z) == PhysicsConstraintAxisLimitMode.LIMITED;\r\n\r\n                min.x = limited[0] ? engine.getAxisMinLimit(constraint, PhysicsConstraintAxis.LINEAR_X)! : 0;\r\n                max.x = limited[0] ? engine.getAxisMaxLimit(constraint, PhysicsConstraintAxis.LINEAR_X)! : 0;\r\n                min.y = limited[1] ? engine.getAxisMinLimit(constraint, PhysicsConstraintAxis.LINEAR_Y)! : 0;\r\n                max.y = limited[1] ? engine.getAxisMaxLimit(constraint, PhysicsConstraintAxis.LINEAR_Y)! : 0;\r\n                min.z = limited[2] ? engine.getAxisMinLimit(constraint, PhysicsConstraintAxis.LINEAR_Z)! : 0;\r\n                max.z = limited[2] ? engine.getAxisMaxLimit(constraint, PhysicsConstraintAxis.LINEAR_Z)! : 0;\r\n\r\n                cage.position.x = min.x + 0.5;\r\n                cage.position.y = min.y + 0.5;\r\n                cage.position.z = min.z + 0.5;\r\n\r\n                cage.scaling.x = max.x - min.x + Epsilon;\r\n                cage.scaling.y = max.y - min.y + Epsilon;\r\n                cage.scaling.z = max.z - min.z + Epsilon;\r\n                parentedConstraintMeshes.push(cage);\r\n            }\r\n\r\n            // Angular\r\n            if (lockCount[0] != 3) {\r\n                for (let axisIndex = 0; axisIndex < 3; axisIndex++) {\r\n                    const axis = constraintAxisAngular[axisIndex];\r\n                    const axisMode = engine.getAxisMode(constraint, axis);\r\n                    let minLimit = 0;\r\n                    let maxLimit = Math.PI * 2;\r\n                    if (axisMode == PhysicsConstraintAxisLimitMode.LIMITED) {\r\n                        minLimit = engine.getAxisMinLimit(constraint, axis)!;\r\n                        maxLimit = engine.getAxisMaxLimit(constraint, axis)!;\r\n                    }\r\n                    if (axisMode != PhysicsConstraintAxisLimitMode.LOCKED && constraint.options.pivotB) {\r\n                        const mesh = this._createAngularConstraintMesh(minLimit, maxLimit, axisIndex, childBody.transformNode, utilityLayerScene);\r\n                        mesh.position.copyFrom(constraint.options.pivotB);\r\n                        parentedConstraintMeshes.push(mesh);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        return parentedConstraintMeshes;\r\n    }\r\n\r\n    /**\r\n     * Clean up physics debug display\r\n     */\r\n    public dispose() {\r\n        // impostors\r\n        for (let index = this._numMeshes - 1; index >= 0; index--) {\r\n            this.hideImpostor(this._impostors[0]);\r\n        }\r\n        // bodies\r\n        for (let index = this._numBodies - 1; index >= 0; index--) {\r\n            this.hideBody(this._bodies[0]);\r\n        }\r\n        // inertia\r\n        for (let index = this._numInertiaBodies - 1; index >= 0; index--) {\r\n            this.hideInertia(this._inertiaBodies[0]);\r\n        }\r\n\r\n        if (this._debugBoxMesh) {\r\n            this._debugBoxMesh.dispose();\r\n        }\r\n        if (this._debugSphereMesh) {\r\n            this._debugSphereMesh.dispose();\r\n        }\r\n        if (this._debugCylinderMesh) {\r\n            this._debugCylinderMesh.dispose();\r\n        }\r\n        if (this._debugMaterial) {\r\n            this._debugMaterial.dispose();\r\n        }\r\n\r\n        this._impostors.length = 0;\r\n        this._scene = null;\r\n        this._physicsEnginePlugin = null;\r\n\r\n        if (this._ownUtilityLayer && this._utilityLayer) {\r\n            this._utilityLayer.dispose();\r\n            this._utilityLayer = null;\r\n        }\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AAGA,OAAO,EAAE,IAAI,EAAE,MAAM,gBAAgB,CAAC;AACtC,OAAO,EAAE,SAAS,EAAE,MAAM,+BAA+B,CAAC;AAC1D,OAAO,EAAE,YAAY,EAAE,MAAM,kCAAkC,CAAC;AAChE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC/E,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAErD,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AACrD,OAAO,EAAE,gBAAgB,EAAE,MAAM,+BAA+B,CAAC;AAGjE,OAAO,EAAE,eAAe,EAAE,MAAM,+BAA+B,CAAC;AAChE,OAAO,EAAE,oBAAoB,EAAE,MAAM,mCAAmC,CAAC;AACzE,OAAO,EAAE,cAAc,EAAE,MAAM,oCAAoC,CAAC;AAEpE,OAAO,EAAE,aAAa,EAAE,MAAM,mCAAmC,CAAC;AAClE,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAExC,OAAO,EAAE,UAAU,EAAE,MAAM,2BAA2B,CAAC;AACvD,OAAO,EAAE,WAAW,EAAE,MAAM,uBAAuB,CAAC;AAEpD,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAC1C,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AACxD,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;;;;;;;;;;;;;;;;;;AAK5C,MAAO,aAAa;IA8CtB;;;;;OAKG,CACH,YAAY,KAAa,EAAE,IAAa,EAAE,wLAAqC,uBAAoB,CAAC,mBAAmB,CAAA;QAnDvH,cAAA,EAAgB,CACN,IAAA,CAAA,UAAU,GAAqC,EAAE,CAAC;QAC5D,cAAA,EAAgB,CACN,IAAA,CAAA,OAAO,GAAkC,EAAE,CAAC;QACtD,cAAA,EAAgB,CACN,IAAA,CAAA,OAAO,GAAiC,EAAE,CAAC;QACrD,cAAA,EAAgB,CACN,IAAA,CAAA,cAAc,GAAiC,EAAE,CAAC;QAC5D,cAAA,EAAgB,CACN,IAAA,CAAA,YAAY,GAAuC,EAAE,CAAC;QAChE,cAAA,EAAgB,CACN,IAAA,CAAA,WAAW,GAAkC,EAAE,CAAC;QAC1D,cAAA,EAAgB,CACN,IAAA,CAAA,cAAc,GAAkC,EAAE,CAAC;QAC7D,cAAA,EAAgB,CACN,IAAA,CAAA,iBAAiB,GAAyC,EAAE,CAAC;QAGvE,cAAA,EAAgB,CACN,IAAA,CAAA,UAAU,GAAG,CAAC,CAAC;QACzB,cAAA,EAAgB,CACN,IAAA,CAAA,UAAU,GAAG,CAAC,CAAC;QACzB,cAAA,EAAgB,CACN,IAAA,CAAA,iBAAiB,GAAG,CAAC,CAAC;QAChC,cAAA,EAAgB,CACN,IAAA,CAAA,eAAe,GAAG,CAAC,CAAC;QAOtB,IAAA,CAAA,gBAAgB,GAAG,KAAK,CAAC;QAQzB,IAAA,CAAA,gBAAgB,GAAG,IAAI,KAAK,EAAQ,CAAC;QAErC,IAAA,CAAA,mBAAmB,GAAG,GAAG,CAAC;QAC1B,IAAA,CAAA,sBAAsB,GAAG,GAAG,CAAC;QASjC,IAAI,CAAC,MAAM,GAAG,KAAK,kKAAI,cAAW,CAAC,gBAAgB,CAAC;QACpD,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACf,OAAO;QACX,CAAC;QACD,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;QAEpD,IAAI,YAAY,EAAE,CAAC;YACf,IAAI,CAAC,oBAAoB,GAAG,YAAY,CAAC,gBAAgB,EAAE,CAAC;QAChE,CAAC;QAED,IAAI,YAAY,EAAE,CAAC;YACf,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QACtC,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,aAAa,GAAG,6KAAI,uBAAoB,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;YAClE,IAAI,CAAC,aAAa,CAAC,qBAAqB,GAAG,KAAK,CAAC;YACjD,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,wBAAwB,GAAG,IAAI,CAAC;YACrE,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QACjC,CAAC;QACD,IAAI,IAAI,EAAE,CAAC;YACP,IAAI,CAAC,mBAAmB,GAAG,GAAG,GAAG,IAAI,CAAC;YACtC,IAAI,CAAC,sBAAsB,GAAG,GAAG,GAAG,IAAI,CAAC;QAC7C,CAAC;IACL,CAAC;IAED;;;;;;OAMG,CACO,kBAAkB,GAAA;QACxB,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC;QAEzC,IAAI,MAAM,EAAE,gBAAgB,EAAE,KAAK,CAAC,EAAE,CAAC;YACnC,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAChC,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAChC,CAAC;IACL,CAAC;IAED;;;;;;OAMG,CACO,oBAAoB,GAAA;QAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,oBAA8C,CAAC;QACnE,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,CAAE,CAAC;YACvC,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAEpC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACZ,SAAS;YACb,CAAC;YAED,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC;gBACtB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC5C,CAAC,MAAM,CAAC;gBACJ,IAAI,QAAQ,CAAC,IAAI,6KAAK,kBAAe,CAAC,YAAY,EAAE,CAAC;oBACjD,SAAS;gBACb,CAAC;gBACD,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBAE7B,IAAI,IAAI,IAAI,MAAM,EAAE,CAAC;oBACjB,MAAM,CAAC,oBAAoB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;gBAChD,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;;;OAMG,CACO,oBAAoB,GAAA;QAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,oBAA8C,CAAC;QACnE,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAI,CAAC;YACpC,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAC7B,IAAI,IAAI,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBACjD,SAAS;YACb,CAAC;YACD,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YACtC,IAAI,IAAI,IAAI,SAAS,EAAE,CAAC;gBACpB,MAAM,CAAC,aAAa,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YAC1C,CAAC;YACD,CAAC,EAAE,CAAC;QACR,CAAC;IACL,CAAC;IAES,oBAAoB,GAAA;QAC1B,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,iBAAiB,EAAI,CAAC;YAC3C,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;YACpC,IAAI,IAAI,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;gBACpD,SAAS;YACb,CAAC;YACD,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;YACpC,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;gBACf,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACzC,CAAC;YACD,CAAC,EAAE,CAAC;QACR,CAAC;IACL,CAAC;IAES,mBAAmB,CAAC,IAAiB,EAAE,WAAyB,EAAA;QACtE,MAAM,gBAAgB,kKAAG,SAAM,CAAC,QAAQ,EAAE,CAAC;QAC3C,MAAM,kBAAkB,kKAAG,SAAM,CAAC,QAAQ,EAAE,CAAC;QAC7C,MAAM,cAAc,kKAAG,SAAM,CAAC,QAAQ,EAAE,CAAC;QACzC,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,CAAC;YACnC,MAAM,aAAa,GAAG,WAAmB,CAAC;YAC1C,MAAM,qBAAqB,GAAG,aAAa,CAAC,wBAAwB,CAAC,UAAW,CAAC;YACjF,MAAM,uBAAuB,GAAI,IAAI,CAAC,aAAsB,CAAC,wBAAwB,CAAC,UAAW,CAAC;YAClG,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;gBACxD,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;gBACxC,IAAI,CAAC,+BAA+B,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;+KAC9D,SAAM,CAAC,cAAc,CAAC,uBAAuB,EAAE,CAAC,GAAG,EAAE,EAAE,kBAAkB,CAAC,CAAC;gBAC3E,gBAAgB,CAAC,aAAa,CAAC,kBAAkB,EAAE,cAAc,CAAC,CAAC;gBACnE,cAAc,CAAC,WAAW,CAAC,qBAAqB,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;YAC9D,CAAC;YACD,aAAa,CAAC,yBAAyB,CAAC,QAAQ,CAAC,CAAC;QACtD,CAAC,MAAM,CAAC;YACJ,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACvC,IAAI,CAAC,+BAA+B,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;YAC9D,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;YAC5E,kBAAkB,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAC/D,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;gBAC5B,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;gBAC3E,kBAAkB,CAAC,aAAa,CAAC,eAAe,EAAE,kBAAkB,CAAC,CAAC;YAC1E,CAAC;YACD,gBAAgB,CAAC,aAAa,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;YACrE,gBAAgB,CAAC,wBAAwB,CAAC,WAAW,CAAC,CAAC;QAC3D,CAAC;IACL,CAAC;IAES,uBAAuB,GAAA;QAC7B,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,EAAE,CAAE,CAAC;YAC5C,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACxC,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;YACvC,IAAI,UAAU,IAAI,IAAI,EAAE,CAAC;gBACrB,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YACrD,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;OAIG,CACO,uBAAuB,CAAC,OAAgB,EAAA;QAC9C,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,4KAAO,EAAE,CAAC;YACpC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC;QACD,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,4KAAO,EAAE,CAAC;YACpC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC;QACD,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,qKAAG,UAAO,EAAE,CAAC;YACpC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC;IACL,CAAC;IAES,sBAAsB,CAAC,UAA6B,EAAE,aAA2B,EAAA;QACvF,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;YAC3B,OAAO;QACX,CAAC;QAED,gCAAgC;QAChC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,UAAU,CAAC,YAAY,CAAC;QAEvF,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,EAAE,CAAC;YACrE,OAAO;QACX,CAAC;QAED,MAAM,WAAW,GAAG,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QACvD,KAAK,MAAM,oBAAoB,IAAI,WAAW,CAAE,CAAC;YAC7C,2BAA2B;YAC3B,MAAM,qBAAqB,GAAG,oBAAoB,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAkB,CAAC;YAC5F,MAAM,oBAAoB,GAAG,oBAAoB,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAkB,CAAC;YAE3F,MAAM,EAAE,UAAU,EAAE,eAAe,EAAE,GAAG,qBAAqB,CAAC,QAAQ,CAAC;YACvE,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,GAAG,oBAAoB,CAAC,QAAQ,CAAC;YAEpE,MAAM,eAAe,GAAG,IAAI,CAAC,0BAA0B,CAAC,UAAU,iKAAE,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC;YAC3G,MAAM,cAAc,GAAG,IAAI,CAAC,0BAA0B,CAAC,SAAS,iKAAE,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;YAExG,eAAe,CAAC,wBAAwB,CAAC,qBAAqB,CAAC,CAAC;YAChE,IAAI,CAAC,uBAAuB,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;YAE5D,cAAc,CAAC,wBAAwB,CAAC,oBAAoB,CAAC,CAAC;YAC9D,IAAI,CAAC,uBAAuB,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAE3D,6CAA6C;YAC7C,MAAM,mBAAmB,GAAG,qBAAqB,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAkB,CAAC;YAC3F,mBAAmB,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAE9C,MAAM,kBAAkB,GAAG,oBAAoB,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAkB,CAAC;YACzF,kBAAkB,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAE7C,iEAAiE;2KACjE,aAAU,CAAC,uBAAuB,CAC9B,wKAAM,CAAC,gBAAgB,CAAC,KAAK,EAAE,SAAS,iKAAE,UAAO,CAAC,UAAU,CAAC,KAAK,EAAE,SAAS,iKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,iKAAE,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAC5H,mBAAmB,CAAC,kBAAmB,CAC1C,CAAC;2KACF,aAAU,CAAC,uBAAuB,gKAC9B,SAAM,CAAC,gBAAgB,CAAC,KAAK,EAAE,SAAS,iKAAE,UAAO,CAAC,UAAU,CAAC,KAAK,EAAE,SAAS,EAAE,4KAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,iKAAE,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAC5H,kBAAkB,CAAC,kBAAmB,CACzC,CAAC;QACN,CAAC;IACL,CAAC;IAED;;;;;OAKG,CACI,YAAY,CAAC,QAAyB,EAAE,UAAiB,EAAA;QAC5D,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACf,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,CAAE,CAAC;YACvC,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,QAAQ,EAAE,CAAC;gBACjC,OAAO,IAAI,CAAC;YAChB,CAAC;QACL,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QAE3D,IAAI,SAAS,EAAE,CAAC;YACZ,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,QAAQ,CAAC;YAC5C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,SAAS,CAAC;YAE1C,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,EAAE,CAAC;gBACxB,IAAI,CAAC,eAAe,GAAG,GAAG,CAAG,CAAD,GAAK,CAAC,kBAAkB,EAAE,CAAC;gBACvD,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC3D,CAAC;YAED,IAAI,CAAC,UAAU,EAAE,CAAC;QACtB,CAAC;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;;;;;;OAQG,CACI,QAAQ,CAAC,IAAiB,EAAA;QAC7B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACf,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,CAAE,CAAC;YACvC,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC1B,OAAO,IAAI,CAAC;YAChB,CAAC;QACL,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAE/C,IAAI,SAAS,EAAE,CAAC;YACZ,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;YACrC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,SAAS,CAAC;YAE9C,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,EAAE,CAAC;gBACxB,IAAI,CAAC,eAAe,GAAG,GAAG,CAAG,CAAD,GAAK,CAAC,kBAAkB,EAAE,CAAC;gBACvD,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC3D,CAAC;YAED,IAAI,CAAC,UAAU,EAAE,CAAC;QACtB,CAAC;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;;OAIG,CACI,WAAW,CAAC,IAAiB,EAAA;QAChC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACf,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC,EAAE,CAAE,CAAC;YAC9C,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;gBACjC,OAAO,IAAI,CAAC;YAChB,CAAC;QACL,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAClD,IAAI,SAAS,EAAE,CAAC;YACZ,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC;YACnD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,SAAS,CAAC;YAExD,IAAI,IAAI,CAAC,iBAAiB,KAAK,CAAC,EAAE,CAAC;gBAC/B,IAAI,CAAC,sBAAsB,GAAG,GAAG,CAAG,CAAD,GAAK,CAAC,oBAAoB,EAAE,CAAC;gBAChE,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAClE,CAAC;YAED,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC7B,CAAC;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;;OAIG,CACI,cAAc,CAAC,UAA6B,EAAA;QAC/C,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACf,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,EAAE,CAAE,CAAC;YAC5C,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,UAAU,EAAE,CAAC;gBACrC,OAAO,IAAI,CAAC;YAChB,CAAC;QACL,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC;QAC3D,IAAI,SAAS,EAAE,CAAC;YACZ,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,UAAU,CAAC;YACrD,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,SAAS,CAAC;YAEzD,IAAI,IAAI,CAAC,eAAe,KAAK,CAAC,EAAE,CAAC;gBAC7B,IAAI,CAAC,yBAAyB,GAAG,GAAG,CAAG,CAAD,GAAK,CAAC,uBAAuB,EAAE,CAAC;gBACtE,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YACrE,CAAC;YAED,IAAI,CAAC,eAAe,EAAE,CAAC;QAC3B,CAAC;QAED,OAAO,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC3C,CAAC;IAED;;;;;;;;OAQG,CACI,YAAY,CAAC,QAAmC,EAAA;QACnD,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACnD,OAAO;QACX,CAAC;QAED,IAAI,OAAO,GAAG,KAAK,CAAC;QACpB,MAAM,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC;QAE/D,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,CAAE,CAAC;YACvC,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,QAAQ,EAAE,CAAC;gBACjC,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBAE7B,IAAI,CAAC,IAAI,EAAE,CAAC;oBACR,SAAS;gBACb,CAAC;gBAED,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;gBACnC,IAAI,CAAC,OAAO,EAAE,CAAC;gBAEf,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAY,CAAC,CAAC;gBAC1D,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;oBACb,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBAC3C,CAAC;gBAED,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;oBACtB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;oBAChD,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;oBACtD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;oBACrC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;gBAC5C,CAAC,MAAM,CAAC;oBACJ,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;oBACvB,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;gBAC9B,CAAC;gBACD,OAAO,GAAG,IAAI,CAAC;gBACf,MAAM;YACV,CAAC;QACL,CAAC;QAED,IAAI,OAAO,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,EAAE,CAAC;YACnC,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC7D,CAAC;IACL,CAAC;IAED;;;;;;;;;OASG,CACI,QAAQ,CAAC,IAA2B,EAAA;QACvC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YAC/C,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,OAAO,GAAG,KAAK,CAAC;QACpB,MAAM,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC;QAE/D,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,CAAE,CAAC;YACvC,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;gBAC3B,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;gBAEjC,IAAI,CAAC,IAAI,EAAE,CAAC;oBACR,SAAS;gBACb,CAAC;gBAED,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;gBACnC,IAAI,CAAC,OAAO,EAAE,CAAC;gBAEf,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;oBACtB,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;oBACxD,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;oBAChD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;oBACzC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;gBACzC,CAAC,MAAM,CAAC;oBACJ,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;oBAC3B,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;gBAC3B,CAAC;gBACD,OAAO,GAAG,IAAI,CAAC;gBACf,MAAM;YACV,CAAC;QACL,CAAC;QAED,IAAI,OAAO,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,EAAE,CAAC;YACnC,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC7D,CAAC;QACD,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;;OAIG,CACI,WAAW,CAAC,IAA2B,EAAA;QAC1C,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YAC/C,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,IAAI,OAAO,GAAG,KAAK,CAAC;QACpB,MAAM,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC;QAE/D,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC,EAAE,CAAE,CAAC;YAC9C,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;gBAClC,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;gBAEpC,IAAI,CAAC,IAAI,EAAE,CAAC;oBACR,SAAS;gBACb,CAAC;gBAED,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;gBACnC,IAAI,CAAC,OAAO,EAAE,CAAC;gBAEf,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACjC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAEjC,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAEzB,OAAO,GAAG,IAAI,CAAC;gBACf,MAAM;YACV,CAAC;QACL,CAAC;QAED,IAAI,OAAO,IAAI,IAAI,CAAC,iBAAiB,KAAK,CAAC,EAAE,CAAC;YAC1C,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACpE,CAAC;QACD,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;OAGG,CACI,cAAc,CAAC,UAAuC,EAAA;QACzD,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACrD,OAAO;QACX,CAAC;QACD,IAAI,OAAO,GAAG,KAAK,CAAC;QACpB,MAAM,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC;QAE/D,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,EAAE,CAAE,CAAC;YAC5C,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,KAAK,UAAU,EAAE,CAAC;gBACtC,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;gBAEzC,IAAI,CAAC,MAAM,EAAE,CAAC;oBACV,SAAS;gBACb,CAAC;gBAED,KAAK,MAAM,IAAI,IAAI,MAAM,CAAE,CAAC;oBACxB,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;oBACnC,IAAI,CAAC,OAAO,EAAE,CAAC;gBACnB,CAAC;gBAED,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC/B,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAEpC,IAAI,CAAC,eAAe,EAAE,CAAC;gBAEvB,IAAI,IAAI,CAAC,eAAe,GAAG,CAAC,EAAE,CAAC;oBAC3B,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;oBAC/D,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;oBACzE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC;oBAC/C,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC;gBACxD,CAAC,MAAM,CAAC;oBACJ,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;oBAC5B,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;gBACrC,CAAC;gBAED,OAAO,GAAG,IAAI,CAAC;gBACf,MAAM;YACV,CAAC;QACL,CAAC;QAED,IAAI,OAAO,IAAI,IAAI,CAAC,eAAe,KAAK,CAAC,EAAE,CAAC;YACxC,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;QACvE,CAAC;IACL,CAAC;IAEO,iBAAiB,CAAC,KAAY,EAAA;QAClC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACvB,IAAI,CAAC,cAAc,GAAG,yKAAI,mBAAgB,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;YACtD,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG,IAAI,CAAC;YACrC,IAAI,CAAC,cAAc,CAAC,aAAa,iKAAG,SAAM,CAAC,KAAK,EAAE,CAAC;YACnD,IAAI,CAAC,cAAc,CAAC,eAAe,GAAG,IAAI,CAAC;QAC/C,CAAC;QAED,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAEO,wBAAwB,CAAC,KAAY,EAAA;QACzC,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC9B,IAAI,CAAC,qBAAqB,GAAG,IAAI,wLAAgB,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;YAC7D,IAAI,CAAC,qBAAqB,CAAC,eAAe,GAAG,IAAI,CAAC;YAClD,IAAI,CAAC,qBAAqB,CAAC,KAAK,GAAG,GAAG,CAAC;QAC3C,CAAC;QAED,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACtC,CAAC;IAEO,4BAA4B,CAAC,UAAkB,EAAE,KAAY,EAAA;QACjE,MAAM,QAAQ,GAAG,yKAAI,mBAAgB,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;QACjD,QAAQ,CAAC,aAAa,GAAG,UAAU,IAAI,CAAC,CAAC,CAAC,+JAAC,SAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC,CAAC,+JAAC,SAAM,CAAC,KAAK,EAAE,CAAC,CAAC,8JAAC,UAAM,CAAC,IAAI,EAAE,CAAC;QAC3G,QAAQ,CAAC,eAAe,GAAG,IAAI,CAAC;QAChC,OAAO,QAAQ,CAAC;IACpB,CAAC;IAEO,gBAAgB,CAAC,KAAY,EAAA;QACjC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtB,IAAI,CAAC,aAAa,+KAAG,YAAA,AAAS,EAAC,wBAAwB,EAAE;gBAAE,IAAI,EAAE,CAAC;YAAA,CAAE,EAAE,KAAK,CAAC,CAAC;YAC7E,IAAI,CAAC,aAAa,CAAC,kBAAkB,kKAAG,aAAU,CAAC,QAAQ,EAAE,CAAC;YAC9D,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAC5D,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACzC,CAAC;QAED,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,4BAA4B,CAAC,CAAC;IAC3E,CAAC;IAEO,mBAAmB,CAAC,KAAY,EAAA;QACpC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACzB,IAAI,CAAC,gBAAgB,GAAG,8LAAA,AAAY,EAAC,2BAA2B,EAAE;gBAAE,QAAQ,EAAE,CAAC;YAAA,CAAE,EAAE,KAAK,CAAC,CAAC;YAC1F,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,kKAAG,aAAU,CAAC,QAAQ,EAAE,CAAC;YACjE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAC/D,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAC5C,CAAC;QAED,OAAO,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,+BAA+B,CAAC,CAAC;IACjF,CAAC;IAEO,oBAAoB,CAAC,KAAY,EAAA;QACrC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC1B,IAAI,CAAC,iBAAiB,OAAG,4LAAA,AAAa,EAAC,4BAA4B,EAAE;gBAAE,MAAM,EAAE,CAAC;YAAA,CAA2B,EAAE,KAAK,CAAC,CAAC;YACpH,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,kKAAG,aAAU,CAAC,QAAQ,EAAE,CAAC;YAClE,IAAI,CAAC,iBAAiB,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAChE,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAC7C,CAAC;QAED,OAAO,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,gCAAgC,CAAC,CAAC;IACnF,CAAC;IAEO,qBAAqB,CAAC,KAAY,EAAA;QACtC,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC3B,IAAI,CAAC,kBAAkB,oLAAG,iBAAA,AAAc,EAAC,6BAA6B,EAAE;gBAAE,WAAW,EAAE,CAAC;gBAAE,cAAc,EAAE,CAAC;gBAAE,MAAM,EAAE,CAAC;YAAA,CAAE,EAAE,KAAK,CAAC,CAAC;YACjI,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,kKAAG,aAAU,CAAC,QAAQ,EAAE,CAAC;YACnE,IAAI,CAAC,kBAAkB,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YACjE,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAC9C,CAAC;QAED,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,iCAAiC,CAAC,CAAC;IACrF,CAAC;IAEO,iBAAiB,CAAC,IAAU,EAAE,KAAY,EAAA;QAC9C,MAAM,aAAa,GAAG,yJAAI,QAAI,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAC7D,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAC9B,aAAa,CAAC,QAAQ,kKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;QACxC,aAAa,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAEvD,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAE1C,OAAO,aAAa,CAAC;IACzB,CAAC;IAEO,aAAa,CAAC,QAAyB,EAAE,UAAiB,EAAA;QAC9D,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,qEAAqE;QACrE,IAAI,UAAU,IAAI,UAAU,CAAC,MAAM,IAAK,UAAU,CAAC,MAAe,CAAC,eAAe,EAAE,CAAC;YACjF,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,GAA2B,IAAI,CAAC;QACxC,MAAM,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC;QAC/D,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;kKACxB,SAAM,CAAC,IAAI,CAAC,gGAAgG,CAAC,CAAC;YAC9G,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,OAAQ,QAAQ,CAAC,IAAI,EAAE,CAAC;YACpB,6KAAK,kBAAe,CAAC,WAAW;gBAC5B,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;gBAChD,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACvC,MAAM;YACV,KAAK,0LAAe,CAAC,cAAc,CAAC;gBAAC,CAAC;oBAClC,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,CAAC;oBACnD,MAAM,MAAM,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAC;oBACpC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC;oBAC5B,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC;oBAC5B,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC;oBAC5B,MAAM;gBACV,CAAC;YACD,6KAAK,kBAAe,CAAC,eAAe,CAAC;gBAAC,CAAC;oBACnC,IAAI,GAAG,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;oBACpD,MAAM,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC;oBAC7C,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;oBACvG,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;oBACnG,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;oBACvG,MAAM;gBACV,CAAC;YACD,6KAAK,kBAAe,CAAC,YAAY;gBAC7B,IAAI,UAAU,EAAE,CAAC;oBACb,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,iBAAiB,CAAC,CAAC;gBACjE,CAAC;gBACD,MAAM;YACV,6KAAK,kBAAe,CAAC,UAAU;gBAC3B,IAAI,UAAU,EAAE,CAAC;oBACb,4BAA4B;oBAC5B,MAAM,WAAW,GAAG,UAAU,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE;wBACzD,OAAO,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrC,CAAC,CAAC,CAAC;oBACH,KAAK,MAAM,CAAC,IAAI,WAAW,CAAE,CAAC;wBAC1B,IAAI,CAAC,CAAC,eAAe,IAAI,CAAC,CAAC,YAAY,EAAE,KAAK,MAAM,EAAE,CAAC;4BACnD,MAAM,YAAY,GAAG,CAAC,CAAC,eAAe,EAAE,CAAC;4BACzC,MAAM,GAAG,GAAG,YAAY,CAAC,WAAW,CAAC,OAAO,CAAC;4BAC7C,MAAM,GAAG,GAAG,YAAY,CAAC,WAAW,CAAC,OAAO,CAAC;4BAC7C,OAAQ,CAAC,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC;gCAC7B,KAAK,0LAAe,CAAC,WAAW;oCAC5B,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;oCAChD,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;oCAC5B,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;oCAC9B,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;oCAChC,MAAM;gCACV,6KAAK,kBAAe,CAAC,cAAc;oCAC/B,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,CAAC;oCACnD,MAAM;gCACV,KAAK,0LAAe,CAAC,gBAAgB;oCACjC,IAAI,GAAG,IAAI,CAAC,qBAAqB,CAAC,iBAAiB,CAAC,CAAC;oCACrD,MAAM;gCACV;oCACI,IAAI,GAAG,IAAI,CAAC;oCACZ,MAAM;4BACd,CAAC;4BACD,IAAI,IAAI,EAAE,CAAC;gCACP,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;gCAC/B,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;gCAC/B,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;gCAC/B,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;4BACpB,CAAC;wBACL,CAAC;oBACL,CAAC;gBACL,CAAC,MAAM,CAAC;0KACJ,SAAM,CAAC,IAAI,CAAC,6DAA6D,CAAC,CAAC;gBAC/E,CAAC;gBACD,IAAI,GAAG,IAAI,CAAC;gBACZ,MAAM;YACV,6KAAK,kBAAe,CAAC,gBAAgB,CAAC;gBAAC,CAAC;oBACpC,IAAI,GAAG,IAAI,CAAC,qBAAqB,CAAC,iBAAiB,CAAC,CAAC;oBACrD,MAAM,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC;oBAC7C,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;oBACnG,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;oBACnG,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;oBACnG,MAAM;gBACV,CAAC;QACL,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;;;OASG,CACK,iBAAiB,CAAC,IAAiB,EAAA;QACvC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC;QAE/D,MAAM,IAAI,GAAG,IAAI,6JAAI,CAAC,QAAQ,EAAE,iBAAiB,CAAC,CAAC;QACnD,MAAM,UAAU,GAAG,wKAAI,aAAU,EAAE,CAAC;QACpC,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAS,CAAC;QAC3C,UAAU,CAAC,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC;QAC1C,UAAU,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;QACtC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAC7B,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,MAAM,cAAc,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC;YAC/E,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,cAAc,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;QACpE,CAAC;QACD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;QAC1D,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,+BAA+B,CAAC,SAAgC,EAAE,MAAc,EAAA;QACpF,MAAM,WAAW,GAAG,SAAS,CAAC,kBAAkB,mKAAI,aAAU,CAAC,QAAQ,EAAE,CAAC;QAC1E,MAAM,YAAY,GAAG,SAAS,CAAC,OAAO,IAAI,yKAAO,CAAC,IAAI,EAAE,CAAC;QACzD,MAAM,MAAM,GAAG,SAAS,CAAC,YAAY,mKAAI,UAAO,CAAC,IAAI,EAAE,CAAC;QAExD,MAAM,QAAQ,GAAG,CAAC,YAAY,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACxE,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,oCAAoC;QAEnF,MAAM,SAAS,GAAG,YAAY,CAAC,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC;QACjD,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,oCAAoC;QAErF,MAAM,SAAS,GAAG,YAAY,CAAC,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC;QACjD,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,oCAAoC;QAErF,MAAM,OAAO,GAAG,4KAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACtC,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QAEhC,MAAM,OAAO,kKAAG,SAAM,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,iKAAE,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3F,MAAM,QAAQ,GAAG,WAAW,CAAC,gBAAgB,gKAAC,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACpE,MAAM,WAAW,kKAAG,SAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,iKAAE,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAEhG,OAAO,CAAC,aAAa,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QACxC,MAAM,CAAC,aAAa,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;QAE1C,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,oBAAoB,CAAC,IAAiB,EAAA;QAC1C,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,MAAM,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC;QAE/D,qGAAqG;QACrG,MAAM,cAAc,gKAAG,cAAW,CAAC,SAAS,CAAC,QAAQ,EAAE;YAAE,IAAI,EAAE,CAAC;QAAA,CAAE,EAAE,iBAAiB,CAAC,CAAC;QACvF,MAAM,SAAS,GAAG,wKAAM,CAAC,QAAQ,EAAE,CAAC;QACpC,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,CAAC;YACnC,MAAM,cAAc,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC;YAC/E,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,EAAE,CAAC,CAAE,CAAC;gBACxD,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;gBACxC,IAAI,CAAC,+BAA+B,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;gBACvD,SAAS,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;YAClD,CAAC;YACD,cAAc,CAAC,qBAAqB,CAAC,QAAQ,EAAE,cAAc,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;QAC9E,CAAC,MAAM,CAAC;YACJ,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACvC,IAAI,CAAC,+BAA+B,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;YACvD,SAAS,CAAC,wBAAwB,CAAC,cAAc,CAAC,CAAC;QACvD,CAAC;QACD,cAAc,CAAC,oBAAoB,EAAE,CAAC;QACtC,cAAc,CAAC,UAAU,GAAG,GAAG,CAAC;QAChC,cAAc,CAAC,UAAU,GAAG,kKAAI,SAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACnD,cAAc,CAAC,QAAQ,GAAG,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,CAAC;QAE3E,OAAO,cAAc,CAAC;IAC1B,CAAC;IAEO,0BAA0B,CAAC,IAAiB,EAAE,MAAc,EAAE,aAAsB,EAAA;QACxF,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC;QACjC,IAAI,aAAa,IAAI,aAAa,IAAI,CAAC,EAAE,CAAC;YACtC,sKAAO,SAAM,CAAC,cAAc,CAAE,KAAc,CAAC,wBAAwB,CAAC,UAAW,EAAE,aAAa,EAAE,MAAM,CAAC,CAAC;QAC9G,CAAC,MAAM,CAAC;YACJ,OAAO,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC,CAAC;QACnD,CAAC;IACL,CAAC;IAEO,4BAA4B,CAAC,QAAgB,EAAE,QAAgB,EAAE,UAAkB,EAAE,MAAqB,EAAE,KAAY,EAAA;QAC5H,MAAM,QAAQ,GAAG,CAAC,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;QACvD,MAAM,IAAI,gKAAG,cAAW,CAAC,cAAc,CAAC,oBAAoB,EAAE;YAAE,MAAM,EAAE,MAAM;YAAE,QAAQ,EAAE,CAAC,GAAG,IAAI,CAAC,sBAAsB;YAAE,GAAG,EAAE,QAAQ;QAAA,CAAE,EAAE,KAAK,CAAC,CAAC;QACnJ,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,4BAA4B,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QACrE,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,MAAM,aAAa,GAAG,MAAM,CAAC,eAAe,CAAC;QAC7C,OAAQ,UAAU,EAAE,CAAC;YACjB,KAAK,CAAC;gBACF,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;gBAChC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;gBAC5C,iBAAiB;gBACjB,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;gBACrC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;gBACrC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;gBACrC,MAAM;YACV,KAAK,CAAC;gBACF,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,GAAG,QAAQ,CAAC;gBAC3C,WAAW;gBACX,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;gBACrC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;gBACrC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;gBACrC,MAAM;YACV,KAAK,CAAC;gBACF,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;gBAChC,WAAW;gBACX,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;gBACrC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;gBACrC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;gBACrC,MAAM;QACd,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,WAAW,CAAC,MAAqB,EAAE,KAAY,EAAA;QACnD,MAAM,IAAI,GAAG,2KAAW,CAAC,SAAS,CAAC,MAAM,EAAE;YAAE,IAAI,EAAE,CAAC;QAAA,CAAE,EAAE,KAAK,CAAC,CAAC;QAC/D,IAAI,CAAC,aAAa,CAAC,mKAAI,UAAO,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;QAClD,MAAM,mBAAmB,GAAG,yKAAI,mBAAgB,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;QACzE,mBAAmB,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,oBAAoB;QACnD,IAAI,CAAC,QAAQ,GAAG,mBAAmB,CAAC;QAEpC,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;QACtB,IAAI,CAAC,UAAU,GAAG,IAAI,uKAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACzC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,uBAAuB,CAAC,UAA6B,EAAA;QACzD,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,MAAM,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC;QAE/D,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;YAC3B,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,gCAAgC;QAChC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,UAAU,CAAC,YAAY,CAAC;QAEvF,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,EAAE,CAAC;YACrE,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,6DAA6D;QAC7D,MAAM,aAAa,GAAG,0JAAI,OAAI,CAAC,0BAA0B,EAAE,iBAAiB,CAAC,CAAC;QAE9E,6EAA6E;QAC7E,MAAM,qBAAqB,GAAG,UAAU,CAAC,wBAAwB,EAAE,CAAC;QAEpE,MAAM,wBAAwB,GAAG,EAAE,CAAC;QACpC,wBAAwB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAE7C,KAAK,MAAM,YAAY,IAAI,qBAAqB,CAAE,CAAC;YAC/C,oDAAoD;YACpD,MAAM,YAAY,GAAG,mKAAI,gBAAa,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;YAC1E,YAAY,CAAC,MAAM,GAAG,aAAa,CAAC;YAEpC,MAAM,EAAE,UAAU,EAAE,eAAe,EAAE,SAAS,EAAE,cAAc,EAAE,GAAG,YAAY,CAAC;YAChF,2BAA2B;YAE3B,MAAM,eAAe,GAAG,IAAI,CAAC,0BAA0B,CAAC,UAAU,iKAAE,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC;YAC3G,MAAM,cAAc,GAAG,IAAI,CAAC,0BAA0B,CAAC,SAAS,iKAAE,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;YAExG,MAAM,qBAAqB,GAAG,mKAAI,gBAAa,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,CAAC;YACxF,gDAAgD;YAChD,qBAAqB,CAAC,MAAM,GAAG,YAAY,CAAC;YAC5C,uEAAuE;YACvE,qBAAqB,CAAC,QAAQ,GAAG;gBAAE,UAAU;gBAAE,eAAe;YAAA,CAAE,CAAC;YACjE,eAAe,CAAC,wBAAwB,CAAC,qBAAqB,CAAC,CAAC;YAEhE,MAAM,oBAAoB,GAAG,mKAAI,gBAAa,CAAC,kBAAkB,EAAE,iBAAiB,CAAC,CAAC;YACtF,+CAA+C;YAC/C,oBAAoB,CAAC,MAAM,GAAG,YAAY,CAAC;YAC3C,sEAAsE;YACtE,oBAAoB,CAAC,QAAQ,GAAG;gBAAE,SAAS;gBAAE,cAAc;YAAA,CAAE,CAAC;YAC9D,cAAc,CAAC,wBAAwB,CAAC,oBAAoB,CAAC,CAAC;YAE9D,iEAAiE;YACjE,MAAM,kBAAkB,kKAAG,aAAU,CAAC,kBAAkB,gKAAC,SAAM,CAAC,gBAAgB,CAAC,KAAK,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,iKAAE,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClJ,MAAM,iBAAiB,kKAAG,aAAU,CAAC,kBAAkB,CAAC,wKAAM,CAAC,gBAAgB,CAAC,KAAK,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,iKAAE,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAEjJ,MAAM,wBAAwB,GAAG,MAAM,CAAC;YACxC,MAAM,uBAAuB,GAAG,MAAM,CAAC;YAEvC,6CAA6C;YAC7C,MAAM,mBAAmB,GAAG,mKAAI,gBAAa,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,CAAC;YACtF,mBAAmB,CAAC,QAAQ,CAAC,QAAQ,CAAC,wBAAwB,CAAC,CAAC;YAChE,mBAAmB,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;YAC5D,mBAAmB,CAAC,MAAM,GAAG,qBAAqB,CAAC;YAEnD,MAAM,kBAAkB,GAAG,mKAAI,gBAAa,CAAC,kBAAkB,EAAE,iBAAiB,CAAC,CAAC;YACpF,kBAAkB,CAAC,MAAM,GAAG,oBAAoB,CAAC;YACjD,kBAAkB,CAAC,QAAQ,CAAC,QAAQ,CAAC,uBAAuB,CAAC,CAAC;YAC9D,kBAAkB,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;YAE1D,iCAAiC;YACjC,MAAM,UAAU,GAAG,+JAAI,aAAU,CAAC,iBAAiB,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAC/E,UAAU,CAAC,KAAK,CAAC,MAAM,GAAG,mBAAmB,CAAC;YAC9C,UAAU,CAAC,KAAK,CAAC,MAAM,GAAG,mBAAmB,CAAC;YAC9C,UAAU,CAAC,KAAK,CAAC,MAAM,GAAG,mBAAmB,CAAC;YAE9C,MAAM,SAAS,GAAG,+JAAI,aAAU,CAAC,iBAAiB,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAC9E,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG,kBAAkB,CAAC;YAC5C,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG,kBAAkB,CAAC;YAC5C,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG,kBAAkB,CAAC;YAE5C,gBAAgB;YAChB,MAAM,MAAM,GAAG,IAAI,CAAC,oBAA+C,CAAC;YAEpE,MAAM,qBAAqB,GAAG;gBAAA,EAAA,mCAAA;gBAAA,EAAA,mCAAA;gBAAA,EAAA,mCAAA;aAAmG,CAAC;YAClI,MAAM,oBAAoB,GAAG;gBAAA,EAAA,kCAAA;gBAAA,EAAA,kCAAA;gBAAA,EAAA,kCAAA;aAAgG,CAAC;YAC9H,MAAM,cAAc,GAAG;gBAAC,qBAAqB;gBAAE,oBAAoB;aAAC,CAAC;YAErE,iCAAiC;YACjC,MAAM,SAAS,GAAG;gBAAC,CAAC;gBAAE,CAAC;aAAC,CAAC;YACzB,IAAK,IAAI,aAAa,GAAG,CAAC,EAAE,aAAa,GAAG,CAAC,EAAE,aAAa,EAAE,CAAE,CAAC;gBAC7D,IAAK,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,IAAI,EAAE,CAAE,CAAC;oBAClC,MAAM,mBAAmB,GAAG,cAAc,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC;oBAChE,MAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,CAAC,UAAU,EAAE,mBAAmB,CAAC,CAAC;oBACrE,IAAI,QAAQ,IAAA,EAAA,yCAAA,EAAyC,GAAE,CAAC;wBACpD,SAAS,CAAC,aAAa,CAAC,EAAE,CAAC;oBAC/B,CAAC;gBACL,CAAC;YACL,CAAC;YAED,+BAA+B;YAC/B,IAAI,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;gBACpB,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,CAAC;gBAEtE,MAAM,GAAG,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBAClC,MAAM,GAAG,GAAG,4KAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBAClC,MAAM,OAAO,GAAG;oBAAC,KAAK;oBAAE,KAAK;oBAAE,KAAK;iBAAC,CAAC;gBAEtC,OAAO,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,WAAW,CAAC,UAAU,EAAA,EAAA,kCAAA,GAAiC,KAAA,EAAA,0CAAA,EAA0C,CAAC;gBACtH,OAAO,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,WAAW,CAAC,UAAU,EAAA,EAAA,kCAAA,GAAiC,KAAA,EAAA,0CAAA,EAA0C,CAAC;gBACtH,OAAO,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,WAAW,CAAC,UAAU,EAAA,EAAA,kCAAA,GAAiC,KAAA,EAAA,0CAAA,EAA0C,CAAC;gBAEtH,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,eAAe,CAAC,UAAU,EAAA,EAAA,kCAAA,GAAkC,CAAC,CAAC,EAAC,CAAC,CAAC;gBAC7F,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,eAAe,CAAC,UAAU,EAAA,EAAA,kCAAA,GAAkC,CAAC,CAAC,EAAC,CAAC,CAAC;gBAC7F,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,eAAe,CAAC,UAAU,EAAA,EAAA,kCAAA,GAAkC,CAAC,CAAC,EAAC,CAAC,CAAC;gBAC7F,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,eAAe,CAAC,UAAU,EAAA,EAAA,kCAAA,GAAkC,CAAC,CAAC,EAAC,CAAC,CAAC;gBAC7F,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,eAAe,CAAC,UAAU,EAAA,EAAA,kCAAA,GAAkC,CAAC,CAAC,EAAC,CAAC,CAAC;gBAC7F,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,eAAe,CAAC,UAAU,EAAA,EAAA,kCAAA,GAAkC,CAAC,CAAC,EAAC,CAAC,CAAC;gBAE7F,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;gBAC9B,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;gBAC9B,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;gBAE9B,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,qKAAG,UAAO,CAAC;gBACzC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,qKAAG,UAAO,CAAC;gBACzC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,qKAAG,UAAO,CAAC;gBACzC,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxC,CAAC;YAED,UAAU;YACV,IAAI,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;gBACpB,IAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,CAAC,EAAE,SAAS,EAAE,CAAE,CAAC;oBACjD,MAAM,IAAI,GAAG,qBAAqB,CAAC,SAAS,CAAC,CAAC;oBAC9C,MAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;oBACtD,IAAI,QAAQ,GAAG,CAAC,CAAC;oBACjB,IAAI,QAAQ,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;oBAC3B,IAAI,QAAQ,IAAA,EAAA,0CAAA,EAA0C,GAAE,CAAC;wBACrD,QAAQ,GAAG,MAAM,CAAC,eAAe,CAAC,UAAU,EAAE,IAAI,CAAE,CAAC;wBACrD,QAAQ,GAAG,MAAM,CAAC,eAAe,CAAC,UAAU,EAAE,IAAI,CAAE,CAAC;oBACzD,CAAC;oBACD,IAAI,QAAQ,IAAA,EAAA,yCAAA,EAAyC,KAAI,UAAU,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;wBACjF,MAAM,IAAI,GAAG,IAAI,CAAC,4BAA4B,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,CAAC,aAAa,EAAE,iBAAiB,CAAC,CAAC;wBAC1H,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;wBAClD,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACxC,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,wBAAwB,CAAC;IACpC,CAAC;IAED;;OAEG,CACI,OAAO,GAAA;QACV,YAAY;QACZ,IAAK,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,EAAE,CAAE,CAAC;YACxD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC;QACD,SAAS;QACT,IAAK,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,EAAE,CAAE,CAAC;YACxD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC;QACD,UAAU;QACV,IAAK,IAAI,KAAK,GAAG,IAAI,CAAC,iBAAiB,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,EAAE,CAAE,CAAC;YAC/D,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC;QAED,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QACjC,CAAC;QACD,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;QACpC,CAAC;QACD,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;QACtC,CAAC;QACD,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;QAClC,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACnB,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QAEjC,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YAC9C,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;YAC7B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC9B,CAAC;IACL,CAAC;CACJ", "debugId": null}}, {"offset": {"line": 1410, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Debug/rayHelper.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Debug/rayHelper.ts"], "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport type { Ray } from \"../Culling/ray\";\r\nimport { Vector3 } from \"../Maths/math.vector\";\r\nimport type { Color3 } from \"../Maths/math.color\";\r\nimport type { Scene } from \"../scene\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { LinesMesh } from \"../Meshes/linesMesh\";\r\n\r\nimport { CreateLines } from \"../Meshes/Builders/linesBuilder\";\r\nimport type { Observer } from \"../Misc/observable\";\r\n\r\n/**\r\n * As raycast might be hard to debug, the RayHelper can help rendering the different rays\r\n * in order to better appreciate the issue one might have.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/interactions/picking_collisions#debugging\r\n */\r\nexport class RayHelper {\r\n    /**\r\n     * Defines the ray we are currently trying to visualize.\r\n     */\r\n    public ray: Nullable<Ray>;\r\n\r\n    private _renderPoints: Vector3[];\r\n    private _renderLine: Nullable<LinesMesh>;\r\n    private _renderFunction: Nullable<() => void>;\r\n    private _scene: Nullable<Scene>;\r\n\r\n    private _onAfterRenderObserver: Nullable<Observer<Scene>>;\r\n    private _onAfterStepObserver: Nullable<Observer<Scene>>;\r\n    private _attachedToMesh: Nullable<AbstractMesh>;\r\n    private _meshSpaceDirection: Vector3;\r\n    private _meshSpaceOrigin: Vector3;\r\n\r\n    /**\r\n     * Helper function to create a colored helper in a scene in one line.\r\n     * @param ray Defines the ray we are currently trying to visualize\r\n     * @param scene Defines the scene the ray is used in\r\n     * @param color Defines the color we want to see the ray in\r\n     * @returns The newly created ray helper.\r\n     */\r\n    public static CreateAndShow(ray: Ray, scene: Scene, color: Color3): RayHelper {\r\n        const helper = new RayHelper(ray);\r\n\r\n        helper.show(scene, color);\r\n\r\n        return helper;\r\n    }\r\n\r\n    /**\r\n     * Instantiate a new ray helper.\r\n     * As raycast might be hard to debug, the RayHelper can help rendering the different rays\r\n     * in order to better appreciate the issue one might have.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/interactions/picking_collisions#debugging\r\n     * @param ray Defines the ray we are currently trying to visualize\r\n     */\r\n    constructor(ray: Ray) {\r\n        this.ray = ray;\r\n    }\r\n\r\n    /**\r\n     * Shows the ray we are willing to debug.\r\n     * @param scene Defines the scene the ray needs to be rendered in\r\n     * @param color Defines the color the ray needs to be rendered in\r\n     */\r\n    public show(scene: Scene, color?: Color3): void {\r\n        if (!this._renderFunction && this.ray) {\r\n            const ray = this.ray;\r\n\r\n            this._renderFunction = () => this._render();\r\n            this._scene = scene;\r\n            this._renderPoints = [ray.origin, ray.origin.add(ray.direction.scale(ray.length))];\r\n            this._renderLine = CreateLines(\"ray\", { points: this._renderPoints, updatable: true }, scene);\r\n            this._renderLine.isPickable = false;\r\n\r\n            if (this._renderFunction) {\r\n                this._scene.registerBeforeRender(this._renderFunction);\r\n            }\r\n        }\r\n\r\n        if (color && this._renderLine) {\r\n            this._renderLine.color.copyFrom(color);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Hides the ray we are debugging.\r\n     */\r\n    public hide(): void {\r\n        if (this._renderFunction && this._scene) {\r\n            this._scene.unregisterBeforeRender(this._renderFunction);\r\n            this._scene = null;\r\n            this._renderFunction = null;\r\n            if (this._renderLine) {\r\n                this._renderLine.dispose();\r\n                this._renderLine = null;\r\n            }\r\n\r\n            this._renderPoints = [];\r\n        }\r\n    }\r\n\r\n    private _render(): void {\r\n        const ray = this.ray;\r\n\r\n        if (!ray) {\r\n            return;\r\n        }\r\n\r\n        const point = this._renderPoints[1];\r\n        const len = Math.min(ray.length, 1000000);\r\n\r\n        point.copyFrom(ray.direction);\r\n        point.scaleInPlace(len);\r\n        point.addInPlace(ray.origin);\r\n\r\n        this._renderPoints[0].copyFrom(ray.origin);\r\n\r\n        CreateLines(\"ray\", { points: this._renderPoints, updatable: true, instance: this._renderLine }, this._scene);\r\n\r\n        this._renderLine?.refreshBoundingInfo();\r\n    }\r\n\r\n    /**\r\n     * Attach a ray helper to a mesh so that we can easily see its orientation for instance or information like its normals.\r\n     * @param mesh Defines the mesh we want the helper attached to\r\n     * @param meshSpaceDirection Defines the direction of the Ray in mesh space (local space of the mesh node)\r\n     * @param meshSpaceOrigin Defines the origin of the Ray in mesh space (local space of the mesh node)\r\n     * @param length Defines the length of the ray\r\n     */\r\n    public attachToMesh(mesh: AbstractMesh, meshSpaceDirection?: Vector3, meshSpaceOrigin?: Vector3, length?: number): void {\r\n        this._attachedToMesh = mesh;\r\n\r\n        const ray = this.ray;\r\n\r\n        if (!ray) {\r\n            return;\r\n        }\r\n\r\n        if (!ray.direction) {\r\n            ray.direction = Vector3.Zero();\r\n        }\r\n\r\n        if (!ray.origin) {\r\n            ray.origin = Vector3.Zero();\r\n        }\r\n\r\n        if (length) {\r\n            ray.length = length;\r\n        }\r\n\r\n        if (!meshSpaceOrigin) {\r\n            meshSpaceOrigin = Vector3.Zero();\r\n        }\r\n\r\n        if (!meshSpaceDirection) {\r\n            // -1 so that this will work with Mesh.lookAt\r\n            meshSpaceDirection = new Vector3(0, 0, -1);\r\n        }\r\n\r\n        if (!this._scene) {\r\n            this._scene = mesh.getScene();\r\n        }\r\n\r\n        if (!this._meshSpaceDirection) {\r\n            this._meshSpaceDirection = meshSpaceDirection.clone();\r\n            this._meshSpaceOrigin = meshSpaceOrigin.clone();\r\n        } else {\r\n            this._meshSpaceDirection.copyFrom(meshSpaceDirection);\r\n            this._meshSpaceOrigin.copyFrom(meshSpaceOrigin);\r\n        }\r\n\r\n        if (!this._onAfterRenderObserver) {\r\n            this._onAfterRenderObserver = this._scene.onBeforeRenderObservable.add(() => this._updateToMesh());\r\n            this._onAfterStepObserver = this._scene.onAfterStepObservable.add(() => this._updateToMesh());\r\n        }\r\n\r\n        // force world matrix computation before the first ray helper computation\r\n        this._attachedToMesh.computeWorldMatrix(true);\r\n\r\n        this._updateToMesh();\r\n    }\r\n\r\n    /**\r\n     * Detach the ray helper from the mesh it has previously been attached to.\r\n     */\r\n    public detachFromMesh(): void {\r\n        if (this._attachedToMesh && this._scene) {\r\n            if (this._onAfterRenderObserver) {\r\n                this._scene.onBeforeRenderObservable.remove(this._onAfterRenderObserver);\r\n                this._scene.onAfterStepObservable.remove(this._onAfterStepObserver);\r\n            }\r\n            this._attachedToMesh = null;\r\n            this._onAfterRenderObserver = null;\r\n            this._onAfterStepObserver = null;\r\n            this._scene = null;\r\n        }\r\n    }\r\n\r\n    private _updateToMesh(): void {\r\n        const ray = this.ray;\r\n\r\n        if (!this._attachedToMesh || !ray) {\r\n            return;\r\n        }\r\n\r\n        if (this._attachedToMesh.isDisposed()) {\r\n            this.detachFromMesh();\r\n            return;\r\n        }\r\n\r\n        this._attachedToMesh.getDirectionToRef(this._meshSpaceDirection, ray.direction);\r\n        Vector3.TransformCoordinatesToRef(this._meshSpaceOrigin, this._attachedToMesh.getWorldMatrix(), ray.origin);\r\n    }\r\n\r\n    /**\r\n     * Dispose the helper and release its associated resources.\r\n     */\r\n    public dispose(): void {\r\n        this.hide();\r\n        this.detachFromMesh();\r\n        this.ray = null;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AAEA,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAM/C,OAAO,EAAE,WAAW,EAAE,MAAM,iCAAiC,CAAC;;;AAQxD,MAAO,SAAS;IAiBlB;;;;;;OAMG,CACI,MAAM,CAAC,aAAa,CAAC,GAAQ,EAAE,KAAY,EAAE,KAAa,EAAA;QAC7D,MAAM,MAAM,GAAG,IAAI,SAAS,CAAC,GAAG,CAAC,CAAC;QAElC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAE1B,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;;OAMG,CACH,YAAY,GAAQ,CAAA;QAChB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;IACnB,CAAC;IAED;;;;OAIG,CACI,IAAI,CAAC,KAAY,EAAE,KAAc,EAAA;QACpC,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;YACpC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;YAErB,IAAI,CAAC,eAAe,GAAG,GAAG,CAAG,CAAD,GAAK,CAAC,OAAO,EAAE,CAAC;YAC5C,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;YACpB,IAAI,CAAC,aAAa,GAAG;gBAAC,GAAG,CAAC,MAAM;gBAAE,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;aAAC,CAAC;YACnF,IAAI,CAAC,WAAW,iLAAG,cAAA,AAAW,EAAC,KAAK,EAAE;gBAAE,MAAM,EAAE,IAAI,CAAC,aAAa;gBAAE,SAAS,EAAE,IAAI;YAAA,CAAE,EAAE,KAAK,CAAC,CAAC;YAC9F,IAAI,CAAC,WAAW,CAAC,UAAU,GAAG,KAAK,CAAC;YAEpC,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvB,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC3D,CAAC;QACL,CAAC;QAED,IAAI,KAAK,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YAC5B,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAC3C,CAAC;IACL,CAAC;IAED;;OAEG,CACI,IAAI,GAAA;QACP,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACtC,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACzD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YACnB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;YAC5B,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBACnB,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;gBAC3B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YAC5B,CAAC;YAED,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QAC5B,CAAC;IACL,CAAC;IAEO,OAAO,GAAA;QACX,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;QAErB,IAAI,CAAC,GAAG,EAAE,CAAC;YACP,OAAO;QACX,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QACpC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAE1C,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC9B,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QACxB,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAE7B,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;SAE3C,2LAAA,AAAW,EAAC,KAAK,EAAE;YAAE,MAAM,EAAE,IAAI,CAAC,aAAa;YAAE,SAAS,EAAE,IAAI;YAAE,QAAQ,EAAE,IAAI,CAAC,WAAW;QAAA,CAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAE7G,IAAI,CAAC,WAAW,EAAE,mBAAmB,EAAE,CAAC;IAC5C,CAAC;IAED;;;;;;OAMG,CACI,YAAY,CAAC,IAAkB,EAAE,kBAA4B,EAAE,eAAyB,EAAE,MAAe,EAAA;QAC5G,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAE5B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;QAErB,IAAI,CAAC,GAAG,EAAE,CAAC;YACP,OAAO;QACX,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC;YACjB,GAAG,CAAC,SAAS,kKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;QACnC,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;YACd,GAAG,CAAC,MAAM,kKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;QAChC,CAAC;QAED,IAAI,MAAM,EAAE,CAAC;YACT,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC;QACxB,CAAC;QAED,IAAI,CAAC,eAAe,EAAE,CAAC;YACnB,eAAe,kKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;QACrC,CAAC;QAED,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACtB,6CAA6C;YAC7C,kBAAkB,GAAG,mKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAClC,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC5B,IAAI,CAAC,mBAAmB,GAAG,kBAAkB,CAAC,KAAK,EAAE,CAAC;YACtD,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC,KAAK,EAAE,CAAC;QACpD,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;YACtD,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC/B,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAG,CAAG,CAAD,GAAK,CAAC,aAAa,EAAE,CAAC,CAAC;YACnG,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,GAAG,CAAC,GAAG,CAAG,CAAD,GAAK,CAAC,aAAa,EAAE,CAAC,CAAC;QAClG,CAAC;QAED,yEAAyE;QACzE,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAE9C,IAAI,CAAC,aAAa,EAAE,CAAC;IACzB,CAAC;IAED;;OAEG,CACI,cAAc,GAAA;QACjB,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACtC,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;gBAC9B,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBACzE,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACxE,CAAC;YACD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;YAC5B,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;YACnC,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;YACjC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACvB,CAAC;IACL,CAAC;IAEO,aAAa,GAAA;QACjB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;QAErB,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,GAAG,EAAE,CAAC;YAChC,OAAO;QACX,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,EAAE,CAAC;YACpC,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,IAAI,CAAC,mBAAmB,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC;uKAChF,UAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,CAAC,cAAc,EAAE,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;IAChH,CAAC;IAED;;OAEG,CACI,OAAO,GAAA;QACV,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;IACpB,CAAC;CACJ", "debugId": null}}, {"offset": {"line": 1580, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Debug/skeletonViewer.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Debug/skeletonViewer.ts"], "sourcesContent": ["import { Vector3, <PERSON>, TmpVectors } from \"../Maths/math.vector\";\r\nimport { Color3, Color4 } from \"../Maths/math.color\";\r\nimport type { Scene } from \"../scene\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { Bone } from \"../Bones/bone\";\r\nimport type { Skeleton } from \"../Bones/skeleton\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport { Mesh } from \"../Meshes/mesh\";\r\nimport type { LinesMesh } from \"../Meshes/linesMesh\";\r\nimport { CreateLineSystem } from \"../Meshes/Builders/linesBuilder\";\r\nimport { UtilityLayerRenderer } from \"../Rendering/utilityLayerRenderer\";\r\nimport { Material } from \"../Materials/material\";\r\nimport { ShaderMaterial } from \"../Materials/shaderMaterial\";\r\nimport { DynamicTexture } from \"../Materials/Textures/dynamicTexture\";\r\nimport { VertexBuffer } from \"../Buffers/buffer\";\r\nimport { Effect } from \"../Materials/effect\";\r\n\r\nimport type { ISkeletonViewerOptions, IBoneWeightShaderOptions, ISkeletonMapShaderOptions, ISkeletonMapShaderColorMapKnot, ISkeletonViewerDisplayOptions } from \"./ISkeletonViewer\";\r\nimport type { Observer } from \"../Misc/observable\";\r\n\r\nimport { CreateSphere } from \"../Meshes/Builders/sphereBuilder\";\r\nimport { ExtrudeShapeCustom } from \"../Meshes/Builders/shapeBuilder\";\r\nimport { TransformNode } from \"../Meshes/transformNode\";\r\nimport { Logger } from \"core/Misc/logger\";\r\n\r\n/**\r\n * Class used to render a debug view of a given skeleton\r\n * @see http://www.babylonjs-playground.com/#1BZJVJ#8\r\n */\r\nexport class SkeletonViewer {\r\n    /** public Display constants BABYLON.SkeletonViewer.DISPLAY_LINES */\r\n    public static readonly DISPLAY_LINES = 0;\r\n    /** public Display constants BABYLON.SkeletonViewer.DISPLAY_SPHERES */\r\n    public static readonly DISPLAY_SPHERES = 1;\r\n    /** public Display constants BABYLON.SkeletonViewer.DISPLAY_SPHERE_AND_SPURS */\r\n    public static readonly DISPLAY_SPHERE_AND_SPURS = 2;\r\n\r\n    /** public static method to create a BoneWeight Shader\r\n     * @param options The constructor options\r\n     * @param scene The scene that the shader is scoped to\r\n     * @returns The created ShaderMaterial\r\n     * @see http://www.babylonjs-playground.com/#1BZJVJ#395\r\n     */\r\n    static CreateBoneWeightShader(options: IBoneWeightShaderOptions, scene: Scene): ShaderMaterial {\r\n        const skeleton: Skeleton = options.skeleton;\r\n        const colorBase: Color3 = options.colorBase ?? Color3.Black();\r\n        const colorZero: Color3 = options.colorZero ?? Color3.Blue();\r\n        const colorQuarter: Color3 = options.colorQuarter ?? Color3.Green();\r\n        const colorHalf: Color3 = options.colorHalf ?? Color3.Yellow();\r\n        const colorFull: Color3 = options.colorFull ?? Color3.Red();\r\n        const targetBoneIndex: number = options.targetBoneIndex ?? 0;\r\n\r\n        Effect.ShadersStore[\"boneWeights:\" + skeleton.name + \"VertexShader\"] = `precision highp float;\r\n\r\n        attribute vec3 position;\r\n        attribute vec2 uv;\r\n\r\n        uniform mat4 view;\r\n        uniform mat4 projection;\r\n        uniform mat4 worldViewProjection;\r\n\r\n        #include<bonesDeclaration>\r\n        #if NUM_BONE_INFLUENCERS == 0\r\n            attribute vec4 matricesIndices;\r\n            attribute vec4 matricesWeights;\r\n        #endif\r\n        #include<bakedVertexAnimationDeclaration>\r\n\r\n        #include<instancesDeclaration>\r\n\r\n        varying vec3 vColor;\r\n\r\n        uniform vec3 colorBase;\r\n        uniform vec3 colorZero;\r\n        uniform vec3 colorQuarter;\r\n        uniform vec3 colorHalf;\r\n        uniform vec3 colorFull;\r\n\r\n        uniform float targetBoneIndex;\r\n\r\n        void main() {\r\n            vec3 positionUpdated = position;\r\n\r\n            #include<instancesVertex>\r\n            #include<bonesVertex>\r\n            #include<bakedVertexAnimation>\r\n\r\n            vec4 worldPos = finalWorld * vec4(positionUpdated, 1.0);\r\n\r\n            vec3 color = colorBase;\r\n            float totalWeight = 0.;\r\n            if(matricesIndices[0] == targetBoneIndex && matricesWeights[0] > 0.){\r\n                totalWeight += matricesWeights[0];\r\n            }\r\n            if(matricesIndices[1] == targetBoneIndex && matricesWeights[1] > 0.){\r\n                totalWeight += matricesWeights[1];\r\n            }\r\n            if(matricesIndices[2] == targetBoneIndex && matricesWeights[2] > 0.){\r\n                totalWeight += matricesWeights[2];\r\n            }\r\n            if(matricesIndices[3] == targetBoneIndex && matricesWeights[3] > 0.){\r\n                totalWeight += matricesWeights[3];\r\n            }\r\n\r\n            color = mix(color, colorZero, smoothstep(0., 0.25, totalWeight));\r\n            color = mix(color, colorQuarter, smoothstep(0.25, 0.5, totalWeight));\r\n            color = mix(color, colorHalf, smoothstep(0.5, 0.75, totalWeight));\r\n            color = mix(color, colorFull, smoothstep(0.75, 1.0, totalWeight));\r\n            vColor = color;\r\n\r\n        gl_Position = projection * view * worldPos;\r\n        }`;\r\n        Effect.ShadersStore[\"boneWeights:\" + skeleton.name + \"FragmentShader\"] = `\r\n            precision highp float;\r\n            varying vec3 vPosition;\r\n\r\n            varying vec3 vColor;\r\n\r\n            void main() {\r\n                vec4 color = vec4(vColor, 1.0);\r\n                gl_FragColor = color;\r\n            }\r\n        `;\r\n        const shader: ShaderMaterial = new ShaderMaterial(\r\n            \"boneWeight:\" + skeleton.name,\r\n            scene,\r\n            {\r\n                vertex: \"boneWeights:\" + skeleton.name,\r\n                fragment: \"boneWeights:\" + skeleton.name,\r\n            },\r\n            {\r\n                attributes: [\"position\", \"normal\", \"matricesIndices\", \"matricesWeights\"],\r\n                uniforms: [\r\n                    \"world\",\r\n                    \"worldView\",\r\n                    \"worldViewProjection\",\r\n                    \"view\",\r\n                    \"projection\",\r\n                    \"viewProjection\",\r\n                    \"colorBase\",\r\n                    \"colorZero\",\r\n                    \"colorQuarter\",\r\n                    \"colorHalf\",\r\n                    \"colorFull\",\r\n                    \"targetBoneIndex\",\r\n                ],\r\n            }\r\n        );\r\n\r\n        shader.setColor3(\"colorBase\", colorBase);\r\n        shader.setColor3(\"colorZero\", colorZero);\r\n        shader.setColor3(\"colorQuarter\", colorQuarter);\r\n        shader.setColor3(\"colorHalf\", colorHalf);\r\n        shader.setColor3(\"colorFull\", colorFull);\r\n        shader.setFloat(\"targetBoneIndex\", targetBoneIndex);\r\n\r\n        shader.getClassName = (): string => {\r\n            return \"BoneWeightShader\";\r\n        };\r\n\r\n        shader.transparencyMode = Material.MATERIAL_OPAQUE;\r\n\r\n        return shader;\r\n    }\r\n\r\n    /** public static method to create a BoneWeight Shader\r\n     * @param options The constructor options\r\n     * @param scene The scene that the shader is scoped to\r\n     * @returns The created ShaderMaterial\r\n     */\r\n    static CreateSkeletonMapShader(options: ISkeletonMapShaderOptions, scene: Scene) {\r\n        const skeleton: Skeleton = options.skeleton;\r\n        const colorMap: ISkeletonMapShaderColorMapKnot[] = options.colorMap ?? [\r\n            {\r\n                color: new Color3(1, 0.38, 0.18),\r\n                location: 0,\r\n            },\r\n            {\r\n                color: new Color3(0.59, 0.18, 1.0),\r\n                location: 0.2,\r\n            },\r\n            {\r\n                color: new Color3(0.59, 1, 0.18),\r\n                location: 0.4,\r\n            },\r\n            {\r\n                color: new Color3(1, 0.87, 0.17),\r\n                location: 0.6,\r\n            },\r\n            {\r\n                color: new Color3(1, 0.17, 0.42),\r\n                location: 0.8,\r\n            },\r\n            {\r\n                color: new Color3(0.17, 0.68, 1.0),\r\n                location: 1.0,\r\n            },\r\n        ];\r\n\r\n        const bufferWidth: number = skeleton.bones.length + 1;\r\n        const colorMapBuffer: number[] = SkeletonViewer._CreateBoneMapColorBuffer(bufferWidth, colorMap, scene);\r\n        const shader = new ShaderMaterial(\r\n            \"boneWeights:\" + skeleton.name,\r\n            scene,\r\n            {\r\n                vertexSource:\r\n                    `precision highp float;\r\n\r\n            attribute vec3 position;\r\n            attribute vec2 uv;\r\n\r\n            uniform mat4 view;\r\n            uniform mat4 projection;\r\n            uniform mat4 worldViewProjection;\r\n            uniform float colorMap[` +\r\n                    skeleton.bones.length * 4 +\r\n                    `];\r\n\r\n            #include<bonesDeclaration>\r\n            #if NUM_BONE_INFLUENCERS == 0\r\n                attribute vec4 matricesIndices;\r\n                attribute vec4 matricesWeights;\r\n            #endif\r\n            #include<bakedVertexAnimationDeclaration>\r\n            #include<instancesDeclaration>\r\n\r\n            varying vec3 vColor;\r\n\r\n            void main() {\r\n                vec3 positionUpdated = position;\r\n\r\n                #include<instancesVertex>\r\n                #include<bonesVertex>\r\n                #include<bakedVertexAnimation>\r\n\r\n                vec3 color = vec3(0.);\r\n                bool first = true;\r\n\r\n                for (int i = 0; i < 4; i++) {\r\n                    int boneIdx = int(matricesIndices[i]);\r\n                    float boneWgt = matricesWeights[i];\r\n\r\n                    vec3 c = vec3(colorMap[boneIdx * 4 + 0], colorMap[boneIdx * 4 + 1], colorMap[boneIdx * 4 + 2]);\r\n\r\n                    if (boneWgt > 0.) {\r\n                        if (first) {\r\n                            first = false;\r\n                            color = c;\r\n                        } else {\r\n                            color = mix(color, c, boneWgt);\r\n                        }\r\n                    }\r\n                }\r\n\r\n                vColor = color;\r\n\r\n                vec4 worldPos = finalWorld * vec4(positionUpdated, 1.0);\r\n\r\n                gl_Position = projection * view * worldPos;\r\n            }`,\r\n                fragmentSource: `\r\n            precision highp float;\r\n            varying vec3 vColor;\r\n\r\n            void main() {\r\n                vec4 color = vec4( vColor, 1.0 );\r\n                gl_FragColor = color;\r\n            }\r\n            `,\r\n            },\r\n            {\r\n                attributes: [\"position\", \"normal\", \"matricesIndices\", \"matricesWeights\"],\r\n                uniforms: [\"world\", \"worldView\", \"worldViewProjection\", \"view\", \"projection\", \"viewProjection\", \"colorMap\"],\r\n            }\r\n        );\r\n\r\n        shader.setFloats(\"colorMap\", colorMapBuffer);\r\n\r\n        shader.getClassName = (): string => {\r\n            return \"SkeletonMapShader\";\r\n        };\r\n\r\n        shader.transparencyMode = Material.MATERIAL_OPAQUE;\r\n\r\n        return shader;\r\n    }\r\n\r\n    /** private static method to create a BoneWeight Shader\r\n     * @param size The size of the buffer to create (usually the bone count)\r\n     * @param colorMap The gradient data to generate\r\n     * @param scene The scene that the shader is scoped to\r\n     * @returns an Array of floats from the color gradient values\r\n     */\r\n    private static _CreateBoneMapColorBuffer(size: number, colorMap: ISkeletonMapShaderColorMapKnot[], scene: Scene) {\r\n        const tempGrad = new DynamicTexture(\"temp\", { width: size, height: 1 }, scene, false);\r\n        const ctx = tempGrad.getContext();\r\n        const grad = ctx.createLinearGradient(0, 0, size, 0);\r\n\r\n        for (const stop of colorMap) {\r\n            grad.addColorStop(stop.location, stop.color.toHexString());\r\n        }\r\n\r\n        ctx.fillStyle = grad;\r\n        ctx.fillRect(0, 0, size, 1);\r\n        tempGrad.update();\r\n        const buffer: number[] = [];\r\n        const data: Uint8ClampedArray = ctx.getImageData(0, 0, size, 1).data;\r\n        const rUnit = 1 / 255;\r\n        for (let i = 0; i < data.length; i++) {\r\n            buffer.push(data[i] * rUnit);\r\n        }\r\n        tempGrad.dispose();\r\n        return buffer;\r\n    }\r\n\r\n    /** If SkeletonViewer scene scope. */\r\n    private _scene: Scene;\r\n\r\n    /** Gets or sets the color used to render the skeleton */\r\n    public color: Color3 = Color3.White();\r\n\r\n    /** Array of the points of the skeleton fo the line view. */\r\n    private _debugLines = new Array<Array<Vector3>>();\r\n\r\n    /** The SkeletonViewers Mesh. */\r\n    private _debugMesh: Nullable<LinesMesh>;\r\n\r\n    /** The local axes Meshes. */\r\n    private _localAxes: Nullable<LinesMesh> = null;\r\n\r\n    /** If SkeletonViewer is enabled. */\r\n    private _isEnabled = true;\r\n\r\n    /** If SkeletonViewer is ready. */\r\n    private _ready: boolean;\r\n\r\n    /** SkeletonViewer render observable. */\r\n    private _obs: Nullable<Observer<Scene>> = null;\r\n\r\n    /** The Utility Layer to render the gizmos in. */\r\n    private _utilityLayer: Nullable<UtilityLayerRenderer>;\r\n\r\n    private _boneIndices: Set<number>;\r\n\r\n    /** Gets the Scene. */\r\n    get scene(): Scene {\r\n        return this._scene;\r\n    }\r\n    /** Gets the utilityLayer. */\r\n    get utilityLayer(): Nullable<UtilityLayerRenderer> {\r\n        return this._utilityLayer;\r\n    }\r\n    /** Checks Ready Status. */\r\n    get isReady(): boolean {\r\n        return this._ready;\r\n    }\r\n    /** Sets Ready Status. */\r\n    set ready(value: boolean) {\r\n        this._ready = value;\r\n    }\r\n    /** Gets the debugMesh */\r\n    get debugMesh(): Nullable<AbstractMesh> | Nullable<LinesMesh> {\r\n        return this._debugMesh;\r\n    }\r\n    /** Sets the debugMesh */\r\n    set debugMesh(value: Nullable<AbstractMesh> | Nullable<LinesMesh>) {\r\n        this._debugMesh = value as any;\r\n    }\r\n    /** Gets the displayMode */\r\n    get displayMode(): number {\r\n        return this.options.displayMode || SkeletonViewer.DISPLAY_LINES;\r\n    }\r\n    /** Sets the displayMode */\r\n    set displayMode(value: number) {\r\n        if (value > SkeletonViewer.DISPLAY_SPHERE_AND_SPURS) {\r\n            value = SkeletonViewer.DISPLAY_LINES;\r\n        }\r\n        this.options.displayMode = value;\r\n    }\r\n    /**\r\n     * Creates a new SkeletonViewer\r\n     * @param skeleton defines the skeleton to render\r\n     * @param mesh defines the mesh attached to the skeleton\r\n     * @param scene defines the hosting scene\r\n     * @param autoUpdateBonesMatrices defines a boolean indicating if bones matrices must be forced to update before rendering (true by default)\r\n     * @param renderingGroupId defines the rendering group id to use with the viewer\r\n     * @param options All of the extra constructor options for the SkeletonViewer\r\n     */\r\n    constructor(\r\n        /** defines the skeleton to render */\r\n        public skeleton: Skeleton,\r\n        /** defines the mesh attached to the skeleton */\r\n        public mesh: Nullable<AbstractMesh>,\r\n        /** The Scene scope*/\r\n        scene: Scene,\r\n        /** [true] defines a boolean indicating if bones matrices must be forced to update before rendering (true by default)  */\r\n        public autoUpdateBonesMatrices: boolean = true,\r\n        /** [3] defines the rendering group id to use with the viewer */\r\n        public renderingGroupId: number = 3,\r\n        /** [Object] is the options for the viewer */\r\n        public options: Partial<ISkeletonViewerOptions> = {}\r\n    ) {\r\n        this._scene = scene;\r\n        this._ready = false;\r\n\r\n        //Defaults\r\n        options.pauseAnimations = options.pauseAnimations ?? true;\r\n        options.returnToRest = options.returnToRest ?? false;\r\n        options.displayMode = options.displayMode ?? SkeletonViewer.DISPLAY_LINES;\r\n        options.displayOptions = options.displayOptions ?? {};\r\n        options.displayOptions.midStep = options.displayOptions.midStep ?? 0.235;\r\n        options.displayOptions.midStepFactor = options.displayOptions.midStepFactor ?? 0.155;\r\n        options.displayOptions.sphereBaseSize = options.displayOptions.sphereBaseSize ?? 0.15;\r\n        options.displayOptions.sphereScaleUnit = options.displayOptions.sphereScaleUnit ?? 2;\r\n        options.displayOptions.sphereFactor = options.displayOptions.sphereFactor ?? 0.865;\r\n        options.displayOptions.spurFollowsChild = options.displayOptions.spurFollowsChild ?? false;\r\n        options.displayOptions.showLocalAxes = options.displayOptions.showLocalAxes ?? false;\r\n        options.displayOptions.localAxesSize = options.displayOptions.localAxesSize ?? 0.075;\r\n        options.computeBonesUsingShaders = options.computeBonesUsingShaders ?? true;\r\n        options.useAllBones = options.useAllBones ?? true;\r\n\r\n        this._boneIndices = new Set();\r\n\r\n        if (!options.useAllBones) {\r\n            const initialMeshBoneIndices = mesh?.getVerticesData(VertexBuffer.MatricesIndicesKind);\r\n            const initialMeshBoneWeights = mesh?.getVerticesData(VertexBuffer.MatricesWeightsKind);\r\n\r\n            if (initialMeshBoneIndices && initialMeshBoneWeights) {\r\n                for (let i = 0; i < initialMeshBoneIndices.length; ++i) {\r\n                    const index = initialMeshBoneIndices[i],\r\n                        weight = initialMeshBoneWeights[i];\r\n                    if (weight !== 0) {\r\n                        this._boneIndices.add(index);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        /* Create Utility Layer */\r\n        this._utilityLayer = new UtilityLayerRenderer(this._scene, false);\r\n        this._utilityLayer.pickUtilitySceneFirst = false;\r\n        this._utilityLayer.utilityLayerScene.autoClearDepthAndStencil = true;\r\n\r\n        let displayMode = this.options.displayMode || 0;\r\n        if (displayMode > SkeletonViewer.DISPLAY_SPHERE_AND_SPURS) {\r\n            displayMode = SkeletonViewer.DISPLAY_LINES;\r\n        }\r\n        this.displayMode = displayMode;\r\n        //Prep the Systems\r\n        this.update();\r\n        this._bindObs();\r\n    }\r\n\r\n    /** The Dynamic bindings for the update functions */\r\n    private _bindObs(): void {\r\n        switch (this.displayMode) {\r\n            case SkeletonViewer.DISPLAY_LINES: {\r\n                this._obs = this.scene.onBeforeRenderObservable.add(() => {\r\n                    this._displayLinesUpdate();\r\n                });\r\n                break;\r\n            }\r\n        }\r\n    }\r\n\r\n    /** Update the viewer to sync with current skeleton state, only used to manually update. */\r\n    public update(): void {\r\n        switch (this.displayMode) {\r\n            case SkeletonViewer.DISPLAY_LINES: {\r\n                this._displayLinesUpdate();\r\n                break;\r\n            }\r\n            case SkeletonViewer.DISPLAY_SPHERES: {\r\n                this._buildSpheresAndSpurs(true);\r\n                break;\r\n            }\r\n            case SkeletonViewer.DISPLAY_SPHERE_AND_SPURS: {\r\n                this._buildSpheresAndSpurs(false);\r\n                break;\r\n            }\r\n        }\r\n\r\n        this._buildLocalAxes();\r\n    }\r\n\r\n    /** Gets or sets a boolean indicating if the viewer is enabled */\r\n    public set isEnabled(value: boolean) {\r\n        if (this.isEnabled === value) {\r\n            return;\r\n        }\r\n\r\n        this._isEnabled = value;\r\n\r\n        if (this.debugMesh) {\r\n            this.debugMesh.setEnabled(value);\r\n        }\r\n\r\n        if (value && !this._obs) {\r\n            this._bindObs();\r\n        } else if (!value && this._obs) {\r\n            this.scene.onBeforeRenderObservable.remove(this._obs);\r\n            this._obs = null;\r\n        }\r\n    }\r\n\r\n    public get isEnabled(): boolean {\r\n        return this._isEnabled;\r\n    }\r\n\r\n    private _getBonePosition(position: Vector3, bone: Bone, meshMat: Matrix, x = 0, y = 0, z = 0): void {\r\n        const tmat = TmpVectors.Matrix[0];\r\n        const parentBone = bone.getParent();\r\n        tmat.copyFrom(bone.getLocalMatrix());\r\n\r\n        if (x !== 0 || y !== 0 || z !== 0) {\r\n            const tmat2 = TmpVectors.Matrix[1];\r\n            Matrix.IdentityToRef(tmat2);\r\n            tmat2.setTranslationFromFloats(x, y, z);\r\n            tmat2.multiplyToRef(tmat, tmat);\r\n        }\r\n\r\n        if (parentBone) {\r\n            tmat.multiplyToRef(parentBone.getAbsoluteMatrix(), tmat);\r\n        }\r\n\r\n        tmat.multiplyToRef(meshMat, tmat);\r\n\r\n        position.x = tmat.m[12];\r\n        position.y = tmat.m[13];\r\n        position.z = tmat.m[14];\r\n    }\r\n\r\n    private _getLinesForBonesWithLength(bones: Bone[], mesh: Nullable<AbstractMesh>): void {\r\n        const len = bones.length;\r\n\r\n        let matrix;\r\n        let meshPos;\r\n        if (mesh) {\r\n            matrix = mesh.getWorldMatrix();\r\n            meshPos = mesh.position;\r\n        } else {\r\n            matrix = new Matrix();\r\n            meshPos = bones[0].position;\r\n        }\r\n        let idx = 0;\r\n        for (let i = 0; i < len; i++) {\r\n            const bone = bones[i];\r\n            let points = this._debugLines[idx];\r\n\r\n            if (bone._index === -1 || (!this._boneIndices.has(bone.getIndex()) && !this.options.useAllBones)) {\r\n                continue;\r\n            }\r\n            if (!points) {\r\n                points = [Vector3.Zero(), Vector3.Zero()];\r\n                this._debugLines[idx] = points;\r\n            }\r\n            this._getBonePosition(points[0], bone, matrix);\r\n            this._getBonePosition(points[1], bone, matrix, 0, bone.length, 0);\r\n            points[0].subtractInPlace(meshPos);\r\n            points[1].subtractInPlace(meshPos);\r\n            idx++;\r\n        }\r\n    }\r\n\r\n    private _getLinesForBonesNoLength(bones: Bone[]): void {\r\n        const len = bones.length;\r\n        let boneNum = 0;\r\n\r\n        const mesh = this.mesh;\r\n        let transformNode;\r\n        let meshPos;\r\n        if (mesh) {\r\n            transformNode = mesh;\r\n            meshPos = mesh.position;\r\n        } else {\r\n            transformNode = new TransformNode(\"\");\r\n            meshPos = bones[0].position;\r\n        }\r\n        for (let i = len - 1; i >= 0; i--) {\r\n            const childBone = bones[i];\r\n            const parentBone = childBone.getParent();\r\n            if (!parentBone || (!this._boneIndices.has(childBone.getIndex()) && !this.options.useAllBones)) {\r\n                continue;\r\n            }\r\n            let points = this._debugLines[boneNum];\r\n            if (!points) {\r\n                points = [Vector3.Zero(), Vector3.Zero()];\r\n                this._debugLines[boneNum] = points;\r\n            }\r\n            childBone.getAbsolutePositionToRef(transformNode, points[0]);\r\n            parentBone.getAbsolutePositionToRef(transformNode, points[1]);\r\n            points[0].subtractInPlace(meshPos);\r\n            points[1].subtractInPlace(meshPos);\r\n            boneNum++;\r\n        }\r\n        if (!mesh) {\r\n            transformNode.dispose();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * function to revert the mesh and scene back to the initial state.\r\n     * @param animationState\r\n     */\r\n    private _revert(animationState: boolean): void {\r\n        if (this.options.pauseAnimations) {\r\n            this.scene.animationsEnabled = animationState;\r\n            this.utilityLayer!.utilityLayerScene.animationsEnabled = animationState;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * function to get the absolute bind pose of a bone by accumulating transformations up the bone hierarchy.\r\n     * @param bone\r\n     * @param matrix\r\n     */\r\n    private _getAbsoluteBindPoseToRef(bone: Nullable<Bone>, matrix: Matrix) {\r\n        if (bone === null || bone._index === -1) {\r\n            matrix.copyFrom(Matrix.Identity());\r\n            return;\r\n        }\r\n\r\n        this._getAbsoluteBindPoseToRef(bone.getParent(), matrix);\r\n        bone.getBindMatrix().multiplyToRef(matrix, matrix);\r\n        return;\r\n    }\r\n\r\n    private _createSpur(anchorPoint: Vector3, bone: Bone, childPoint: Vector3, childBone: Nullable<Bone>, displayOptions: ISkeletonViewerDisplayOptions, utilityLayerScene: Scene) {\r\n        const dir = childPoint.subtract(anchorPoint);\r\n        const h = dir.length();\r\n        const up = dir.normalize().scale(h);\r\n\r\n        const midStep = displayOptions.midStep || 0.165;\r\n        const midStepFactor = displayOptions.midStepFactor || 0.215;\r\n\r\n        const up0 = up.scale(midStep);\r\n\r\n        const spur = ExtrudeShapeCustom(\r\n            \"skeletonViewer\",\r\n            {\r\n                shape: [new Vector3(1, -1, 0), new Vector3(1, 1, 0), new Vector3(-1, 1, 0), new Vector3(-1, -1, 0), new Vector3(1, -1, 0)],\r\n                path: [Vector3.Zero(), up0, up],\r\n                scaleFunction: (i: number) => {\r\n                    switch (i) {\r\n                        case 0:\r\n                        case 2:\r\n                            return 0;\r\n                        case 1:\r\n                            return h * midStepFactor;\r\n                    }\r\n                    return 0;\r\n                },\r\n                sideOrientation: Mesh.DEFAULTSIDE,\r\n                updatable: false,\r\n            },\r\n            utilityLayerScene\r\n        );\r\n\r\n        const numVertices = spur.getTotalVertices();\r\n        const mwk: number[] = [],\r\n            mik: number[] = [];\r\n\r\n        for (let i = 0; i < numVertices; i++) {\r\n            mwk.push(1, 0, 0, 0);\r\n\r\n            // Select verts at end of spur (ie vert 10 to 14) and bind to child\r\n            // bone if spurFollowsChild is enabled.\r\n            if (childBone && displayOptions.spurFollowsChild && i > 9) {\r\n                mik.push(childBone.getIndex(), 0, 0, 0);\r\n            } else {\r\n                mik.push(bone.getIndex(), 0, 0, 0);\r\n            }\r\n        }\r\n\r\n        spur.position = anchorPoint.clone();\r\n\r\n        spur.setVerticesData(VertexBuffer.MatricesWeightsKind, mwk, false);\r\n        spur.setVerticesData(VertexBuffer.MatricesIndicesKind, mik, false);\r\n        spur.convertToFlatShadedMesh();\r\n\r\n        return spur;\r\n    }\r\n\r\n    private _getBoundingSphereForBone(boneIndex: number) {\r\n        if (!this.mesh) {\r\n            return null;\r\n        }\r\n\r\n        const positions = this.mesh.getVerticesData(VertexBuffer.PositionKind);\r\n        const indices = this.mesh.getIndices();\r\n        const boneWeights = this.mesh.getVerticesData(VertexBuffer.MatricesWeightsKind);\r\n        const boneIndices = this.mesh.getVerticesData(VertexBuffer.MatricesIndicesKind);\r\n        if (!positions || !indices || !boneWeights || !boneIndices) {\r\n            return null;\r\n        }\r\n\r\n        const min = new Vector3(Number.MAX_VALUE, Number.MAX_VALUE, Number.MAX_VALUE);\r\n        const max = new Vector3(-Number.MAX_VALUE, -Number.MAX_VALUE, -Number.MAX_VALUE);\r\n\r\n        let found = 0;\r\n        for (let i = 0; i < indices.length; ++i) {\r\n            const vertexIndex = indices[i];\r\n\r\n            for (let b = 0; b < 4; ++b) {\r\n                const bIndex = boneIndices[vertexIndex * 4 + b];\r\n                const bWeight = boneWeights[vertexIndex * 4 + b];\r\n\r\n                if (bIndex === boneIndex && bWeight > 1e-5) {\r\n                    Vector3.FromArrayToRef(positions, vertexIndex * 3, TmpVectors.Vector3[0]);\r\n                    min.minimizeInPlace(TmpVectors.Vector3[0]);\r\n                    max.maximizeInPlace(TmpVectors.Vector3[0]);\r\n                    found++;\r\n                    break;\r\n                }\r\n            }\r\n        }\r\n\r\n        return found > 1\r\n            ? {\r\n                  center: Vector3.Center(min, max),\r\n                  radius: Vector3.Distance(min, max) / 2,\r\n              }\r\n            : null;\r\n    }\r\n\r\n    /**\r\n     * function to build and bind sphere joint points and spur bone representations.\r\n     * @param spheresOnly\r\n     */\r\n    private _buildSpheresAndSpurs(spheresOnly = true): void {\r\n        if (this._debugMesh) {\r\n            this._debugMesh.dispose();\r\n            this._debugMesh = null;\r\n            this.ready = false;\r\n        }\r\n\r\n        this._ready = false;\r\n        const utilityLayerScene = this.utilityLayer?.utilityLayerScene as Scene;\r\n        const bones: Bone[] = this.skeleton.bones;\r\n        const spheres: Array<[Mesh, Bone]> = [];\r\n        const spurs: Mesh[] = [];\r\n\r\n        const animationState = this.scene.animationsEnabled;\r\n\r\n        try {\r\n            if (this.options.pauseAnimations) {\r\n                this.scene.animationsEnabled = false;\r\n                utilityLayerScene.animationsEnabled = false;\r\n            }\r\n\r\n            if (this.options.returnToRest) {\r\n                this.skeleton.returnToRest();\r\n            }\r\n\r\n            if (this.autoUpdateBonesMatrices) {\r\n                this.skeleton.computeAbsoluteMatrices();\r\n            }\r\n\r\n            let longestBoneLength = Number.NEGATIVE_INFINITY;\r\n            const displayOptions = this.options.displayOptions || {};\r\n\r\n            for (let i = 0; i < bones.length; i++) {\r\n                const bone = bones[i];\r\n\r\n                if (bone._index === -1 || (!this._boneIndices.has(bone.getIndex()) && !this.options.useAllBones)) {\r\n                    continue;\r\n                }\r\n\r\n                const boneAbsoluteBindPoseTransform = new Matrix();\r\n                this._getAbsoluteBindPoseToRef(bone, boneAbsoluteBindPoseTransform);\r\n\r\n                const anchorPoint = new Vector3();\r\n\r\n                boneAbsoluteBindPoseTransform.decompose(undefined, undefined, anchorPoint);\r\n\r\n                if (bone.children.length > 0) {\r\n                    for (const bc of bone.children) {\r\n                        const childAbsoluteBindPoseTransform: Matrix = new Matrix();\r\n                        bc.getLocalMatrix().multiplyToRef(boneAbsoluteBindPoseTransform, childAbsoluteBindPoseTransform);\r\n                        const childPoint = new Vector3();\r\n                        childAbsoluteBindPoseTransform.decompose(undefined, undefined, childPoint);\r\n                        const distanceFromParent = Vector3.Distance(anchorPoint, childPoint);\r\n                        if (distanceFromParent > longestBoneLength) {\r\n                            longestBoneLength = distanceFromParent;\r\n                        }\r\n                        if (spheresOnly) {\r\n                            return;\r\n                        }\r\n\r\n                        spurs.push(this._createSpur(anchorPoint, bone, childPoint, bc, displayOptions, utilityLayerScene));\r\n                    }\r\n                } else {\r\n                    const boundingSphere = this._getBoundingSphereForBone(bone.getIndex());\r\n                    if (boundingSphere) {\r\n                        if (boundingSphere.radius > longestBoneLength) {\r\n                            longestBoneLength = boundingSphere.radius;\r\n                        }\r\n                        if (!spheresOnly) {\r\n                            let childPoint;\r\n                            const parentBone = bone.getParent();\r\n                            if (parentBone) {\r\n                                this._getAbsoluteBindPoseToRef(parentBone, boneAbsoluteBindPoseTransform);\r\n                                boneAbsoluteBindPoseTransform.decompose(undefined, undefined, TmpVectors.Vector3[0]);\r\n                                childPoint = anchorPoint.subtract(TmpVectors.Vector3[0]).normalize().scale(boundingSphere.radius).add(anchorPoint);\r\n                            } else {\r\n                                childPoint = boundingSphere.center.subtract(anchorPoint).normalize().scale(boundingSphere.radius).add(anchorPoint);\r\n                            }\r\n                            spurs.push(this._createSpur(anchorPoint, bone, childPoint, null, displayOptions, utilityLayerScene));\r\n                        }\r\n                    }\r\n                }\r\n\r\n                const sphereBaseSize = displayOptions.sphereBaseSize || 0.2;\r\n\r\n                const sphere = CreateSphere(\r\n                    \"skeletonViewer\",\r\n                    {\r\n                        segments: 6,\r\n                        diameter: sphereBaseSize,\r\n                        updatable: true,\r\n                    },\r\n                    utilityLayerScene\r\n                );\r\n\r\n                const numVertices = sphere.getTotalVertices();\r\n\r\n                const mwk: number[] = [],\r\n                    mik: number[] = [];\r\n\r\n                for (let i = 0; i < numVertices; i++) {\r\n                    mwk.push(1, 0, 0, 0);\r\n                    mik.push(bone.getIndex(), 0, 0, 0);\r\n                }\r\n\r\n                sphere.setVerticesData(VertexBuffer.MatricesWeightsKind, mwk, false);\r\n                sphere.setVerticesData(VertexBuffer.MatricesIndicesKind, mik, false);\r\n\r\n                sphere.position = anchorPoint.clone();\r\n                spheres.push([sphere, bone]);\r\n            }\r\n\r\n            const sphereScaleUnit = displayOptions.sphereScaleUnit || 2;\r\n            const sphereFactor = displayOptions.sphereFactor || 0.85;\r\n\r\n            const meshes = [];\r\n            for (let i = 0; i < spheres.length; i++) {\r\n                const [sphere, bone] = spheres[i];\r\n                const scale = 1 / (sphereScaleUnit / longestBoneLength);\r\n\r\n                let _stepsOut = 0;\r\n                let _b = bone;\r\n\r\n                while (_b.getParent() && (_b.getParent() as Bone).getIndex() !== -1) {\r\n                    _stepsOut++;\r\n                    _b = _b.getParent() as Bone;\r\n                }\r\n                sphere.scaling.scaleInPlace(scale * Math.pow(sphereFactor, _stepsOut));\r\n                meshes.push(sphere);\r\n            }\r\n\r\n            this.debugMesh = Mesh.MergeMeshes(meshes.concat(spurs), true, true);\r\n            if (this.debugMesh) {\r\n                this.debugMesh.renderingGroupId = this.renderingGroupId;\r\n                this.debugMesh.skeleton = this.skeleton;\r\n                this.debugMesh.parent = this.mesh;\r\n                this.debugMesh.computeBonesUsingShaders = this.options.computeBonesUsingShaders ?? true;\r\n                this.debugMesh.alwaysSelectAsActiveMesh = true;\r\n            }\r\n\r\n            const light = this.utilityLayer!._getSharedGizmoLight();\r\n            light.intensity = 0.7;\r\n\r\n            this._revert(animationState);\r\n            this.ready = true;\r\n        } catch (err) {\r\n            Logger.Error(err);\r\n            this._revert(animationState);\r\n            this.dispose();\r\n        }\r\n    }\r\n\r\n    private _buildLocalAxes(): void {\r\n        if (this._localAxes) {\r\n            this._localAxes.dispose();\r\n        }\r\n\r\n        this._localAxes = null;\r\n\r\n        const displayOptions = this.options.displayOptions || {};\r\n\r\n        if (!displayOptions.showLocalAxes) {\r\n            return;\r\n        }\r\n\r\n        const targetScene = this._utilityLayer!.utilityLayerScene;\r\n        const size = displayOptions.localAxesSize || 0.075;\r\n        const lines = [];\r\n        const colors = [];\r\n        const red = new Color4(1, 0, 0, 1);\r\n        const green = new Color4(0, 1, 0, 1);\r\n        const blue = new Color4(0, 0, 1, 1);\r\n\r\n        const mwk: number[] = [];\r\n        const mik: number[] = [];\r\n        const vertsPerBone = 6;\r\n\r\n        for (const i in this.skeleton.bones) {\r\n            const bone = this.skeleton.bones[i];\r\n\r\n            if (bone._index === -1 || (!this._boneIndices.has(bone.getIndex()) && !this.options.useAllBones)) {\r\n                continue;\r\n            }\r\n\r\n            const boneAbsoluteBindPoseTransform = new Matrix();\r\n            const boneOrigin = new Vector3();\r\n\r\n            this._getAbsoluteBindPoseToRef(bone, boneAbsoluteBindPoseTransform);\r\n            boneAbsoluteBindPoseTransform.decompose(undefined, TmpVectors.Quaternion[0], boneOrigin);\r\n\r\n            const m = new Matrix();\r\n            TmpVectors.Quaternion[0].toRotationMatrix(m);\r\n\r\n            const boneAxisX = Vector3.TransformCoordinates(new Vector3(0 + size, 0, 0), m);\r\n            const boneAxisY = Vector3.TransformCoordinates(new Vector3(0, 0 + size, 0), m);\r\n            const boneAxisZ = Vector3.TransformCoordinates(new Vector3(0, 0, 0 + size), m);\r\n\r\n            const axisX = [boneOrigin, boneOrigin.add(boneAxisX)];\r\n            const axisY = [boneOrigin, boneOrigin.add(boneAxisY)];\r\n            const axisZ = [boneOrigin, boneOrigin.add(boneAxisZ)];\r\n\r\n            const linePoints = [axisX, axisY, axisZ];\r\n            const lineColors = [\r\n                [red, red],\r\n                [green, green],\r\n                [blue, blue],\r\n            ];\r\n\r\n            lines.push(...linePoints);\r\n            colors.push(...lineColors);\r\n\r\n            for (let j = 0; j < vertsPerBone; j++) {\r\n                mwk.push(1, 0, 0, 0);\r\n                mik.push(bone.getIndex(), 0, 0, 0);\r\n            }\r\n        }\r\n\r\n        this._localAxes = CreateLineSystem(\"localAxes\", { lines: lines, colors: colors, updatable: true }, targetScene);\r\n        this._localAxes.setVerticesData(VertexBuffer.MatricesWeightsKind, mwk, false);\r\n        this._localAxes.setVerticesData(VertexBuffer.MatricesIndicesKind, mik, false);\r\n        this._localAxes.skeleton = this.skeleton;\r\n        this._localAxes.renderingGroupId = this.renderingGroupId + 1;\r\n        this._localAxes.parent = this.mesh;\r\n        this._localAxes.computeBonesUsingShaders = this.options.computeBonesUsingShaders ?? true;\r\n    }\r\n\r\n    /** Update the viewer to sync with current skeleton state, only used for the line display. */\r\n    private _displayLinesUpdate(): void {\r\n        if (!this._utilityLayer) {\r\n            return;\r\n        }\r\n\r\n        if (this.autoUpdateBonesMatrices) {\r\n            this.skeleton.computeAbsoluteMatrices();\r\n        }\r\n\r\n        if (this.skeleton.bones[0].length === undefined) {\r\n            this._getLinesForBonesNoLength(this.skeleton.bones);\r\n        } else {\r\n            this._getLinesForBonesWithLength(this.skeleton.bones, this.mesh);\r\n        }\r\n\r\n        const targetScene = this._utilityLayer.utilityLayerScene;\r\n\r\n        if (targetScene) {\r\n            if (!this._debugMesh) {\r\n                this._debugMesh = CreateLineSystem(\"\", { lines: this._debugLines, updatable: true, instance: null }, targetScene);\r\n                this._debugMesh.renderingGroupId = this.renderingGroupId;\r\n            } else {\r\n                CreateLineSystem(\"\", { lines: this._debugLines, updatable: true, instance: this._debugMesh }, targetScene);\r\n            }\r\n            if (this.mesh) {\r\n                this._debugMesh.position.copyFrom(this.mesh.position);\r\n            } else {\r\n                this._debugMesh.position.copyFrom(this.skeleton.bones[0].position);\r\n            }\r\n            this._debugMesh.color = this.color;\r\n        }\r\n    }\r\n    /** Changes the displayMode of the skeleton viewer\r\n     * @param mode The displayMode numerical value\r\n     */\r\n    public changeDisplayMode(mode: number): void {\r\n        const wasEnabled = this.isEnabled ? true : false;\r\n        if (this.displayMode !== mode) {\r\n            this.isEnabled = false;\r\n            if (this._debugMesh) {\r\n                this._debugMesh.dispose();\r\n                this._debugMesh = null;\r\n                this.ready = false;\r\n            }\r\n            this.displayMode = mode;\r\n\r\n            this.update();\r\n            this._bindObs();\r\n            this.isEnabled = wasEnabled;\r\n        }\r\n    }\r\n\r\n    /** Sets a display option of the skeleton viewer\r\n     *\r\n     * | Option           | Type    | Default | Description |\r\n     * | ---------------- | ------- | ------- | ----------- |\r\n     * | midStep          | float   | 0.235   | A percentage between a bone and its child that determines the widest part of a spur. Only used when `displayMode` is set to `DISPLAY_SPHERE_AND_SPURS`. |\r\n     * | midStepFactor    | float   | 0.15    | Mid step width expressed as a factor of the length. A value of 0.5 makes the spur width half of the spur length. Only used when `displayMode` is set to `DISPLAY_SPHERE_AND_SPURS`. |\r\n     * | sphereBaseSize   | float   | 2       | Sphere base size. Only used when `displayMode` is set to `DISPLAY_SPHERE_AND_SPURS`. |\r\n     * | sphereScaleUnit  | float   | 0.865   | Sphere scale factor used to scale spheres in relation to the longest bone. Only used when `displayMode` is set to `DISPLAY_SPHERE_AND_SPURS`. |\r\n     * | spurFollowsChild | boolean | false   | Whether a spur should attach its far end to the child bone. |\r\n     * | showLocalAxes    | boolean | false   | Displays local axes on all bones. |\r\n     * | localAxesSize    | float   | 0.075   | Determines the length of each local axis. |\r\n     *\r\n     * @param option String of the option name\r\n     * @param value The numerical option value\r\n     */\r\n    public changeDisplayOptions(option: string, value: number | boolean): void {\r\n        const wasEnabled = this.isEnabled ? true : false;\r\n        (this.options.displayOptions as any)[option] = value;\r\n        this.isEnabled = false;\r\n        if (this._debugMesh) {\r\n            this._debugMesh.dispose();\r\n            this._debugMesh = null;\r\n            this.ready = false;\r\n        }\r\n        this.update();\r\n        this._bindObs();\r\n        this.isEnabled = wasEnabled;\r\n    }\r\n\r\n    /** Release associated resources */\r\n    public dispose(): void {\r\n        this.isEnabled = false;\r\n        if (this._debugMesh) {\r\n            this._debugMesh.dispose();\r\n            this._debugMesh = null;\r\n        }\r\n\r\n        if (this._utilityLayer) {\r\n            this._utilityLayer.dispose();\r\n            this._utilityLayer = null;\r\n        }\r\n\r\n        this.ready = false;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AACnE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAMrD,OAAO,EAAE,IAAI,EAAE,MAAM,gBAAgB,CAAC;AAEtC,OAAO,EAAE,gBAAgB,EAAE,MAAM,iCAAiC,CAAC;AACnE,OAAO,EAAE,oBAAoB,EAAE,MAAM,mCAAmC,CAAC;AACzE,OAAO,EAAE,QAAQ,EAAE,MAAM,uBAAuB,CAAC;AACjD,OAAO,EAAE,cAAc,EAAE,MAAM,6BAA6B,CAAC;AAC7D,OAAO,EAAE,cAAc,EAAE,MAAM,sCAAsC,CAAC;AACtE,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AACjD,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAK7C,OAAO,EAAE,YAAY,EAAE,MAAM,kCAAkC,CAAC;AAChE,OAAO,EAAE,kBAAkB,EAAE,MAAM,iCAAiC,CAAC;AACrE,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AACxD,OAAO,EAAE,MAAM,EAAE,0BAAyB;;;;;;;;;;;;;;;AAMpC,MAAO,cAAc;IAQvB;;;;;OAKG,CACH,MAAM,CAAC,sBAAsB,CAAC,OAAiC,EAAE,KAAY,EAAA;QACzE,MAAM,QAAQ,GAAa,OAAO,CAAC,QAAQ,CAAC;QAC5C,MAAM,SAAS,GAAW,OAAO,CAAC,SAAS,kKAAI,SAAM,CAAC,KAAK,EAAE,CAAC;QAC9D,MAAM,SAAS,GAAW,OAAO,CAAC,SAAS,kKAAI,SAAM,CAAC,IAAI,EAAE,CAAC;QAC7D,MAAM,YAAY,GAAW,OAAO,CAAC,YAAY,kKAAI,SAAM,CAAC,KAAK,EAAE,CAAC;QACpE,MAAM,SAAS,GAAW,OAAO,CAAC,SAAS,IAAI,uKAAM,CAAC,MAAM,EAAE,CAAC;QAC/D,MAAM,SAAS,GAAW,OAAO,CAAC,SAAS,kKAAI,SAAM,CAAC,GAAG,EAAE,CAAC;QAC5D,MAAM,eAAe,GAAW,OAAO,CAAC,eAAe,IAAI,CAAC,CAAC;mKAE7D,SAAM,CAAC,YAAY,CAAC,cAAc,GAAG,QAAQ,CAAC,IAAI,GAAG,cAAc,CAAC,GAAG,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UA2DrE,CAAC;mKACH,SAAM,CAAC,YAAY,CAAC,cAAc,GAAG,QAAQ,CAAC,IAAI,GAAG,gBAAgB,CAAC,GAAG,CAAA;;;;;;;;;;SAUxE,CAAC;QACF,MAAM,MAAM,GAAmB,uKAAI,iBAAc,CAC7C,aAAa,GAAG,QAAQ,CAAC,IAAI,EAC7B,KAAK,EACL;YACI,MAAM,EAAE,cAAc,GAAG,QAAQ,CAAC,IAAI;YACtC,QAAQ,EAAE,cAAc,GAAG,QAAQ,CAAC,IAAI;SAC3C,EACD;YACI,UAAU,EAAE;gBAAC,UAAU;gBAAE,QAAQ;gBAAE,iBAAiB;gBAAE,iBAAiB;aAAC;YACxE,QAAQ,EAAE;gBACN,OAAO;gBACP,WAAW;gBACX,qBAAqB;gBACrB,MAAM;gBACN,YAAY;gBACZ,gBAAgB;gBAChB,WAAW;gBACX,WAAW;gBACX,cAAc;gBACd,WAAW;gBACX,WAAW;gBACX,iBAAiB;aACpB;SACJ,CACJ,CAAC;QAEF,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QACzC,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QACzC,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;QAC/C,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QACzC,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QACzC,MAAM,CAAC,QAAQ,CAAC,iBAAiB,EAAE,eAAe,CAAC,CAAC;QAEpD,MAAM,CAAC,YAAY,GAAG,GAAW,EAAE;YAC/B,OAAO,kBAAkB,CAAC;QAC9B,CAAC,CAAC;QAEF,MAAM,CAAC,gBAAgB,+JAAG,YAAQ,CAAC,eAAe,CAAC;QAEnD,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;OAIG,CACH,MAAM,CAAC,uBAAuB,CAAC,OAAkC,EAAE,KAAY,EAAA;QAC3E,MAAM,QAAQ,GAAa,OAAO,CAAC,QAAQ,CAAC;QAC5C,MAAM,QAAQ,GAAqC,OAAO,CAAC,QAAQ,IAAI;YACnE;gBACI,KAAK,EAAE,kKAAI,SAAM,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;gBAChC,QAAQ,EAAE,CAAC;aACd;YACD;gBACI,KAAK,EAAE,iKAAI,UAAM,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC;gBAClC,QAAQ,EAAE,GAAG;aAChB;YACD;gBACI,KAAK,EAAE,kKAAI,SAAM,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;gBAChC,QAAQ,EAAE,GAAG;aAChB;YACD;gBACI,KAAK,EAAE,kKAAI,SAAM,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;gBAChC,QAAQ,EAAE,GAAG;aAChB;YACD;gBACI,KAAK,EAAE,kKAAI,SAAM,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;gBAChC,QAAQ,EAAE,GAAG;aAChB;YACD;gBACI,KAAK,EAAE,kKAAI,SAAM,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC;gBAClC,QAAQ,EAAE,GAAG;aAChB;SACJ,CAAC;QAEF,MAAM,WAAW,GAAW,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;QACtD,MAAM,cAAc,GAAa,cAAc,CAAC,yBAAyB,CAAC,WAAW,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QACxG,MAAM,MAAM,GAAG,uKAAI,iBAAc,CAC7B,cAAc,GAAG,QAAQ,CAAC,IAAI,EAC9B,KAAK,EACL;YACI,YAAY,EACR,CAAA;;;;;;;;oCAQgB,GAChB,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,GACzB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;cA2CN;YACE,cAAc,EAAE,CAAA;;;;;;;;aAQnB;SACA,EACD;YACI,UAAU,EAAE;gBAAC,UAAU;gBAAE,QAAQ;gBAAE,iBAAiB;gBAAE,iBAAiB;aAAC;YACxE,QAAQ,EAAE;gBAAC,OAAO;gBAAE,WAAW;gBAAE,qBAAqB;gBAAE,MAAM;gBAAE,YAAY;gBAAE,gBAAgB;gBAAE,UAAU;aAAC;SAC9G,CACJ,CAAC;QAEF,MAAM,CAAC,SAAS,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;QAE7C,MAAM,CAAC,YAAY,GAAG,GAAW,EAAE;YAC/B,OAAO,mBAAmB,CAAC;QAC/B,CAAC,CAAC;QAEF,MAAM,CAAC,gBAAgB,gKAAG,WAAQ,CAAC,eAAe,CAAC;QAEnD,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;OAKG,CACK,MAAM,CAAC,yBAAyB,CAAC,IAAY,EAAE,QAA0C,EAAE,KAAY,EAAA;QAC3G,MAAM,QAAQ,GAAG,mLAAI,iBAAc,CAAC,MAAM,EAAE;YAAE,KAAK,EAAE,IAAI;YAAE,MAAM,EAAE,CAAC;QAAA,CAAE,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QACtF,MAAM,GAAG,GAAG,QAAQ,CAAC,UAAU,EAAE,CAAC;QAClC,MAAM,IAAI,GAAG,GAAG,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QAErD,KAAK,MAAM,IAAI,IAAI,QAAQ,CAAE,CAAC;YAC1B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC;QAC/D,CAAC;QAED,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC;QACrB,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QAC5B,QAAQ,CAAC,MAAM,EAAE,CAAC;QAClB,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,IAAI,GAAsB,GAAG,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;QACrE,MAAM,KAAK,GAAG,CAAC,GAAG,GAAG,CAAC;QACtB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;YACnC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QACjC,CAAC;QACD,QAAQ,CAAC,OAAO,EAAE,CAAC;QACnB,OAAO,MAAM,CAAC;IAClB,CAAC;IA+BD,oBAAA,EAAsB,CACtB,IAAI,KAAK,GAAA;QACL,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IACD,2BAAA,EAA6B,CAC7B,IAAI,YAAY,GAAA;QACZ,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IACD,yBAAA,EAA2B,CAC3B,IAAI,OAAO,GAAA;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IACD,uBAAA,EAAyB,CACzB,IAAI,KAAK,CAAC,KAAc,EAAA;QACpB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,CAAC;IACD,uBAAA,EAAyB,CACzB,IAAI,SAAS,GAAA;QACT,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IACD,uBAAA,EAAyB,CACzB,IAAI,SAAS,CAAC,KAAmD,EAAA;QAC7D,IAAI,CAAC,UAAU,GAAG,KAAY,CAAC;IACnC,CAAC;IACD,yBAAA,EAA2B,CAC3B,IAAI,WAAW,GAAA;QACX,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,cAAc,CAAC,aAAa,CAAC;IACpE,CAAC;IACD,yBAAA,EAA2B,CAC3B,IAAI,WAAW,CAAC,KAAa,EAAA;QACzB,IAAI,KAAK,GAAG,cAAc,CAAC,wBAAwB,EAAE,CAAC;YAClD,KAAK,GAAG,cAAc,CAAC,aAAa,CAAC;QACzC,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,KAAK,CAAC;IACrC,CAAC;IACD;;;;;;;;OAQG,CACH,YACI,mCAAA,EAAqC,CAC9B,QAAkB,EACzB,8CAAA,EAAgD,CACzC,IAA4B,EACnC,mBAAA,EAAqB,CACrB,KAAY,EACZ,uHAAA,EAAyH,CAClH,0BAAmC,IAAI,EAC9C,8DAAA,EAAgE,CACzD,mBAA2B,CAAC,EACnC,2CAAA,EAA6C,CACtC,UAA2C,CAAA,CAAE,CAAA;QAV7C,IAAA,CAAA,QAAQ,GAAR,QAAQ,CAAU;QAElB,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAwB;QAI5B,IAAA,CAAA,uBAAuB,GAAvB,uBAAuB,CAAgB;QAEvC,IAAA,CAAA,gBAAgB,GAAhB,gBAAgB,CAAY;QAE5B,IAAA,CAAA,OAAO,GAAP,OAAO,CAAsC;QAlFxD,uDAAA,EAAyD,CAClD,IAAA,CAAA,KAAK,iKAAW,SAAM,CAAC,KAAK,EAAE,CAAC;QAEtC,0DAAA,EAA4D,CACpD,IAAA,CAAA,WAAW,GAAG,IAAI,KAAK,EAAkB,CAAC;QAKlD,2BAAA,EAA6B,CACrB,IAAA,CAAA,UAAU,GAAwB,IAAI,CAAC;QAE/C,kCAAA,EAAoC,CAC5B,IAAA,CAAA,UAAU,GAAG,IAAI,CAAC;QAK1B,sCAAA,EAAwC,CAChC,IAAA,CAAA,IAAI,GAA8B,IAAI,CAAC;QAiE3C,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QAEpB,UAAU;QACV,OAAO,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,IAAI,IAAI,CAAC;QAC1D,OAAO,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,IAAI,KAAK,CAAC;QACrD,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,cAAc,CAAC,aAAa,CAAC;QAC1E,OAAO,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,IAAI,CAAA,CAAE,CAAC;QACtD,OAAO,CAAC,cAAc,CAAC,OAAO,GAAG,OAAO,CAAC,cAAc,CAAC,OAAO,IAAI,KAAK,CAAC;QACzE,OAAO,CAAC,cAAc,CAAC,aAAa,GAAG,OAAO,CAAC,cAAc,CAAC,aAAa,IAAI,KAAK,CAAC;QACrF,OAAO,CAAC,cAAc,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC,cAAc,IAAI,IAAI,CAAC;QACtF,OAAO,CAAC,cAAc,CAAC,eAAe,GAAG,OAAO,CAAC,cAAc,CAAC,eAAe,IAAI,CAAC,CAAC;QACrF,OAAO,CAAC,cAAc,CAAC,YAAY,GAAG,OAAO,CAAC,cAAc,CAAC,YAAY,IAAI,KAAK,CAAC;QACnF,OAAO,CAAC,cAAc,CAAC,gBAAgB,GAAG,OAAO,CAAC,cAAc,CAAC,gBAAgB,IAAI,KAAK,CAAC;QAC3F,OAAO,CAAC,cAAc,CAAC,aAAa,GAAG,OAAO,CAAC,cAAc,CAAC,aAAa,IAAI,KAAK,CAAC;QACrF,OAAO,CAAC,cAAc,CAAC,aAAa,GAAG,OAAO,CAAC,cAAc,CAAC,aAAa,IAAI,KAAK,CAAC;QACrF,OAAO,CAAC,wBAAwB,GAAG,OAAO,CAAC,wBAAwB,IAAI,IAAI,CAAC;QAC5E,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC;QAElD,IAAI,CAAC,YAAY,GAAG,IAAI,GAAG,EAAE,CAAC;QAE9B,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YACvB,MAAM,sBAAsB,GAAG,IAAI,EAAE,eAAe,0JAAC,eAAY,CAAC,mBAAmB,CAAC,CAAC;YACvF,MAAM,sBAAsB,GAAG,IAAI,EAAE,eAAe,0JAAC,eAAY,CAAC,mBAAmB,CAAC,CAAC;YAEvF,IAAI,sBAAsB,IAAI,sBAAsB,EAAE,CAAC;gBACnD,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,sBAAsB,CAAC,MAAM,EAAE,EAAE,CAAC,CAAE,CAAC;oBACrD,MAAM,KAAK,GAAG,sBAAsB,CAAC,CAAC,CAAC,EACnC,MAAM,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC;oBACvC,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC;wBACf,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;oBACjC,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;QAED,wBAAA,EAA0B,CAC1B,IAAI,CAAC,aAAa,GAAG,6KAAI,uBAAoB,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAClE,IAAI,CAAC,aAAa,CAAC,qBAAqB,GAAG,KAAK,CAAC;QACjD,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,wBAAwB,GAAG,IAAI,CAAC;QAErE,IAAI,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,CAAC;QAChD,IAAI,WAAW,GAAG,cAAc,CAAC,wBAAwB,EAAE,CAAC;YACxD,WAAW,GAAG,cAAc,CAAC,aAAa,CAAC;QAC/C,CAAC;QACD,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,kBAAkB;QAClB,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,IAAI,CAAC,QAAQ,EAAE,CAAC;IACpB,CAAC;IAED,kDAAA,EAAoD,CAC5C,QAAQ,GAAA;QACZ,OAAQ,IAAI,CAAC,WAAW,EAAE,CAAC;YACvB,KAAK,cAAc,CAAC,aAAa,CAAC;gBAAC,CAAC;oBAChC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAG,EAAE;wBACrD,IAAI,CAAC,mBAAmB,EAAE,CAAC;oBAC/B,CAAC,CAAC,CAAC;oBACH,MAAM;gBACV,CAAC;QACL,CAAC;IACL,CAAC;IAED,yFAAA,EAA2F,CACpF,MAAM,GAAA;QACT,OAAQ,IAAI,CAAC,WAAW,EAAE,CAAC;YACvB,KAAK,cAAc,CAAC,aAAa,CAAC;gBAAC,CAAC;oBAChC,IAAI,CAAC,mBAAmB,EAAE,CAAC;oBAC3B,MAAM;gBACV,CAAC;YACD,KAAK,cAAc,CAAC,eAAe,CAAC;gBAAC,CAAC;oBAClC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;oBACjC,MAAM;gBACV,CAAC;YACD,KAAK,cAAc,CAAC,wBAAwB,CAAC;gBAAC,CAAC;oBAC3C,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;oBAClC,MAAM;gBACV,CAAC;QACL,CAAC;QAED,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAED,+DAAA,EAAiE,CACjE,IAAW,SAAS,CAAC,KAAc,EAAA;QAC/B,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAAE,CAAC;YAC3B,OAAO;QACX,CAAC;QAED,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QAExB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACrC,CAAC;QAED,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACtB,IAAI,CAAC,QAAQ,EAAE,CAAC;QACpB,CAAC,MAAM,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YAC7B,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACrB,CAAC;IACL,CAAC;IAED,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAEO,gBAAgB,CAAC,QAAiB,EAAE,IAAU,EAAE,OAAe,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAA;QACxF,MAAM,IAAI,GAAG,4KAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAClC,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QACpC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;QAErC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YAChC,MAAM,KAAK,kKAAG,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;2KACnC,SAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAC5B,KAAK,CAAC,wBAAwB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACxC,KAAK,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACpC,CAAC;QAED,IAAI,UAAU,EAAE,CAAC;YACb,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,iBAAiB,EAAE,EAAE,IAAI,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAElC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACxB,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACxB,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5B,CAAC;IAEO,2BAA2B,CAAC,KAAa,EAAE,IAA4B,EAAA;QAC3E,MAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC;QAEzB,IAAI,MAAM,CAAC;QACX,IAAI,OAAO,CAAC;QACZ,IAAI,IAAI,EAAE,CAAC;YACP,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YAC/B,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC5B,CAAC,MAAM,CAAC;YACJ,MAAM,GAAG,mKAAI,SAAM,EAAE,CAAC;YACtB,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;QAChC,CAAC;QACD,IAAI,GAAG,GAAG,CAAC,CAAC;QACZ,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,CAAE,CAAC;YAC3B,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACtB,IAAI,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;YAEnC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC,IAAI,AAAC,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAE,CAAC;gBAC/F,SAAS;YACb,CAAC;YACD,IAAI,CAAC,MAAM,EAAE,CAAC;gBACV,MAAM,GAAG;mLAAC,UAAO,CAAC,IAAI,EAAE;mLAAE,UAAO,CAAC,IAAI,EAAE;iBAAC,CAAC;gBAC1C,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC;YACnC,CAAC;YACD,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;YAC/C,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YAClE,MAAM,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YACnC,MAAM,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YACnC,GAAG,EAAE,CAAC;QACV,CAAC;IACL,CAAC;IAEO,yBAAyB,CAAC,KAAa,EAAA;QAC3C,MAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC;QACzB,IAAI,OAAO,GAAG,CAAC,CAAC;QAEhB,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,IAAI,aAAa,CAAC;QAClB,IAAI,OAAO,CAAC;QACZ,IAAI,IAAI,EAAE,CAAC;YACP,aAAa,GAAG,IAAI,CAAC;YACrB,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC5B,CAAC,MAAM,CAAC;YACJ,aAAa,GAAG,IAAI,+KAAa,CAAC,EAAE,CAAC,CAAC;YACtC,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;QAChC,CAAC;QACD,IAAK,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;YAChC,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAC3B,MAAM,UAAU,GAAG,SAAS,CAAC,SAAS,EAAE,CAAC;YACzC,IAAI,CAAC,UAAU,IAAI,AAAC,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAE,CAAC;gBAC7F,SAAS;YACb,CAAC;YACD,IAAI,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YACvC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACV,MAAM,GAAG;mLAAC,UAAO,CAAC,IAAI,EAAE;mLAAE,UAAO,CAAC,IAAI,EAAE;iBAAC,CAAC;gBAC1C,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC;YACvC,CAAC;YACD,SAAS,CAAC,wBAAwB,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7D,UAAU,CAAC,wBAAwB,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9D,MAAM,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YACnC,MAAM,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YACnC,OAAO,EAAE,CAAC;QACd,CAAC;QACD,IAAI,CAAC,IAAI,EAAE,CAAC;YACR,aAAa,CAAC,OAAO,EAAE,CAAC;QAC5B,CAAC;IACL,CAAC;IAED;;;OAGG,CACK,OAAO,CAAC,cAAuB,EAAA;QACnC,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;YAC/B,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,cAAc,CAAC;YAC9C,IAAI,CAAC,YAAa,CAAC,iBAAiB,CAAC,iBAAiB,GAAG,cAAc,CAAC;QAC5E,CAAC;IACL,CAAC;IAED;;;;OAIG,CACK,yBAAyB,CAAC,IAAoB,EAAE,MAAc,EAAA;QAClE,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE,CAAC;YACtC,MAAM,CAAC,QAAQ,gKAAC,SAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;YACnC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,CAAC,CAAC;QACzD,IAAI,CAAC,aAAa,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACnD,OAAO;IACX,CAAC;IAEO,WAAW,CAAC,WAAoB,EAAE,IAAU,EAAE,UAAmB,EAAE,SAAyB,EAAE,cAA6C,EAAE,iBAAwB,EAAA;QACzK,MAAM,GAAG,GAAG,UAAU,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QAC7C,MAAM,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC;QACvB,MAAM,EAAE,GAAG,GAAG,CAAC,SAAS,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAEpC,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,IAAI,KAAK,CAAC;QAChD,MAAM,aAAa,GAAG,cAAc,CAAC,aAAa,IAAI,KAAK,CAAC;QAE5D,MAAM,GAAG,GAAG,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAE9B,MAAM,IAAI,iLAAG,qBAAA,AAAkB,EAC3B,gBAAgB,EAChB;YACI,KAAK,EAAE;gBAAC,mKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;gBAAE,mKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBAAE,mKAAI,UAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBAAE,mKAAI,UAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;gBAAE,mKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;aAAC;YAC1H,IAAI,EAAE;+KAAC,UAAO,CAAC,IAAI,EAAE;gBAAE,GAAG;gBAAE,EAAE;aAAC;YAC/B,aAAa,EAAE,CAAC,CAAS,EAAE,EAAE;gBACzB,OAAQ,CAAC,EAAE,CAAC;oBACR,KAAK,CAAC,CAAC;oBACP,KAAK,CAAC;wBACF,OAAO,CAAC,CAAC;oBACb,KAAK,CAAC;wBACF,OAAO,CAAC,GAAG,aAAa,CAAC;gBACjC,CAAC;gBACD,OAAO,CAAC,CAAC;YACb,CAAC;YACD,eAAe,wJAAE,OAAI,CAAC,WAAW;YACjC,SAAS,EAAE,KAAK;SACnB,EACD,iBAAiB,CACpB,CAAC;QAEF,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC5C,MAAM,GAAG,GAAa,EAAE,EACpB,GAAG,GAAa,EAAE,CAAC;QAEvB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,CAAE,CAAC;YACnC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAErB,mEAAmE;YACnE,uCAAuC;YACvC,IAAI,SAAS,IAAI,cAAc,CAAC,gBAAgB,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;gBACxD,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC5C,CAAC,MAAM,CAAC;gBACJ,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACvC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC,KAAK,EAAE,CAAC;QAEpC,IAAI,CAAC,eAAe,0JAAC,eAAY,CAAC,mBAAmB,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;QACnE,IAAI,CAAC,eAAe,CAAC,wKAAY,CAAC,mBAAmB,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;QACnE,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAE/B,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,yBAAyB,CAAC,SAAiB,EAAA;QAC/C,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACb,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,0JAAC,eAAY,CAAC,YAAY,CAAC,CAAC;QACvE,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;QACvC,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,0JAAC,eAAY,CAAC,mBAAmB,CAAC,CAAC;QAChF,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,wKAAY,CAAC,mBAAmB,CAAC,CAAC;QAChF,IAAI,CAAC,SAAS,IAAI,CAAC,OAAO,IAAI,CAAC,WAAW,IAAI,CAAC,WAAW,EAAE,CAAC;YACzD,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,GAAG,GAAG,mKAAI,UAAO,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;QAC9E,MAAM,GAAG,GAAG,mKAAI,UAAO,CAAC,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAEjF,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAE,CAAC;YACtC,MAAM,WAAW,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAE/B,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAE,CAAC;gBACzB,MAAM,MAAM,GAAG,WAAW,CAAC,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;gBAChD,MAAM,OAAO,GAAG,WAAW,CAAC,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;gBAEjD,IAAI,MAAM,KAAK,SAAS,IAAI,OAAO,GAAG,IAAI,EAAE,CAAC;oBACzC,yKAAO,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,GAAG,CAAC,iKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC1E,GAAG,CAAC,eAAe,gKAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC3C,GAAG,CAAC,eAAe,CAAC,4KAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC3C,KAAK,EAAE,CAAC;oBACR,MAAM;gBACV,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,KAAK,GAAG,CAAC,GACV;YACI,MAAM,iKAAE,UAAO,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC;YAChC,MAAM,iKAAE,UAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC;SACzC,GACD,IAAI,CAAC;IACf,CAAC;IAED;;;OAGG,CACK,qBAAqB,CAAC,WAAW,GAAG,IAAI,EAAA;QAC5C,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;YAC1B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACvB,CAAC;QAED,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,MAAM,iBAAiB,GAAG,IAAI,CAAC,YAAY,EAAE,iBAA0B,CAAC;QACxE,MAAM,KAAK,GAAW,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;QAC1C,MAAM,OAAO,GAAwB,EAAE,CAAC;QACxC,MAAM,KAAK,GAAW,EAAE,CAAC;QAEzB,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC;QAEpD,IAAI,CAAC;YACD,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;gBAC/B,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,KAAK,CAAC;gBACrC,iBAAiB,CAAC,iBAAiB,GAAG,KAAK,CAAC;YAChD,CAAC;YAED,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;gBAC5B,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC;YACjC,CAAC;YAED,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBAC/B,IAAI,CAAC,QAAQ,CAAC,uBAAuB,EAAE,CAAC;YAC5C,CAAC;YAED,IAAI,iBAAiB,GAAG,MAAM,CAAC,iBAAiB,CAAC;YACjD,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,CAAA,CAAE,CAAC;YAEzD,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;gBACpC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBAEtB,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC,IAAI,AAAC,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAE,CAAC;oBAC/F,SAAS;gBACb,CAAC;gBAED,MAAM,6BAA6B,GAAG,mKAAI,SAAM,EAAE,CAAC;gBACnD,IAAI,CAAC,yBAAyB,CAAC,IAAI,EAAE,6BAA6B,CAAC,CAAC;gBAEpE,MAAM,WAAW,GAAG,mKAAI,UAAO,EAAE,CAAC;gBAElC,6BAA6B,CAAC,SAAS,CAAC,SAAS,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;gBAE3E,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC3B,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAE,CAAC;wBAC7B,MAAM,8BAA8B,GAAW,kKAAI,UAAM,EAAE,CAAC;wBAC5D,EAAE,CAAC,cAAc,EAAE,CAAC,aAAa,CAAC,6BAA6B,EAAE,8BAA8B,CAAC,CAAC;wBACjG,MAAM,UAAU,GAAG,mKAAI,UAAO,EAAE,CAAC;wBACjC,8BAA8B,CAAC,SAAS,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;wBAC3E,MAAM,kBAAkB,GAAG,yKAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;wBACrE,IAAI,kBAAkB,GAAG,iBAAiB,EAAE,CAAC;4BACzC,iBAAiB,GAAG,kBAAkB,CAAC;wBAC3C,CAAC;wBACD,IAAI,WAAW,EAAE,CAAC;4BACd,OAAO;wBACX,CAAC;wBAED,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,IAAI,EAAE,UAAU,EAAE,EAAE,EAAE,cAAc,EAAE,iBAAiB,CAAC,CAAC,CAAC;oBACvG,CAAC;gBACL,CAAC,MAAM,CAAC;oBACJ,MAAM,cAAc,GAAG,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;oBACvE,IAAI,cAAc,EAAE,CAAC;wBACjB,IAAI,cAAc,CAAC,MAAM,GAAG,iBAAiB,EAAE,CAAC;4BAC5C,iBAAiB,GAAG,cAAc,CAAC,MAAM,CAAC;wBAC9C,CAAC;wBACD,IAAI,CAAC,WAAW,EAAE,CAAC;4BACf,IAAI,UAAU,CAAC;4BACf,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;4BACpC,IAAI,UAAU,EAAE,CAAC;gCACb,IAAI,CAAC,yBAAyB,CAAC,UAAU,EAAE,6BAA6B,CAAC,CAAC;gCAC1E,6BAA6B,CAAC,SAAS,CAAC,SAAS,EAAE,SAAS,iKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;gCACrF,UAAU,GAAG,WAAW,CAAC,QAAQ,gKAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;4BACvH,CAAC,MAAM,CAAC;gCACJ,UAAU,GAAG,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,SAAS,EAAE,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;4BACvH,CAAC;4BACD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,cAAc,EAAE,iBAAiB,CAAC,CAAC,CAAC;wBACzG,CAAC;oBACL,CAAC;gBACL,CAAC;gBAED,MAAM,cAAc,GAAG,cAAc,CAAC,cAAc,IAAI,GAAG,CAAC;gBAE5D,MAAM,MAAM,GAAG,8LAAA,AAAY,EACvB,gBAAgB,EAChB;oBACI,QAAQ,EAAE,CAAC;oBACX,QAAQ,EAAE,cAAc;oBACxB,SAAS,EAAE,IAAI;iBAClB,EACD,iBAAiB,CACpB,CAAC;gBAEF,MAAM,WAAW,GAAG,MAAM,CAAC,gBAAgB,EAAE,CAAC;gBAE9C,MAAM,GAAG,GAAa,EAAE,EACpB,GAAG,GAAa,EAAE,CAAC;gBAEvB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,CAAE,CAAC;oBACnC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;oBACrB,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBACvC,CAAC;gBAED,MAAM,CAAC,eAAe,0JAAC,eAAY,CAAC,mBAAmB,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;gBACrE,MAAM,CAAC,eAAe,0JAAC,eAAY,CAAC,mBAAmB,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;gBAErE,MAAM,CAAC,QAAQ,GAAG,WAAW,CAAC,KAAK,EAAE,CAAC;gBACtC,OAAO,CAAC,IAAI,CAAC;oBAAC,MAAM;oBAAE,IAAI;iBAAC,CAAC,CAAC;YACjC,CAAC;YAED,MAAM,eAAe,GAAG,cAAc,CAAC,eAAe,IAAI,CAAC,CAAC;YAC5D,MAAM,YAAY,GAAG,cAAc,CAAC,YAAY,IAAI,IAAI,CAAC;YAEzD,MAAM,MAAM,GAAG,EAAE,CAAC;YAClB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;gBACtC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;gBAClC,MAAM,KAAK,GAAG,CAAC,GAAG,CAAC,eAAe,GAAG,iBAAiB,CAAC,CAAC;gBAExD,IAAI,SAAS,GAAG,CAAC,CAAC;gBAClB,IAAI,EAAE,GAAG,IAAI,CAAC;gBAEd,MAAO,EAAE,CAAC,SAAS,EAAE,IAAK,EAAE,CAAC,SAAS,EAAW,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAE,CAAC;oBAClE,SAAS,EAAE,CAAC;oBACZ,EAAE,GAAG,EAAE,CAAC,SAAS,EAAU,CAAC;gBAChC,CAAC;gBACD,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC,CAAC;gBACvE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACxB,CAAC;YAED,IAAI,CAAC,SAAS,yJAAG,OAAI,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YACpE,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjB,IAAI,CAAC,SAAS,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;gBACxD,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;gBACxC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC;gBAClC,IAAI,CAAC,SAAS,CAAC,wBAAwB,GAAG,IAAI,CAAC,OAAO,CAAC,wBAAwB,IAAI,IAAI,CAAC;gBACxF,IAAI,CAAC,SAAS,CAAC,wBAAwB,GAAG,IAAI,CAAC;YACnD,CAAC;YAED,MAAM,KAAK,GAAG,IAAI,CAAC,YAAa,CAAC,oBAAoB,EAAE,CAAC;YACxD,KAAK,CAAC,SAAS,GAAG,GAAG,CAAC;YAEtB,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YAC7B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QACtB,CAAC,CAAC,OAAO,GAAG,EAAE,CAAC;kKACX,SAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAClB,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YAC7B,IAAI,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC;IACL,CAAC;IAEO,eAAe,GAAA;QACnB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QAC9B,CAAC;QAED,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAEvB,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,CAAA,CAAE,CAAC;QAEzD,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,CAAC;YAChC,OAAO;QACX,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,aAAc,CAAC,iBAAiB,CAAC;QAC1D,MAAM,IAAI,GAAG,cAAc,CAAC,aAAa,IAAI,KAAK,CAAC;QACnD,MAAM,KAAK,GAAG,EAAE,CAAC;QACjB,MAAM,MAAM,GAAG,EAAE,CAAC;QAClB,MAAM,GAAG,GAAG,kKAAI,SAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACnC,MAAM,KAAK,GAAG,kKAAI,SAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACrC,MAAM,IAAI,GAAG,kKAAI,SAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEpC,MAAM,GAAG,GAAa,EAAE,CAAC;QACzB,MAAM,GAAG,GAAa,EAAE,CAAC;QACzB,MAAM,YAAY,GAAG,CAAC,CAAC;QAEvB,IAAK,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAE,CAAC;YAClC,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAEpC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC,IAAI,AAAC,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAE,CAAC;gBAC/F,SAAS;YACb,CAAC;YAED,MAAM,6BAA6B,GAAG,IAAI,wKAAM,EAAE,CAAC;YACnD,MAAM,UAAU,GAAG,mKAAI,UAAO,EAAE,CAAC;YAEjC,IAAI,CAAC,yBAAyB,CAAC,IAAI,EAAE,6BAA6B,CAAC,CAAC;YACpE,6BAA6B,CAAC,SAAS,CAAC,SAAS,EAAE,4KAAU,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;YAEzF,MAAM,CAAC,GAAG,IAAI,wKAAM,EAAE,CAAC;YACvB,4KAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;YAE7C,MAAM,SAAS,kKAAG,UAAO,CAAC,oBAAoB,CAAC,mKAAI,UAAO,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC/E,MAAM,SAAS,kKAAG,UAAO,CAAC,oBAAoB,CAAC,mKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC/E,MAAM,SAAS,kKAAG,UAAO,CAAC,oBAAoB,CAAC,IAAI,yKAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;YAE/E,MAAM,KAAK,GAAG;gBAAC,UAAU;gBAAE,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC;aAAC,CAAC;YACtD,MAAM,KAAK,GAAG;gBAAC,UAAU;gBAAE,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC;aAAC,CAAC;YACtD,MAAM,KAAK,GAAG;gBAAC,UAAU;gBAAE,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC;aAAC,CAAC;YAEtD,MAAM,UAAU,GAAG;gBAAC,KAAK;gBAAE,KAAK;gBAAE,KAAK;aAAC,CAAC;YACzC,MAAM,UAAU,GAAG;gBACf;oBAAC,GAAG;oBAAE,GAAG;iBAAC;gBACV;oBAAC,KAAK;oBAAE,KAAK;iBAAC;gBACd;oBAAC,IAAI;oBAAE,IAAI;iBAAC;aACf,CAAC;YAEF,KAAK,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,CAAC;YAC1B,MAAM,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,CAAC;YAE3B,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,CAAE,CAAC;gBACpC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBACrB,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACvC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,UAAU,OAAG,6LAAA,AAAgB,EAAC,WAAW,EAAE;YAAE,KAAK,EAAE,KAAK;YAAE,MAAM,EAAE,MAAM;YAAE,SAAS,EAAE,IAAI;QAAA,CAAE,EAAE,WAAW,CAAC,CAAC;QAChH,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,wKAAY,CAAC,mBAAmB,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;QAC9E,IAAI,CAAC,UAAU,CAAC,eAAe,0JAAC,eAAY,CAAC,mBAAmB,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;QAC9E,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QACzC,IAAI,CAAC,UAAU,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;QAC7D,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC;QACnC,IAAI,CAAC,UAAU,CAAC,wBAAwB,GAAG,IAAI,CAAC,OAAO,CAAC,wBAAwB,IAAI,IAAI,CAAC;IAC7F,CAAC;IAED,2FAAA,EAA6F,CACrF,mBAAmB,GAAA;QACvB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtB,OAAO;QACX,CAAC;QAED,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC/B,IAAI,CAAC,QAAQ,CAAC,uBAAuB,EAAE,CAAC;QAC5C,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YAC9C,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACxD,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QACrE,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC;QAEzD,IAAI,WAAW,EAAE,CAAC;YACd,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;gBACnB,IAAI,CAAC,UAAU,iLAAG,mBAAA,AAAgB,EAAC,EAAE,EAAE;oBAAE,KAAK,EAAE,IAAI,CAAC,WAAW;oBAAE,SAAS,EAAE,IAAI;oBAAE,QAAQ,EAAE,IAAI;gBAAA,CAAE,EAAE,WAAW,CAAC,CAAC;gBAClH,IAAI,CAAC,UAAU,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;YAC7D,CAAC,MAAM,CAAC;8LACJ,mBAAA,AAAgB,EAAC,EAAE,EAAE;oBAAE,KAAK,EAAE,IAAI,CAAC,WAAW;oBAAE,SAAS,EAAE,IAAI;oBAAE,QAAQ,EAAE,IAAI,CAAC,UAAU;gBAAA,CAAE,EAAE,WAAW,CAAC,CAAC;YAC/G,CAAC;YACD,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;gBACZ,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC1D,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;YACvE,CAAC;YACD,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACvC,CAAC;IACL,CAAC;IACD;;OAEG,CACI,iBAAiB,CAAC,IAAY,EAAA;QACjC,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QACjD,IAAI,IAAI,CAAC,WAAW,KAAK,IAAI,EAAE,CAAC;YAC5B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YACvB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;gBAC1B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;gBACvB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;YACvB,CAAC;YACD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YAExB,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC;QAChC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;OAcG,CACI,oBAAoB,CAAC,MAAc,EAAE,KAAuB,EAAA;QAC/D,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QAChD,IAAI,CAAC,OAAO,CAAC,cAAsB,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;QACrD,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;YAC1B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACvB,CAAC;QACD,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC;IAChC,CAAC;IAED,iCAAA,EAAmC,CAC5B,OAAO,GAAA;QACV,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;YAC1B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAC3B,CAAC;QAED,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;YAC7B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC9B,CAAC;QAED,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACvB,CAAC;;AA7/BD,kEAAA,EAAoE,CAC7C,eAAA,aAAa,GAAG,CAAC,AAAJ,CAAK;AACzC,oEAAA,EAAsE,CAC/C,eAAA,eAAe,GAAG,CAAC,AAAJ,CAAK;AAC3C,6EAAA,EAA+E,CACxD,eAAA,wBAAwB,GAAG,CAAC,AAAJ,CAAK", "debugId": null}}, {"offset": {"line": 2518, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Debug/ISkeletonViewer.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Debug/ISkeletonViewer.ts"], "sourcesContent": ["import type { Skeleton } from \"../Bones/skeleton\";\r\nimport type { Color3 } from \"../Maths/math.color\";\r\n\r\n/**\r\n * Defines the options associated with the creation of a SkeletonViewer.\r\n */\r\nexport interface ISkeletonViewerOptions {\r\n    /** Should the system pause animations before building the Viewer? */\r\n    pauseAnimations: boolean;\r\n\r\n    /** Should the system return the skeleton to rest before building? */\r\n    returnToRest: boolean;\r\n\r\n    /** public Display Mode of the Viewer */\r\n    displayMode: number;\r\n\r\n    /** Flag to toggle if the Viewer should use the CPU for animations or not? */\r\n    displayOptions: ISkeletonViewerDisplayOptions;\r\n\r\n    /** Flag to toggle if the Viewer should use the CPU for animations or not? */\r\n    computeBonesUsingShaders: boolean;\r\n\r\n    /** Flag ignore non weighted bones */\r\n    useAllBones: boolean;\r\n}\r\n\r\n/**\r\n * Defines how to display the various bone meshes for the viewer.\r\n */\r\nexport interface ISkeletonViewerDisplayOptions {\r\n    /** How far down to start tapering the bone spurs */\r\n    midStep?: number;\r\n\r\n    /** How big is the midStep? */\r\n    midStepFactor?: number;\r\n\r\n    /** Base for the Sphere Size */\r\n    sphereBaseSize?: number;\r\n\r\n    /** The ratio of the sphere to the longest bone in units */\r\n    sphereScaleUnit?: number;\r\n\r\n    /** Ratio for the Sphere Size */\r\n    sphereFactor?: number;\r\n\r\n    /** Whether a spur should attach its far end to the child bone position */\r\n    spurFollowsChild?: boolean;\r\n\r\n    /** Whether to show local axes or not  */\r\n    showLocalAxes?: boolean;\r\n\r\n    /** Length of each local axis */\r\n    localAxesSize?: number;\r\n}\r\n\r\n/**\r\n * Defines the constructor options for the BoneWeight Shader.\r\n */\r\nexport interface IBoneWeightShaderOptions {\r\n    /** Skeleton to Map */\r\n    skeleton: Skeleton;\r\n\r\n    /** Colors for Uninfluenced bones */\r\n    colorBase?: Color3;\r\n\r\n    /** Colors for 0.0-0.25 Weight bones */\r\n    colorZero?: Color3;\r\n\r\n    /** Color for 0.25-0.5 Weight Influence */\r\n    colorQuarter?: Color3;\r\n\r\n    /** Color for 0.5-0.75 Weight Influence */\r\n    colorHalf?: Color3;\r\n\r\n    /** Color for 0.75-1 Weight Influence */\r\n    colorFull?: Color3;\r\n\r\n    /** Color for Zero Weight Influence */\r\n    targetBoneIndex?: number;\r\n}\r\n\r\n/**\r\n * Simple structure of the gradient steps for the Color Map.\r\n */\r\nexport interface ISkeletonMapShaderColorMapKnot {\r\n    /** Color of the Knot */\r\n    color: Color3;\r\n    /** Location of the Knot */\r\n    location: number;\r\n}\r\n\r\n/**\r\n * Defines the constructor options for the SkeletonMap Shader.\r\n */\r\nexport interface ISkeletonMapShaderOptions {\r\n    /** Skeleton to Map */\r\n    skeleton: Skeleton;\r\n    /** Array of ColorMapKnots that make the gradient must be ordered with knot[i].location < knot[i+1].location*/\r\n    colorMap?: ISkeletonMapShaderColorMapKnot[];\r\n}\r\n"], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2525, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Debug/directionalLightFrustumViewer.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Debug/directionalLightFrustumViewer.ts"], "sourcesContent": ["import type { Nullable } from \"core/types\";\r\nimport type { Camera } from \"../Cameras/camera\";\r\nimport type { DirectionalLight } from \"../Lights/directionalLight\";\r\nimport { StandardMaterial } from \"../Materials/standardMaterial\";\r\nimport { Color3 } from \"../Maths/math.color\";\r\nimport { Matrix, TmpVectors, Vector3 } from \"../Maths/math.vector\";\r\nimport { CreateLines } from \"../Meshes/Builders/linesBuilder\";\r\nimport type { LinesMesh } from \"../Meshes/linesMesh\";\r\nimport { Mesh } from \"../Meshes/mesh\";\r\nimport { VertexData } from \"../Meshes/mesh.vertexData\";\r\nimport { TransformNode } from \"../Meshes/transformNode\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Constants } from \"core/Engines/constants\";\r\nimport type { FrameGraph } from \"core/FrameGraph/frameGraph\";\r\nimport { FrameGraphUtils } from \"core/FrameGraph/frameGraphUtils\";\r\n\r\n/**\r\n * Class used to render a debug view of the frustum for a directional light\r\n * @see https://playground.babylonjs.com/#7EFGSG#4\r\n * @since 5.0.0\r\n */\r\nexport class DirectionalLightFrustumViewer {\r\n    private _scene: Scene;\r\n    private _light: DirectionalLight;\r\n    private _camera: Nullable<Camera>;\r\n    private _inverseViewMatrix: Matrix;\r\n    private _visible: boolean;\r\n\r\n    private _rootNode: TransformNode;\r\n    private _lightHelperFrustumMeshes: Mesh[];\r\n\r\n    private _nearLinesPoints: Vector3[];\r\n    private _farLinesPoints: Vector3[];\r\n    private _trLinesPoints: Vector3[];\r\n    private _brLinesPoints: Vector3[];\r\n    private _tlLinesPoints: Vector3[];\r\n    private _blLinesPoints: Vector3[];\r\n\r\n    private _nearPlaneVertices: number[];\r\n    private _farPlaneVertices: number[];\r\n    private _rightPlaneVertices: number[];\r\n    private _leftPlaneVertices: number[];\r\n    private _topPlaneVertices: number[];\r\n    private _bottomPlaneVertices: number[];\r\n\r\n    private _oldPosition: Vector3 = new Vector3(Number.NaN, Number.NaN, Number.NaN);\r\n    private _oldDirection: Vector3 = new Vector3(Number.NaN, Number.NaN, Number.NaN);\r\n    private _oldAutoCalc: boolean;\r\n    private _oldMinZ: number;\r\n    private _oldMaxZ: number;\r\n    private _oldOrthoLeft: number;\r\n    private _oldOrthoRight: number;\r\n    private _oldOrthoTop: number;\r\n    private _oldOrthoBottom: number;\r\n\r\n    private _transparency = 0.3;\r\n    /**\r\n     * Gets or sets the transparency of the frustum planes\r\n     */\r\n    public get transparency(): number {\r\n        return this._transparency;\r\n    }\r\n\r\n    public set transparency(alpha: number) {\r\n        this._transparency = alpha;\r\n        for (let i = 6; i < 12; ++i) {\r\n            this._lightHelperFrustumMeshes[i].material!.alpha = alpha;\r\n        }\r\n    }\r\n\r\n    private _showLines = true;\r\n    /**\r\n     * true to display the edges of the frustum\r\n     */\r\n    public get showLines(): boolean {\r\n        return this._showLines;\r\n    }\r\n\r\n    public set showLines(show: boolean) {\r\n        if (this._showLines === show) {\r\n            return;\r\n        }\r\n        this._showLines = show;\r\n        for (let i = 0; i < 6; ++i) {\r\n            this._lightHelperFrustumMeshes[i].setEnabled(show);\r\n        }\r\n    }\r\n\r\n    private _showPlanes = true;\r\n    /**\r\n     * true to display the planes of the frustum\r\n     */\r\n    public get showPlanes(): boolean {\r\n        return this._showPlanes;\r\n    }\r\n\r\n    public set showPlanes(show: boolean) {\r\n        if (this._showPlanes === show) {\r\n            return;\r\n        }\r\n        this._showPlanes = show;\r\n        for (let i = 6; i < 12; ++i) {\r\n            this._lightHelperFrustumMeshes[i].setEnabled(show);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Creates a new frustum viewer\r\n     * @param light directional light to display the frustum for\r\n     * @param camera camera used to retrieve the minZ / maxZ values if the shadowMinZ/shadowMaxZ values of the light are not setup\r\n     */\r\n    constructor(light: DirectionalLight, camera: Nullable<Camera> = null) {\r\n        this._scene = light.getScene();\r\n        this._light = light;\r\n        this._camera = camera;\r\n        this._inverseViewMatrix = Matrix.Identity();\r\n        this._lightHelperFrustumMeshes = [];\r\n        this._createGeometry();\r\n        this.show();\r\n        this.update();\r\n    }\r\n\r\n    /**\r\n     * Shows the frustum\r\n     */\r\n    public show() {\r\n        if (this._scene.frameGraph) {\r\n            this._removeMeshesFromFrameGraph(this._scene.frameGraph);\r\n            this._addMeshesToFrameGraph(this._scene.frameGraph);\r\n        }\r\n        this._lightHelperFrustumMeshes.forEach((mesh, index) => {\r\n            mesh.setEnabled((index < 6 && this._showLines) || (index >= 6 && this._showPlanes));\r\n        });\r\n        this._oldPosition.set(Number.NaN, Number.NaN, Number.NaN);\r\n        this._visible = true;\r\n    }\r\n\r\n    /**\r\n     * Hides the frustum\r\n     */\r\n    public hide() {\r\n        if (this._scene.frameGraph) {\r\n            this._removeMeshesFromFrameGraph(this._scene.frameGraph);\r\n        }\r\n        for (const mesh of this._lightHelperFrustumMeshes) {\r\n            mesh.setEnabled(false);\r\n        }\r\n        this._visible = false;\r\n    }\r\n\r\n    private _addMeshesToFrameGraph(frameGraph: FrameGraph) {\r\n        const objectRenderer = FrameGraphUtils.FindMainObjectRenderer(frameGraph);\r\n        if (objectRenderer && objectRenderer.objectList.meshes) {\r\n            for (const mesh of this._lightHelperFrustumMeshes) {\r\n                objectRenderer.objectList.meshes.push(mesh);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _removeMeshesFromFrameGraph(frameGraph: FrameGraph) {\r\n        const objectRenderer = FrameGraphUtils.FindMainObjectRenderer(frameGraph);\r\n        if (objectRenderer && objectRenderer.objectList.meshes) {\r\n            for (const mesh of this._lightHelperFrustumMeshes) {\r\n                const index = objectRenderer.objectList.meshes!.indexOf(mesh);\r\n                if (index !== -1) {\r\n                    objectRenderer.objectList.meshes.splice(index, 1);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Updates the frustum.\r\n     * Call this method to update the frustum view if the light has changed position/direction\r\n     */\r\n    public update() {\r\n        if (!this._visible) {\r\n            return;\r\n        }\r\n\r\n        if (\r\n            this._oldPosition.equals(this._light.position) &&\r\n            this._oldDirection.equals(this._light.direction) &&\r\n            this._oldAutoCalc === this._light.autoCalcShadowZBounds &&\r\n            this._oldMinZ === this._light.shadowMinZ &&\r\n            this._oldMaxZ === this._light.shadowMaxZ &&\r\n            this._oldOrthoLeft === this._light.orthoLeft &&\r\n            this._oldOrthoRight === this._light.orthoRight &&\r\n            this._oldOrthoTop === this._light.orthoTop &&\r\n            this._oldOrthoBottom === this._light.orthoBottom\r\n        ) {\r\n            return;\r\n        }\r\n\r\n        this._oldPosition.copyFrom(this._light.position);\r\n        this._oldDirection.copyFrom(this._light.direction);\r\n        this._oldAutoCalc = this._light.autoCalcShadowZBounds;\r\n        this._oldMinZ = this._light.shadowMinZ;\r\n        this._oldMaxZ = this._light.shadowMaxZ;\r\n        this._oldOrthoLeft = this._light.orthoLeft;\r\n        this._oldOrthoRight = this._light.orthoRight;\r\n        this._oldOrthoTop = this._light.orthoTop;\r\n        this._oldOrthoBottom = this._light.orthoBottom;\r\n\r\n        TmpVectors.Vector3[0].set(\r\n            this._light.orthoLeft,\r\n            this._light.orthoBottom,\r\n            this._light.shadowMinZ !== undefined ? this._light.shadowMinZ : (this._camera?.minZ ?? Constants.ShadowMinZ)\r\n        ); // min light extents\r\n        TmpVectors.Vector3[1].set(\r\n            this._light.orthoRight,\r\n            this._light.orthoTop,\r\n            this._light.shadowMaxZ !== undefined ? this._light.shadowMaxZ : (this._camera?.maxZ ?? Constants.ShadowMaxZ)\r\n        ); // max light extents\r\n\r\n        const invLightView = this._getInvertViewMatrix();\r\n\r\n        TmpVectors.Vector3[2].copyFromFloats(TmpVectors.Vector3[1].x, TmpVectors.Vector3[1].y, TmpVectors.Vector3[0].z); // n1\r\n        TmpVectors.Vector3[3].copyFromFloats(TmpVectors.Vector3[1].x, TmpVectors.Vector3[0].y, TmpVectors.Vector3[0].z); // n2\r\n        TmpVectors.Vector3[4].copyFromFloats(TmpVectors.Vector3[0].x, TmpVectors.Vector3[0].y, TmpVectors.Vector3[0].z); // n3\r\n        TmpVectors.Vector3[5].copyFromFloats(TmpVectors.Vector3[0].x, TmpVectors.Vector3[1].y, TmpVectors.Vector3[0].z); // n4\r\n\r\n        Vector3.TransformCoordinatesToRef(TmpVectors.Vector3[2], invLightView, TmpVectors.Vector3[2]); // near1\r\n        Vector3.TransformCoordinatesToRef(TmpVectors.Vector3[3], invLightView, TmpVectors.Vector3[3]); // near2\r\n        Vector3.TransformCoordinatesToRef(TmpVectors.Vector3[4], invLightView, TmpVectors.Vector3[4]); // near3\r\n        Vector3.TransformCoordinatesToRef(TmpVectors.Vector3[5], invLightView, TmpVectors.Vector3[5]); // near4\r\n\r\n        TmpVectors.Vector3[6].copyFromFloats(TmpVectors.Vector3[1].x, TmpVectors.Vector3[1].y, TmpVectors.Vector3[1].z); // f1\r\n        TmpVectors.Vector3[7].copyFromFloats(TmpVectors.Vector3[1].x, TmpVectors.Vector3[0].y, TmpVectors.Vector3[1].z); // f2\r\n        TmpVectors.Vector3[8].copyFromFloats(TmpVectors.Vector3[0].x, TmpVectors.Vector3[0].y, TmpVectors.Vector3[1].z); // f3\r\n        TmpVectors.Vector3[9].copyFromFloats(TmpVectors.Vector3[0].x, TmpVectors.Vector3[1].y, TmpVectors.Vector3[1].z); // f4\r\n\r\n        Vector3.TransformCoordinatesToRef(TmpVectors.Vector3[6], invLightView, TmpVectors.Vector3[6]); // far1\r\n        Vector3.TransformCoordinatesToRef(TmpVectors.Vector3[7], invLightView, TmpVectors.Vector3[7]); // far2\r\n        Vector3.TransformCoordinatesToRef(TmpVectors.Vector3[8], invLightView, TmpVectors.Vector3[8]); // far3\r\n        Vector3.TransformCoordinatesToRef(TmpVectors.Vector3[9], invLightView, TmpVectors.Vector3[9]); // far4\r\n\r\n        CreateLines(\"nearlines\", { updatable: true, points: this._nearLinesPoints, instance: this._lightHelperFrustumMeshes[0] as LinesMesh }, this._scene);\r\n\r\n        CreateLines(\"farlines\", { updatable: true, points: this._farLinesPoints, instance: this._lightHelperFrustumMeshes[1] as LinesMesh }, this._scene);\r\n\r\n        CreateLines(\"trlines\", { updatable: true, points: this._trLinesPoints, instance: this._lightHelperFrustumMeshes[2] as LinesMesh }, this._scene);\r\n\r\n        CreateLines(\"brlines\", { updatable: true, points: this._brLinesPoints, instance: this._lightHelperFrustumMeshes[3] as LinesMesh }, this._scene);\r\n\r\n        CreateLines(\"tllines\", { updatable: true, points: this._tlLinesPoints, instance: this._lightHelperFrustumMeshes[4] as LinesMesh }, this._scene);\r\n\r\n        CreateLines(\"bllines\", { updatable: true, points: this._blLinesPoints, instance: this._lightHelperFrustumMeshes[5] as LinesMesh }, this._scene);\r\n\r\n        TmpVectors.Vector3[2].toArray(this._nearPlaneVertices, 0);\r\n        TmpVectors.Vector3[3].toArray(this._nearPlaneVertices, 3);\r\n        TmpVectors.Vector3[4].toArray(this._nearPlaneVertices, 6);\r\n        TmpVectors.Vector3[5].toArray(this._nearPlaneVertices, 9);\r\n        this._lightHelperFrustumMeshes[6].geometry?.updateVerticesDataDirectly(\"position\", this._nearPlaneVertices, 0);\r\n\r\n        TmpVectors.Vector3[6].toArray(this._farPlaneVertices, 0);\r\n        TmpVectors.Vector3[7].toArray(this._farPlaneVertices, 3);\r\n        TmpVectors.Vector3[8].toArray(this._farPlaneVertices, 6);\r\n        TmpVectors.Vector3[9].toArray(this._farPlaneVertices, 9);\r\n        this._lightHelperFrustumMeshes[7].geometry?.updateVerticesDataDirectly(\"position\", this._farPlaneVertices, 0);\r\n\r\n        TmpVectors.Vector3[2].toArray(this._rightPlaneVertices, 0);\r\n        TmpVectors.Vector3[6].toArray(this._rightPlaneVertices, 3);\r\n        TmpVectors.Vector3[7].toArray(this._rightPlaneVertices, 6);\r\n        TmpVectors.Vector3[3].toArray(this._rightPlaneVertices, 9);\r\n        this._lightHelperFrustumMeshes[8].geometry?.updateVerticesDataDirectly(\"position\", this._rightPlaneVertices, 0);\r\n\r\n        TmpVectors.Vector3[5].toArray(this._leftPlaneVertices, 0);\r\n        TmpVectors.Vector3[9].toArray(this._leftPlaneVertices, 3);\r\n        TmpVectors.Vector3[8].toArray(this._leftPlaneVertices, 6);\r\n        TmpVectors.Vector3[4].toArray(this._leftPlaneVertices, 9);\r\n        this._lightHelperFrustumMeshes[9].geometry?.updateVerticesDataDirectly(\"position\", this._leftPlaneVertices, 0);\r\n\r\n        TmpVectors.Vector3[2].toArray(this._topPlaneVertices, 0);\r\n        TmpVectors.Vector3[6].toArray(this._topPlaneVertices, 3);\r\n        TmpVectors.Vector3[9].toArray(this._topPlaneVertices, 6);\r\n        TmpVectors.Vector3[5].toArray(this._topPlaneVertices, 9);\r\n        this._lightHelperFrustumMeshes[10].geometry?.updateVerticesDataDirectly(\"position\", this._topPlaneVertices, 0);\r\n\r\n        TmpVectors.Vector3[3].toArray(this._bottomPlaneVertices, 0);\r\n        TmpVectors.Vector3[7].toArray(this._bottomPlaneVertices, 3);\r\n        TmpVectors.Vector3[8].toArray(this._bottomPlaneVertices, 6);\r\n        TmpVectors.Vector3[4].toArray(this._bottomPlaneVertices, 9);\r\n        this._lightHelperFrustumMeshes[11].geometry?.updateVerticesDataDirectly(\"position\", this._bottomPlaneVertices, 0);\r\n    }\r\n\r\n    /**\r\n     * Dispose of the class / remove the frustum view\r\n     */\r\n    public dispose() {\r\n        if (this._scene.frameGraph) {\r\n            this._removeMeshesFromFrameGraph(this._scene.frameGraph);\r\n        }\r\n        for (const mesh of this._lightHelperFrustumMeshes) {\r\n            mesh.material?.dispose();\r\n            mesh.dispose();\r\n        }\r\n        this._rootNode.dispose();\r\n    }\r\n\r\n    protected _createGeometry() {\r\n        this._rootNode = new TransformNode(\"directionalLightHelperRoot_\" + this._light.name, this._scene);\r\n        this._rootNode.parent = this._light.parent;\r\n\r\n        this._nearLinesPoints = [TmpVectors.Vector3[0], TmpVectors.Vector3[1], TmpVectors.Vector3[2], TmpVectors.Vector3[3], TmpVectors.Vector3[4]];\r\n        const nearLines = CreateLines(\"nearlines\", { updatable: true, points: this._nearLinesPoints }, this._scene);\r\n        nearLines.parent = this._rootNode;\r\n        nearLines.alwaysSelectAsActiveMesh = true;\r\n\r\n        this._farLinesPoints = [TmpVectors.Vector3[5], TmpVectors.Vector3[6], TmpVectors.Vector3[7], TmpVectors.Vector3[8], TmpVectors.Vector3[9]];\r\n        const farLines = CreateLines(\"farlines\", { updatable: true, points: this._farLinesPoints }, this._scene);\r\n        farLines.parent = this._rootNode;\r\n        farLines.alwaysSelectAsActiveMesh = true;\r\n\r\n        this._trLinesPoints = [TmpVectors.Vector3[10], TmpVectors.Vector3[11]];\r\n        const trLines = CreateLines(\"trlines\", { updatable: true, points: this._trLinesPoints }, this._scene);\r\n        trLines.parent = this._rootNode;\r\n        trLines.alwaysSelectAsActiveMesh = true;\r\n\r\n        this._brLinesPoints = [TmpVectors.Vector3[12], TmpVectors.Vector3[0]];\r\n        const brLines = CreateLines(\"brlines\", { updatable: true, points: this._brLinesPoints }, this._scene);\r\n        brLines.parent = this._rootNode;\r\n        brLines.alwaysSelectAsActiveMesh = true;\r\n\r\n        this._tlLinesPoints = [TmpVectors.Vector3[1], TmpVectors.Vector3[2]];\r\n        const tlLines = CreateLines(\"tllines\", { updatable: true, points: this._tlLinesPoints }, this._scene);\r\n        tlLines.parent = this._rootNode;\r\n        tlLines.alwaysSelectAsActiveMesh = true;\r\n\r\n        this._blLinesPoints = [TmpVectors.Vector3[3], TmpVectors.Vector3[4]];\r\n        const blLines = CreateLines(\"bllines\", { updatable: true, points: this._blLinesPoints }, this._scene);\r\n        blLines.parent = this._rootNode;\r\n        blLines.alwaysSelectAsActiveMesh = true;\r\n\r\n        this._lightHelperFrustumMeshes.push(nearLines, farLines, trLines, brLines, tlLines, blLines);\r\n\r\n        const makePlane = (name: string, color: Color3, positions: number[]) => {\r\n            const plane = new Mesh(name + \"plane\", this._scene);\r\n            const mat = new StandardMaterial(name + \"PlaneMat\", this._scene);\r\n\r\n            plane.material = mat;\r\n            plane.parent = this._rootNode;\r\n            plane.alwaysSelectAsActiveMesh = true;\r\n\r\n            mat.emissiveColor = color;\r\n            mat.alpha = this.transparency;\r\n            mat.backFaceCulling = false;\r\n            mat.disableLighting = true;\r\n\r\n            const indices = [0, 1, 2, 0, 2, 3];\r\n\r\n            const vertexData = new VertexData();\r\n\r\n            vertexData.positions = positions;\r\n            vertexData.indices = indices;\r\n\r\n            vertexData.applyToMesh(plane, true);\r\n\r\n            this._lightHelperFrustumMeshes.push(plane);\r\n        };\r\n\r\n        this._nearPlaneVertices = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];\r\n        this._farPlaneVertices = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];\r\n        this._rightPlaneVertices = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];\r\n        this._leftPlaneVertices = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];\r\n        this._topPlaneVertices = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];\r\n        this._bottomPlaneVertices = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];\r\n\r\n        makePlane(\"near\", new Color3(1, 0, 0), this._nearPlaneVertices);\r\n        makePlane(\"far\", new Color3(0.3, 0, 0), this._farPlaneVertices);\r\n        makePlane(\"right\", new Color3(0, 1, 0), this._rightPlaneVertices);\r\n        makePlane(\"left\", new Color3(0, 0.3, 0), this._leftPlaneVertices);\r\n        makePlane(\"top\", new Color3(0, 0, 1), this._topPlaneVertices);\r\n        makePlane(\"bottom\", new Color3(0, 0, 0.3), this._bottomPlaneVertices);\r\n\r\n        this._nearLinesPoints[0] = TmpVectors.Vector3[2];\r\n        this._nearLinesPoints[1] = TmpVectors.Vector3[3];\r\n        this._nearLinesPoints[2] = TmpVectors.Vector3[4];\r\n        this._nearLinesPoints[3] = TmpVectors.Vector3[5];\r\n        this._nearLinesPoints[4] = TmpVectors.Vector3[2];\r\n\r\n        this._farLinesPoints[0] = TmpVectors.Vector3[6];\r\n        this._farLinesPoints[1] = TmpVectors.Vector3[7];\r\n        this._farLinesPoints[2] = TmpVectors.Vector3[8];\r\n        this._farLinesPoints[3] = TmpVectors.Vector3[9];\r\n        this._farLinesPoints[4] = TmpVectors.Vector3[6];\r\n\r\n        this._trLinesPoints[0] = TmpVectors.Vector3[2];\r\n        this._trLinesPoints[1] = TmpVectors.Vector3[6];\r\n\r\n        this._brLinesPoints[0] = TmpVectors.Vector3[3];\r\n        this._brLinesPoints[1] = TmpVectors.Vector3[7];\r\n\r\n        this._tlLinesPoints[0] = TmpVectors.Vector3[4];\r\n        this._tlLinesPoints[1] = TmpVectors.Vector3[8];\r\n\r\n        this._blLinesPoints[0] = TmpVectors.Vector3[5];\r\n        this._blLinesPoints[1] = TmpVectors.Vector3[9];\r\n    }\r\n\r\n    protected _getInvertViewMatrix(): Matrix {\r\n        Matrix.LookAtLHToRef(this._light.position, this._light.position.add(this._light.direction), Vector3.UpReadOnly, this._inverseViewMatrix);\r\n        this._inverseViewMatrix.invertToRef(this._inverseViewMatrix);\r\n        return this._inverseViewMatrix;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AAGA,OAAO,EAAE,gBAAgB,EAAE,MAAM,+BAA+B,CAAC;AACjE,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AACnE,OAAO,EAAE,WAAW,EAAE,MAAM,iCAAiC,CAAC;AAE9D,OAAO,EAAE,IAAI,EAAE,MAAM,gBAAgB,CAAC;AACtC,OAAO,EAAE,UAAU,EAAE,MAAM,2BAA2B,CAAC;AACvD,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AAIxD,OAAO,EAAE,eAAe,EAAE,yCAAwC;;;;;;;;;AAO5D,MAAO,6BAA6B;IAmCtC;;OAEG,CACH,IAAW,YAAY,GAAA;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED,IAAW,YAAY,CAAC,KAAa,EAAA;QACjC,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC3B,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,CAAE,CAAC;YAC1B,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,QAAS,CAAC,KAAK,GAAG,KAAK,CAAC;QAC9D,CAAC;IACL,CAAC;IAGD;;OAEG,CACH,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED,IAAW,SAAS,CAAC,IAAa,EAAA;QAC9B,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE,CAAC;YAC3B,OAAO;QACX,CAAC;QACD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAE,CAAC;YACzB,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACvD,CAAC;IACL,CAAC;IAGD;;OAEG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,IAAW,UAAU,CAAC,IAAa,EAAA;QAC/B,IAAI,IAAI,CAAC,WAAW,KAAK,IAAI,EAAE,CAAC;YAC5B,OAAO;QACX,CAAC;QACD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,CAAE,CAAC;YAC1B,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACvD,CAAC;IACL,CAAC;IAED;;;;OAIG,CACH,YAAY,KAAuB,EAAE,SAA2B,IAAI,CAAA;QAlE5D,IAAA,CAAA,YAAY,GAAY,mKAAI,UAAO,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;QACxE,IAAA,CAAA,aAAa,GAAY,mKAAI,UAAO,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;QASzE,IAAA,CAAA,aAAa,GAAG,GAAG,CAAC;QAepB,IAAA,CAAA,UAAU,GAAG,IAAI,CAAC;QAkBlB,IAAA,CAAA,WAAW,GAAG,IAAI,CAAC;QAwBvB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;QAC/B,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,kBAAkB,kKAAG,SAAM,CAAC,QAAQ,EAAE,CAAC;QAC5C,IAAI,CAAC,yBAAyB,GAAG,EAAE,CAAC;QACpC,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,IAAI,CAAC,MAAM,EAAE,CAAC;IAClB,CAAC;IAED;;OAEG,CACI,IAAI,GAAA;QACP,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YACzB,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACzD,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QACxD,CAAC;QACD,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YACnD,IAAI,CAAC,UAAU,CAAC,AAAC,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,GAAK,CAAD,IAAM,IAAI,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;QACxF,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;QAC1D,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACzB,CAAC;IAED;;OAEG,CACI,IAAI,GAAA;QACP,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YACzB,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAC7D,CAAC;QACD,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,yBAAyB,CAAE,CAAC;YAChD,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAC3B,CAAC;QACD,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;IAC1B,CAAC;IAEO,sBAAsB,CAAC,UAAsB,EAAA;QACjD,MAAM,cAAc,wKAAG,kBAAe,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;QAC1E,IAAI,cAAc,IAAI,cAAc,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;YACrD,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,yBAAyB,CAAE,CAAC;gBAChD,cAAc,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChD,CAAC;QACL,CAAC;IACL,CAAC;IAEO,2BAA2B,CAAC,UAAsB,EAAA;QACtD,MAAM,cAAc,wKAAG,kBAAe,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;QAC1E,IAAI,cAAc,IAAI,cAAc,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;YACrD,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,yBAAyB,CAAE,CAAC;gBAChD,MAAM,KAAK,GAAG,cAAc,CAAC,UAAU,CAAC,MAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBAC9D,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;oBACf,cAAc,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBACtD,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,MAAM,GAAA;QACT,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACjB,OAAO;QACX,CAAC;QAED,IACI,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAC9C,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAChD,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,MAAM,CAAC,qBAAqB,IACvD,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,MAAM,CAAC,UAAU,IACxC,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,MAAM,CAAC,UAAU,IACxC,IAAI,CAAC,aAAa,KAAK,IAAI,CAAC,MAAM,CAAC,SAAS,IAC5C,IAAI,CAAC,cAAc,KAAK,IAAI,CAAC,MAAM,CAAC,UAAU,IAC9C,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,MAAM,CAAC,QAAQ,IAC1C,IAAI,CAAC,eAAe,KAAK,IAAI,CAAC,MAAM,CAAC,WAAW,EAClD,CAAC;YACC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACjD,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACnD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC;QACtD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;QACvC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;QACvC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;QAC3C,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;QAC7C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;QACzC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;uKAE/C,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CACrB,IAAI,CAAC,MAAM,CAAC,SAAS,EACrB,IAAI,CAAC,MAAM,CAAC,WAAW,EACvB,IAAI,CAAC,MAAM,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,AAAC,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI,IAAA,IAAS,CAAC,UAAU,CAAC,CAC/G,CAAC,CAAC,CAAA,mBAAoB;uKACvB,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CACrB,IAAI,CAAC,MAAM,CAAC,UAAU,EACtB,IAAI,CAAC,MAAM,CAAC,QAAQ,EACpB,IAAI,CAAC,MAAM,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,AAAC,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI,QAAS,CAAC,UAAU,CAAC,CAC/G,CAAC,CAAC,KAAA,eAAoB;QAEvB,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;uKAEjD,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,gKAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,iKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,iKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK;sKACtH,cAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,gKAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,iKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,iKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK;uKACtH,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,gKAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,iKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,gKAAE,cAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK;uKACtH,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,gKAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,iKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,iKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK;uKAEtH,UAAO,CAAC,yBAAyB,gKAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,YAAY,iKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ;uKACvG,UAAO,CAAC,yBAAyB,CAAC,4KAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,YAAY,iKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ;uKACvG,UAAO,CAAC,yBAAyB,gKAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,YAAY,iKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ;uKACvG,UAAO,CAAC,yBAAyB,gKAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,YAAY,iKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ;uKAEvG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,+JAAC,cAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,iKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,iKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK;uKACtH,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,gKAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,iKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,iKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK;uKACtH,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,+JAAC,cAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,iKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,iKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK;uKACtH,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,gKAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,iKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,iKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK;QAEtH,yKAAO,CAAC,yBAAyB,gKAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,YAAY,iKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO;uKACtG,UAAO,CAAC,yBAAyB,gKAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,YAAY,iKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO;uKACtG,UAAO,CAAC,yBAAyB,CAAC,4KAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,YAAY,iKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO;QACtG,yKAAO,CAAC,yBAAyB,gKAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,YAAY,iKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO;QAEtG,4LAAA,AAAW,EAAC,WAAW,EAAE;YAAE,SAAS,EAAE,IAAI;YAAE,MAAM,EAAE,IAAI,CAAC,gBAAgB;YAAE,QAAQ,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAc;QAAA,CAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;sLAEpJ,cAAA,AAAW,EAAC,UAAU,EAAE;YAAE,SAAS,EAAE,IAAI;YAAE,MAAM,EAAE,IAAI,CAAC,eAAe;YAAE,QAAQ,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAc;QAAA,CAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;sLAElJ,cAAA,AAAW,EAAC,SAAS,EAAE;YAAE,SAAS,EAAE,IAAI;YAAE,MAAM,EAAE,IAAI,CAAC,cAAc;YAAE,QAAQ,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAc;QAAA,CAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAEhJ,4LAAA,AAAW,EAAC,SAAS,EAAE;YAAE,SAAS,EAAE,IAAI;YAAE,MAAM,EAAE,IAAI,CAAC,cAAc;YAAE,QAAQ,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAc;QAAA,CAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;SAEhJ,2LAAA,AAAW,EAAC,SAAS,EAAE;YAAE,SAAS,EAAE,IAAI;YAAE,MAAM,EAAE,IAAI,CAAC,cAAc;YAAE,QAAQ,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAc;QAAA,CAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAEhJ,4LAAA,AAAW,EAAC,SAAS,EAAE;YAAE,SAAS,EAAE,IAAI;YAAE,MAAM,EAAE,IAAI,CAAC,cAAc;YAAE,QAAQ,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAc;QAAA,CAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;uKAEhJ,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;QAC1D,4KAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;uKAC1D,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;uKAC1D,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;QAC1D,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,0BAA0B,CAAC,UAAU,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;QAE/G,4KAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;uKACzD,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;uKACzD,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;sKACzD,cAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;QACzD,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,0BAA0B,CAAC,UAAU,EAAE,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;uKAE9G,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC;uKAC3D,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC;uKAC3D,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC;uKAC3D,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC;QAC3D,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,0BAA0B,CAAC,UAAU,EAAE,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC;uKAEhH,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;QAC1D,4KAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;uKAC1D,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;uKAC1D,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;QAC1D,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,0BAA0B,CAAC,UAAU,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;uKAE/G,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;QACzD,4KAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;uKACzD,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;uKACzD,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;QACzD,IAAI,CAAC,yBAAyB,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,0BAA0B,CAAC,UAAU,EAAE,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;uKAE/G,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;uKAC5D,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;uKAC5D,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;uKAC5D,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;QAC5D,IAAI,CAAC,yBAAyB,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,0BAA0B,CAAC,UAAU,EAAE,IAAI,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;IACtH,CAAC;IAED;;OAEG,CACI,OAAO,GAAA;QACV,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YACzB,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAC7D,CAAC;QACD,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,yBAAyB,CAAE,CAAC;YAChD,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC;YACzB,IAAI,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;IAES,eAAe,GAAA;QACrB,IAAI,CAAC,SAAS,GAAG,mKAAI,gBAAa,CAAC,6BAA6B,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAClG,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QAE3C,IAAI,CAAC,gBAAgB,GAAG;2KAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC;2KAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC;2KAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC;2KAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC;0KAAE,cAAU,CAAC,OAAO,CAAC,CAAC,CAAC;SAAC,CAAC;QAC5I,MAAM,SAAS,iLAAG,cAAA,AAAW,EAAC,WAAW,EAAE;YAAE,SAAS,EAAE,IAAI;YAAE,MAAM,EAAE,IAAI,CAAC,gBAAgB;QAAA,CAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAC5G,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC;QAClC,SAAS,CAAC,wBAAwB,GAAG,IAAI,CAAC;QAE1C,IAAI,CAAC,eAAe,GAAG;YAAC,4KAAU,CAAC,OAAO,CAAC,CAAC,CAAC;2KAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC;2KAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC;2KAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC;2KAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC;SAAC,CAAC;QAC3I,MAAM,QAAQ,iLAAG,cAAA,AAAW,EAAC,UAAU,EAAE;YAAE,SAAS,EAAE,IAAI;YAAE,MAAM,EAAE,IAAI,CAAC,eAAe;QAAA,CAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QACzG,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC;QACjC,QAAQ,CAAC,wBAAwB,GAAG,IAAI,CAAC;QAEzC,IAAI,CAAC,cAAc,GAAG;2KAAC,aAAU,CAAC,OAAO,CAAC,EAAE,CAAC;2KAAE,aAAU,CAAC,OAAO,CAAC,EAAE,CAAC;SAAC,CAAC;QACvE,MAAM,OAAO,iLAAG,cAAA,AAAW,EAAC,SAAS,EAAE;YAAE,SAAS,EAAE,IAAI;YAAE,MAAM,EAAE,IAAI,CAAC,cAAc;QAAA,CAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QACtG,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC;QAChC,OAAO,CAAC,wBAAwB,GAAG,IAAI,CAAC;QAExC,IAAI,CAAC,cAAc,GAAG;2KAAC,aAAU,CAAC,OAAO,CAAC,EAAE,CAAC;2KAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC;SAAC,CAAC;QACtE,MAAM,OAAO,OAAG,wLAAA,AAAW,EAAC,SAAS,EAAE;YAAE,SAAS,EAAE,IAAI;YAAE,MAAM,EAAE,IAAI,CAAC,cAAc;QAAA,CAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QACtG,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC;QAChC,OAAO,CAAC,wBAAwB,GAAG,IAAI,CAAC;QAExC,IAAI,CAAC,cAAc,GAAG;2KAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC;2KAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC;SAAC,CAAC;QACrE,MAAM,OAAO,GAAG,4LAAA,AAAW,EAAC,SAAS,EAAE;YAAE,SAAS,EAAE,IAAI;YAAE,MAAM,EAAE,IAAI,CAAC,cAAc;QAAA,CAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QACtG,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC;QAChC,OAAO,CAAC,wBAAwB,GAAG,IAAI,CAAC;QAExC,IAAI,CAAC,cAAc,GAAG;2KAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC;2KAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC;SAAC,CAAC;QACrE,MAAM,OAAO,iLAAG,cAAA,AAAW,EAAC,SAAS,EAAE;YAAE,SAAS,EAAE,IAAI;YAAE,MAAM,EAAE,IAAI,CAAC,cAAc;QAAA,CAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QACtG,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC;QAChC,OAAO,CAAC,wBAAwB,GAAG,IAAI,CAAC;QAExC,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;QAE7F,MAAM,SAAS,GAAG,CAAC,IAAY,EAAE,KAAa,EAAE,SAAmB,EAAE,EAAE;YACnE,MAAM,KAAK,GAAG,IAAI,6JAAI,CAAC,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YACpD,MAAM,GAAG,GAAG,yKAAI,mBAAgB,CAAC,IAAI,GAAG,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YAEjE,KAAK,CAAC,QAAQ,GAAG,GAAG,CAAC;YACrB,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC;YAC9B,KAAK,CAAC,wBAAwB,GAAG,IAAI,CAAC;YAEtC,GAAG,CAAC,aAAa,GAAG,KAAK,CAAC;YAC1B,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC;YAC9B,GAAG,CAAC,eAAe,GAAG,KAAK,CAAC;YAC5B,GAAG,CAAC,eAAe,GAAG,IAAI,CAAC;YAE3B,MAAM,OAAO,GAAG;gBAAC,CAAC;gBAAE,CAAC;gBAAE,CAAC;gBAAE,CAAC;gBAAE,CAAC;gBAAE,CAAC;aAAC,CAAC;YAEnC,MAAM,UAAU,GAAG,wKAAI,aAAU,EAAE,CAAC;YAEpC,UAAU,CAAC,SAAS,GAAG,SAAS,CAAC;YACjC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;YAE7B,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YAEpC,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/C,CAAC,CAAC;QAEF,IAAI,CAAC,kBAAkB,GAAG;YAAC,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;SAAC,CAAC;QAC/D,IAAI,CAAC,iBAAiB,GAAG;YAAC,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;SAAC,CAAC;QAC9D,IAAI,CAAC,mBAAmB,GAAG;YAAC,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;SAAC,CAAC;QAChE,IAAI,CAAC,kBAAkB,GAAG;YAAC,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;SAAC,CAAC;QAC/D,IAAI,CAAC,iBAAiB,GAAG;YAAC,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;SAAC,CAAC;QAC9D,IAAI,CAAC,oBAAoB,GAAG;YAAC,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;SAAC,CAAC;QAEjE,SAAS,CAAC,MAAM,EAAE,iKAAI,UAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAChE,SAAS,CAAC,KAAK,EAAE,kKAAI,SAAM,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAChE,SAAS,CAAC,OAAO,EAAE,kKAAI,SAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAClE,SAAS,CAAC,MAAM,EAAE,kKAAI,SAAM,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAClE,SAAS,CAAC,KAAK,EAAE,kKAAI,SAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC9D,SAAS,CAAC,QAAQ,EAAE,kKAAI,SAAM,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAEtE,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,GAAG,4KAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,iKAAG,cAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAEjD,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAChD,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAChD,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAChD,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAChD,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAEhD,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC/C,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAE/C,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC/C,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAE/C,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC/C,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAE/C,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC/C,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IACnD,CAAC;IAES,oBAAoB,GAAA;QAC1B,wKAAM,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,iKAAE,UAAO,CAAC,UAAU,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACzI,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC7D,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;CACJ", "debugId": null}}, {"offset": {"line": 2970, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Debug/index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Debug/index.ts"], "sourcesContent": ["export * from \"./axesViewer\";\r\nexport * from \"./boneAxesViewer\";\r\nexport * from \"./debugLayer\";\r\nexport * from \"./physicsViewer\";\r\nexport * from \"./rayHelper\";\r\nexport * from \"./skeletonViewer\";\r\nexport * from \"./ISkeletonViewer\";\r\nexport * from \"./directionalLightFrustumViewer\";\r\n"], "names": [], "mappings": ";AAAA,cAAc,cAAc,CAAC;AAC7B,cAAc,kBAAkB,CAAC;AACjC,cAAc,cAAc,CAAC;AAC7B,cAAc,iBAAiB,CAAC;AAChC,cAAc,aAAa,CAAC;AAC5B,cAAc,kBAAkB,CAAC;AACjC,cAAc,mBAAmB,CAAC;AAClC,cAAc,iCAAiC,CAAC", "debugId": null}}]}