{"version": 3, "file": "flyCameraMouseInput.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Cameras/Inputs/flyCameraMouseInput.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAC;AAGlD,OAAO,EAAE,gBAAgB,EAAE,MAAM,mCAAmC,CAAC;AAGrE,OAAO,EAAE,iBAAiB,EAAE,MAAM,4BAA4B,CAAC;AAE/D,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AACrD,OAAO,EAAE,IAAI,EAAE,MAAM,uBAAuB,CAAC;AAC7C,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AAEzC;;;GAGG;AACH,MAAM,OAAO,mBAAmB;IAqD5B;;;OAGG;IACH;QA9CA;;WAEG;QAEI,YAAO,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE3B;;WAEG;QACI,eAAU,GAAa,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEzC;;WAEG;QACI,iBAAY,GAAa,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE3C;;WAEG;QACI,gBAAW,GAAa,CAAC,CAAC,CAAC,CAAC;QAEnC;;;;;;WAMG;QACI,iBAAY,GAAW,CAAC,CAAC,CAAC;QAEjC;;;WAGG;QAEI,uBAAkB,GAAG,MAAM,CAAC;QAI3B,sBAAiB,GAAuC,IAAI,CAAC;IAOtD,CAAC;IAEhB;;;OAGG;IACI,aAAa,CAAC,gBAA0B;QAC3C,gBAAgB,GAAG,KAAK,CAAC,gCAAgC,CAAC,SAAS,CAAC,CAAC;QACrE,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;QAE1C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,aAAa,CAAC,yBAAyB,CAC3E,CAAC,CAAM,EAAE,EAAE;YACP,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,EACD,iBAAiB,CAAC,WAAW,GAAG,iBAAiB,CAAC,SAAS,GAAG,iBAAiB,CAAC,WAAW,CAC9F,CAAC;QAEF,oCAAoC;QACpC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC1E,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;gBAC1B,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;YACrD,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,aAAa;QAChB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,aAAa,CAAC,4BAA4B,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAElF,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,wBAAwB,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAE3E,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;YAC9B,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC;QACvC,CAAC;IACL,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,qBAAqB,CAAC;IACjC,CAAC;IAED;;;OAGG;IACI,aAAa;QAChB,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,wDAAwD;IAChD,aAAa,CAAC,CAAM;QACxB,MAAM,CAAC,GAAkB,CAAC,CAAC,KAAK,CAAC;QAEjC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;QAElC,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC,WAAW,KAAK,OAAO,EAAE,CAAC;YAClD,OAAO;QACX,CAAC;QAED,yDAAyD;QACzD,IAAI,CAAC,CAAC,IAAI,KAAK,iBAAiB,CAAC,WAAW,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YACpF,OAAO;QACX,CAAC;QAED,MAAM,UAAU,GAAgB,CAAC,CAAC,MAAM,CAAC;QAEzC,cAAc;QACd,IAAI,CAAC,CAAC,IAAI,KAAK,iBAAiB,CAAC,WAAW,EAAE,CAAC;YAC3C,IAAI,CAAC;gBACD,UAAU,EAAE,iBAAiB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;YAC/C,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACT,qDAAqD;YACzD,CAAC;YAED,IAAI,CAAC,iBAAiB,GAAG;gBACrB,CAAC,EAAE,CAAC,CAAC,OAAO;gBACZ,CAAC,EAAE,CAAC,CAAC,OAAO;aACf,CAAC;YAEF,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,MAAM,CAAC;YAE7B,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAC1B,CAAC,CAAC,cAAc,EAAE,CAAC;YACvB,CAAC;YAED,wDAAwD;YACxD,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;gBACvB,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;YAC/B,CAAC;QACL,CAAC;QACD,YAAY;aACP,IAAI,CAAC,CAAC,IAAI,KAAK,iBAAiB,CAAC,SAAS,EAAE,CAAC;YAC9C,IAAI,CAAC;gBACD,UAAU,EAAE,qBAAqB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;YACnD,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACT,qDAAqD;YACzD,CAAC;YAED,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;YAEvB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;YAC9B,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAC1B,CAAC,CAAC,cAAc,EAAE,CAAC;YACvB,CAAC;QACL,CAAC;QACD,cAAc;aACT,IAAI,CAAC,CAAC,IAAI,KAAK,iBAAiB,CAAC,WAAW,EAAE,CAAC;YAChD,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAC1B,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;oBACvB,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;gBAC/B,CAAC;gBAED,OAAO;YACX,CAAC;YAED,MAAM,OAAO,GAAG,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;YACrD,MAAM,OAAO,GAAG,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;YAErD,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAErC,IAAI,CAAC,iBAAiB,GAAG;gBACrB,CAAC,EAAE,CAAC,CAAC,OAAO;gBACZ,CAAC,EAAE,CAAC,CAAC,OAAO;aACf,CAAC;YAEF,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAC1B,CAAC,CAAC,cAAc,EAAE,CAAC;YACvB,CAAC;QACL,CAAC;IACL,CAAC;IAED,gDAAgD;IACxC,YAAY,CAAC,CAAM;QACvB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;QAElC,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;YACxB,OAAO;QACX,CAAC;QAED,MAAM,OAAO,GAAG,CAAC,CAAC,SAAS,CAAC;QAC5B,MAAM,OAAO,GAAG,CAAC,CAAC,SAAS,CAAC;QAE5B,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAErC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAE9B,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC1B,CAAC,CAAC,cAAc,EAAE,CAAC;QACvB,CAAC;IACL,CAAC;IAED;;;;OAIG;IACK,aAAa,CAAC,OAAe,EAAE,OAAe;QAClD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,oBAAoB,GAAG,MAAM,CAAC,8BAA8B,EAAE,CAAC;QAErE,OAAO,IAAI,oBAAoB,CAAC;QAEhC,MAAM,CAAC,GAAG,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAC5C,MAAM,CAAC,GAAG,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAE5C,kCAAkC;QAClC,MAAM,eAAe,GAAG,UAAU,CAAC,oBAAoB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACjH,IAAI,cAA0B,CAAC;QAE/B,SAAS;QACT,IACI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE;YACzB,OAAO,CAAC,KAAK,IAAI,CAAC,YAAY,CAAC;QACnC,CAAC,CAAC,EACJ,CAAC;YACC,2CAA2C;YAC3C,cAAc,GAAG,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACpD,6BAA6B;YAC7B,eAAe,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;QACpD,CAAC;QAED,OAAO;QACP,IACI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE;YACvB,OAAO,CAAC,KAAK,IAAI,CAAC,YAAY,CAAC;QACnC,CAAC,CAAC,EACJ,CAAC;YACC,2CAA2C;YAC3C,cAAc,GAAG,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACpD,2BAA2B;YAC3B,eAAe,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;YAEhD,6DAA6D;YAC7D,MAAM,KAAK,GAAG,MAAM,CAAC,eAAe,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,oCAAoC;YAC9F,IAAI,MAAM,CAAC,UAAU,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,EAAE,CAAC;gBAC/E,MAAM,YAAY,GAAG,MAAM,CAAC,oBAAoB,GAAG,CAAC,CAAC,CAAC;gBACtD,2CAA2C;gBAC3C,cAAc,GAAG,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;gBAC/D,2BAA2B;gBAC3B,eAAe,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;YACpD,CAAC;QACL,CAAC;QAED,QAAQ;QACR,IACI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE;YACxB,OAAO,CAAC,KAAK,IAAI,CAAC,YAAY,CAAC;QACnC,CAAC,CAAC,EACJ,CAAC;YACC,2CAA2C;YAC3C,cAAc,GAAG,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACrD,iBAAiB;YACjB,MAAM,CAAC,UAAU,IAAI,CAAC,CAAC;YACvB,6BAA6B;YAC7B,eAAe,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;QACpD,CAAC;QAED,qDAAqD;QACrD,eAAe,CAAC,kBAAkB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IACxD,CAAC;CACJ;AAhRU;IADN,SAAS,EAAE;oDACe;AA+BpB;IADN,SAAS,EAAE;+DACuB;AAmPjC,gBAAiB,CAAC,qBAAqB,CAAC,GAAG,mBAAmB,CAAC", "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport { serialize } from \"../../Misc/decorators\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport type { ICameraInput } from \"../../Cameras/cameraInputsManager\";\r\nimport { CameraInputTypes } from \"../../Cameras/cameraInputsManager\";\r\nimport type { FlyCamera } from \"../../Cameras/flyCamera\";\r\nimport type { PointerInfo } from \"../../Events/pointerEvents\";\r\nimport { PointerEventTypes } from \"../../Events/pointerEvents\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Quaternion } from \"../../Maths/math.vector\";\r\nimport { Axis } from \"../../Maths/math.axis\";\r\nimport { Tools } from \"../../Misc/tools\";\r\nimport type { IPointerEvent } from \"../../Events/deviceInputEvents\";\r\n/**\r\n * Listen to mouse events to control the camera.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/customizingCameraInputs\r\n */\r\nexport class FlyCameraMouseInput implements ICameraInput<FlyCamera> {\r\n    /**\r\n     * Defines the camera the input is attached to.\r\n     */\r\n    public camera: FlyCamera;\r\n\r\n    /**\r\n     * Defines if touch is enabled. (Default is true.)\r\n     */\r\n    public touchEnabled: boolean;\r\n\r\n    /**\r\n     * Defines the buttons associated with the input to handle camera rotation.\r\n     */\r\n    @serialize()\r\n    public buttons = [0, 1, 2];\r\n\r\n    /**\r\n     * Assign buttons for Yaw control.\r\n     */\r\n    public buttonsYaw: number[] = [-1, 0, 1];\r\n\r\n    /**\r\n     * Assign buttons for Pitch control.\r\n     */\r\n    public buttonsPitch: number[] = [-1, 0, 1];\r\n\r\n    /**\r\n     * Assign buttons for Roll control.\r\n     */\r\n    public buttonsRoll: number[] = [2];\r\n\r\n    /**\r\n     * Detect if any button is being pressed while mouse is moved.\r\n     * -1 = Mouse locked.\r\n     * 0 = Left button.\r\n     * 1 = Middle Button.\r\n     * 2 = Right Button.\r\n     */\r\n    public activeButton: number = -1;\r\n\r\n    /**\r\n     * Defines the pointer's angular sensibility, to control the camera rotation speed.\r\n     * Higher values reduce its sensitivity.\r\n     */\r\n    @serialize()\r\n    public angularSensibility = 1000.0;\r\n\r\n    private _observer: Nullable<Observer<PointerInfo>>;\r\n    private _rollObserver: Nullable<Observer<Scene>>;\r\n    private _previousPosition: Nullable<{ x: number; y: number }> = null;\r\n    private _noPreventDefault: boolean | undefined;\r\n\r\n    /**\r\n     * Listen to mouse events to control the camera.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/customizingCameraInputs\r\n     */\r\n    constructor() {}\r\n\r\n    /**\r\n     * Attach the mouse control to the HTML DOM element.\r\n     * @param noPreventDefault Defines whether events caught by the controls should call preventdefault().\r\n     */\r\n    public attachControl(noPreventDefault?: boolean): void {\r\n        noPreventDefault = Tools.BackCompatCameraNoPreventDefault(arguments);\r\n        this._noPreventDefault = noPreventDefault;\r\n\r\n        this._observer = this.camera.getScene()._inputManager._addCameraPointerObserver(\r\n            (p: any) => {\r\n                this._pointerInput(p);\r\n            },\r\n            PointerEventTypes.POINTERDOWN | PointerEventTypes.POINTERUP | PointerEventTypes.POINTERMOVE\r\n        );\r\n\r\n        // Correct Roll by rate, if enabled.\r\n        this._rollObserver = this.camera.getScene().onBeforeRenderObservable.add(() => {\r\n            if (this.camera.rollCorrect) {\r\n                this.camera.restoreRoll(this.camera.rollCorrect);\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Detach the current controls from the specified dom element.\r\n     */\r\n    public detachControl(): void {\r\n        if (this._observer) {\r\n            this.camera.getScene()._inputManager._removeCameraPointerObserver(this._observer);\r\n\r\n            this.camera.getScene().onBeforeRenderObservable.remove(this._rollObserver);\r\n\r\n            this._observer = null;\r\n            this._rollObserver = null;\r\n            this._previousPosition = null;\r\n            this._noPreventDefault = undefined;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the class name of the current input.\r\n     * @returns the class name.\r\n     */\r\n    public getClassName(): string {\r\n        return \"FlyCameraMouseInput\";\r\n    }\r\n\r\n    /**\r\n     * Get the friendly name associated with the input class.\r\n     * @returns the input's friendly name.\r\n     */\r\n    public getSimpleName(): string {\r\n        return \"mouse\";\r\n    }\r\n\r\n    // Track mouse movement, when the pointer is not locked.\r\n    private _pointerInput(p: any): void {\r\n        const e = <IPointerEvent>p.event;\r\n\r\n        const camera = this.camera;\r\n        const engine = camera.getEngine();\r\n\r\n        if (!this.touchEnabled && e.pointerType === \"touch\") {\r\n            return;\r\n        }\r\n\r\n        // Mouse is moved but an unknown mouse button is pressed.\r\n        if (p.type !== PointerEventTypes.POINTERMOVE && this.buttons.indexOf(e.button) === -1) {\r\n            return;\r\n        }\r\n\r\n        const srcElement = <HTMLElement>e.target;\r\n\r\n        // Mouse down.\r\n        if (p.type === PointerEventTypes.POINTERDOWN) {\r\n            try {\r\n                srcElement?.setPointerCapture(e.pointerId);\r\n            } catch (e) {\r\n                // Nothing to do with the error. Execution continues.\r\n            }\r\n\r\n            this._previousPosition = {\r\n                x: e.clientX,\r\n                y: e.clientY,\r\n            };\r\n\r\n            this.activeButton = e.button;\r\n\r\n            if (!this._noPreventDefault) {\r\n                e.preventDefault();\r\n            }\r\n\r\n            // This is required to move while pointer button is down\r\n            if (engine.isPointerLock) {\r\n                this._onMouseMove(p.event);\r\n            }\r\n        }\r\n        // Mouse up.\r\n        else if (p.type === PointerEventTypes.POINTERUP) {\r\n            try {\r\n                srcElement?.releasePointerCapture(e.pointerId);\r\n            } catch (e) {\r\n                // Nothing to do with the error. Execution continues.\r\n            }\r\n\r\n            this.activeButton = -1;\r\n\r\n            this._previousPosition = null;\r\n            if (!this._noPreventDefault) {\r\n                e.preventDefault();\r\n            }\r\n        }\r\n        // Mouse move.\r\n        else if (p.type === PointerEventTypes.POINTERMOVE) {\r\n            if (!this._previousPosition) {\r\n                if (engine.isPointerLock) {\r\n                    this._onMouseMove(p.event);\r\n                }\r\n\r\n                return;\r\n            }\r\n\r\n            const offsetX = e.clientX - this._previousPosition.x;\r\n            const offsetY = e.clientY - this._previousPosition.y;\r\n\r\n            this._rotateCamera(offsetX, offsetY);\r\n\r\n            this._previousPosition = {\r\n                x: e.clientX,\r\n                y: e.clientY,\r\n            };\r\n\r\n            if (!this._noPreventDefault) {\r\n                e.preventDefault();\r\n            }\r\n        }\r\n    }\r\n\r\n    // Track mouse movement, when pointer is locked.\r\n    private _onMouseMove(e: any): void {\r\n        const camera = this.camera;\r\n        const engine = camera.getEngine();\r\n\r\n        if (!engine.isPointerLock) {\r\n            return;\r\n        }\r\n\r\n        const offsetX = e.movementX;\r\n        const offsetY = e.movementY;\r\n\r\n        this._rotateCamera(offsetX, offsetY);\r\n\r\n        this._previousPosition = null;\r\n\r\n        if (!this._noPreventDefault) {\r\n            e.preventDefault();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Rotate camera by mouse offset.\r\n     * @param offsetX\r\n     * @param offsetY\r\n     */\r\n    private _rotateCamera(offsetX: number, offsetY: number): void {\r\n        const camera = this.camera;\r\n        const handednessMultiplier = camera._calculateHandednessMultiplier();\r\n\r\n        offsetX *= handednessMultiplier;\r\n\r\n        const x = offsetX / this.angularSensibility;\r\n        const y = offsetY / this.angularSensibility;\r\n\r\n        // Initialize to current rotation.\r\n        const currentRotation = Quaternion.RotationYawPitchRoll(camera.rotation.y, camera.rotation.x, camera.rotation.z);\r\n        let rotationChange: Quaternion;\r\n\r\n        // Pitch.\r\n        if (\r\n            this.buttonsPitch.some((v) => {\r\n                return v === this.activeButton;\r\n            })\r\n        ) {\r\n            // Apply change in Radians to vector Angle.\r\n            rotationChange = Quaternion.RotationAxis(Axis.X, y);\r\n            // Apply Pitch to quaternion.\r\n            currentRotation.multiplyInPlace(rotationChange);\r\n        }\r\n\r\n        // Yaw.\r\n        if (\r\n            this.buttonsYaw.some((v) => {\r\n                return v === this.activeButton;\r\n            })\r\n        ) {\r\n            // Apply change in Radians to vector Angle.\r\n            rotationChange = Quaternion.RotationAxis(Axis.Y, x);\r\n            // Apply Yaw to quaternion.\r\n            currentRotation.multiplyInPlace(rotationChange);\r\n\r\n            // Add Roll, if banked turning is enabled, within Roll limit.\r\n            const limit = camera.bankedTurnLimit + camera._trackRoll; // Defaults to 90° plus manual roll.\r\n            if (camera.bankedTurn && -limit < camera.rotation.z && camera.rotation.z < limit) {\r\n                const bankingDelta = camera.bankedTurnMultiplier * -x;\r\n                // Apply change in Radians to vector Angle.\r\n                rotationChange = Quaternion.RotationAxis(Axis.Z, bankingDelta);\r\n                // Apply Yaw to quaternion.\r\n                currentRotation.multiplyInPlace(rotationChange);\r\n            }\r\n        }\r\n\r\n        // Roll.\r\n        if (\r\n            this.buttonsRoll.some((v) => {\r\n                return v === this.activeButton;\r\n            })\r\n        ) {\r\n            // Apply change in Radians to vector Angle.\r\n            rotationChange = Quaternion.RotationAxis(Axis.Z, -x);\r\n            // Track Rolling.\r\n            camera._trackRoll -= x;\r\n            // Apply Pitch to quaternion.\r\n            currentRotation.multiplyInPlace(rotationChange);\r\n        }\r\n\r\n        // Apply rotationQuaternion to Euler camera.rotation.\r\n        currentRotation.toEulerAnglesToRef(camera.rotation);\r\n    }\r\n}\r\n\r\n(<any>CameraInputTypes)[\"FlyCameraMouseInput\"] = FlyCameraMouseInput;\r\n"]}