{"version": 3, "file": "engine.transformFeedback.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/Extensions/engine.transformFeedback.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAG9C,gBAAgB;AAChB,wEAAwE;AACxE,MAAM,CAAC,IAAI,+BAA+B,GAAG,IAAI,CAAC;AAwDlD,MAAM,CAAC,SAAS,CAAC,uBAAuB,GAAG;IACvC,MAAM,iBAAiB,GAAG,IAAI,CAAC,GAAG,CAAC,uBAAuB,EAAE,CAAC;IAC7D,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACrB,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;IAC3D,CAAC;IACD,OAAO,iBAAiB,CAAC;AAC7B,CAAC,CAAC;AAEF,MAAM,CAAC,SAAS,CAAC,uBAAuB,GAAG,UAAU,KAA6B;IAC9E,IAAI,CAAC,GAAG,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC;AAC5C,CAAC,CAAC;AAEF,MAAM,CAAC,SAAS,CAAC,qBAAqB,GAAG,UAAU,KAAuC;IACtF,IAAI,CAAC,GAAG,CAAC,qBAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;AACvE,CAAC,CAAC;AAEF,MAAM,CAAC,SAAS,CAAC,sBAAsB,GAAG,UAAU,YAAqB,IAAI;IACzE,IAAI,CAAC,GAAG,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AACtF,CAAC,CAAC;AAEF,MAAM,CAAC,SAAS,CAAC,oBAAoB,GAAG;IACpC,IAAI,CAAC,GAAG,CAAC,oBAAoB,EAAE,CAAC;AACpC,CAAC,CAAC;AAEF,MAAM,CAAC,SAAS,CAAC,2BAA2B,GAAG,UAAU,OAAqB,EAAE,KAAe;IAC3F,IAAI,CAAC,GAAG,CAAC,yBAAyB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;AACrF,CAAC,CAAC;AAEF,MAAM,CAAC,SAAS,CAAC,2BAA2B,GAAG,UAAU,KAA2B;IAChF,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,yBAAyB,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AAC5G,CAAC,CAAC;AAEF,MAAM,CAAC,SAAS,CAAC,2BAA2B,GAAG,UAAU,MAAuB;IAC5E,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,yBAAyB,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;AAC7E,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport { Engine } from \"../../Engines/engine\";\r\nimport type { DataBuffer } from \"../../Buffers/dataBuffer\";\r\n\r\n/** @internal */\r\n// eslint-disable-next-line no-var, @typescript-eslint/naming-convention\r\nexport var _forceTransformFeedbackToBundle = true;\r\n\r\ndeclare module \"../../Engines/engine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface Engine {\r\n        /**\r\n         * Creates a webGL transform feedback object\r\n         * Please makes sure to check webGLVersion property to check if you are running webGL 2+\r\n         * @returns the webGL transform feedback object\r\n         */\r\n        createTransformFeedback(): WebGLTransformFeedback;\r\n\r\n        /**\r\n         * Delete a webGL transform feedback object\r\n         * @param value defines the webGL transform feedback object to delete\r\n         */\r\n        deleteTransformFeedback(value: WebGLTransformFeedback): void;\r\n\r\n        /**\r\n         * Bind a webGL transform feedback object to the webgl context\r\n         * @param value defines the webGL transform feedback object to bind\r\n         */\r\n        bindTransformFeedback(value: Nullable<WebGLTransformFeedback>): void;\r\n\r\n        /**\r\n         * Begins a transform feedback operation\r\n         * @param usePoints defines if points or triangles must be used\r\n         */\r\n        beginTransformFeedback(usePoints: boolean): void;\r\n\r\n        /**\r\n         * Ends a transform feedback operation\r\n         */\r\n        endTransformFeedback(): void;\r\n\r\n        /**\r\n         * Specify the varyings to use with transform feedback\r\n         * @param program defines the associated webGL program\r\n         * @param value defines the list of strings representing the varying names\r\n         */\r\n        setTranformFeedbackVaryings(program: WebGLProgram, value: string[]): void;\r\n\r\n        /**\r\n         * Bind a webGL buffer for a transform feedback operation\r\n         * @param value defines the webGL buffer to bind\r\n         */\r\n        bindTransformFeedbackBuffer(value: Nullable<DataBuffer>): void;\r\n\r\n        /**\r\n         * Read data back from the bound transform feedback buffer\r\n         * @param target defines the webGL buffer to write to\r\n         */\r\n        readTransformFeedbackBuffer(target: ArrayBufferView): void;\r\n    }\r\n}\r\n\r\nEngine.prototype.createTransformFeedback = function (): WebGLTransformFeedback {\r\n    const transformFeedback = this._gl.createTransformFeedback();\r\n    if (!transformFeedback) {\r\n        throw new Error(\"Unable to create Transform Feedback\");\r\n    }\r\n    return transformFeedback;\r\n};\r\n\r\nEngine.prototype.deleteTransformFeedback = function (value: WebGLTransformFeedback): void {\r\n    this._gl.deleteTransformFeedback(value);\r\n};\r\n\r\nEngine.prototype.bindTransformFeedback = function (value: Nullable<WebGLTransformFeedback>): void {\r\n    this._gl.bindTransformFeedback(this._gl.TRANSFORM_FEEDBACK, value);\r\n};\r\n\r\nEngine.prototype.beginTransformFeedback = function (usePoints: boolean = true): void {\r\n    this._gl.beginTransformFeedback(usePoints ? this._gl.POINTS : this._gl.TRIANGLES);\r\n};\r\n\r\nEngine.prototype.endTransformFeedback = function (): void {\r\n    this._gl.endTransformFeedback();\r\n};\r\n\r\nEngine.prototype.setTranformFeedbackVaryings = function (program: WebGLProgram, value: string[]): void {\r\n    this._gl.transformFeedbackVaryings(program, value, this._gl.INTERLEAVED_ATTRIBS);\r\n};\r\n\r\nEngine.prototype.bindTransformFeedbackBuffer = function (value: Nullable<DataBuffer>): void {\r\n    this._gl.bindBufferBase(this._gl.TRANSFORM_FEEDBACK_BUFFER, 0, value ? value.underlyingResource : null);\r\n};\r\n\r\nEngine.prototype.readTransformFeedbackBuffer = function (target: ArrayBufferView): void {\r\n    this._gl.getBufferSubData(this._gl.TRANSFORM_FEEDBACK_BUFFER, 0, target);\r\n};\r\n"]}