import type { SmartArrayNoDuplicate } from "../../Misc/smartArray.js";
import { Vector3 } from "../../Maths/math.vector.js";
import type { Ray } from "../../Culling/ray.js";
import type { Plane } from "../../Maths/math.plane.js";
/**
 * Contains an array of blocks representing the octree
 */
export interface IOctreeContainer<T> {
    /**
     * Blocks within the octree
     */
    blocks: Array<OctreeBlock<T>>;
}
/**
 * Class used to store a cell in an octree
 * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/optimizeOctrees
 */
export declare class OctreeBlock<T> {
    /**
     * Gets the content of the current block
     */
    entries: T[];
    /**
     * Gets the list of block children
     */
    blocks: Array<OctreeBlock<T>>;
    private _depth;
    private _maxDepth;
    private _capacity;
    private _minPoint;
    private _maxPoint;
    private _boundingVectors;
    private _creationFunc;
    /**
     * Creates a new block
     * @param minPoint defines the minimum vector (in world space) of the block's bounding box
     * @param maxPoint defines the maximum vector (in world space) of the block's bounding box
     * @param capacity defines the maximum capacity of this block (if capacity is reached the block will be split into sub blocks)
     * @param depth defines the current depth of this block in the octree
     * @param maxDepth defines the maximal depth allowed (beyond this value, the capacity is ignored)
     * @param creationFunc defines a callback to call when an element is added to the block
     */
    constructor(minPoint: Vector3, maxPoint: Vector3, capacity: number, depth: number, maxDepth: number, creationFunc: (entry: T, block: OctreeBlock<T>) => void);
    /**
     * Gets the maximum capacity of this block (if capacity is reached the block will be split into sub blocks)
     */
    get capacity(): number;
    /**
     * Gets the minimum vector (in world space) of the block's bounding box
     */
    get minPoint(): Vector3;
    /**
     * Gets the maximum vector (in world space) of the block's bounding box
     */
    get maxPoint(): Vector3;
    /**
     * Add a new element to this block
     * @param entry defines the element to add
     */
    addEntry(entry: T): void;
    /**
     * Remove an element from this block
     * @param entry defines the element to remove
     */
    removeEntry(entry: T): void;
    /**
     * Add an array of elements to this block
     * @param entries defines the array of elements to add
     */
    addEntries(entries: T[]): void;
    /**
     * Test if the current block intersects the frustum planes and if yes, then add its content to the selection array
     * @param frustumPlanes defines the frustum planes to test
     * @param selection defines the array to store current content if selection is positive
     * @param allowDuplicate defines if the selection array can contains duplicated entries
     */
    select(frustumPlanes: Plane[], selection: SmartArrayNoDuplicate<T>, allowDuplicate?: boolean): void;
    /**
     * Test if the current block intersect with the given bounding sphere and if yes, then add its content to the selection array
     * @param sphereCenter defines the bounding sphere center
     * @param sphereRadius defines the bounding sphere radius
     * @param selection defines the array to store current content if selection is positive
     * @param allowDuplicate defines if the selection array can contains duplicated entries
     */
    intersects(sphereCenter: Vector3, sphereRadius: number, selection: SmartArrayNoDuplicate<T>, allowDuplicate?: boolean): void;
    /**
     * Test if the current block intersect with the given ray and if yes, then add its content to the selection array
     * @param ray defines the ray to test with
     * @param selection defines the array to store current content if selection is positive
     */
    intersectsRay(ray: Ray, selection: SmartArrayNoDuplicate<T>): void;
    /**
     * Subdivide the content into child blocks (this block will then be empty)
     */
    createInnerBlocks(): void;
    /**
     * @internal
     */
    static _CreateBlocks<T>(worldMin: Vector3, worldMax: Vector3, entries: T[], maxBlockCapacity: number, currentDepth: number, maxDepth: number, target: IOctreeContainer<T>, creationFunc: (entry: T, block: OctreeBlock<T>) => void): void;
}
