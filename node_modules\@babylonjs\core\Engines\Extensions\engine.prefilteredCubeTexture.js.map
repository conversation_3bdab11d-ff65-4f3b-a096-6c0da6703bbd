{"version": 3, "file": "engine.prefilteredCubeTexture.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/Extensions/engine.prefilteredCubeTexture.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;AACtD,OAAO,EAAE,eAAe,EAAyB,MAAM,0CAA0C,CAAC;AAClG,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAG3C,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AACzC,OAAO,EAAE,mBAAmB,EAAE,2CAAuC;AACrE,OAAO,EAAE,WAAW,EAAE,gDAA4C;AAiClE,UAAU,CAAC,SAAS,CAAC,4BAA4B,GAAG,UAChD,OAAe,EACf,KAAsB,EACtB,QAAgB,EAChB,SAAiB,EACjB,SAAyE,IAAI,EAC7E,UAAiE,IAAI,EACrE,MAAe,EACf,kBAAuB,IAAI,EAC3B,oBAA6B,IAAI;IAEjC,MAAM,aAAa,GAAG,KAAK,EAAE,QAAa,EAAE,EAAE;QAC1C,IAAI,CAAC,QAAQ,EAAE,CAAC;YACZ,IAAI,MAAM,EAAE,CAAC;gBACT,MAAM,CAAC,IAAI,CAAC,CAAC;YACjB,CAAC;YACD,OAAO;QACX,CAAC;QAED,MAAM,OAAO,GAAG,QAAQ,CAAC,OAA0B,CAAC;QACpD,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACrB,OAAO,CAAC,oBAAoB,GAAG,IAAI,mBAAmB,EAAE,CAAC;QAC7D,CAAC;aAAM,IAAI,QAAQ,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3C,OAAO,CAAC,oBAAoB,GAAG,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC;QACrE,CAAC;QACD,OAAO,CAAC,OAAO,gDAAwC,CAAC;QAExD,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC,UAAU,EAAE,CAAC;YAC5B,wDAAwD;YACxD,IAAI,MAAM,EAAE,CAAC;gBACT,MAAM,CAAC,OAAO,CAAC,CAAC;YACpB,CAAC;YACD,OAAO;QACX,CAAC;QAED,MAAM,SAAS,GAAG,CAAC,CAAC;QAEpB,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QACpB,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;QAC7B,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,OAAO;QACX,CAAC;QAED,gEAAgE;QAChE,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,MAAM,qBAAiB,CAAC;QAEnD,MAAM,QAAQ,GAAkB,EAAE,CAAC;QACnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;YACjC,2EAA2E;YAC3E,MAAM,UAAU,GAAG,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;YACvC,MAAM,SAAS,GAAG,CAAC,GAAG,UAAU,CAAC;YAEjC,MAAM,WAAW,GAAG,SAAS,CAAC,CAAC,gBAAgB;YAC/C,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,QAAQ,GAAG,SAAS,CAAC,CAAC,gBAAgB;YAE7E,MAAM,QAAQ,GAAG,WAAW,GAAG,CAAC,WAAW,GAAG,WAAW,CAAC,GAAG,SAAS,CAAC;YACvE,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC;YAE7E,MAAM,gBAAgB,GAAG,IAAI,eAAe,CAAC,IAAI,qCAA6B,CAAC;YAC/E,gBAAgB,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;YACrC,gBAAgB,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;YACzC,gBAAgB,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC;YAClF,gBAAgB,CAAC,MAAM,GAAG,gBAAgB,CAAC,KAAK,CAAC;YACjD,gBAAgB,CAAC,MAAM,GAAG,IAAI,CAAC;YAC/B,gBAAgB,CAAC,YAAY,GAAG,SAAS,CAAC,yBAAyB,CAAC;YACpE,gBAAgB,CAAC,YAAY,GAAG,SAAS,CAAC,yBAAyB,CAAC;YACpE,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,IAAI,CAAC,CAAC;YAEvE,gBAAgB,CAAC,YAAY,GAAG,SAAS,CAAC,qBAAqB,CAAC;YAChE,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,kBAAkB,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC;YACxE,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,kBAAkB,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC;YACxE,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,aAAa,CAAC,CAAC;YAC3E,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,aAAa,CAAC,CAAC;YAE3E,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;gBACjB,MAAM,IAAI,GAAY,QAAQ,CAAC,IAAI,CAAC;gBACpC,MAAM,IAAI,GAAQ,QAAQ,CAAC,IAAI,CAAC;gBAChC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAErC,QAAQ,CAAC,eAAe,CAAC,IAAI,EAAE,gBAAgB,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC;YACvF,CAAC;iBAAM,CAAC;gBACJ,MAAM,CAAC,IAAI,CAAC,wDAAwD,CAAC,CAAC;YAC1E,CAAC;YAED,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;YAErD,2CAA2C;YAC3C,MAAM,UAAU,GAAG,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC;YAC1C,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC;YAC1B,UAAU,CAAC,QAAQ,GAAG,gBAAgB,CAAC;YAEvC,gBAAgB,CAAC,OAAO,GAAG,IAAI,CAAC;YAChC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC9B,CAAC;QAED,OAAO,CAAC,eAAe,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;QACtC,OAAO,CAAC,cAAc,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;QACrC,OAAO,CAAC,cAAc,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;QAErC,IAAI,MAAM,EAAE,CAAC;YACT,MAAM,CAAC,OAAO,CAAC,CAAC;QACpB,CAAC;IACL,CAAC,CAAC;IAEF,kEAAkE;IAClE,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,aAAa,EAAE,OAAO,EAAE,MAAM,EAAE,eAAe,EAAE,iBAAiB,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;AACxJ,CAAC,CAAC", "sourcesContent": ["import { ThinEngine } from \"../../Engines/thinEngine\";\r\nimport { InternalTexture, InternalTextureSource } from \"../../Materials/Textures/internalTexture\";\r\nimport { Logger } from \"../../Misc/logger\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Constants } from \"../constants\";\r\nimport { SphericalPolynomial } from \"core/Maths/sphericalPolynomial\";\r\nimport { BaseTexture } from \"core/Materials/Textures/baseTexture\";\r\nimport type { DDSInfo } from \"core/Misc/dds\";\r\n\r\ndeclare module \"../../Engines/abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /**\r\n         * Create a cube texture from prefiltered data (ie. the mipmaps contain ready to use data for PBR reflection)\r\n         * @param rootUrl defines the url where the file to load is located\r\n         * @param scene defines the current scene\r\n         * @param lodScale defines scale to apply to the mip map selection\r\n         * @param lodOffset defines offset to apply to the mip map selection\r\n         * @param onLoad defines an optional callback raised when the texture is loaded\r\n         * @param onError defines an optional callback raised if there is an issue to load the texture\r\n         * @param format defines the format of the data\r\n         * @param forcedExtension defines the extension to use to pick the right loader\r\n         * @param createPolynomials defines wheter or not to create polynomails harmonics for the texture\r\n         * @returns the cube texture as an InternalTexture\r\n         */\r\n        createPrefilteredCubeTexture(\r\n            rootUrl: string,\r\n            scene: Nullable<Scene>,\r\n            lodScale: number,\r\n            lodOffset: number,\r\n            onLoad?: Nullable<(internalTexture: Nullable<InternalTexture>) => void>,\r\n            onError?: Nullable<(message?: string, exception?: any) => void>,\r\n            format?: number,\r\n            forcedExtension?: any,\r\n            createPolynomials?: boolean\r\n        ): InternalTexture;\r\n    }\r\n}\r\n\r\nThinEngine.prototype.createPrefilteredCubeTexture = function (\r\n    rootUrl: string,\r\n    scene: Nullable<Scene>,\r\n    lodScale: number,\r\n    lodOffset: number,\r\n    onLoad: Nullable<(internalTexture: Nullable<InternalTexture>) => void> = null,\r\n    onError: Nullable<(message?: string, exception?: any) => void> = null,\r\n    format?: number,\r\n    forcedExtension: any = null,\r\n    createPolynomials: boolean = true\r\n): InternalTexture {\r\n    const callbackAsync = async (loadData: any) => {\r\n        if (!loadData) {\r\n            if (onLoad) {\r\n                onLoad(null);\r\n            }\r\n            return;\r\n        }\r\n\r\n        const texture = loadData.texture as InternalTexture;\r\n        if (!createPolynomials) {\r\n            texture._sphericalPolynomial = new SphericalPolynomial();\r\n        } else if (loadData.info.sphericalPolynomial) {\r\n            texture._sphericalPolynomial = loadData.info.sphericalPolynomial;\r\n        }\r\n        texture._source = InternalTextureSource.CubePrefiltered;\r\n\r\n        if (this.getCaps().textureLOD) {\r\n            // Do not add extra process if texture lod is supported.\r\n            if (onLoad) {\r\n                onLoad(texture);\r\n            }\r\n            return;\r\n        }\r\n\r\n        const mipSlices = 3;\r\n\r\n        const gl = this._gl;\r\n        const width = loadData.width;\r\n        if (!width) {\r\n            return;\r\n        }\r\n\r\n        // eslint-disable-next-line @typescript-eslint/naming-convention\r\n        const { DDSTools } = await import(\"core/Misc/dds\");\r\n\r\n        const textures: BaseTexture[] = [];\r\n        for (let i = 0; i < mipSlices; i++) {\r\n            //compute LOD from even spacing in smoothness (matching shader calculation)\r\n            const smoothness = i / (mipSlices - 1);\r\n            const roughness = 1 - smoothness;\r\n\r\n            const minLODIndex = lodOffset; // roughness = 0\r\n            const maxLODIndex = Math.log2(width) * lodScale + lodOffset; // roughness = 1\r\n\r\n            const lodIndex = minLODIndex + (maxLODIndex - minLODIndex) * roughness;\r\n            const mipmapIndex = Math.round(Math.min(Math.max(lodIndex, 0), maxLODIndex));\r\n\r\n            const glTextureFromLod = new InternalTexture(this, InternalTextureSource.Temp);\r\n            glTextureFromLod.type = texture.type;\r\n            glTextureFromLod.format = texture.format;\r\n            glTextureFromLod.width = Math.pow(2, Math.max(Math.log2(width) - mipmapIndex, 0));\r\n            glTextureFromLod.height = glTextureFromLod.width;\r\n            glTextureFromLod.isCube = true;\r\n            glTextureFromLod._cachedWrapU = Constants.TEXTURE_CLAMP_ADDRESSMODE;\r\n            glTextureFromLod._cachedWrapV = Constants.TEXTURE_CLAMP_ADDRESSMODE;\r\n            this._bindTextureDirectly(gl.TEXTURE_CUBE_MAP, glTextureFromLod, true);\r\n\r\n            glTextureFromLod.samplingMode = Constants.TEXTURE_LINEAR_LINEAR;\r\n            gl.texParameteri(gl.TEXTURE_CUBE_MAP, gl.TEXTURE_MAG_FILTER, gl.LINEAR);\r\n            gl.texParameteri(gl.TEXTURE_CUBE_MAP, gl.TEXTURE_MIN_FILTER, gl.LINEAR);\r\n            gl.texParameteri(gl.TEXTURE_CUBE_MAP, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);\r\n            gl.texParameteri(gl.TEXTURE_CUBE_MAP, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);\r\n\r\n            if (loadData.isDDS) {\r\n                const info: DDSInfo = loadData.info;\r\n                const data: any = loadData.data;\r\n                this._unpackFlipY(info.isCompressed);\r\n\r\n                DDSTools.UploadDDSLevels(this, glTextureFromLod, data, info, true, 6, mipmapIndex);\r\n            } else {\r\n                Logger.Warn(\"DDS is the only prefiltered cube map supported so far.\");\r\n            }\r\n\r\n            this._bindTextureDirectly(gl.TEXTURE_CUBE_MAP, null);\r\n\r\n            // Wrap in a base texture for easy binding.\r\n            const lodTexture = new BaseTexture(scene);\r\n            lodTexture._isCube = true;\r\n            lodTexture._texture = glTextureFromLod;\r\n\r\n            glTextureFromLod.isReady = true;\r\n            textures.push(lodTexture);\r\n        }\r\n\r\n        texture._lodTextureHigh = textures[2];\r\n        texture._lodTextureMid = textures[1];\r\n        texture._lodTextureLow = textures[0];\r\n\r\n        if (onLoad) {\r\n            onLoad(texture);\r\n        }\r\n    };\r\n\r\n    // eslint-disable-next-line @typescript-eslint/no-misused-promises\r\n    return this.createCubeTexture(rootUrl, scene, null, false, callbackAsync, onError, format, forcedExtension, createPolynomials, lodScale, lodOffset);\r\n};\r\n"]}