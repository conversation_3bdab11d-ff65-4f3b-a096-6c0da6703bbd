{"version": 3, "file": "deviceEnums.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/DeviceInput/InputDevices/deviceEnums.ts"], "names": [], "mappings": "AAAA,yDAAyD;AACzD;;GAEG;AACH,MAAM,CAAN,IAAY,UAiBX;AAjBD,WAAY,UAAU;IAClB,cAAc;IACd,iDAAW,CAAA;IACX,eAAe;IACf,mDAAY,CAAA;IACZ,YAAY;IACZ,6CAAS,CAAA;IACT,qBAAqB;IACrB,6CAAS,CAAA;IACT,qBAAqB;IACrB,qDAAa,CAAA;IACb,WAAW;IACX,2CAAQ,CAAA;IACR,wBAAwB;IACxB,+CAAU,CAAA;IACV,oBAAoB;IACpB,qDAAa,CAAA;AACjB,CAAC,EAjBW,UAAU,KAAV,UAAU,QAiBrB;AAED,eAAe;AACf;;GAEG;AACH,MAAM,CAAN,IAAY,YAuBX;AAvBD,WAAY,YAAY;IACpB,wEAAwE;IACxE,2DAAc,CAAA;IACd,sEAAsE;IACtE,uDAAY,CAAA;IACZ,0BAA0B;IAC1B,yDAAa,CAAA;IACb,mBAAmB;IACnB,6DAAe,CAAA;IACf,kBAAkB;IAClB,2DAAc,CAAA;IACd,mBAAmB;IACnB,6DAAe,CAAA;IACf,sBAAsB;IACtB,mEAAkB,CAAA;IAClB,oBAAoB;IACpB,6DAAe,CAAA;IACf,oBAAoB;IACpB,6DAAe,CAAA;IACf,oBAAoB;IACpB,6DAAe,CAAA;IACf,qEAAqE;IACrE,gDAAS,CAAA;AACb,CAAC,EAvBW,YAAY,KAAZ,YAAY,QAuBvB;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAkB,kBAyBjB;AAzBD,WAAkB,kBAAkB;IAChC,sBAAsB;IACtB,uEAAoC,CAAA;IACpC,oBAAoB;IACpB,mEAAY,CAAA;IACZ,0BAA0B;IAC1B,qEAAa,CAAA;IACb,mBAAmB;IACnB,yEAAe,CAAA;IACf,kBAAkB;IAClB,uEAAc,CAAA;IACd,mBAAmB;IACnB,yEAAe,CAAA;IACf,sBAAsB;IACtB,+EAAkB,CAAA;IAClB,oBAAoB;IACpB,yEAAe,CAAA;IACf,oBAAoB;IACpB,yEAAe,CAAA;IACf,oBAAoB;IACpB,yEAAe,CAAA;IACf,cAAc;IACd,kFAAoB,CAAA;IACpB,cAAc;IACd,8EAAkB,CAAA;AACtB,CAAC,EAzBiB,kBAAkB,KAAlB,kBAAkB,QAyBnC;AAED;;GAEG;AACH,MAAM,CAAN,IAAkB,cA6CjB;AA7CD,WAAkB,cAAc;IAC5B,YAAY;IACZ,qDAAS,CAAA;IACT,aAAa;IACb,uDAAU,CAAA;IACV,aAAa;IACb,uDAAU,CAAA;IACV,eAAe;IACf,2DAAY,CAAA;IACZ,SAAS;IACT,+CAAM,CAAA;IACN,SAAS;IACT,+CAAM,CAAA;IACN,SAAS;IACT,+CAAM,CAAA;IACN,SAAS;IACT,+CAAM,CAAA;IACN,YAAY;IACZ,qDAAS,CAAA;IACT,cAAc;IACd,yDAAW,CAAA;IACX,SAAS;IACT,gDAAO,CAAA;IACP,SAAS;IACT,gDAAO,CAAA;IACP,aAAa;IACb,wDAAW,CAAA;IACX,eAAe;IACf,4DAAa,CAAA;IACb,eAAe;IACf,4DAAa,CAAA;IACb,aAAa;IACb,8DAAc,CAAA;IACd,WAAW;IACX,oDAAS,CAAA;IACT,eAAe;IACf,4DAAa,CAAA;IACb,kBAAkB;IAClB,kEAAgB,CAAA;IAChB,kBAAkB;IAClB,kEAAgB,CAAA;IAChB,kBAAkB;IAClB,kEAAgB,CAAA;IAChB,kBAAkB;IAClB,kEAAgB,CAAA;AACpB,CAAC,EA7CiB,cAAc,KAAd,cAAc,QA6C/B;AAED;;GAEG;AACH,MAAM,CAAN,IAAkB,cA6CjB;AA7CD,WAAkB,cAAc;IAC5B,YAAY;IACZ,qDAAS,CAAA;IACT,aAAa;IACb,uDAAU,CAAA;IACV,aAAa;IACb,uDAAU,CAAA;IACV,eAAe;IACf,2DAAY,CAAA;IACZ,SAAS;IACT,+CAAM,CAAA;IACN,SAAS;IACT,+CAAM,CAAA;IACN,SAAS;IACT,+CAAM,CAAA;IACN,SAAS;IACT,+CAAM,CAAA;IACN,aAAa;IACb,uDAAU,CAAA;IACV,cAAc;IACd,yDAAW,CAAA;IACX,SAAS;IACT,gDAAO,CAAA;IACP,SAAS;IACT,gDAAO,CAAA;IACP,aAAa;IACb,wDAAW,CAAA;IACX,eAAe;IACf,4DAAa,CAAA;IACb,eAAe;IACf,4DAAa,CAAA;IACb,aAAa;IACb,8DAAc,CAAA;IACd,WAAW;IACX,oDAAS,CAAA;IACT,eAAe;IACf,4DAAa,CAAA;IACb,kBAAkB;IAClB,kEAAgB,CAAA;IAChB,kBAAkB;IAClB,kEAAgB,CAAA;IAChB,kBAAkB;IAClB,kEAAgB,CAAA;IAChB,kBAAkB;IAClB,kEAAgB,CAAA;AACpB,CAAC,EA7CiB,cAAc,KAAd,cAAc,QA6C/B;AAED;;GAEG;AACH,MAAM,CAAN,IAAkB,SA2CjB;AA3CD,WAAkB,SAAS;IACvB,QAAQ;IACR,mCAAK,CAAA;IACL,QAAQ;IACR,mCAAK,CAAA;IACL,QAAQ;IACR,mCAAK,CAAA;IACL,QAAQ;IACR,mCAAK,CAAA;IACL,SAAS;IACT,qCAAM,CAAA;IACN,SAAS;IACT,qCAAM,CAAA;IACN,SAAS;IACT,qCAAM,CAAA;IACN,SAAS;IACT,qCAAM,CAAA;IACN,WAAW;IACX,yCAAQ,CAAA;IACR,YAAY;IACZ,2CAAS,CAAA;IACT,SAAS;IACT,sCAAO,CAAA;IACP,SAAS;IACT,sCAAO,CAAA;IACP,aAAa;IACb,8CAAW,CAAA;IACX,eAAe;IACf,kDAAa,CAAA;IACb,eAAe;IACf,kDAAa,CAAA;IACb,aAAa;IACb,oDAAc,CAAA;IACd,WAAW;IACX,0CAAS,CAAA;IACT,kBAAkB;IAClB,wDAAgB,CAAA;IAChB,kBAAkB;IAClB,wDAAgB,CAAA;IAChB,kBAAkB;IAClB,wDAAgB,CAAA;IAChB,kBAAkB;IAClB,wDAAgB,CAAA;AACpB,CAAC,EA3CiB,SAAS,KAAT,SAAS,QA2C1B;AAED;;GAEG;AACH,MAAM,CAAN,IAAkB,WA6CjB;AA7CD,WAAkB,WAAW;IACzB,QAAQ;IACR,uCAAK,CAAA;IACL,QAAQ;IACR,uCAAK,CAAA;IACL,QAAQ;IACR,uCAAK,CAAA;IACL,QAAQ;IACR,uCAAK,CAAA;IACL,QAAQ;IACR,uCAAK,CAAA;IACL,QAAQ;IACR,uCAAK,CAAA;IACL,SAAS;IACT,yCAAM,CAAA;IACN,SAAS;IACT,yCAAM,CAAA;IACN,YAAY;IACZ,+CAAS,CAAA;IACT,WAAW;IACX,6CAAQ,CAAA;IACR,SAAS;IACT,0CAAO,CAAA;IACP,SAAS;IACT,0CAAO,CAAA;IACP,aAAa;IACb,kDAAW,CAAA;IACX,eAAe;IACf,sDAAa,CAAA;IACb,eAAe;IACf,sDAAa,CAAA;IACb,aAAa;IACb,wDAAc,CAAA;IACd,WAAW;IACX,8CAAS,CAAA;IACT,cAAc;IACd,oDAAY,CAAA;IACZ,kBAAkB;IAClB,4DAAgB,CAAA;IAChB,kBAAkB;IAClB,4DAAgB,CAAA;IAChB,kBAAkB;IAClB,4DAAgB,CAAA;IAChB,kBAAkB;IAClB,4DAAgB,CAAA;AACpB,CAAC,EA7CiB,WAAW,KAAX,WAAW,QA6C5B", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\n/**\r\n * Enum for Device Types\r\n */\r\nexport enum DeviceType {\r\n    /** Generic */\r\n    Generic = 0,\r\n    /** Keyboard */\r\n    Keyboard = 1,\r\n    /** Mouse */\r\n    Mouse = 2,\r\n    /** Touch Pointers */\r\n    Touch = 3,\r\n    /** PS4 Dual Shock */\r\n    DualShock = 4,\r\n    /** Xbox */\r\n    Xbox = 5,\r\n    /** Switch Controller */\r\n    Switch = 6,\r\n    /** PS5 DualSense */\r\n    DualSense = 7,\r\n}\r\n\r\n// Device Enums\r\n/**\r\n * Enum for All Pointers (Touch/Mouse)\r\n */\r\nexport enum PointerInput {\r\n    /** Horizontal Axis (Not used in events/observables; only in polling) */\r\n    Horizontal = 0,\r\n    /** Vertical Axis (Not used in events/observables; only in polling) */\r\n    Vertical = 1,\r\n    /** Left Click or Touch */\r\n    LeftClick = 2,\r\n    /** Middle Click */\r\n    MiddleClick = 3,\r\n    /** Right Click */\r\n    RightClick = 4,\r\n    /** Browser Back */\r\n    BrowserBack = 5,\r\n    /** Browser Forward */\r\n    BrowserForward = 6,\r\n    /** Mouse Wheel X */\r\n    MouseWheelX = 7,\r\n    /** Mouse Wheel Y */\r\n    MouseWheelY = 8,\r\n    /** Mouse Wheel Z */\r\n    MouseWheelZ = 9,\r\n    /** Used in events/observables to identify if x/y changes occurred */\r\n    Move = 12,\r\n}\r\n\r\n/** @internal */\r\nexport const enum NativePointerInput {\r\n    /** Horizontal Axis */\r\n    Horizontal = PointerInput.Horizontal,\r\n    /** Vertical Axis */\r\n    Vertical = 1,\r\n    /** Left Click or Touch */\r\n    LeftClick = 2,\r\n    /** Middle Click */\r\n    MiddleClick = 3,\r\n    /** Right Click */\r\n    RightClick = 4,\r\n    /** Browser Back */\r\n    BrowserBack = 5,\r\n    /** Browser Forward */\r\n    BrowserForward = 6,\r\n    /** Mouse Wheel X */\r\n    MouseWheelX = 7,\r\n    /** Mouse Wheel Y */\r\n    MouseWheelY = 8,\r\n    /** Mouse Wheel Z */\r\n    MouseWheelZ = 9,\r\n    /** Delta X */\r\n    DeltaHorizontal = 10,\r\n    /** Delta Y */\r\n    DeltaVertical = 11,\r\n}\r\n\r\n/**\r\n * Enum for Dual Shock Gamepad\r\n */\r\nexport const enum DualShockInput {\r\n    /** Cross */\r\n    Cross = 0,\r\n    /** Circle */\r\n    Circle = 1,\r\n    /** Square */\r\n    Square = 2,\r\n    /** Triangle */\r\n    Triangle = 3,\r\n    /** L1 */\r\n    L1 = 4,\r\n    /** R1 */\r\n    R1 = 5,\r\n    /** L2 */\r\n    L2 = 6,\r\n    /** R2 */\r\n    R2 = 7,\r\n    /** Share */\r\n    Share = 8,\r\n    /** Options */\r\n    Options = 9,\r\n    /** L3 */\r\n    L3 = 10,\r\n    /** R3 */\r\n    R3 = 11,\r\n    /** DPadUp */\r\n    DPadUp = 12,\r\n    /** DPadDown */\r\n    DPadDown = 13,\r\n    /** DPadLeft */\r\n    DPadLeft = 14,\r\n    /** DRight */\r\n    DPadRight = 15,\r\n    /** Home */\r\n    Home = 16,\r\n    /** TouchPad */\r\n    TouchPad = 17,\r\n    /** LStickXAxis */\r\n    LStickXAxis = 18,\r\n    /** LStickYAxis */\r\n    LStickYAxis = 19,\r\n    /** RStickXAxis */\r\n    RStickXAxis = 20,\r\n    /** RStickYAxis */\r\n    RStickYAxis = 21,\r\n}\r\n\r\n/**\r\n * Enum for Dual Sense Gamepad\r\n */\r\nexport const enum DualSenseInput {\r\n    /** Cross */\r\n    Cross = 0,\r\n    /** Circle */\r\n    Circle = 1,\r\n    /** Square */\r\n    Square = 2,\r\n    /** Triangle */\r\n    Triangle = 3,\r\n    /** L1 */\r\n    L1 = 4,\r\n    /** R1 */\r\n    R1 = 5,\r\n    /** L2 */\r\n    L2 = 6,\r\n    /** R2 */\r\n    R2 = 7,\r\n    /** Create */\r\n    Create = 8,\r\n    /** Options */\r\n    Options = 9,\r\n    /** L3 */\r\n    L3 = 10,\r\n    /** R3 */\r\n    R3 = 11,\r\n    /** DPadUp */\r\n    DPadUp = 12,\r\n    /** DPadDown */\r\n    DPadDown = 13,\r\n    /** DPadLeft */\r\n    DPadLeft = 14,\r\n    /** DRight */\r\n    DPadRight = 15,\r\n    /** Home */\r\n    Home = 16,\r\n    /** TouchPad */\r\n    TouchPad = 17,\r\n    /** LStickXAxis */\r\n    LStickXAxis = 18,\r\n    /** LStickYAxis */\r\n    LStickYAxis = 19,\r\n    /** RStickXAxis */\r\n    RStickXAxis = 20,\r\n    /** RStickYAxis */\r\n    RStickYAxis = 21,\r\n}\r\n\r\n/**\r\n * Enum for Xbox Gamepad\r\n */\r\nexport const enum XboxInput {\r\n    /** A */\r\n    A = 0,\r\n    /** B */\r\n    B = 1,\r\n    /** X */\r\n    X = 2,\r\n    /** Y */\r\n    Y = 3,\r\n    /** LB */\r\n    LB = 4,\r\n    /** RB */\r\n    RB = 5,\r\n    /** LT */\r\n    LT = 6,\r\n    /** RT */\r\n    RT = 7,\r\n    /** Back */\r\n    Back = 8,\r\n    /** Start */\r\n    Start = 9,\r\n    /** LS */\r\n    LS = 10,\r\n    /** RS */\r\n    RS = 11,\r\n    /** DPadUp */\r\n    DPadUp = 12,\r\n    /** DPadDown */\r\n    DPadDown = 13,\r\n    /** DPadLeft */\r\n    DPadLeft = 14,\r\n    /** DRight */\r\n    DPadRight = 15,\r\n    /** Home */\r\n    Home = 16,\r\n    /** LStickXAxis */\r\n    LStickXAxis = 17,\r\n    /** LStickYAxis */\r\n    LStickYAxis = 18,\r\n    /** RStickXAxis */\r\n    RStickXAxis = 19,\r\n    /** RStickYAxis */\r\n    RStickYAxis = 20,\r\n}\r\n\r\n/**\r\n * Enum for Switch (Pro/JoyCon L+R) Gamepad\r\n */\r\nexport const enum SwitchInput {\r\n    /** B */\r\n    B = 0,\r\n    /** A */\r\n    A = 1,\r\n    /** Y */\r\n    Y = 2,\r\n    /** X */\r\n    X = 3,\r\n    /** L */\r\n    L = 4,\r\n    /** R */\r\n    R = 5,\r\n    /** ZL */\r\n    ZL = 6,\r\n    /** ZR */\r\n    ZR = 7,\r\n    /** Minus */\r\n    Minus = 8,\r\n    /** Plus */\r\n    Plus = 9,\r\n    /** LS */\r\n    LS = 10,\r\n    /** RS */\r\n    RS = 11,\r\n    /** DPadUp */\r\n    DPadUp = 12,\r\n    /** DPadDown */\r\n    DPadDown = 13,\r\n    /** DPadLeft */\r\n    DPadLeft = 14,\r\n    /** DRight */\r\n    DPadRight = 15,\r\n    /** Home */\r\n    Home = 16,\r\n    /** Capture */\r\n    Capture = 17,\r\n    /** LStickXAxis */\r\n    LStickXAxis = 18,\r\n    /** LStickYAxis */\r\n    LStickYAxis = 19,\r\n    /** RStickXAxis */\r\n    RStickXAxis = 20,\r\n    /** RStickYAxis */\r\n    RStickYAxis = 21,\r\n}\r\n"]}