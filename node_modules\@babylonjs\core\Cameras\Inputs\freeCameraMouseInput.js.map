{"version": 3, "file": "freeCameraMouseInput.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Cameras/Inputs/freeCameraMouseInput.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACnD,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAC;AAGlD,OAAO,EAAE,gBAAgB,EAAE,MAAM,mCAAmC,CAAC;AAGrE,OAAO,EAAE,iBAAiB,EAAE,MAAM,4BAA4B,CAAC;AAC/D,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AAEzC;;;GAGG;AACH,MAAM,OAAO,oBAAoB;IAqC7B;;;;OAIG;IACH;IACI;;OAEG;IACI,eAAe,IAAI;QAAnB,iBAAY,GAAZ,YAAY,CAAO;QAxC9B;;WAEG;QAEI,YAAO,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE3B;;WAEG;QAEI,uBAAkB,GAAG,MAAM,CAAC;QAK3B,sBAAiB,GAAuC,IAAI,CAAC;QAErE;;WAEG;QACI,6BAAwB,GAAG,IAAI,UAAU,EAAwC,CAAC;QACzF;;;WAGG;QACI,yBAAoB,GAAG,IAAI,CAAC;QAE3B,yBAAoB,GAAW,CAAC,CAAC,CAAC;QAClC,qBAAgB,GAAW,CAAC,CAAC,CAAC;IAanC,CAAC;IAEJ;;;OAGG;IACI,aAAa,CAAC,gBAA0B;QAC3C,gBAAgB,GAAG,KAAK,CAAC,gCAAgC,CAAC,SAAS,CAAC,CAAC;QACrE,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QACvC,MAAM,OAAO,GAAG,MAAM,CAAC,eAAe,EAAE,CAAC;QAEzC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtB,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,EAAE,EAAE;gBACvB,MAAM,GAAG,GAAkB,CAAC,CAAC,KAAK,CAAC;gBACnC,MAAM,OAAO,GAAG,GAAG,CAAC,WAAW,KAAK,OAAO,CAAC;gBAE5C,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,OAAO,EAAE,CAAC;oBAChC,OAAO;gBACX,CAAC;gBAED,IAAI,CAAC,CAAC,IAAI,KAAK,iBAAiB,CAAC,WAAW,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;oBACtF,OAAO;gBACX,CAAC;gBAED,MAAM,UAAU,GAAgB,GAAG,CAAC,MAAM,CAAC;gBAE3C,IAAI,CAAC,CAAC,IAAI,KAAK,iBAAiB,CAAC,WAAW,EAAE,CAAC;oBAC3C,wHAAwH;oBACxH,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,gBAAgB,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,IAAI,IAAI,CAAC,oBAAoB,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;wBAC9F,OAAO;oBACX,CAAC;oBAED,IAAI,CAAC,gBAAgB,GAAG,GAAG,CAAC,SAAS,CAAC;oBACtC,IAAI,CAAC;wBACD,UAAU,EAAE,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;oBACjD,CAAC;oBAAC,OAAO,CAAC,EAAE,CAAC;wBACT,wDAAwD;oBAC5D,CAAC;oBAED,IAAI,IAAI,CAAC,oBAAoB,KAAK,CAAC,CAAC,EAAE,CAAC;wBACnC,IAAI,CAAC,oBAAoB,GAAG,GAAG,CAAC,MAAM,CAAC;oBAC3C,CAAC;oBAED,IAAI,CAAC,iBAAiB,GAAG;wBACrB,CAAC,EAAE,GAAG,CAAC,OAAO;wBACd,CAAC,EAAE,GAAG,CAAC,OAAO;qBACjB,CAAC;oBAEF,IAAI,CAAC,gBAAgB,EAAE,CAAC;wBACpB,GAAG,CAAC,cAAc,EAAE,CAAC;wBACrB,IAAI,OAAO,EAAE,CAAC;4BACV,OAAO,CAAC,KAAK,EAAE,CAAC;wBACpB,CAAC;oBACL,CAAC;oBAED,wDAAwD;oBACxD,IAAI,MAAM,CAAC,aAAa,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;wBAC5C,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;oBAC/B,CAAC;gBACL,CAAC;qBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,iBAAiB,CAAC,SAAS,EAAE,CAAC;oBAChD,mGAAmG;oBACnG,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,gBAAgB,KAAK,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,OAAO,IAAI,IAAI,CAAC,oBAAoB,KAAK,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;wBACjH,OAAO;oBACX,CAAC;oBAED,IAAI,CAAC;wBACD,UAAU,EAAE,qBAAqB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;oBACrD,CAAC;oBAAC,OAAO,CAAC,EAAE,CAAC;wBACT,+BAA+B;oBACnC,CAAC;oBACD,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,CAAC;oBAE/B,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;oBAC9B,IAAI,CAAC,gBAAgB,EAAE,CAAC;wBACpB,GAAG,CAAC,cAAc,EAAE,CAAC;oBACzB,CAAC;oBAED,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC;gBAC/B,CAAC;qBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,iBAAiB,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,gBAAgB,KAAK,GAAG,CAAC,SAAS,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;oBAC3G,IAAI,MAAM,CAAC,aAAa,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;wBAC5C,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;oBAC/B,CAAC;yBAAM,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;wBAChC,MAAM,oBAAoB,GAAG,IAAI,CAAC,MAAM,CAAC,8BAA8B,EAAE,CAAC;wBAC1E,MAAM,OAAO,GAAG,CAAC,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC;wBAChF,MAAM,OAAO,GAAG,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;wBAEvD,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;4BAC5B,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC;4BAClE,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC;wBACtE,CAAC;wBACD,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;wBAEtF,IAAI,CAAC,iBAAiB,GAAG;4BACrB,CAAC,EAAE,GAAG,CAAC,OAAO;4BACd,CAAC,EAAE,GAAG,CAAC,OAAO;yBACjB,CAAC;wBAEF,IAAI,CAAC,gBAAgB,EAAE,CAAC;4BACpB,GAAG,CAAC,cAAc,EAAE,CAAC;wBACzB,CAAC;oBACL,CAAC;gBACL,CAAC;YACL,CAAC,CAAC;QACN,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,CAAC,GAAG,EAAE,EAAE;YACxB,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;gBACxB,OAAO;YACX,CAAC;YAED,MAAM,oBAAoB,GAAG,IAAI,CAAC,MAAM,CAAC,8BAA8B,EAAE,CAAC;YAC1E,MAAM,OAAO,GAAG,GAAG,CAAC,SAAS,GAAG,oBAAoB,CAAC;YAErD,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC;YAElE,MAAM,OAAO,GAAG,GAAG,CAAC,SAAS,CAAC;YAC9B,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC;YAElE,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;YAE9B,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACpB,GAAG,CAAC,cAAc,EAAE,CAAC;YACzB,CAAC;QACL,CAAC,CAAC;QAEF,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM;aACvB,QAAQ,EAAE;aACV,aAAa,CAAC,yBAAyB,CAAC,IAAI,CAAC,aAAa,EAAE,iBAAiB,CAAC,WAAW,GAAG,iBAAiB,CAAC,SAAS,GAAG,iBAAiB,CAAC,WAAW,CAAC,CAAC;QAE9J,IAAI,OAAO,EAAE,CAAC;YACV,IAAI,CAAC,gBAAgB,GAAG,CAAC,GAAe,EAAE,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,GAAmB,CAAC,CAAC;YACrF,OAAO,CAAC,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC,CAAC,4DAA4D;QACvI,CAAC;IACL,CAAC;IAED;;;;OAIG;IACI,aAAa,CAAC,GAAiB;QAClC,GAAG,CAAC,cAAc,EAAE,CAAC;IACzB,CAAC;IAED;;OAEG;IACI,aAAa;QAChB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,aAAa,CAAC,4BAA4B,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAElF,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACxB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;gBACvC,MAAM,OAAO,GAAG,MAAM,CAAC,eAAe,EAAE,CAAC;gBACzC,IAAI,OAAO,EAAE,CAAC;oBACV,OAAO,CAAC,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBACtE,CAAC;YACL,CAAC;YAED,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;gBAChC,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;YAC1C,CAAC;YAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YACzB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAClC,CAAC;QAED,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC;QAC3B,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,CAAC;IACnC,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,sBAAsB,CAAC;IAClC,CAAC;IAED;;;OAGG;IACI,aAAa;QAChB,OAAO,OAAO,CAAC;IACnB,CAAC;CACJ;AAhOU;IADN,SAAS,EAAE;qDACe;AAMpB;IADN,SAAS,EAAE;gEACuB;AA4NjC,gBAAiB,CAAC,sBAAsB,CAAC,GAAG,oBAAoB,CAAC", "sourcesContent": ["import type { Observer, EventState } from \"../../Misc/observable\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport { serialize } from \"../../Misc/decorators\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { ICameraInput } from \"../../Cameras/cameraInputsManager\";\r\nimport { CameraInputTypes } from \"../../Cameras/cameraInputsManager\";\r\nimport type { FreeCamera } from \"../../Cameras/freeCamera\";\r\nimport type { PointerInfo } from \"../../Events/pointerEvents\";\r\nimport { PointerEventTypes } from \"../../Events/pointerEvents\";\r\nimport { Tools } from \"../../Misc/tools\";\r\nimport type { IMouseEvent, IPointerEvent } from \"../../Events/deviceInputEvents\";\r\n/**\r\n * Manage the mouse inputs to control the movement of a free camera.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/customizingCameraInputs\r\n */\r\nexport class FreeCameraMouseInput implements ICameraInput<FreeCamera> {\r\n    /**\r\n     * Defines the camera the input is attached to.\r\n     */\r\n    public camera: FreeCamera;\r\n\r\n    /**\r\n     * Defines the buttons associated with the input to handle camera move.\r\n     */\r\n    @serialize()\r\n    public buttons = [0, 1, 2];\r\n\r\n    /**\r\n     * Defines the pointer angular sensibility  along the X and Y axis or how fast is the camera rotating.\r\n     */\r\n    @serialize()\r\n    public angularSensibility = 2000.0;\r\n\r\n    private _pointerInput: (p: PointerInfo, s: EventState) => void;\r\n    private _onMouseMove: Nullable<(e: IMouseEvent) => any>;\r\n    private _observer: Nullable<Observer<PointerInfo>>;\r\n    private _previousPosition: Nullable<{ x: number; y: number }> = null;\r\n\r\n    /**\r\n     * Observable for when a pointer move event occurs containing the move offset\r\n     */\r\n    public onPointerMovedObservable = new Observable<{ offsetX: number; offsetY: number }>();\r\n    /**\r\n     * @internal\r\n     * If the camera should be rotated automatically based on pointer movement\r\n     */\r\n    public _allowCameraRotation = true;\r\n\r\n    private _currentActiveButton: number = -1;\r\n    private _activePointerId: number = -1;\r\n    private _contextMenuBind: (evt: MouseEvent) => void;\r\n\r\n    /**\r\n     * Manage the mouse inputs to control the movement of a free camera.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/customizingCameraInputs\r\n     * @param touchEnabled Defines if touch is enabled or not\r\n     */\r\n    constructor(\r\n        /**\r\n         * [true] Define if touch is enabled in the mouse input\r\n         */\r\n        public touchEnabled = true\r\n    ) {}\r\n\r\n    /**\r\n     * Attach the input controls to a specific dom element to get the input from.\r\n     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\r\n     */\r\n    public attachControl(noPreventDefault?: boolean): void {\r\n        noPreventDefault = Tools.BackCompatCameraNoPreventDefault(arguments);\r\n        const engine = this.camera.getEngine();\r\n        const element = engine.getInputElement();\r\n\r\n        if (!this._pointerInput) {\r\n            this._pointerInput = (p) => {\r\n                const evt = <IPointerEvent>p.event;\r\n                const isTouch = evt.pointerType === \"touch\";\r\n\r\n                if (!this.touchEnabled && isTouch) {\r\n                    return;\r\n                }\r\n\r\n                if (p.type !== PointerEventTypes.POINTERMOVE && this.buttons.indexOf(evt.button) === -1) {\r\n                    return;\r\n                }\r\n\r\n                const srcElement = <HTMLElement>evt.target;\r\n\r\n                if (p.type === PointerEventTypes.POINTERDOWN) {\r\n                    // If the input is touch with more than one touch OR if the input is mouse and there is already an active button, return\r\n                    if ((isTouch && this._activePointerId !== -1) || (!isTouch && this._currentActiveButton !== -1)) {\r\n                        return;\r\n                    }\r\n\r\n                    this._activePointerId = evt.pointerId;\r\n                    try {\r\n                        srcElement?.setPointerCapture(evt.pointerId);\r\n                    } catch (e) {\r\n                        //Nothing to do with the error. Execution will continue.\r\n                    }\r\n\r\n                    if (this._currentActiveButton === -1) {\r\n                        this._currentActiveButton = evt.button;\r\n                    }\r\n\r\n                    this._previousPosition = {\r\n                        x: evt.clientX,\r\n                        y: evt.clientY,\r\n                    };\r\n\r\n                    if (!noPreventDefault) {\r\n                        evt.preventDefault();\r\n                        if (element) {\r\n                            element.focus();\r\n                        }\r\n                    }\r\n\r\n                    // This is required to move while pointer button is down\r\n                    if (engine.isPointerLock && this._onMouseMove) {\r\n                        this._onMouseMove(p.event);\r\n                    }\r\n                } else if (p.type === PointerEventTypes.POINTERUP) {\r\n                    // If input is touch with a different touch id OR if input is mouse with a different button, return\r\n                    if ((isTouch && this._activePointerId !== evt.pointerId) || (!isTouch && this._currentActiveButton !== evt.button)) {\r\n                        return;\r\n                    }\r\n\r\n                    try {\r\n                        srcElement?.releasePointerCapture(evt.pointerId);\r\n                    } catch (e) {\r\n                        //Nothing to do with the error.\r\n                    }\r\n                    this._currentActiveButton = -1;\r\n\r\n                    this._previousPosition = null;\r\n                    if (!noPreventDefault) {\r\n                        evt.preventDefault();\r\n                    }\r\n\r\n                    this._activePointerId = -1;\r\n                } else if (p.type === PointerEventTypes.POINTERMOVE && (this._activePointerId === evt.pointerId || !isTouch)) {\r\n                    if (engine.isPointerLock && this._onMouseMove) {\r\n                        this._onMouseMove(p.event);\r\n                    } else if (this._previousPosition) {\r\n                        const handednessMultiplier = this.camera._calculateHandednessMultiplier();\r\n                        const offsetX = (evt.clientX - this._previousPosition.x) * handednessMultiplier;\r\n                        const offsetY = evt.clientY - this._previousPosition.y;\r\n\r\n                        if (this._allowCameraRotation) {\r\n                            this.camera.cameraRotation.y += offsetX / this.angularSensibility;\r\n                            this.camera.cameraRotation.x += offsetY / this.angularSensibility;\r\n                        }\r\n                        this.onPointerMovedObservable.notifyObservers({ offsetX: offsetX, offsetY: offsetY });\r\n\r\n                        this._previousPosition = {\r\n                            x: evt.clientX,\r\n                            y: evt.clientY,\r\n                        };\r\n\r\n                        if (!noPreventDefault) {\r\n                            evt.preventDefault();\r\n                        }\r\n                    }\r\n                }\r\n            };\r\n        }\r\n\r\n        this._onMouseMove = (evt) => {\r\n            if (!engine.isPointerLock) {\r\n                return;\r\n            }\r\n\r\n            const handednessMultiplier = this.camera._calculateHandednessMultiplier();\r\n            const offsetX = evt.movementX * handednessMultiplier;\r\n\r\n            this.camera.cameraRotation.y += offsetX / this.angularSensibility;\r\n\r\n            const offsetY = evt.movementY;\r\n            this.camera.cameraRotation.x += offsetY / this.angularSensibility;\r\n\r\n            this._previousPosition = null;\r\n\r\n            if (!noPreventDefault) {\r\n                evt.preventDefault();\r\n            }\r\n        };\r\n\r\n        this._observer = this.camera\r\n            .getScene()\r\n            ._inputManager._addCameraPointerObserver(this._pointerInput, PointerEventTypes.POINTERDOWN | PointerEventTypes.POINTERUP | PointerEventTypes.POINTERMOVE);\r\n\r\n        if (element) {\r\n            this._contextMenuBind = (evt: MouseEvent) => this.onContextMenu(evt as PointerEvent);\r\n            element.addEventListener(\"contextmenu\", this._contextMenuBind, false); // TODO: We need to figure out how to handle this for Native\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Called on JS contextmenu event.\r\n     * Override this method to provide functionality.\r\n     * @param evt the context menu event\r\n     */\r\n    public onContextMenu(evt: PointerEvent): void {\r\n        evt.preventDefault();\r\n    }\r\n\r\n    /**\r\n     * Detach the current controls from the specified dom element.\r\n     */\r\n    public detachControl(): void {\r\n        if (this._observer) {\r\n            this.camera.getScene()._inputManager._removeCameraPointerObserver(this._observer);\r\n\r\n            if (this._contextMenuBind) {\r\n                const engine = this.camera.getEngine();\r\n                const element = engine.getInputElement();\r\n                if (element) {\r\n                    element.removeEventListener(\"contextmenu\", this._contextMenuBind);\r\n                }\r\n            }\r\n\r\n            if (this.onPointerMovedObservable) {\r\n                this.onPointerMovedObservable.clear();\r\n            }\r\n\r\n            this._observer = null;\r\n            this._onMouseMove = null;\r\n            this._previousPosition = null;\r\n        }\r\n\r\n        this._activePointerId = -1;\r\n        this._currentActiveButton = -1;\r\n    }\r\n\r\n    /**\r\n     * Gets the class name of the current input.\r\n     * @returns the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"FreeCameraMouseInput\";\r\n    }\r\n\r\n    /**\r\n     * Get the friendly name associated with the input class.\r\n     * @returns the input friendly name\r\n     */\r\n    public getSimpleName(): string {\r\n        return \"mouse\";\r\n    }\r\n}\r\n\r\n(<any>CameraInputTypes)[\"FreeCameraMouseInput\"] = FreeCameraMouseInput;\r\n"]}