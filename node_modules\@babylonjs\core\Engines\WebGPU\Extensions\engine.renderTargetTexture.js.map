{"version": 3, "file": "engine.renderTargetTexture.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Engines/WebGPU/Extensions/engine.renderTargetTexture.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,gBAAgB,EAAE,kCAAsC;AAgBjE,gBAAgB,CAAC,SAAS,CAAC,sBAAsB,GAAG,UAChD,OAAe,EACf,OAAuC,EACvC,OAAsC,EACtC,IAAa;IAEb,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC;QAC3C,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;IAChE,CAAC;SAAM,CAAC;QACJ,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAC1D,CAAC;AACL,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"../../../types\";\r\nimport type { RenderTargetTexture } from \"../../../Materials/Textures/renderTargetTexture\";\r\nimport { ThinWebGPUEngine } from \"core/Engines/thinWebGPUEngine\";\r\n\r\ndeclare module \"../../abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /**\r\n         * Sets a depth stencil texture from a render target to the according uniform.\r\n         * @param channel The texture channel\r\n         * @param uniform The uniform to set\r\n         * @param texture The render target texture containing the depth stencil texture to apply\r\n         * @param name The texture name\r\n         */\r\n        setDepthStencilTexture(channel: number, uniform: Nullable<WebGLUniformLocation>, texture: Nullable<RenderTargetTexture>, name?: string): void;\r\n    }\r\n}\r\n\r\nThinWebGPUEngine.prototype.setDepthStencilTexture = function (\r\n    channel: number,\r\n    uniform: Nullable<WebGLUniformLocation>,\r\n    texture: Nullable<RenderTargetTexture>,\r\n    name?: string\r\n): void {\r\n    if (!texture || !texture.depthStencilTexture) {\r\n        this._setTexture(channel, null, undefined, undefined, name);\r\n    } else {\r\n        this._setTexture(channel, texture, false, true, name);\r\n    }\r\n};\r\n"]}