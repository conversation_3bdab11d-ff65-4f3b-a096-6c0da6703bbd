{"version": 3, "file": "deviceOrientationCamera.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Cameras/deviceOrientationCamera.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAE1C,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC3D,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAE/B,OAAO,2CAA2C,CAAC;AACnD,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAE1C,IAAI,CAAC,kBAAkB,CAAC,yBAAyB,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;IAC/D,OAAO,GAAG,EAAE,CAAC,IAAI,uBAAuB,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC;AAC1E,CAAC,CAAC,CAAC;AAEH,mEAAmE;AACnE;;;GAGG;AACH,MAAM,OAAO,uBAAwB,SAAQ,UAAU;IAMnD;;;;;OAKG;IACH,YAAY,IAAY,EAAE,QAAiB,EAAE,KAAa;QACtD,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QAVzB,uBAAkB,GAAG,IAAI,UAAU,EAAE,CAAC;QACtC,mDAA8C,GAAG,IAAI,CAAC;QA8CtD,gBAAW,GAAG,CAAC,CAAC;QApCpB,IAAI,CAAC,gBAAgB,GAAG,IAAI,UAAU,EAAE,CAAC;QACzC,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC;QAEnC,0EAA0E;QAC1E,IAAI,IAAI,CAAC,MAAM,CAAC,uBAAuB,EAAE,CAAC;YACtC,IAAI,CAAC,MAAM,CAAC,uBAAuB,CAAC,qCAAqC,CAAC,OAAO,CAAC,GAAG,EAAE;gBACnF,IAAI,IAAI,CAAC,8CAA8C,EAAE,CAAC;oBACtD,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;wBAC1B,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,oBAAoB,GAAG,KAAK,CAAC;wBACrD,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;4BACvD,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,EAAE,CAAC;gCACxB,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;oCAC3B,IAAI,CAAC,kBAAkB,GAAG,IAAI,UAAU,EAAE,CAAC;gCAC/C,CAAC;gCACD,6FAA6F;gCAC7F,UAAU,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;gCAC7F,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;4BAC5F,CAAC;wBACL,CAAC,CAAC,CAAC;oBACP,CAAC;gBACL,CAAC;YACL,CAAC,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAED;;OAEG;IACH,IAAW,6CAA6C;QACpD,OAAO,IAAI,CAAC,8CAA8C,CAAC;IAC/D,CAAC;IAED,IAAW,6CAA6C,CAAC,KAAc;QACnE,IAAI,CAAC,8CAA8C,GAAG,KAAK,CAAC;IAChE,CAAC;IAGD;;;OAGG;IACI,wBAAwB,CAAC,UAAU,GAAG,CAAC,GAAG,GAAG;QAChD,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;IAClC,CAAC;IAED;;;;OAIG;IACa,YAAY;QACxB,OAAO,yBAAyB,CAAC;IACrC,CAAC;IAED;;;OAGG;IACa,YAAY;QACxB,KAAK,CAAC,YAAY,EAAE,CAAC;QACrB,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACxD,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC5F,CAAC;IACL,CAAC;IAED;;;OAGG;IACI,sBAAsB,CAAC,OAAa,IAAI,CAAC,CAAC;QAC7C,iEAAiE;QACjE,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC3B,OAAO;QACX,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC3B,IAAI,CAAC,kBAAkB,GAAG,IAAI,UAAU,EAAE,CAAC;QAC/C,CAAC;QAED,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACnF,MAAM,IAAI,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAE7B,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,CAAC;YAC1B,IAAI,CAAO,IAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACnB,IAAI,CAAC,kBAAmB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YACjD,CAAC;iBAAM,CAAC;gBACE,IAAI,CAAC,kBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;YACnD,CAAC;QACL,CAAC;QACD,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,CAAC;QACpC,uBAAuB;QACvB,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;IAC5F,CAAC;CACJ", "sourcesContent": ["import { FreeCamera } from \"./freeCamera\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Quaternion, Vector3 } from \"../Maths/math.vector\";\r\nimport { Node } from \"../node\";\r\n\r\nimport \"./Inputs/freeCameraDeviceOrientationInput\";\r\nimport { Axis } from \"../Maths/math.axis\";\r\n\r\nNode.AddNodeConstructor(\"DeviceOrientationCamera\", (name, scene) => {\r\n    return () => new DeviceOrientationCamera(name, Vector3.Zero(), scene);\r\n});\r\n\r\n// We're mainly based on the logic defined into the FreeCamera code\r\n/**\r\n * This is a camera specifically designed to react to device orientation events such as a modern mobile device\r\n * being tilted forward or back and left or right.\r\n */\r\nexport class DeviceOrientationCamera extends FreeCamera {\r\n    private _initialQuaternion: Quaternion;\r\n    private _quaternionCache: Quaternion;\r\n    private _tmpDragQuaternion = new Quaternion();\r\n    private _disablePointerInputWhenUsingDeviceOrientation = true;\r\n\r\n    /**\r\n     * Creates a new device orientation camera\r\n     * @param name The name of the camera\r\n     * @param position The start position camera\r\n     * @param scene The scene the camera belongs to\r\n     */\r\n    constructor(name: string, position: Vector3, scene?: Scene) {\r\n        super(name, position, scene);\r\n        this._quaternionCache = new Quaternion();\r\n        this.inputs.addDeviceOrientation();\r\n\r\n        // When the orientation sensor fires it's first event, disable mouse input\r\n        if (this.inputs._deviceOrientationInput) {\r\n            this.inputs._deviceOrientationInput._onDeviceOrientationChangedObservable.addOnce(() => {\r\n                if (this._disablePointerInputWhenUsingDeviceOrientation) {\r\n                    if (this.inputs._mouseInput) {\r\n                        this.inputs._mouseInput._allowCameraRotation = false;\r\n                        this.inputs._mouseInput.onPointerMovedObservable.add((e) => {\r\n                            if (this._dragFactor != 0) {\r\n                                if (!this._initialQuaternion) {\r\n                                    this._initialQuaternion = new Quaternion();\r\n                                }\r\n                                // Rotate the initial space around the y axis to allow users to \"turn around\" via touch/mouse\r\n                                Quaternion.FromEulerAnglesToRef(0, e.offsetX * this._dragFactor, 0, this._tmpDragQuaternion);\r\n                                this._initialQuaternion.multiplyToRef(this._tmpDragQuaternion, this._initialQuaternion);\r\n                            }\r\n                        });\r\n                    }\r\n                }\r\n            });\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating that pointer input must be disabled on first orientation sensor update (Default: true)\r\n     */\r\n    public get disablePointerInputWhenUsingDeviceOrientation() {\r\n        return this._disablePointerInputWhenUsingDeviceOrientation;\r\n    }\r\n\r\n    public set disablePointerInputWhenUsingDeviceOrientation(value: boolean) {\r\n        this._disablePointerInputWhenUsingDeviceOrientation = value;\r\n    }\r\n\r\n    private _dragFactor = 0;\r\n    /**\r\n     * Enabled turning on the y axis when the orientation sensor is active\r\n     * @param dragFactor the factor that controls the turn speed (default: 1/300)\r\n     */\r\n    public enableHorizontalDragging(dragFactor = 1 / 300) {\r\n        this._dragFactor = dragFactor;\r\n    }\r\n\r\n    /**\r\n     * Gets the current instance class name (\"DeviceOrientationCamera\").\r\n     * This helps avoiding instanceof at run time.\r\n     * @returns the class name\r\n     */\r\n    public override getClassName(): string {\r\n        return \"DeviceOrientationCamera\";\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     * Checks and applies the current values of the inputs to the camera. (Internal use only)\r\n     */\r\n    public override _checkInputs(): void {\r\n        super._checkInputs();\r\n        this._quaternionCache.copyFrom(this.rotationQuaternion);\r\n        if (this._initialQuaternion) {\r\n            this._initialQuaternion.multiplyToRef(this.rotationQuaternion, this.rotationQuaternion);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Reset the camera to its default orientation on the specified axis only.\r\n     * @param axis The axis to reset\r\n     */\r\n    public resetToCurrentRotation(axis: Axis = Axis.Y): void {\r\n        //can only work if this camera has a rotation quaternion already.\r\n        if (!this.rotationQuaternion) {\r\n            return;\r\n        }\r\n\r\n        if (!this._initialQuaternion) {\r\n            this._initialQuaternion = new Quaternion();\r\n        }\r\n\r\n        this._initialQuaternion.copyFrom(this._quaternionCache || this.rotationQuaternion);\r\n        const list = [\"x\", \"y\", \"z\"];\r\n\r\n        for (const axisName of list) {\r\n            if (!(<any>axis)[axisName]) {\r\n                (<any>this._initialQuaternion)[axisName] = 0;\r\n            } else {\r\n                (<any>this._initialQuaternion)[axisName] *= -1;\r\n            }\r\n        }\r\n        this._initialQuaternion.normalize();\r\n        //force rotation update\r\n        this._initialQuaternion.multiplyToRef(this.rotationQuaternion, this.rotationQuaternion);\r\n    }\r\n}\r\n"]}