{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Culling/boundingBox.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Culling/boundingBox.ts"], "sourcesContent": ["import type { DeepImmutable, Nullable } from \"../types\";\r\nimport { BuildArray } from \"../Misc/arrayTools\";\r\nimport { Matrix, Vector3 } from \"../Maths/math.vector\";\r\nimport type { BoundingSphere } from \"../Culling/boundingSphere\";\r\n\r\nimport type { ICullable } from \"./boundingInfo\";\r\nimport { Epsilon } from \"../Maths/math.constants\";\r\nimport type { Plane } from \"../Maths/math.plane\";\r\n\r\nimport type { DrawWrapper } from \"../Materials/drawWrapper\";\r\n\r\n/**\r\n * Class used to store bounding box information\r\n */\r\nexport class BoundingBox implements ICullable {\r\n    /**\r\n     * Gets the 8 vectors representing the bounding box in local space\r\n     */\r\n    public readonly vectors: Vector3[] = BuildArray(8, Vector3.Zero);\r\n    /**\r\n     * Gets the center of the bounding box in local space\r\n     */\r\n    public readonly center: Vector3 = Vector3.Zero();\r\n    /**\r\n     * Gets the center of the bounding box in world space\r\n     */\r\n    public readonly centerWorld: Vector3 = Vector3.Zero();\r\n    /**\r\n     * Gets half the size of the extent in local space. Multiply by 2 to obtain the full size of the box!\r\n     */\r\n    public readonly extendSize: Vector3 = Vector3.Zero();\r\n    /**\r\n     * Gets half the size of the extent in world space. Multiply by 2 to obtain the full size of the box!\r\n     */\r\n    public readonly extendSizeWorld: Vector3 = Vector3.Zero();\r\n    /**\r\n     * Gets the OBB (object bounding box) directions\r\n     */\r\n    public readonly directions: Vector3[] = BuildArray(3, Vector3.Zero);\r\n    /**\r\n     * Gets the 8 vectors representing the bounding box in world space\r\n     */\r\n    public readonly vectorsWorld: Vector3[] = BuildArray(8, Vector3.Zero);\r\n    /**\r\n     * Gets the minimum vector in world space\r\n     */\r\n    public readonly minimumWorld: Vector3 = Vector3.Zero();\r\n    /**\r\n     * Gets the maximum vector in world space\r\n     */\r\n    public readonly maximumWorld: Vector3 = Vector3.Zero();\r\n    /**\r\n     * Gets the minimum vector in local space\r\n     */\r\n    public readonly minimum: Vector3 = Vector3.Zero();\r\n    /**\r\n     * Gets the maximum vector in local space\r\n     */\r\n    public readonly maximum: Vector3 = Vector3.Zero();\r\n\r\n    private _worldMatrix: DeepImmutable<Matrix>;\r\n    private static readonly _TmpVector3 = BuildArray(3, Vector3.Zero);\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _tag: number;\r\n\r\n    /** @internal */\r\n    public _drawWrapperFront: Nullable<DrawWrapper> = null;\r\n    /** @internal */\r\n    public _drawWrapperBack: Nullable<DrawWrapper> = null;\r\n\r\n    /**\r\n     * Creates a new bounding box\r\n     * @param min defines the minimum vector (in local space)\r\n     * @param max defines the maximum vector (in local space)\r\n     * @param worldMatrix defines the new world matrix\r\n     */\r\n    constructor(min: DeepImmutable<Vector3>, max: DeepImmutable<Vector3>, worldMatrix?: DeepImmutable<Matrix>) {\r\n        this.reConstruct(min, max, worldMatrix);\r\n    }\r\n\r\n    // Methods\r\n\r\n    /**\r\n     * Recreates the entire bounding box from scratch as if we call the constructor in place\r\n     * @param min defines the new minimum vector (in local space)\r\n     * @param max defines the new maximum vector (in local space)\r\n     * @param worldMatrix defines the new world matrix\r\n     */\r\n    public reConstruct(min: DeepImmutable<Vector3>, max: DeepImmutable<Vector3>, worldMatrix?: DeepImmutable<Matrix>) {\r\n        const minX = min.x,\r\n            minY = min.y,\r\n            minZ = min.z,\r\n            maxX = max.x,\r\n            maxY = max.y,\r\n            maxZ = max.z;\r\n        const vectors = this.vectors;\r\n\r\n        this.minimum.copyFromFloats(minX, minY, minZ);\r\n        this.maximum.copyFromFloats(maxX, maxY, maxZ);\r\n        vectors[0].copyFromFloats(minX, minY, minZ);\r\n        vectors[1].copyFromFloats(maxX, maxY, maxZ);\r\n        vectors[2].copyFromFloats(maxX, minY, minZ);\r\n        vectors[3].copyFromFloats(minX, maxY, minZ);\r\n        vectors[4].copyFromFloats(minX, minY, maxZ);\r\n        vectors[5].copyFromFloats(maxX, maxY, minZ);\r\n        vectors[6].copyFromFloats(minX, maxY, maxZ);\r\n        vectors[7].copyFromFloats(maxX, minY, maxZ);\r\n\r\n        // OBB\r\n        max.addToRef(min, this.center).scaleInPlace(0.5);\r\n        max.subtractToRef(min, this.extendSize).scaleInPlace(0.5);\r\n\r\n        this._worldMatrix = worldMatrix || Matrix.IdentityReadOnly;\r\n\r\n        this._update(this._worldMatrix);\r\n    }\r\n\r\n    /**\r\n     * Scale the current bounding box by applying a scale factor\r\n     * @param factor defines the scale factor to apply\r\n     * @returns the current bounding box\r\n     */\r\n    public scale(factor: number): BoundingBox {\r\n        const tmpVectors = BoundingBox._TmpVector3;\r\n        const diff = this.maximum.subtractToRef(this.minimum, tmpVectors[0]);\r\n        const len = diff.length();\r\n        diff.normalizeFromLength(len);\r\n        const distance = len * factor;\r\n        const newRadius = diff.scaleInPlace(distance * 0.5);\r\n\r\n        const min = this.center.subtractToRef(newRadius, tmpVectors[1]);\r\n        const max = this.center.addToRef(newRadius, tmpVectors[2]);\r\n\r\n        this.reConstruct(min, max, this._worldMatrix);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Gets the world matrix of the bounding box\r\n     * @returns a matrix\r\n     */\r\n    public getWorldMatrix(): DeepImmutable<Matrix> {\r\n        return this._worldMatrix;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _update(world: DeepImmutable<Matrix>): void {\r\n        const minWorld = this.minimumWorld;\r\n        const maxWorld = this.maximumWorld;\r\n        const directions = this.directions;\r\n        const vectorsWorld = this.vectorsWorld;\r\n        const vectors = this.vectors;\r\n\r\n        if (!world.isIdentity()) {\r\n            minWorld.setAll(Number.MAX_VALUE);\r\n            maxWorld.setAll(-Number.MAX_VALUE);\r\n\r\n            for (let index = 0; index < 8; ++index) {\r\n                const v = vectorsWorld[index];\r\n                Vector3.TransformCoordinatesToRef(vectors[index], world, v);\r\n                minWorld.minimizeInPlace(v);\r\n                maxWorld.maximizeInPlace(v);\r\n            }\r\n\r\n            // Extend\r\n            maxWorld.subtractToRef(minWorld, this.extendSizeWorld).scaleInPlace(0.5);\r\n            maxWorld.addToRef(minWorld, this.centerWorld).scaleInPlace(0.5);\r\n        } else {\r\n            minWorld.copyFrom(this.minimum);\r\n            maxWorld.copyFrom(this.maximum);\r\n            for (let index = 0; index < 8; ++index) {\r\n                vectorsWorld[index].copyFrom(vectors[index]);\r\n            }\r\n\r\n            // Extend\r\n            this.extendSizeWorld.copyFrom(this.extendSize);\r\n            this.centerWorld.copyFrom(this.center);\r\n        }\r\n\r\n        Vector3.FromArrayToRef(world.m, 0, directions[0]);\r\n        Vector3.FromArrayToRef(world.m, 4, directions[1]);\r\n        Vector3.FromArrayToRef(world.m, 8, directions[2]);\r\n\r\n        this._worldMatrix = world;\r\n    }\r\n\r\n    /**\r\n     * Tests if the bounding box is intersecting the frustum planes\r\n     * @param frustumPlanes defines the frustum planes to test\r\n     * @returns true if there is an intersection\r\n     */\r\n    public isInFrustum(frustumPlanes: Array<DeepImmutable<Plane>>): boolean {\r\n        return BoundingBox.IsInFrustum(this.vectorsWorld, frustumPlanes);\r\n    }\r\n\r\n    /**\r\n     * Tests if the bounding box is entirely inside the frustum planes\r\n     * @param frustumPlanes defines the frustum planes to test\r\n     * @returns true if there is an inclusion\r\n     */\r\n    public isCompletelyInFrustum(frustumPlanes: Array<DeepImmutable<Plane>>): boolean {\r\n        return BoundingBox.IsCompletelyInFrustum(this.vectorsWorld, frustumPlanes);\r\n    }\r\n\r\n    /**\r\n     * Tests if a point is inside the bounding box\r\n     * @param point defines the point to test\r\n     * @returns true if the point is inside the bounding box\r\n     */\r\n    public intersectsPoint(point: DeepImmutable<Vector3>): boolean {\r\n        const min = this.minimumWorld;\r\n        const max = this.maximumWorld;\r\n        const minX = min.x,\r\n            minY = min.y,\r\n            minZ = min.z,\r\n            maxX = max.x,\r\n            maxY = max.y,\r\n            maxZ = max.z;\r\n        const pointX = point.x,\r\n            pointY = point.y,\r\n            pointZ = point.z;\r\n        const delta = -Epsilon;\r\n\r\n        if (maxX - pointX < delta || delta > pointX - minX) {\r\n            return false;\r\n        }\r\n\r\n        if (maxY - pointY < delta || delta > pointY - minY) {\r\n            return false;\r\n        }\r\n\r\n        if (maxZ - pointZ < delta || delta > pointZ - minZ) {\r\n            return false;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Tests if the bounding box intersects with a bounding sphere\r\n     * @param sphere defines the sphere to test\r\n     * @returns true if there is an intersection\r\n     */\r\n    public intersectsSphere(sphere: DeepImmutable<BoundingSphere>): boolean {\r\n        return BoundingBox.IntersectsSphere(this.minimumWorld, this.maximumWorld, sphere.centerWorld, sphere.radiusWorld);\r\n    }\r\n\r\n    /**\r\n     * Tests if the bounding box intersects with a box defined by a min and max vectors\r\n     * @param min defines the min vector to use\r\n     * @param max defines the max vector to use\r\n     * @returns true if there is an intersection\r\n     */\r\n    public intersectsMinMax(min: DeepImmutable<Vector3>, max: DeepImmutable<Vector3>): boolean {\r\n        const myMin = this.minimumWorld;\r\n        const myMax = this.maximumWorld;\r\n        const myMinX = myMin.x,\r\n            myMinY = myMin.y,\r\n            myMinZ = myMin.z,\r\n            myMaxX = myMax.x,\r\n            myMaxY = myMax.y,\r\n            myMaxZ = myMax.z;\r\n        const minX = min.x,\r\n            minY = min.y,\r\n            minZ = min.z,\r\n            maxX = max.x,\r\n            maxY = max.y,\r\n            maxZ = max.z;\r\n        if (myMaxX < minX || myMinX > maxX) {\r\n            return false;\r\n        }\r\n\r\n        if (myMaxY < minY || myMinY > maxY) {\r\n            return false;\r\n        }\r\n\r\n        if (myMaxZ < minZ || myMinZ > maxZ) {\r\n            return false;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Disposes the resources of the class\r\n     */\r\n    public dispose(): void {\r\n        this._drawWrapperFront?.dispose();\r\n        this._drawWrapperBack?.dispose();\r\n    }\r\n\r\n    // Statics\r\n\r\n    /**\r\n     * Tests if two bounding boxes are intersections\r\n     * @param box0 defines the first box to test\r\n     * @param box1 defines the second box to test\r\n     * @returns true if there is an intersection\r\n     */\r\n    public static Intersects(box0: DeepImmutable<BoundingBox>, box1: DeepImmutable<BoundingBox>): boolean {\r\n        return box0.intersectsMinMax(box1.minimumWorld, box1.maximumWorld);\r\n    }\r\n\r\n    /**\r\n     * Tests if a bounding box defines by a min/max vectors intersects a sphere\r\n     * @param minPoint defines the minimum vector of the bounding box\r\n     * @param maxPoint defines the maximum vector of the bounding box\r\n     * @param sphereCenter defines the sphere center\r\n     * @param sphereRadius defines the sphere radius\r\n     * @returns true if there is an intersection\r\n     */\r\n    public static IntersectsSphere(minPoint: DeepImmutable<Vector3>, maxPoint: DeepImmutable<Vector3>, sphereCenter: DeepImmutable<Vector3>, sphereRadius: number): boolean {\r\n        const vector = BoundingBox._TmpVector3[0];\r\n        Vector3.ClampToRef(sphereCenter, minPoint, maxPoint, vector);\r\n        const num = Vector3.DistanceSquared(sphereCenter, vector);\r\n        return num <= sphereRadius * sphereRadius;\r\n    }\r\n\r\n    /**\r\n     * Tests if a bounding box defined with 8 vectors is entirely inside frustum planes\r\n     * @param boundingVectors defines an array of 8 vectors representing a bounding box\r\n     * @param frustumPlanes defines the frustum planes to test\r\n     * @returns true if there is an inclusion\r\n     */\r\n    public static IsCompletelyInFrustum(boundingVectors: Array<DeepImmutable<Vector3>>, frustumPlanes: Array<DeepImmutable<Plane>>): boolean {\r\n        for (let p = 0; p < 6; ++p) {\r\n            const frustumPlane = frustumPlanes[p];\r\n            for (let i = 0; i < 8; ++i) {\r\n                if (frustumPlane.dotCoordinate(boundingVectors[i]) < 0) {\r\n                    return false;\r\n                }\r\n            }\r\n        }\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Tests if a bounding box defined with 8 vectors intersects frustum planes\r\n     * @param boundingVectors defines an array of 8 vectors representing a bounding box\r\n     * @param frustumPlanes defines the frustum planes to test\r\n     * @returns true if there is an intersection\r\n     */\r\n    public static IsInFrustum(boundingVectors: Array<DeepImmutable<Vector3>>, frustumPlanes: Array<DeepImmutable<Plane>>): boolean {\r\n        for (let p = 0; p < 6; ++p) {\r\n            let canReturnFalse = true;\r\n            const frustumPlane = frustumPlanes[p];\r\n            for (let i = 0; i < 8; ++i) {\r\n                if (frustumPlane.dotCoordinate(boundingVectors[i]) >= 0) {\r\n                    canReturnFalse = false;\r\n                    break;\r\n                }\r\n            }\r\n            if (canReturnFalse) {\r\n                return false;\r\n            }\r\n        }\r\n        return true;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AACA,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAIvD,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;;;;AAQ5C,MAAO,WAAW;IAqEpB,UAAU;IAEV;;;;;OAKG,CACI,WAAW,CAAC,GAA2B,EAAE,GAA2B,EAAE,WAAmC,EAAA;QAC5G,MAAM,IAAI,GAAG,GAAG,CAAC,CAAC,EACd,IAAI,GAAG,GAAG,CAAC,CAAC,EACZ,IAAI,GAAG,GAAG,CAAC,CAAC,EACZ,IAAI,GAAG,GAAG,CAAC,CAAC,EACZ,IAAI,GAAG,GAAG,CAAC,CAAC,EACZ,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC;QACjB,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAE7B,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAC9C,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAC9C,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAC5C,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAC5C,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAC5C,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAC5C,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAC5C,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAC5C,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAC5C,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAE5C,MAAM;QACN,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QACjD,GAAG,CAAC,aAAa,CAAC,GAAG,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QAE1D,IAAI,CAAC,YAAY,GAAG,WAAW,sKAAI,SAAM,CAAC,gBAAgB,CAAC;QAE3D,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACpC,CAAC;IAED;;;;OAIG,CACI,KAAK,CAAC,MAAc,EAAA;QACvB,MAAM,UAAU,GAAG,WAAW,CAAC,WAAW,CAAC;QAC3C,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QACrE,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QAC1B,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;QAC9B,MAAM,QAAQ,GAAG,GAAG,GAAG,MAAM,CAAC;QAC9B,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,GAAG,GAAG,CAAC,CAAC;QAEpD,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAChE,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAE3D,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAE9C,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG,CACI,cAAc,GAAA;QACjB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;OAEG,CACI,OAAO,CAAC,KAA4B,EAAA;QACvC,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC;QACnC,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC;QACnC,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACnC,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QACvC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAE7B,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC;YACtB,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAClC,QAAQ,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAEnC,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,EAAE,KAAK,CAAE,CAAC;gBACrC,MAAM,CAAC,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;kLAC9B,UAAO,CAAC,yBAAyB,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;gBAC5D,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;gBAC5B,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC;YAED,SAAS;YACT,QAAQ,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;YACzE,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QACpE,CAAC,MAAM,CAAC;YACJ,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAChC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAChC,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,EAAE,KAAK,CAAE,CAAC;gBACrC,YAAY,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;YACjD,CAAC;YAED,SAAS;YACT,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC/C,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC3C,CAAC;0KAED,UAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;0KAClD,UAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;0KAClD,UAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAElD,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;IAC9B,CAAC;IAED;;;;OAIG,CACI,WAAW,CAAC,aAA0C,EAAA;QACzD,OAAO,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC;IACrE,CAAC;IAED;;;;OAIG,CACI,qBAAqB,CAAC,aAA0C,EAAA;QACnE,OAAO,WAAW,CAAC,qBAAqB,CAAC,IAAI,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC;IAC/E,CAAC;IAED;;;;OAIG,CACI,eAAe,CAAC,KAA6B,EAAA;QAChD,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC;QAC9B,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC;QAC9B,MAAM,IAAI,GAAG,GAAG,CAAC,CAAC,EACd,IAAI,GAAG,GAAG,CAAC,CAAC,EACZ,IAAI,GAAG,GAAG,CAAC,CAAC,EACZ,IAAI,GAAG,GAAG,CAAC,CAAC,EACZ,IAAI,GAAG,GAAG,CAAC,CAAC,EACZ,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC;QACjB,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,EAClB,MAAM,GAAG,KAAK,CAAC,CAAC,EAChB,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC;QACrB,MAAM,KAAK,GAAG,sKAAC,UAAO,CAAC;QAEvB,IAAI,IAAI,GAAG,MAAM,GAAG,KAAK,IAAI,KAAK,GAAG,MAAM,GAAG,IAAI,EAAE,CAAC;YACjD,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,GAAG,MAAM,GAAG,KAAK,IAAI,KAAK,GAAG,MAAM,GAAG,IAAI,EAAE,CAAC;YACjD,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,GAAG,MAAM,GAAG,KAAK,IAAI,KAAK,GAAG,MAAM,GAAG,IAAI,EAAE,CAAC;YACjD,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,gBAAgB,CAAC,MAAqC,EAAA;QACzD,OAAO,WAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC;IACtH,CAAC;IAED;;;;;OAKG,CACI,gBAAgB,CAAC,GAA2B,EAAE,GAA2B,EAAA;QAC5E,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC;QAChC,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC;QAChC,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,EAClB,MAAM,GAAG,KAAK,CAAC,CAAC,EAChB,MAAM,GAAG,KAAK,CAAC,CAAC,EAChB,MAAM,GAAG,KAAK,CAAC,CAAC,EAChB,MAAM,GAAG,KAAK,CAAC,CAAC,EAChB,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC;QACrB,MAAM,IAAI,GAAG,GAAG,CAAC,CAAC,EACd,IAAI,GAAG,GAAG,CAAC,CAAC,EACZ,IAAI,GAAG,GAAG,CAAC,CAAC,EACZ,IAAI,GAAG,GAAG,CAAC,CAAC,EACZ,IAAI,GAAG,GAAG,CAAC,CAAC,EACZ,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC;QACjB,IAAI,MAAM,GAAG,IAAI,IAAI,MAAM,GAAG,IAAI,EAAE,CAAC;YACjC,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,MAAM,GAAG,IAAI,IAAI,MAAM,GAAG,IAAI,EAAE,CAAC;YACjC,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,MAAM,GAAG,IAAI,IAAI,MAAM,GAAG,IAAI,EAAE,CAAC;YACjC,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG,CACI,OAAO,GAAA;;uCACN,CAAC,iBAAiB,cAAtB,sEAAwB,OAAO,EAAE,CAAC;sCAC9B,CAAC,gBAAgB,2DAArB,uBAAuB,OAAO,EAAE,CAAC;IACrC,CAAC;IAED,UAAU;IAEV;;;;;OAKG,CACI,MAAM,CAAC,UAAU,CAAC,IAAgC,EAAE,IAAgC,EAAA;QACvF,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;IACvE,CAAC;IAED;;;;;;;OAOG,CACI,MAAM,CAAC,gBAAgB,CAAC,QAAgC,EAAE,QAAgC,EAAE,YAAoC,EAAE,YAAoB,EAAA;QACzJ,MAAM,MAAM,GAAG,WAAW,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;0KAC1C,UAAO,CAAC,UAAU,CAAC,YAAY,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;QAC7D,MAAM,GAAG,qKAAG,UAAO,CAAC,eAAe,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;QAC1D,OAAO,GAAG,IAAI,YAAY,GAAG,YAAY,CAAC;IAC9C,CAAC;IAED;;;;;OAKG,CACI,MAAM,CAAC,qBAAqB,CAAC,eAA8C,EAAE,aAA0C,EAAA;QAC1H,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAE,CAAC;YACzB,MAAM,YAAY,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;YACtC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAE,CAAC;gBACzB,IAAI,YAAY,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;oBACrD,OAAO,KAAK,CAAC;gBACjB,CAAC;YACL,CAAC;QACL,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG,CACI,MAAM,CAAC,WAAW,CAAC,eAA8C,EAAE,aAA0C,EAAA;QAChH,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAE,CAAC;YACzB,IAAI,cAAc,GAAG,IAAI,CAAC;YAC1B,MAAM,YAAY,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;YACtC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAE,CAAC;gBACzB,IAAI,YAAY,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;oBACtD,cAAc,GAAG,KAAK,CAAC;oBACvB,MAAM;gBACV,CAAC;YACL,CAAC;YACD,IAAI,cAAc,EAAE,CAAC;gBACjB,OAAO,KAAK,CAAC;YACjB,CAAC;QACL,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAlSD;;;;;OAKG,CACH,YAAY,GAA2B,EAAE,GAA2B,EAAE,WAAmC,CAAA;QAhEzG;;WAEG,CACa,IAAA,CAAA,OAAO,oKAAc,aAAA,AAAU,EAAC,CAAC,EAAE,4KAAO,CAAC,IAAI,CAAC,CAAC;QACjE;;WAEG,CACa,IAAA,CAAA,MAAM,qKAAY,UAAO,CAAC,IAAI,EAAE,CAAC;QACjD;;WAEG,CACa,IAAA,CAAA,WAAW,GAAY,4KAAO,CAAC,IAAI,EAAE,CAAC;QACtD;;WAEG,CACa,IAAA,CAAA,UAAU,qKAAY,UAAO,CAAC,IAAI,EAAE,CAAC;QACrD;;WAEG,CACa,IAAA,CAAA,eAAe,qKAAY,UAAO,CAAC,IAAI,EAAE,CAAC;QAC1D;;WAEG,CACa,IAAA,CAAA,UAAU,IAAc,6KAAA,AAAU,EAAC,CAAC,oKAAE,UAAO,CAAC,IAAI,CAAC,CAAC;QACpE;;WAEG,CACa,IAAA,CAAA,YAAY,OAAc,0KAAA,AAAU,EAAC,CAAC,oKAAE,UAAO,CAAC,IAAI,CAAC,CAAC;QACtE;;WAEG,CACa,IAAA,CAAA,YAAY,qKAAY,UAAO,CAAC,IAAI,EAAE,CAAC;QACvD;;WAEG,CACa,IAAA,CAAA,YAAY,qKAAY,UAAO,CAAC,IAAI,EAAE,CAAC;QACvD;;WAEG,CACa,IAAA,CAAA,OAAO,oKAAY,WAAO,CAAC,IAAI,EAAE,CAAC;QAClD;;WAEG,CACa,IAAA,CAAA,OAAO,qKAAY,UAAO,CAAC,IAAI,EAAE,CAAC;QAUlD,cAAA,EAAgB,CACT,IAAA,CAAA,iBAAiB,GAA0B,IAAI,CAAC;QACvD,cAAA,EAAgB,CACT,IAAA,CAAA,gBAAgB,GAA0B,IAAI,CAAC;QASlD,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,EAAE,WAAW,CAAC,CAAC;IAC5C,CAAC;;AApBuB,YAAA,WAAW,oKAAG,aAAA,AAAU,EAAC,CAAC,oKAAE,UAAO,CAAC,IAAI,CAAC,AAA9B,CAA+B", "debugId": null}}, {"offset": {"line": 275, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Culling/boundingSphere.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Culling/boundingSphere.ts"], "sourcesContent": ["import type { DeepImmutable } from \"../types\";\r\nimport { <PERSON><PERSON><PERSON>rray } from \"../Misc/arrayTools\";\r\nimport { Matrix, Vector3 } from \"../Maths/math.vector\";\r\nimport type { Plane } from \"../Maths/math.plane\";\r\n\r\n/**\r\n * Class used to store bounding sphere information\r\n */\r\nexport class BoundingSphere {\r\n    /**\r\n     * Gets the center of the bounding sphere in local space\r\n     */\r\n    public readonly center = Vector3.Zero();\r\n    /**\r\n     * Radius of the bounding sphere in local space\r\n     */\r\n    public radius: number;\r\n    /**\r\n     * Gets the center of the bounding sphere in world space\r\n     */\r\n    public readonly centerWorld = Vector3.Zero();\r\n    /**\r\n     * Radius of the bounding sphere in world space\r\n     */\r\n    public radiusWorld: number;\r\n    /**\r\n     * Gets the minimum vector in local space\r\n     */\r\n    public readonly minimum = Vector3.Zero();\r\n    /**\r\n     * Gets the maximum vector in local space\r\n     */\r\n    public readonly maximum = Vector3.Zero();\r\n\r\n    private _worldMatrix: DeepImmutable<Matrix>;\r\n    private static readonly _TmpVector3 = BuildArray(3, Vector3.Zero);\r\n\r\n    /**\r\n     * Creates a new bounding sphere\r\n     * @param min defines the minimum vector (in local space)\r\n     * @param max defines the maximum vector (in local space)\r\n     * @param worldMatrix defines the new world matrix\r\n     */\r\n    constructor(min: DeepImmutable<Vector3>, max: DeepImmutable<Vector3>, worldMatrix?: DeepImmutable<Matrix>) {\r\n        this.reConstruct(min, max, worldMatrix);\r\n    }\r\n\r\n    /**\r\n     * Recreates the entire bounding sphere from scratch as if we call the constructor in place\r\n     * @param min defines the new minimum vector (in local space)\r\n     * @param max defines the new maximum vector (in local space)\r\n     * @param worldMatrix defines the new world matrix\r\n     */\r\n    public reConstruct(min: DeepImmutable<Vector3>, max: DeepImmutable<Vector3>, worldMatrix?: DeepImmutable<Matrix>) {\r\n        this.minimum.copyFrom(min);\r\n        this.maximum.copyFrom(max);\r\n\r\n        const distance = Vector3.Distance(min, max);\r\n\r\n        max.addToRef(min, this.center).scaleInPlace(0.5);\r\n        this.radius = distance * 0.5;\r\n\r\n        this._update(worldMatrix || Matrix.IdentityReadOnly);\r\n    }\r\n\r\n    /**\r\n     * Scale the current bounding sphere by applying a scale factor\r\n     * @param factor defines the scale factor to apply\r\n     * @returns the current bounding box\r\n     */\r\n    public scale(factor: number): BoundingSphere {\r\n        const newRadius = this.radius * factor;\r\n        const tmpVectors = BoundingSphere._TmpVector3;\r\n        const tempRadiusVector = tmpVectors[0].setAll(newRadius);\r\n        const min = this.center.subtractToRef(tempRadiusVector, tmpVectors[1]);\r\n        const max = this.center.addToRef(tempRadiusVector, tmpVectors[2]);\r\n\r\n        this.reConstruct(min, max, this._worldMatrix);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Gets the world matrix of the bounding box\r\n     * @returns a matrix\r\n     */\r\n    public getWorldMatrix(): DeepImmutable<Matrix> {\r\n        return this._worldMatrix;\r\n    }\r\n\r\n    // Methods\r\n    /**\r\n     * @internal\r\n     */\r\n    public _update(worldMatrix: DeepImmutable<Matrix>): void {\r\n        if (!worldMatrix.isIdentity()) {\r\n            Vector3.TransformCoordinatesToRef(this.center, worldMatrix, this.centerWorld);\r\n            const tempVector = BoundingSphere._TmpVector3[0];\r\n            Vector3.TransformNormalFromFloatsToRef(1.0, 1.0, 1.0, worldMatrix, tempVector);\r\n            this.radiusWorld = Math.max(Math.abs(tempVector.x), Math.abs(tempVector.y), Math.abs(tempVector.z)) * this.radius;\r\n        } else {\r\n            this.centerWorld.copyFrom(this.center);\r\n            this.radiusWorld = this.radius;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Tests if the bounding sphere is intersecting the frustum planes\r\n     * @param frustumPlanes defines the frustum planes to test\r\n     * @returns true if there is an intersection\r\n     */\r\n    public isInFrustum(frustumPlanes: Array<DeepImmutable<Plane>>): boolean {\r\n        const center = this.centerWorld;\r\n        const radius = this.radiusWorld;\r\n        for (let i = 0; i < 6; i++) {\r\n            if (frustumPlanes[i].dotCoordinate(center) <= -radius) {\r\n                return false;\r\n            }\r\n        }\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Tests if the bounding sphere center is in between the frustum planes.\r\n     * Used for optimistic fast inclusion.\r\n     * @param frustumPlanes defines the frustum planes to test\r\n     * @returns true if the sphere center is in between the frustum planes\r\n     */\r\n    public isCenterInFrustum(frustumPlanes: Array<DeepImmutable<Plane>>): boolean {\r\n        const center = this.centerWorld;\r\n        for (let i = 0; i < 6; i++) {\r\n            if (frustumPlanes[i].dotCoordinate(center) < 0) {\r\n                return false;\r\n            }\r\n        }\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Tests if a point is inside the bounding sphere\r\n     * @param point defines the point to test\r\n     * @returns true if the point is inside the bounding sphere\r\n     */\r\n    public intersectsPoint(point: DeepImmutable<Vector3>): boolean {\r\n        const squareDistance = Vector3.DistanceSquared(this.centerWorld, point);\r\n        if (this.radiusWorld * this.radiusWorld < squareDistance) {\r\n            return false;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    // Statics\r\n    /**\r\n     * Checks if two sphere intersect\r\n     * @param sphere0 sphere 0\r\n     * @param sphere1 sphere 1\r\n     * @returns true if the spheres intersect\r\n     */\r\n    public static Intersects(sphere0: DeepImmutable<BoundingSphere>, sphere1: DeepImmutable<BoundingSphere>): boolean {\r\n        const squareDistance = Vector3.DistanceSquared(sphere0.centerWorld, sphere1.centerWorld);\r\n        const radiusSum = sphere0.radiusWorld + sphere1.radiusWorld;\r\n\r\n        if (radiusSum * radiusSum < squareDistance) {\r\n            return false;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Creates a sphere from a center and a radius\r\n     * @param center The center\r\n     * @param radius radius\r\n     * @param matrix Optional worldMatrix\r\n     * @returns The sphere\r\n     */\r\n    public static CreateFromCenterAndRadius(center: DeepImmutable<Vector3>, radius: number, matrix?: DeepImmutable<Matrix>): BoundingSphere {\r\n        this._TmpVector3[0].copyFrom(center);\r\n        this._TmpVector3[1].copyFromFloats(0, 0, radius);\r\n        this._TmpVector3[2].copyFrom(center);\r\n        this._TmpVector3[0].addInPlace(this._TmpVector3[1]);\r\n        this._TmpVector3[2].subtractInPlace(this._TmpVector3[1]);\r\n\r\n        const sphere = new BoundingSphere(this._TmpVector3[0], this._TmpVector3[2]);\r\n\r\n        if (matrix) {\r\n            sphere._worldMatrix = matrix;\r\n        } else {\r\n            sphere._worldMatrix = Matrix.Identity();\r\n        }\r\n\r\n        return sphere;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AACA,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;;;AAMjD,MAAO,cAAc;IAuCvB;;;;;OAKG,CACI,WAAW,CAAC,GAA2B,EAAE,GAA2B,EAAE,WAAmC,EAAA;QAC5G,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAE3B,MAAM,QAAQ,GAAG,4KAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QAE5C,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QACjD,IAAI,CAAC,MAAM,GAAG,QAAQ,GAAG,GAAG,CAAC;QAE7B,IAAI,CAAC,OAAO,CAAC,WAAW,sKAAI,SAAM,CAAC,gBAAgB,CAAC,CAAC;IACzD,CAAC;IAED;;;;OAIG,CACI,KAAK,CAAC,MAAc,EAAA;QACvB,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACvC,MAAM,UAAU,GAAG,cAAc,CAAC,WAAW,CAAC;QAC9C,MAAM,gBAAgB,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACzD,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,gBAAgB,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QACvE,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,gBAAgB,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAElE,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAE9C,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG,CACI,cAAc,GAAA;QACjB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED,UAAU;IACV;;OAEG,CACI,OAAO,CAAC,WAAkC,EAAA;QAC7C,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,EAAE,CAAC;YAC5B,4KAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;YAC9E,MAAM,UAAU,GAAG,cAAc,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;8KACjD,UAAO,CAAC,8BAA8B,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;YAC/E,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;QACtH,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACvC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC;QACnC,CAAC;IACL,CAAC;IAED;;;;OAIG,CACI,WAAW,CAAC,aAA0C,EAAA;QACzD,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;QAChC,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;QAChC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;YACzB,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACpD,OAAO,KAAK,CAAC;YACjB,CAAC;QACL,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG,CACI,iBAAiB,CAAC,aAA0C,EAAA;QAC/D,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;QAChC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;YACzB,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC7C,OAAO,KAAK,CAAC;YACjB,CAAC;QACL,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,eAAe,CAAC,KAA6B,EAAA;QAChD,MAAM,cAAc,qKAAG,UAAO,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;QACxE,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,GAAG,cAAc,EAAE,CAAC;YACvD,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,UAAU;IACV;;;;;OAKG,CACI,MAAM,CAAC,UAAU,CAAC,OAAsC,EAAE,OAAsC,EAAA;QACnG,MAAM,cAAc,qKAAG,UAAO,CAAC,eAAe,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC;QACzF,MAAM,SAAS,GAAG,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QAE5D,IAAI,SAAS,GAAG,SAAS,GAAG,cAAc,EAAE,CAAC;YACzC,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG,CACI,MAAM,CAAC,yBAAyB,CAAC,MAA8B,EAAE,MAAc,EAAE,MAA8B,EAAA;QAClH,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACrC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;QACjD,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACrC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;QACpD,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;QAEzD,MAAM,MAAM,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;QAE5E,IAAI,MAAM,EAAE,CAAC;YACT,MAAM,CAAC,YAAY,GAAG,MAAM,CAAC;QACjC,CAAC,MAAM,CAAC;YACJ,MAAM,CAAC,YAAY,qKAAG,SAAM,CAAC,QAAQ,EAAE,CAAC;QAC5C,CAAC;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IA5JD;;;;;OAKG,CACH,YAAY,GAA2B,EAAE,GAA2B,EAAE,WAAmC,CAAA;QAlCzG;;WAEG,CACa,IAAA,CAAA,MAAM,qKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;QAKxC;;WAEG,CACa,IAAA,CAAA,WAAW,qKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;QAK7C;;WAEG,CACa,IAAA,CAAA,OAAO,qKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;QACzC;;WAEG,CACa,IAAA,CAAA,OAAO,qKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;QAYrC,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,EAAE,WAAW,CAAC,CAAC;IAC5C,CAAC;;AAVuB,eAAA,WAAW,oKAAG,aAAA,AAAU,EAAC,CAAC,oKAAE,UAAO,CAAC,IAAI,CAAC,AAA9B,CAA+B", "debugId": null}}, {"offset": {"line": 428, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Culling/boundingInfo.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Culling/boundingInfo.ts"], "sourcesContent": ["import type { DeepImmutable } from \"../types\";\r\nimport { <PERSON><PERSON><PERSON><PERSON>y } from \"../Misc/arrayTools\";\r\nimport type { Matrix } from \"../Maths/math.vector\";\r\nimport { TmpVectors, Vector3 } from \"../Maths/math.vector\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport { BoundingBox } from \"./boundingBox\";\r\nimport { BoundingSphere } from \"./boundingSphere\";\r\nimport type { Plane } from \"../Maths/math.plane\";\r\n\r\nimport type { Collider } from \"../Collisions/collider\";\r\n\r\nconst _Result0 = { min: 0, max: 0 };\r\nconst _Result1 = { min: 0, max: 0 };\r\nconst ComputeBoxExtents = (axis: DeepImmutable<Vector3>, box: DeepImmutable<BoundingBox>, result: { min: number; max: number }) => {\r\n    const p = Vector3.Dot(box.centerWorld, axis);\r\n\r\n    const r0 = Math.abs(Vector3.Dot(box.directions[0], axis)) * box.extendSize.x;\r\n    const r1 = Math.abs(Vector3.Dot(box.directions[1], axis)) * box.extendSize.y;\r\n    const r2 = Math.abs(Vector3.Dot(box.directions[2], axis)) * box.extendSize.z;\r\n\r\n    const r = r0 + r1 + r2;\r\n    result.min = p - r;\r\n    result.max = p + r;\r\n};\r\n\r\nconst AxisOverlap = (axis: DeepImmutable<Vector3>, box0: DeepImmutable<BoundingBox>, box1: DeepImmutable<BoundingBox>): boolean => {\r\n    ComputeBoxExtents(axis, box0, _Result0);\r\n    ComputeBoxExtents(axis, box1, _Result1);\r\n    return !(_Result0.min > _Result1.max || _Result1.min > _Result0.max);\r\n};\r\n\r\n/**\r\n * Interface for cullable objects\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/materials_introduction#back-face-culling\r\n */\r\nexport interface ICullable {\r\n    /**\r\n     * Checks if the object or part of the object is in the frustum\r\n     * @param frustumPlanes Camera near/planes\r\n     * @returns true if the object is in frustum otherwise false\r\n     */\r\n    isInFrustum(frustumPlanes: Plane[]): boolean;\r\n    /**\r\n     * Checks if a cullable object (mesh...) is in the camera frustum\r\n     * Unlike isInFrustum this checks the full bounding box\r\n     * @param frustumPlanes Camera near/planes\r\n     * @returns true if the object is in frustum otherwise false\r\n     */\r\n    isCompletelyInFrustum(frustumPlanes: Plane[]): boolean;\r\n}\r\n\r\n/**\r\n * Info for a bounding data of a mesh\r\n */\r\nexport class BoundingInfo implements ICullable {\r\n    /**\r\n     * Bounding box for the mesh\r\n     */\r\n    public readonly boundingBox: BoundingBox;\r\n    /**\r\n     * Bounding sphere for the mesh\r\n     */\r\n    public readonly boundingSphere: BoundingSphere;\r\n\r\n    private _isLocked = false;\r\n\r\n    private static readonly _TmpVector3 = BuildArray(2, Vector3.Zero);\r\n\r\n    /**\r\n     * Constructs bounding info\r\n     * @param minimum min vector of the bounding box/sphere\r\n     * @param maximum max vector of the bounding box/sphere\r\n     * @param worldMatrix defines the new world matrix\r\n     */\r\n    constructor(minimum: DeepImmutable<Vector3>, maximum: DeepImmutable<Vector3>, worldMatrix?: DeepImmutable<Matrix>) {\r\n        this.boundingBox = new BoundingBox(minimum, maximum, worldMatrix);\r\n        this.boundingSphere = new BoundingSphere(minimum, maximum, worldMatrix);\r\n    }\r\n\r\n    /**\r\n     * Recreates the entire bounding info from scratch as if we call the constructor in place\r\n     * @param min defines the new minimum vector (in local space)\r\n     * @param max defines the new maximum vector (in local space)\r\n     * @param worldMatrix defines the new world matrix\r\n     */\r\n    public reConstruct(min: DeepImmutable<Vector3>, max: DeepImmutable<Vector3>, worldMatrix?: DeepImmutable<Matrix>) {\r\n        this.boundingBox.reConstruct(min, max, worldMatrix);\r\n        this.boundingSphere.reConstruct(min, max, worldMatrix);\r\n    }\r\n\r\n    /**\r\n     * min vector of the bounding box/sphere\r\n     */\r\n    public get minimum(): Vector3 {\r\n        return this.boundingBox.minimum;\r\n    }\r\n\r\n    /**\r\n     * max vector of the bounding box/sphere\r\n     */\r\n    public get maximum(): Vector3 {\r\n        return this.boundingBox.maximum;\r\n    }\r\n\r\n    /**\r\n     * If the info is locked and won't be updated to avoid perf overhead\r\n     */\r\n    public get isLocked(): boolean {\r\n        return this._isLocked;\r\n    }\r\n\r\n    public set isLocked(value: boolean) {\r\n        this._isLocked = value;\r\n    }\r\n\r\n    // Methods\r\n    /**\r\n     * Updates the bounding sphere and box\r\n     * @param world world matrix to be used to update\r\n     */\r\n    public update(world: DeepImmutable<Matrix>) {\r\n        if (this._isLocked) {\r\n            return;\r\n        }\r\n        this.boundingBox._update(world);\r\n        this.boundingSphere._update(world);\r\n    }\r\n\r\n    /**\r\n     * Recreate the bounding info to be centered around a specific point given a specific extend.\r\n     * @param center New center of the bounding info\r\n     * @param extend New extend of the bounding info\r\n     * @returns the current bounding info\r\n     */\r\n    public centerOn(center: DeepImmutable<Vector3>, extend: DeepImmutable<Vector3>): BoundingInfo {\r\n        const minimum = BoundingInfo._TmpVector3[0].copyFrom(center).subtractInPlace(extend);\r\n        const maximum = BoundingInfo._TmpVector3[1].copyFrom(center).addInPlace(extend);\r\n\r\n        this.boundingBox.reConstruct(minimum, maximum, this.boundingBox.getWorldMatrix());\r\n        this.boundingSphere.reConstruct(minimum, maximum, this.boundingBox.getWorldMatrix());\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Grows the bounding info to include the given point.\r\n     * @param point The point that will be included in the current bounding info (in local space)\r\n     * @returns the current bounding info\r\n     */\r\n    public encapsulate(point: Vector3): BoundingInfo {\r\n        const minimum = Vector3.Minimize(this.minimum, point);\r\n        const maximum = Vector3.Maximize(this.maximum, point);\r\n        this.reConstruct(minimum, maximum, this.boundingBox.getWorldMatrix());\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Grows the bounding info to encapsulate the given bounding info.\r\n     * @param toEncapsulate The bounding info that will be encapsulated in the current bounding info\r\n     * @returns the current bounding info\r\n     */\r\n    public encapsulateBoundingInfo(toEncapsulate: BoundingInfo): BoundingInfo {\r\n        const invw = TmpVectors.Matrix[0];\r\n        this.boundingBox.getWorldMatrix().invertToRef(invw);\r\n\r\n        const v = TmpVectors.Vector3[0];\r\n\r\n        Vector3.TransformCoordinatesToRef(toEncapsulate.boundingBox.minimumWorld, invw, v);\r\n        this.encapsulate(v);\r\n\r\n        Vector3.TransformCoordinatesToRef(toEncapsulate.boundingBox.maximumWorld, invw, v);\r\n        this.encapsulate(v);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Scale the current bounding info by applying a scale factor\r\n     * @param factor defines the scale factor to apply\r\n     * @returns the current bounding info\r\n     */\r\n    public scale(factor: number): BoundingInfo {\r\n        this.boundingBox.scale(factor);\r\n        this.boundingSphere.scale(factor);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Returns `true` if the bounding info is within the frustum defined by the passed array of planes.\r\n     * @param frustumPlanes defines the frustum to test\r\n     * @param strategy defines the strategy to use for the culling (default is BABYLON.AbstractMesh.CULLINGSTRATEGY_STANDARD)\r\n     * The different strategies available are:\r\n     * * BABYLON.AbstractMesh.CULLINGSTRATEGY_STANDARD most accurate but slower @see https://doc.babylonjs.com/typedoc/classes/BABYLON.AbstractMesh#CULLINGSTRATEGY_STANDARD\r\n     * * BABYLON.AbstractMesh.CULLINGSTRATEGY_BOUNDINGSPHERE_ONLY faster but less accurate @see https://doc.babylonjs.com/typedoc/classes/BABYLON.AbstractMesh#CULLINGSTRATEGY_BOUNDINGSPHERE_ONLY\r\n     * * BABYLON.AbstractMesh.CULLINGSTRATEGY_OPTIMISTIC_INCLUSION can be faster if always visible @see https://doc.babylonjs.com/typedoc/classes/BABYLON.AbstractMesh#CULLINGSTRATEGY_OPTIMISTIC_INCLUSION\r\n     * * BABYLON.AbstractMesh.CULLINGSTRATEGY_OPTIMISTIC_INCLUSION_THEN_BSPHERE_ONLY can be faster if always visible @see https://doc.babylonjs.com/typedoc/classes/BABYLON.AbstractMesh#CULLINGSTRATEGY_OPTIMISTIC_INCLUSION_THEN_BSPHERE_ONLY\r\n     * @returns true if the bounding info is in the frustum planes\r\n     */\r\n    public isInFrustum(frustumPlanes: Array<DeepImmutable<Plane>>, strategy: number = Constants.MESHES_CULLINGSTRATEGY_STANDARD): boolean {\r\n        const inclusionTest =\r\n            strategy === Constants.MESHES_CULLINGSTRATEGY_OPTIMISTIC_INCLUSION || strategy === Constants.MESHES_CULLINGSTRATEGY_OPTIMISTIC_INCLUSION_THEN_BSPHERE_ONLY;\r\n        if (inclusionTest) {\r\n            if (this.boundingSphere.isCenterInFrustum(frustumPlanes)) {\r\n                return true;\r\n            }\r\n        }\r\n\r\n        if (!this.boundingSphere.isInFrustum(frustumPlanes)) {\r\n            return false;\r\n        }\r\n\r\n        const bSphereOnlyTest =\r\n            strategy === Constants.MESHES_CULLINGSTRATEGY_BOUNDINGSPHERE_ONLY || strategy === Constants.MESHES_CULLINGSTRATEGY_OPTIMISTIC_INCLUSION_THEN_BSPHERE_ONLY;\r\n        if (bSphereOnlyTest) {\r\n            return true;\r\n        }\r\n\r\n        return this.boundingBox.isInFrustum(frustumPlanes);\r\n    }\r\n\r\n    /**\r\n     * Gets the world distance between the min and max points of the bounding box\r\n     */\r\n    public get diagonalLength(): number {\r\n        const boundingBox = this.boundingBox;\r\n        const diag = boundingBox.maximumWorld.subtractToRef(boundingBox.minimumWorld, BoundingInfo._TmpVector3[0]);\r\n        return diag.length();\r\n    }\r\n\r\n    /**\r\n     * Checks if a cullable object (mesh...) is in the camera frustum\r\n     * Unlike isInFrustum this checks the full bounding box\r\n     * @param frustumPlanes Camera near/planes\r\n     * @returns true if the object is in frustum otherwise false\r\n     */\r\n    public isCompletelyInFrustum(frustumPlanes: Array<DeepImmutable<Plane>>): boolean {\r\n        return this.boundingBox.isCompletelyInFrustum(frustumPlanes);\r\n    }\r\n    /**\r\n     * @internal\r\n     */\r\n    public _checkCollision(collider: Collider): boolean {\r\n        return collider._canDoCollision(this.boundingSphere.centerWorld, this.boundingSphere.radiusWorld, this.boundingBox.minimumWorld, this.boundingBox.maximumWorld);\r\n    }\r\n\r\n    /**\r\n     * Checks if a point is inside the bounding box and bounding sphere or the mesh\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/interactions/mesh_intersect\r\n     * @param point the point to check intersection with\r\n     * @returns if the point intersects\r\n     */\r\n    public intersectsPoint(point: DeepImmutable<Vector3>): boolean {\r\n        if (!this.boundingSphere.centerWorld) {\r\n            return false;\r\n        }\r\n\r\n        if (!this.boundingSphere.intersectsPoint(point)) {\r\n            return false;\r\n        }\r\n\r\n        if (!this.boundingBox.intersectsPoint(point)) {\r\n            return false;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Checks if another bounding info intersects the bounding box and bounding sphere or the mesh\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/interactions/mesh_intersect\r\n     * @param boundingInfo the bounding info to check intersection with\r\n     * @param precise if the intersection should be done using OBB\r\n     * @returns if the bounding info intersects\r\n     */\r\n    public intersects(boundingInfo: DeepImmutable<BoundingInfo>, precise: boolean): boolean {\r\n        if (!BoundingSphere.Intersects(this.boundingSphere, boundingInfo.boundingSphere)) {\r\n            return false;\r\n        }\r\n\r\n        if (!BoundingBox.Intersects(this.boundingBox, boundingInfo.boundingBox)) {\r\n            return false;\r\n        }\r\n\r\n        if (!precise) {\r\n            return true;\r\n        }\r\n\r\n        const box0 = this.boundingBox;\r\n        const box1 = boundingInfo.boundingBox;\r\n\r\n        if (!AxisOverlap(box0.directions[0], box0, box1)) {\r\n            return false;\r\n        }\r\n        if (!AxisOverlap(box0.directions[1], box0, box1)) {\r\n            return false;\r\n        }\r\n        if (!AxisOverlap(box0.directions[2], box0, box1)) {\r\n            return false;\r\n        }\r\n        if (!AxisOverlap(box1.directions[0], box0, box1)) {\r\n            return false;\r\n        }\r\n        if (!AxisOverlap(box1.directions[1], box0, box1)) {\r\n            return false;\r\n        }\r\n        if (!AxisOverlap(box1.directions[2], box0, box1)) {\r\n            return false;\r\n        }\r\n        if (!AxisOverlap(Vector3.Cross(box0.directions[0], box1.directions[0]), box0, box1)) {\r\n            return false;\r\n        }\r\n        if (!AxisOverlap(Vector3.Cross(box0.directions[0], box1.directions[1]), box0, box1)) {\r\n            return false;\r\n        }\r\n        if (!AxisOverlap(Vector3.Cross(box0.directions[0], box1.directions[2]), box0, box1)) {\r\n            return false;\r\n        }\r\n        if (!AxisOverlap(Vector3.Cross(box0.directions[1], box1.directions[0]), box0, box1)) {\r\n            return false;\r\n        }\r\n        if (!AxisOverlap(Vector3.Cross(box0.directions[1], box1.directions[1]), box0, box1)) {\r\n            return false;\r\n        }\r\n        if (!AxisOverlap(Vector3.Cross(box0.directions[1], box1.directions[2]), box0, box1)) {\r\n            return false;\r\n        }\r\n        if (!AxisOverlap(Vector3.Cross(box0.directions[2], box1.directions[0]), box0, box1)) {\r\n            return false;\r\n        }\r\n        if (!AxisOverlap(Vector3.Cross(box0.directions[2], box1.directions[1]), box0, box1)) {\r\n            return false;\r\n        }\r\n        if (!AxisOverlap(Vector3.Cross(box0.directions[2], box1.directions[2]), box0, box1)) {\r\n            return false;\r\n        }\r\n\r\n        return true;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AACA,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAEhD,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAE3D,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;;;;;AAKlD,MAAM,QAAQ,GAAG;IAAE,GAAG,EAAE,CAAC;IAAE,GAAG,EAAE,CAAC;AAAA,CAAE,CAAC;AACpC,MAAM,QAAQ,GAAG;IAAE,GAAG,EAAE,CAAC;IAAE,GAAG,EAAE,CAAC;AAAA,CAAE,CAAC;AACpC,MAAM,iBAAiB,GAAG,CAAC,IAA4B,EAAE,GAA+B,EAAE,MAAoC,EAAE,EAAE;IAC9H,MAAM,CAAC,qKAAG,UAAO,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;IAE7C,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,mKAAC,UAAO,CAAC,GAAG,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;IAC7E,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,mKAAC,UAAO,CAAC,GAAG,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;IAC7E,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,mKAAC,UAAO,CAAC,GAAG,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;IAE7E,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;IACvB,MAAM,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IACnB,MAAM,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;AACvB,CAAC,CAAC;AAEF,MAAM,WAAW,GAAG,CAAC,IAA4B,EAAE,IAAgC,EAAE,IAAgC,EAAW,EAAE;IAC9H,iBAAiB,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;IACxC,iBAAiB,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;IACxC,OAAO,CAAC,CAAC,QAAQ,CAAC,GAAG,GAAG,QAAQ,CAAC,GAAG,IAAI,QAAQ,CAAC,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;AACzE,CAAC,CAAC;AAyBI,MAAO,YAAY;IAyBrB;;;;;OAKG,CACI,WAAW,CAAC,GAA2B,EAAE,GAA2B,EAAE,WAAmC,EAAA;QAC5G,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,EAAE,WAAW,CAAC,CAAC;QACpD,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,EAAE,WAAW,CAAC,CAAC;IAC3D,CAAC;IAED;;OAEG,CACH,IAAW,OAAO,GAAA;QACd,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;IACpC,CAAC;IAED;;OAEG,CACH,IAAW,OAAO,GAAA;QACd,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;IACpC,CAAC;IAED;;OAEG,CACH,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED,IAAW,QAAQ,CAAC,KAAc,EAAA;QAC9B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;IAC3B,CAAC;IAED,UAAU;IACV;;;OAGG,CACI,MAAM,CAAC,KAA4B,EAAA;QACtC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,OAAO;QACX,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAChC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IACvC,CAAC;IAED;;;;;OAKG,CACI,QAAQ,CAAC,MAA8B,EAAE,MAA8B,EAAA;QAC1E,MAAM,OAAO,GAAG,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QACrF,MAAM,OAAO,GAAG,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAEhF,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC,CAAC;QAClF,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC,CAAC;QAErF,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,WAAW,CAAC,KAAc,EAAA;QAC7B,MAAM,OAAO,GAAG,4KAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACtD,MAAM,OAAO,qKAAG,UAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACtD,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC,CAAC;QAEtE,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,uBAAuB,CAAC,aAA2B,EAAA;QACtD,MAAM,IAAI,GAAG,+KAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAEpD,MAAM,CAAC,qKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;0KAEhC,UAAO,CAAC,yBAAyB,CAAC,aAAa,CAAC,WAAW,CAAC,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QACnF,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;yKAEpB,WAAO,CAAC,yBAAyB,CAAC,aAAa,CAAC,WAAW,CAAC,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QACnF,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAEpB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,KAAK,CAAC,MAAc,EAAA;QACvB,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAC/B,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAElC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;;;;OAUG,CACI,WAAW,CAAC,aAA0C,EAAqB,MAAS,CAAC,+BAA+B;uBAA5D,iEAAmB;QAC9E,MAAM,aAAa,GACf,QAAQ,KAAK,KAAA,IAAS,CAAC,QAAA,mCAA2C,IAAI,QAAQ,KAAK,SAAS,CAAC,6DAA6D,CAAC;QAC/J,IAAI,aAAa,EAAE,CAAC;YAChB,IAAI,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,aAAa,CAAC,EAAE,CAAC;gBACvD,OAAO,IAAI,CAAC;YAChB,CAAC;QACL,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,aAAa,CAAC,EAAE,CAAC;YAClD,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,MAAM,eAAe,GACjB,QAAQ,KAAK,KAAA,IAAS,CAAC,QAAA,kCAA0C,IAAI,QAAQ,KAAK,SAAS,CAAC,6DAA6D,CAAC;QAC9J,IAAI,eAAe,EAAE,CAAC;YAClB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG,CACH,IAAW,cAAc,GAAA;QACrB,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,MAAM,IAAI,GAAG,WAAW,CAAC,YAAY,CAAC,aAAa,CAAC,WAAW,CAAC,YAAY,EAAE,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3G,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;IACzB,CAAC;IAED;;;;;OAKG,CACI,qBAAqB,CAAC,aAA0C,EAAA;QACnE,OAAO,IAAI,CAAC,WAAW,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;IACjE,CAAC;IACD;;OAEG,CACI,eAAe,CAAC,QAAkB,EAAA;QACrC,OAAO,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;IACpK,CAAC;IAED;;;;;OAKG,CACI,eAAe,CAAC,KAA6B,EAAA;QAChD,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC;YACnC,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,CAAC;YAC9C,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,CAAC;YAC3C,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG,CACI,UAAU,CAAC,YAAyC,EAAE,OAAgB,EAAA;QACzE,IAAI,qKAAC,iBAAc,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE,YAAY,CAAC,cAAc,CAAC,EAAE,CAAC;YAC/E,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,kKAAC,cAAW,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,YAAY,CAAC,WAAW,CAAC,EAAE,CAAC;YACtE,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC;QAC9B,MAAM,IAAI,GAAG,YAAY,CAAC,WAAW,CAAC;QAEtC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YAC/C,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YAC/C,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YAC/C,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YAC/C,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YAC/C,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YAC/C,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,IAAI,CAAC,WAAW,mKAAC,UAAO,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YAClF,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,IAAI,CAAC,WAAW,mKAAC,UAAO,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YAClF,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,IAAI,CAAC,WAAW,mKAAC,UAAO,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YAClF,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,IAAI,CAAC,WAAW,kKAAC,WAAO,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YAClF,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,IAAI,CAAC,WAAW,mKAAC,UAAO,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YAClF,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,IAAI,CAAC,WAAW,mKAAC,UAAO,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YAClF,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,IAAI,CAAC,WAAW,mKAAC,UAAO,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YAClF,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,IAAI,CAAC,WAAW,mKAAC,UAAO,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YAClF,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,IAAI,CAAC,WAAW,mKAAC,UAAO,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YAClF,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IA/QD;;;;;OAKG,CACH,YAAY,OAA+B,EAAE,OAA+B,EAAE,WAAmC,CAAA;QAVzG,IAAA,CAAA,SAAS,GAAG,KAAK,CAAC;QAWtB,IAAI,CAAC,WAAW,GAAG,qKAAI,cAAW,CAAC,OAAO,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;QAClE,IAAI,CAAC,cAAc,GAAG,wKAAI,iBAAc,CAAC,OAAO,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;IAC5E,CAAC;;AAXuB,aAAA,WAAW,oKAAG,aAAA,AAAU,EAAC,CAAC,oKAAE,UAAO,CAAC,IAAI,CAAC,AAA9B,CAA+B", "debugId": null}}, {"offset": {"line": 690, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Culling/ray.core.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Culling/ray.core.ts"], "sourcesContent": ["import { <PERSON><PERSON><PERSON> } from \"core/Maths/math.constants\";\r\nimport { Matrix, TmpVectors, Vector3 } from \"core/Maths/math.vector\";\r\nimport { BuildArray } from \"core/Misc/arrayTools\";\r\nimport { IntersectionInfo } from \"../Collisions/intersectionInfo\";\r\nimport type { BoundingBox } from \"./boundingBox\";\r\nimport type { BoundingSphere } from \"./boundingSphere\";\r\nimport type { DeepImmutable, float, Nullable } from \"core/types\";\r\nimport type { Plane } from \"core/Maths/math.plane\";\r\nimport type { AbstractMesh } from \"core/Meshes/abstractMesh\";\r\nimport { PickingInfo } from \"core/Collisions/pickingInfo\";\r\nimport { EngineStore } from \"core/Engines/engineStore\";\r\nimport type { Scene } from \"core/scene\";\r\nimport type { Camera } from \"core/Cameras/camera\";\r\nimport type { Mesh } from \"core/Meshes/mesh\";\r\nimport { _ImportHelper } from \"core/import.helper\";\r\n\r\n/**\r\n * Type used to define predicate for selecting meshes and instances (if exist)\r\n */\r\nexport type MeshPredicate = (mesh: AbstractMesh, thinInstanceIndex: number) => boolean;\r\n\r\n/**\r\n * Type used to define predicate used to select faces when a mesh intersection is detected\r\n */\r\nexport type TrianglePickingPredicate = (p0: Vector3, p1: Vector3, p2: Vector3, ray: Ray, i0: number, i1: number, i2: number) => boolean;\r\n\r\n/**\r\n * This class allows user to customize internal picking mechanism\r\n */\r\nexport interface IPickingCustomization {\r\n    /**\r\n     * Predicate to select faces when a mesh intersection is detected\r\n     */\r\n    internalPickerForMesh?: (\r\n        pickingInfo: Nullable<PickingInfo>,\r\n        rayFunction: (world: Matrix, enableDistantPicking: boolean) => Ray,\r\n        mesh: AbstractMesh,\r\n        world: Matrix,\r\n        fastCheck?: boolean,\r\n        onlyBoundingInfo?: boolean,\r\n        trianglePredicate?: TrianglePickingPredicate,\r\n        skipBoundingInfo?: boolean\r\n    ) => PickingInfo;\r\n}\r\n\r\n/**\r\n * Use this object to customize mesh picking behavior\r\n */\r\nexport const PickingCustomization: IPickingCustomization = {\r\n    internalPickerForMesh: undefined,\r\n};\r\n\r\n/**\r\n * Class representing a ray with position and direction\r\n */\r\nexport class Ray {\r\n    private static readonly _TmpVector3 = BuildArray(6, Vector3.Zero);\r\n    private static _RayDistant = Ray.Zero();\r\n    private _tmpRay: Ray;\r\n\r\n    /**\r\n     * Creates a new ray\r\n     * @param origin origin point\r\n     * @param direction direction\r\n     * @param length length of the ray\r\n     * @param epsilon The epsilon value to use when calculating the ray/triangle intersection (default: Epsilon from math constants)\r\n     */\r\n    constructor(\r\n        /** origin point */\r\n        public origin: Vector3,\r\n        /** direction */\r\n        public direction: Vector3,\r\n        /** [Number.MAX_VALUE] length of the ray */\r\n        public length: number = Number.MAX_VALUE,\r\n        /** [Epsilon] The epsilon value to use when calculating the ray/triangle intersection (default: Epsilon from math constants) */\r\n        public epsilon: number = Epsilon\r\n    ) {}\r\n\r\n    // Methods\r\n\r\n    /**\r\n     * Clone the current ray\r\n     * @returns a new ray\r\n     */\r\n    public clone(): Ray {\r\n        return new Ray(this.origin.clone(), this.direction.clone(), this.length);\r\n    }\r\n\r\n    /**\r\n     * Checks if the ray intersects a box\r\n     * This does not account for the ray length by design to improve perfs.\r\n     * @param minimum bound of the box\r\n     * @param maximum bound of the box\r\n     * @param intersectionTreshold extra extend to be added to the box in all direction\r\n     * @returns if the box was hit\r\n     */\r\n    public intersectsBoxMinMax(minimum: DeepImmutable<Vector3>, maximum: DeepImmutable<Vector3>, intersectionTreshold: number = 0): boolean {\r\n        const newMinimum = Ray._TmpVector3[0].copyFromFloats(minimum.x - intersectionTreshold, minimum.y - intersectionTreshold, minimum.z - intersectionTreshold);\r\n        const newMaximum = Ray._TmpVector3[1].copyFromFloats(maximum.x + intersectionTreshold, maximum.y + intersectionTreshold, maximum.z + intersectionTreshold);\r\n        let d = 0.0;\r\n        let maxValue = Number.MAX_VALUE;\r\n        let inv: number;\r\n        let min: number;\r\n        let max: number;\r\n        let temp: number;\r\n        if (Math.abs(this.direction.x) < 0.0000001) {\r\n            if (this.origin.x < newMinimum.x || this.origin.x > newMaximum.x) {\r\n                return false;\r\n            }\r\n        } else {\r\n            inv = 1.0 / this.direction.x;\r\n            min = (newMinimum.x - this.origin.x) * inv;\r\n            max = (newMaximum.x - this.origin.x) * inv;\r\n            if (max === -Infinity) {\r\n                max = Infinity;\r\n            }\r\n\r\n            if (min > max) {\r\n                temp = min;\r\n                min = max;\r\n                max = temp;\r\n            }\r\n\r\n            d = Math.max(min, d);\r\n            maxValue = Math.min(max, maxValue);\r\n\r\n            if (d > maxValue) {\r\n                return false;\r\n            }\r\n        }\r\n\r\n        if (Math.abs(this.direction.y) < 0.0000001) {\r\n            if (this.origin.y < newMinimum.y || this.origin.y > newMaximum.y) {\r\n                return false;\r\n            }\r\n        } else {\r\n            inv = 1.0 / this.direction.y;\r\n            min = (newMinimum.y - this.origin.y) * inv;\r\n            max = (newMaximum.y - this.origin.y) * inv;\r\n\r\n            if (max === -Infinity) {\r\n                max = Infinity;\r\n            }\r\n\r\n            if (min > max) {\r\n                temp = min;\r\n                min = max;\r\n                max = temp;\r\n            }\r\n\r\n            d = Math.max(min, d);\r\n            maxValue = Math.min(max, maxValue);\r\n\r\n            if (d > maxValue) {\r\n                return false;\r\n            }\r\n        }\r\n\r\n        if (Math.abs(this.direction.z) < 0.0000001) {\r\n            if (this.origin.z < newMinimum.z || this.origin.z > newMaximum.z) {\r\n                return false;\r\n            }\r\n        } else {\r\n            inv = 1.0 / this.direction.z;\r\n            min = (newMinimum.z - this.origin.z) * inv;\r\n            max = (newMaximum.z - this.origin.z) * inv;\r\n\r\n            if (max === -Infinity) {\r\n                max = Infinity;\r\n            }\r\n\r\n            if (min > max) {\r\n                temp = min;\r\n                min = max;\r\n                max = temp;\r\n            }\r\n\r\n            d = Math.max(min, d);\r\n            maxValue = Math.min(max, maxValue);\r\n\r\n            if (d > maxValue) {\r\n                return false;\r\n            }\r\n        }\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Checks if the ray intersects a box\r\n     * This does not account for the ray length by design to improve perfs.\r\n     * @param box the bounding box to check\r\n     * @param intersectionTreshold extra extend to be added to the BoundingBox in all direction\r\n     * @returns if the box was hit\r\n     */\r\n    public intersectsBox(box: DeepImmutable<BoundingBox>, intersectionTreshold: number = 0): boolean {\r\n        return this.intersectsBoxMinMax(box.minimum, box.maximum, intersectionTreshold);\r\n    }\r\n\r\n    /**\r\n     * If the ray hits a sphere\r\n     * @param sphere the bounding sphere to check\r\n     * @param intersectionTreshold extra extend to be added to the BoundingSphere in all direction\r\n     * @returns true if it hits the sphere\r\n     */\r\n    public intersectsSphere(sphere: DeepImmutable<BoundingSphere>, intersectionTreshold: number = 0): boolean {\r\n        const x = sphere.center.x - this.origin.x;\r\n        const y = sphere.center.y - this.origin.y;\r\n        const z = sphere.center.z - this.origin.z;\r\n        const pyth = x * x + y * y + z * z;\r\n        const radius = sphere.radius + intersectionTreshold;\r\n        const rr = radius * radius;\r\n\r\n        if (pyth <= rr) {\r\n            return true;\r\n        }\r\n\r\n        const dot = x * this.direction.x + y * this.direction.y + z * this.direction.z;\r\n        if (dot < 0.0) {\r\n            return false;\r\n        }\r\n\r\n        const temp = pyth - dot * dot;\r\n\r\n        return temp <= rr;\r\n    }\r\n\r\n    /**\r\n     * If the ray hits a triange\r\n     * @param vertex0 triangle vertex\r\n     * @param vertex1 triangle vertex\r\n     * @param vertex2 triangle vertex\r\n     * @returns intersection information if hit\r\n     */\r\n    public intersectsTriangle(vertex0: DeepImmutable<Vector3>, vertex1: DeepImmutable<Vector3>, vertex2: DeepImmutable<Vector3>): Nullable<IntersectionInfo> {\r\n        const edge1 = Ray._TmpVector3[0];\r\n        const edge2 = Ray._TmpVector3[1];\r\n        const pvec = Ray._TmpVector3[2];\r\n        const tvec = Ray._TmpVector3[3];\r\n        const qvec = Ray._TmpVector3[4];\r\n\r\n        vertex1.subtractToRef(vertex0, edge1);\r\n        vertex2.subtractToRef(vertex0, edge2);\r\n        Vector3.CrossToRef(this.direction, edge2, pvec);\r\n        const det = Vector3.Dot(edge1, pvec);\r\n\r\n        if (det === 0) {\r\n            return null;\r\n        }\r\n\r\n        const invdet = 1 / det;\r\n\r\n        this.origin.subtractToRef(vertex0, tvec);\r\n\r\n        const bv = Vector3.Dot(tvec, pvec) * invdet;\r\n\r\n        if (bv < -this.epsilon || bv > 1.0 + this.epsilon) {\r\n            return null;\r\n        }\r\n\r\n        Vector3.CrossToRef(tvec, edge1, qvec);\r\n\r\n        const bw = Vector3.Dot(this.direction, qvec) * invdet;\r\n\r\n        if (bw < -this.epsilon || bv + bw > 1.0 + this.epsilon) {\r\n            return null;\r\n        }\r\n\r\n        //check if the distance is longer than the predefined length.\r\n        const distance = Vector3.Dot(edge2, qvec) * invdet;\r\n        if (distance > this.length) {\r\n            return null;\r\n        }\r\n\r\n        return new IntersectionInfo(1 - bv - bw, bv, distance);\r\n    }\r\n\r\n    /**\r\n     * Checks if ray intersects a plane\r\n     * @param plane the plane to check\r\n     * @returns the distance away it was hit\r\n     */\r\n    public intersectsPlane(plane: DeepImmutable<Plane>): Nullable<number> {\r\n        let distance: number;\r\n        const result1 = Vector3.Dot(plane.normal, this.direction);\r\n        if (Math.abs(result1) < 9.99999997475243e-7) {\r\n            return null;\r\n        } else {\r\n            const result2 = Vector3.Dot(plane.normal, this.origin);\r\n            distance = (-plane.d - result2) / result1;\r\n            if (distance < 0.0) {\r\n                if (distance < -9.99999997475243e-7) {\r\n                    return null;\r\n                } else {\r\n                    return 0;\r\n                }\r\n            }\r\n\r\n            return distance;\r\n        }\r\n    }\r\n    /**\r\n     * Calculate the intercept of a ray on a given axis\r\n     * @param axis to check 'x' | 'y' | 'z'\r\n     * @param offset from axis interception (i.e. an offset of 1y is intercepted above ground)\r\n     * @returns a vector containing the coordinates where 'axis' is equal to zero (else offset), or null if there is no intercept.\r\n     */\r\n    public intersectsAxis(axis: string, offset: number = 0): Nullable<Vector3> {\r\n        switch (axis) {\r\n            case \"y\": {\r\n                const t = (this.origin.y - offset) / this.direction.y;\r\n                if (t > 0) {\r\n                    return null;\r\n                }\r\n                return new Vector3(this.origin.x + this.direction.x * -t, offset, this.origin.z + this.direction.z * -t);\r\n            }\r\n            case \"x\": {\r\n                const t = (this.origin.x - offset) / this.direction.x;\r\n                if (t > 0) {\r\n                    return null;\r\n                }\r\n                return new Vector3(offset, this.origin.y + this.direction.y * -t, this.origin.z + this.direction.z * -t);\r\n            }\r\n            case \"z\": {\r\n                const t = (this.origin.z - offset) / this.direction.z;\r\n                if (t > 0) {\r\n                    return null;\r\n                }\r\n                return new Vector3(this.origin.x + this.direction.x * -t, this.origin.y + this.direction.y * -t, offset);\r\n            }\r\n            default:\r\n                return null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Checks if ray intersects a mesh. The ray is defined in WORLD space. A mesh triangle can be picked both from its front and back sides,\r\n     * irrespective of orientation.\r\n     * @param mesh the mesh to check\r\n     * @param fastCheck defines if the first intersection will be used (and not the closest)\r\n     * @param trianglePredicate defines an optional predicate used to select faces when a mesh intersection is detected\r\n     * @param onlyBoundingInfo defines a boolean indicating if picking should only happen using bounding info (false by default)\r\n     * @param worldToUse defines the world matrix to use to get the world coordinate of the intersection point\r\n     * @param skipBoundingInfo a boolean indicating if we should skip the bounding info check\r\n     * @returns picking info of the intersection\r\n     */\r\n    public intersectsMesh(\r\n        mesh: DeepImmutable<AbstractMesh>,\r\n        fastCheck?: boolean,\r\n        trianglePredicate?: TrianglePickingPredicate,\r\n        onlyBoundingInfo = false,\r\n        worldToUse?: Matrix,\r\n        skipBoundingInfo = false\r\n    ): PickingInfo {\r\n        const tm = TmpVectors.Matrix[0];\r\n\r\n        mesh.getWorldMatrix().invertToRef(tm);\r\n\r\n        if (this._tmpRay) {\r\n            Ray.TransformToRef(this, tm, this._tmpRay);\r\n        } else {\r\n            this._tmpRay = Ray.Transform(this, tm);\r\n        }\r\n\r\n        return mesh.intersects(this._tmpRay, fastCheck, trianglePredicate, onlyBoundingInfo, worldToUse, skipBoundingInfo);\r\n    }\r\n\r\n    /**\r\n     * Checks if ray intersects a mesh\r\n     * @param meshes the meshes to check\r\n     * @param fastCheck defines if the first intersection will be used (and not the closest)\r\n     * @param results array to store result in\r\n     * @returns Array of picking infos\r\n     */\r\n    public intersectsMeshes(meshes: Array<DeepImmutable<AbstractMesh>>, fastCheck?: boolean, results?: Array<PickingInfo>): Array<PickingInfo> {\r\n        if (results) {\r\n            results.length = 0;\r\n        } else {\r\n            results = [];\r\n        }\r\n\r\n        for (let i = 0; i < meshes.length; i++) {\r\n            const pickInfo = this.intersectsMesh(meshes[i], fastCheck);\r\n\r\n            if (pickInfo.hit) {\r\n                results.push(pickInfo);\r\n            }\r\n        }\r\n\r\n        results.sort(this._comparePickingInfo);\r\n\r\n        return results;\r\n    }\r\n\r\n    private _comparePickingInfo(pickingInfoA: DeepImmutable<PickingInfo>, pickingInfoB: DeepImmutable<PickingInfo>): number {\r\n        if (pickingInfoA.distance < pickingInfoB.distance) {\r\n            return -1;\r\n        } else if (pickingInfoA.distance > pickingInfoB.distance) {\r\n            return 1;\r\n        } else {\r\n            return 0;\r\n        }\r\n    }\r\n\r\n    private static _Smallnum = 0.00000001;\r\n    private static _Rayl = 10e8;\r\n\r\n    /**\r\n     * Intersection test between the ray and a given segment within a given tolerance (threshold)\r\n     * @param sega the first point of the segment to test the intersection against\r\n     * @param segb the second point of the segment to test the intersection against\r\n     * @param threshold the tolerance margin, if the ray doesn't intersect the segment but is close to the given threshold, the intersection is successful\r\n     * @returns the distance from the ray origin to the intersection point if there's intersection, or -1 if there's no intersection\r\n     */\r\n    intersectionSegment(sega: DeepImmutable<Vector3>, segb: DeepImmutable<Vector3>, threshold: number): number {\r\n        const o = this.origin;\r\n        const u = TmpVectors.Vector3[0];\r\n        const rsegb = TmpVectors.Vector3[1];\r\n        const v = TmpVectors.Vector3[2];\r\n        const w = TmpVectors.Vector3[3];\r\n\r\n        segb.subtractToRef(sega, u);\r\n\r\n        this.direction.scaleToRef(Ray._Rayl, v);\r\n        o.addToRef(v, rsegb);\r\n\r\n        sega.subtractToRef(o, w);\r\n\r\n        const a = Vector3.Dot(u, u); // always >= 0\r\n        const b = Vector3.Dot(u, v);\r\n        const c = Vector3.Dot(v, v); // always >= 0\r\n        const d = Vector3.Dot(u, w);\r\n        const e = Vector3.Dot(v, w);\r\n        const discriminant = a * c - b * b; // always >= 0\r\n        let sN: number,\r\n            sD = discriminant; // sc = sN / sD, default sD = D >= 0\r\n        let tN: number,\r\n            tD = discriminant; // tc = tN / tD, default tD = D >= 0\r\n\r\n        // compute the line parameters of the two closest points\r\n        if (discriminant < Ray._Smallnum) {\r\n            // the lines are almost parallel\r\n            sN = 0.0; // force using point P0 on segment S1\r\n            sD = 1.0; // to prevent possible division by 0.0 later\r\n            tN = e;\r\n            tD = c;\r\n        } else {\r\n            // get the closest points on the infinite lines\r\n            sN = b * e - c * d;\r\n            tN = a * e - b * d;\r\n            if (sN < 0.0) {\r\n                // sc < 0 => the s=0 edge is visible\r\n                sN = 0.0;\r\n                tN = e;\r\n                tD = c;\r\n            } else if (sN > sD) {\r\n                // sc > 1 => the s=1 edge is visible\r\n                sN = sD;\r\n                tN = e + b;\r\n                tD = c;\r\n            }\r\n        }\r\n\r\n        if (tN < 0.0) {\r\n            // tc < 0 => the t=0 edge is visible\r\n            tN = 0.0;\r\n            // recompute sc for this edge\r\n            if (-d < 0.0) {\r\n                sN = 0.0;\r\n            } else if (-d > a) {\r\n                sN = sD;\r\n            } else {\r\n                sN = -d;\r\n                sD = a;\r\n            }\r\n        } else if (tN > tD) {\r\n            // tc > 1 => the t=1 edge is visible\r\n            tN = tD;\r\n            // recompute sc for this edge\r\n            if (-d + b < 0.0) {\r\n                sN = 0;\r\n            } else if (-d + b > a) {\r\n                sN = sD;\r\n            } else {\r\n                sN = -d + b;\r\n                sD = a;\r\n            }\r\n        }\r\n        // finally do the division to get sc and tc\r\n        const sc = Math.abs(sN) < Ray._Smallnum ? 0.0 : sN / sD;\r\n        const tc = Math.abs(tN) < Ray._Smallnum ? 0.0 : tN / tD;\r\n\r\n        // get the difference of the two closest points\r\n        const qtc = TmpVectors.Vector3[4];\r\n        v.scaleToRef(tc, qtc);\r\n        const qsc = TmpVectors.Vector3[5];\r\n        u.scaleToRef(sc, qsc);\r\n        qsc.addInPlace(w);\r\n        const dP = TmpVectors.Vector3[6];\r\n        qsc.subtractToRef(qtc, dP); // = S1(sc) - S2(tc)\r\n\r\n        const isIntersected = tc > 0 && tc <= this.length && dP.lengthSquared() < threshold * threshold; // return intersection result\r\n\r\n        if (isIntersected) {\r\n            return qsc.length();\r\n        }\r\n        return -1;\r\n    }\r\n\r\n    /**\r\n     * Update the ray from viewport position\r\n     * @param x position\r\n     * @param y y position\r\n     * @param viewportWidth viewport width\r\n     * @param viewportHeight viewport height\r\n     * @param world world matrix\r\n     * @param view view matrix\r\n     * @param projection projection matrix\r\n     * @param enableDistantPicking defines if picking should handle large values for mesh position/scaling (false by default)\r\n     * @returns this ray updated\r\n     */\r\n    public update(\r\n        x: number,\r\n        y: number,\r\n        viewportWidth: number,\r\n        viewportHeight: number,\r\n        world: DeepImmutable<Matrix>,\r\n        view: DeepImmutable<Matrix>,\r\n        projection: DeepImmutable<Matrix>,\r\n        enableDistantPicking: boolean = false\r\n    ): Ray {\r\n        if (enableDistantPicking) {\r\n            // With world matrices having great values (like 8000000000 on 1 or more scaling or position axis),\r\n            // multiplying view/projection/world and doing invert will result in loss of float precision in the matrix.\r\n            // One way to fix it is to compute the ray with world at identity then transform the ray in object space.\r\n            // This is slower (2 matrix inverts instead of 1) but precision is preserved.\r\n            // This is hidden behind `EnableDistantPicking` flag (default is false)\r\n            if (!Ray._RayDistant) {\r\n                Ray._RayDistant = Ray.Zero();\r\n            }\r\n\r\n            Ray._RayDistant.unprojectRayToRef(x, y, viewportWidth, viewportHeight, Matrix.IdentityReadOnly, view, projection);\r\n\r\n            const tm = TmpVectors.Matrix[0];\r\n            world.invertToRef(tm);\r\n            Ray.TransformToRef(Ray._RayDistant, tm, this);\r\n        } else {\r\n            this.unprojectRayToRef(x, y, viewportWidth, viewportHeight, world, view, projection);\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    // Statics\r\n    /**\r\n     * Creates a ray with origin and direction of 0,0,0\r\n     * @returns the new ray\r\n     */\r\n    public static Zero(): Ray {\r\n        return new Ray(Vector3.Zero(), Vector3.Zero());\r\n    }\r\n\r\n    /**\r\n     * Creates a new ray from screen space and viewport\r\n     * @param x position\r\n     * @param y y position\r\n     * @param viewportWidth viewport width\r\n     * @param viewportHeight viewport height\r\n     * @param world world matrix\r\n     * @param view view matrix\r\n     * @param projection projection matrix\r\n     * @returns new ray\r\n     */\r\n    public static CreateNew(\r\n        x: number,\r\n        y: number,\r\n        viewportWidth: number,\r\n        viewportHeight: number,\r\n        world: DeepImmutable<Matrix>,\r\n        view: DeepImmutable<Matrix>,\r\n        projection: DeepImmutable<Matrix>\r\n    ): Ray {\r\n        const result = Ray.Zero();\r\n\r\n        return result.update(x, y, viewportWidth, viewportHeight, world, view, projection);\r\n    }\r\n\r\n    /**\r\n     * Function will create a new transformed ray starting from origin and ending at the end point. Ray's length will be set, and ray will be\r\n     * transformed to the given world matrix.\r\n     * @param origin The origin point\r\n     * @param end The end point\r\n     * @param world a matrix to transform the ray to. Default is the identity matrix.\r\n     * @returns the new ray\r\n     */\r\n    public static CreateNewFromTo(origin: Vector3, end: Vector3, world: DeepImmutable<Matrix> = Matrix.IdentityReadOnly): Ray {\r\n        const result = new Ray(new Vector3(0, 0, 0), new Vector3(0, 0, 0));\r\n        return Ray.CreateFromToToRef(origin, end, result, world);\r\n    }\r\n\r\n    /**\r\n     * Function will update a transformed ray starting from origin and ending at the end point. Ray's length will be set, and ray will be\r\n     * transformed to the given world matrix.\r\n     * @param origin The origin point\r\n     * @param end The end point\r\n     * @param result the object to store the result\r\n     * @param world a matrix to transform the ray to. Default is the identity matrix.\r\n     * @returns the ref ray\r\n     */\r\n    public static CreateFromToToRef(origin: Vector3, end: Vector3, result: Ray, world: DeepImmutable<Matrix> = Matrix.IdentityReadOnly): Ray {\r\n        result.origin.copyFrom(origin);\r\n        const direction = end.subtractToRef(origin, result.direction);\r\n        const length = Math.sqrt(direction.x * direction.x + direction.y * direction.y + direction.z * direction.z);\r\n        result.length = length;\r\n        result.direction.normalize();\r\n\r\n        return Ray.TransformToRef(result, world, result);\r\n    }\r\n\r\n    /**\r\n     * Transforms a ray by a matrix\r\n     * @param ray ray to transform\r\n     * @param matrix matrix to apply\r\n     * @returns the resulting new ray\r\n     */\r\n    public static Transform(ray: DeepImmutable<Ray>, matrix: DeepImmutable<Matrix>): Ray {\r\n        const result = new Ray(new Vector3(0, 0, 0), new Vector3(0, 0, 0));\r\n        Ray.TransformToRef(ray, matrix, result);\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Transforms a ray by a matrix\r\n     * @param ray ray to transform\r\n     * @param matrix matrix to apply\r\n     * @param result ray to store result in\r\n     * @returns the updated result ray\r\n     */\r\n    public static TransformToRef(ray: DeepImmutable<Ray>, matrix: DeepImmutable<Matrix>, result: Ray): Ray {\r\n        Vector3.TransformCoordinatesToRef(ray.origin, matrix, result.origin);\r\n        Vector3.TransformNormalToRef(ray.direction, matrix, result.direction);\r\n        result.length = ray.length;\r\n        result.epsilon = ray.epsilon;\r\n\r\n        const dir = result.direction;\r\n        const len = dir.length();\r\n\r\n        if (!(len === 0 || len === 1)) {\r\n            const num = 1.0 / len;\r\n            dir.x *= num;\r\n            dir.y *= num;\r\n            dir.z *= num;\r\n            result.length *= len;\r\n        }\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Unproject a ray from screen space to object space\r\n     * @param sourceX defines the screen space x coordinate to use\r\n     * @param sourceY defines the screen space y coordinate to use\r\n     * @param viewportWidth defines the current width of the viewport\r\n     * @param viewportHeight defines the current height of the viewport\r\n     * @param world defines the world matrix to use (can be set to Identity to go to world space)\r\n     * @param view defines the view matrix to use\r\n     * @param projection defines the projection matrix to use\r\n     */\r\n    public unprojectRayToRef(\r\n        sourceX: float,\r\n        sourceY: float,\r\n        viewportWidth: number,\r\n        viewportHeight: number,\r\n        world: DeepImmutable<Matrix>,\r\n        view: DeepImmutable<Matrix>,\r\n        projection: DeepImmutable<Matrix>\r\n    ): void {\r\n        const matrix = TmpVectors.Matrix[0];\r\n        world.multiplyToRef(view, matrix);\r\n        matrix.multiplyToRef(projection, matrix);\r\n        matrix.invert();\r\n\r\n        const engine = EngineStore.LastCreatedEngine;\r\n        const nearScreenSource = TmpVectors.Vector3[0];\r\n        nearScreenSource.x = (sourceX / viewportWidth) * 2 - 1;\r\n        nearScreenSource.y = -((sourceY / viewportHeight) * 2 - 1);\r\n        nearScreenSource.z = engine?.useReverseDepthBuffer ? 1 : engine?.isNDCHalfZRange ? 0 : -1;\r\n\r\n        // far Z need to be close but < to 1 or camera projection matrix with maxZ = 0 will NaN\r\n        const farScreenSource = TmpVectors.Vector3[1].copyFromFloats(nearScreenSource.x, nearScreenSource.y, 1.0 - 1e-8);\r\n        const nearVec3 = TmpVectors.Vector3[2];\r\n        const farVec3 = TmpVectors.Vector3[3];\r\n        Vector3.TransformCoordinatesToRef(nearScreenSource, matrix, nearVec3);\r\n        Vector3.TransformCoordinatesToRef(farScreenSource, matrix, farVec3);\r\n\r\n        this.origin.copyFrom(nearVec3);\r\n        farVec3.subtractToRef(nearVec3, this.direction);\r\n        this.direction.normalize();\r\n    }\r\n}\r\n\r\n/**\r\n * Creates a ray that can be used to pick in the scene\r\n * @param scene defines the scene to use for the picking\r\n * @param x defines the x coordinate of the origin (on-screen)\r\n * @param y defines the y coordinate of the origin (on-screen)\r\n * @param world defines the world matrix to use if you want to pick in object space (instead of world space)\r\n * @param camera defines the camera to use for the picking\r\n * @param cameraViewSpace defines if picking will be done in view space (false by default)\r\n * @returns a Ray\r\n */\r\nexport function CreatePickingRay(scene: Scene, x: number, y: number, world: Nullable<Matrix>, camera: Nullable<Camera>, cameraViewSpace = false): Ray {\r\n    const result = Ray.Zero();\r\n\r\n    CreatePickingRayToRef(scene, x, y, world, result, camera, cameraViewSpace);\r\n\r\n    return result;\r\n}\r\n\r\n/**\r\n * Creates a ray that can be used to pick in the scene\r\n * @param scene defines the scene to use for the picking\r\n * @param x defines the x coordinate of the origin (on-screen)\r\n * @param y defines the y coordinate of the origin (on-screen)\r\n * @param world defines the world matrix to use if you want to pick in object space (instead of world space)\r\n * @param result defines the ray where to store the picking ray\r\n * @param camera defines the camera to use for the picking\r\n * @param cameraViewSpace defines if picking will be done in view space (false by default)\r\n * @param enableDistantPicking defines if picking should handle large values for mesh position/scaling (false by default)\r\n * @returns the current scene\r\n */\r\nexport function CreatePickingRayToRef(\r\n    scene: Scene,\r\n    x: number,\r\n    y: number,\r\n    world: Nullable<Matrix>,\r\n    result: Ray,\r\n    camera: Nullable<Camera>,\r\n    cameraViewSpace = false,\r\n    enableDistantPicking = false\r\n): Scene {\r\n    const engine = scene.getEngine();\r\n\r\n    if (!camera && !(camera = scene.activeCamera!)) {\r\n        return scene;\r\n    }\r\n\r\n    const cameraViewport = camera.viewport;\r\n    const renderHeight = engine.getRenderHeight();\r\n    const { x: vx, y: vy, width, height } = cameraViewport.toGlobal(engine.getRenderWidth(), renderHeight);\r\n\r\n    // Moving coordinates to local viewport world\r\n    const levelInv = 1 / engine.getHardwareScalingLevel();\r\n    x = x * levelInv - vx;\r\n    y = y * levelInv - (renderHeight - vy - height);\r\n\r\n    result.update(\r\n        x,\r\n        y,\r\n        width,\r\n        height,\r\n        world ? world : Matrix.IdentityReadOnly,\r\n        cameraViewSpace ? Matrix.IdentityReadOnly : camera.getViewMatrix(),\r\n        camera.getProjectionMatrix(),\r\n        enableDistantPicking\r\n    );\r\n    return scene;\r\n}\r\n\r\n/**\r\n * Creates a ray that can be used to pick in the scene\r\n * @param scene defines the scene to use for the picking\r\n * @param x defines the x coordinate of the origin (on-screen)\r\n * @param y defines the y coordinate of the origin (on-screen)\r\n * @param camera defines the camera to use for the picking\r\n * @returns a Ray\r\n */\r\nexport function CreatePickingRayInCameraSpace(scene: Scene, x: number, y: number, camera?: Camera): Ray {\r\n    const result = Ray.Zero();\r\n\r\n    CreatePickingRayInCameraSpaceToRef(scene, x, y, result, camera);\r\n\r\n    return result;\r\n}\r\n\r\n/**\r\n * Creates a ray that can be used to pick in the scene\r\n * @param scene defines the scene to use for the picking\r\n * @param x defines the x coordinate of the origin (on-screen)\r\n * @param y defines the y coordinate of the origin (on-screen)\r\n * @param result defines the ray where to store the picking ray\r\n * @param camera defines the camera to use for the picking\r\n * @returns the current scene\r\n */\r\nexport function CreatePickingRayInCameraSpaceToRef(scene: Scene, x: number, y: number, result: Ray, camera?: Camera): Scene {\r\n    if (!PickingInfo) {\r\n        return scene;\r\n    }\r\n\r\n    const engine = scene.getEngine();\r\n\r\n    if (!camera && !(camera = scene.activeCamera!)) {\r\n        throw new Error(\"Active camera not set\");\r\n    }\r\n\r\n    const cameraViewport = camera.viewport;\r\n    const renderHeight = engine.getRenderHeight();\r\n    const { x: vx, y: vy, width, height } = cameraViewport.toGlobal(engine.getRenderWidth(), renderHeight);\r\n    const identity = Matrix.Identity();\r\n\r\n    // Moving coordinates to local viewport world\r\n    const levelInv = 1 / engine.getHardwareScalingLevel();\r\n    x = x * levelInv - vx;\r\n    y = y * levelInv - (renderHeight - vy - height);\r\n    result.update(x, y, width, height, identity, identity, camera.getProjectionMatrix());\r\n    return scene;\r\n}\r\n\r\nfunction InternalPickForMesh(\r\n    pickingInfo: Nullable<PickingInfo>,\r\n    rayFunction: (world: Matrix, enableDistantPicking: boolean) => Ray,\r\n    mesh: AbstractMesh,\r\n    world: Matrix,\r\n    fastCheck?: boolean,\r\n    onlyBoundingInfo?: boolean,\r\n    trianglePredicate?: TrianglePickingPredicate,\r\n    skipBoundingInfo?: boolean\r\n) {\r\n    const ray = rayFunction(world, mesh.enableDistantPicking);\r\n\r\n    const result = mesh.intersects(ray, fastCheck, trianglePredicate, onlyBoundingInfo, world, skipBoundingInfo);\r\n    if (!result || !result.hit) {\r\n        return null;\r\n    }\r\n\r\n    if (!fastCheck && pickingInfo != null && result.distance >= pickingInfo.distance) {\r\n        return null;\r\n    }\r\n\r\n    return result;\r\n}\r\n\r\nfunction InternalPick(\r\n    scene: Scene,\r\n    rayFunction: (world: Matrix, enableDistantPicking: boolean) => Ray,\r\n    predicate?: MeshPredicate,\r\n    fastCheck?: boolean,\r\n    onlyBoundingInfo?: boolean,\r\n    trianglePredicate?: TrianglePickingPredicate\r\n): PickingInfo {\r\n    let pickingInfo = null;\r\n\r\n    const computeWorldMatrixForCamera = !!(scene.activeCameras && scene.activeCameras.length > 1 && scene.cameraToUseForPointers !== scene.activeCamera);\r\n    const currentCamera = scene.cameraToUseForPointers || scene.activeCamera;\r\n    const picker = PickingCustomization.internalPickerForMesh || InternalPickForMesh;\r\n\r\n    for (let meshIndex = 0; meshIndex < scene.meshes.length; meshIndex++) {\r\n        const mesh = scene.meshes[meshIndex];\r\n\r\n        if (predicate) {\r\n            if (!predicate(mesh, -1)) {\r\n                continue;\r\n            }\r\n        } else if (!mesh.isEnabled() || !mesh.isVisible || !mesh.isPickable) {\r\n            continue;\r\n        }\r\n\r\n        const forceCompute = computeWorldMatrixForCamera && mesh.isWorldMatrixCameraDependent();\r\n        const world = mesh.computeWorldMatrix(forceCompute, currentCamera);\r\n\r\n        if (mesh.hasThinInstances && (mesh as Mesh).thinInstanceEnablePicking) {\r\n            // first check if the ray intersects the whole bounding box/sphere of the mesh\r\n            const result = picker(pickingInfo, rayFunction, mesh, world, true, true, trianglePredicate);\r\n            if (result) {\r\n                if (onlyBoundingInfo) {\r\n                    // the user only asked for a bounding info check so we can return\r\n                    return result;\r\n                }\r\n                const tmpMatrix = TmpVectors.Matrix[1];\r\n                const thinMatrices = (mesh as Mesh).thinInstanceGetWorldMatrices();\r\n                for (let index = 0; index < thinMatrices.length; index++) {\r\n                    if (predicate && !predicate(mesh, index)) {\r\n                        continue;\r\n                    }\r\n                    const thinMatrix = thinMatrices[index];\r\n                    thinMatrix.multiplyToRef(world, tmpMatrix);\r\n                    const result = picker(pickingInfo, rayFunction, mesh, tmpMatrix, fastCheck, onlyBoundingInfo, trianglePredicate, true);\r\n\r\n                    if (result) {\r\n                        pickingInfo = result;\r\n                        pickingInfo.thinInstanceIndex = index;\r\n\r\n                        if (fastCheck) {\r\n                            return pickingInfo;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        } else {\r\n            const result = picker(pickingInfo, rayFunction, mesh, world, fastCheck, onlyBoundingInfo, trianglePredicate);\r\n\r\n            if (result) {\r\n                pickingInfo = result;\r\n\r\n                if (fastCheck) {\r\n                    return pickingInfo;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    return pickingInfo || new PickingInfo();\r\n}\r\n\r\nfunction InternalMultiPick(\r\n    scene: Scene,\r\n    rayFunction: (world: Matrix, enableDistantPicking: boolean) => Ray,\r\n    predicate?: MeshPredicate,\r\n    trianglePredicate?: TrianglePickingPredicate\r\n): Nullable<PickingInfo[]> {\r\n    if (!PickingInfo) {\r\n        return null;\r\n    }\r\n    const pickingInfos: PickingInfo[] = [];\r\n    const computeWorldMatrixForCamera = !!(scene.activeCameras && scene.activeCameras.length > 1 && scene.cameraToUseForPointers !== scene.activeCamera);\r\n    const currentCamera = scene.cameraToUseForPointers || scene.activeCamera;\r\n    const picker = PickingCustomization.internalPickerForMesh || InternalPickForMesh;\r\n\r\n    for (let meshIndex = 0; meshIndex < scene.meshes.length; meshIndex++) {\r\n        const mesh = scene.meshes[meshIndex];\r\n\r\n        if (predicate) {\r\n            if (!predicate(mesh, -1)) {\r\n                continue;\r\n            }\r\n        } else if (!mesh.isEnabled() || !mesh.isVisible || !mesh.isPickable) {\r\n            continue;\r\n        }\r\n\r\n        const forceCompute = computeWorldMatrixForCamera && mesh.isWorldMatrixCameraDependent();\r\n        const world = mesh.computeWorldMatrix(forceCompute, currentCamera);\r\n\r\n        if (mesh.hasThinInstances && (mesh as Mesh).thinInstanceEnablePicking) {\r\n            const result = picker(null, rayFunction, mesh, world, true, true, trianglePredicate);\r\n            if (result) {\r\n                const tmpMatrix = TmpVectors.Matrix[1];\r\n                const thinMatrices = (mesh as Mesh).thinInstanceGetWorldMatrices();\r\n                for (let index = 0; index < thinMatrices.length; index++) {\r\n                    if (predicate && !predicate(mesh, index)) {\r\n                        continue;\r\n                    }\r\n                    const thinMatrix = thinMatrices[index];\r\n                    thinMatrix.multiplyToRef(world, tmpMatrix);\r\n                    const result = picker(null, rayFunction, mesh, tmpMatrix, false, false, trianglePredicate, true);\r\n\r\n                    if (result) {\r\n                        result.thinInstanceIndex = index;\r\n                        pickingInfos.push(result);\r\n                    }\r\n                }\r\n            }\r\n        } else {\r\n            const result = picker(null, rayFunction, mesh, world, false, false, trianglePredicate);\r\n\r\n            if (result) {\r\n                pickingInfos.push(result);\r\n            }\r\n        }\r\n    }\r\n\r\n    return pickingInfos;\r\n}\r\n\r\n/** Launch a ray to try to pick a mesh in the scene using only bounding information of the main mesh (not using submeshes)\r\n * @param scene defines the scene to use for the picking\r\n * @param x position on screen\r\n * @param y position on screen\r\n * @param predicate Predicate function used to determine eligible meshes. Can be set to null. In this case, a mesh must be enabled, visible and with isPickable set to true. thinInstanceIndex is -1 when the mesh is non-instanced\r\n * @param fastCheck defines if the first intersection will be used (and not the closest)\r\n * @param camera to use for computing the picking ray. Can be set to null. In this case, the scene.activeCamera will be used\r\n * @returns a PickingInfo (Please note that some info will not be set like distance, bv, bu and everything that cannot be capture by only using bounding infos)\r\n */\r\nexport function PickWithBoundingInfo(scene: Scene, x: number, y: number, predicate?: MeshPredicate, fastCheck?: boolean, camera?: Nullable<Camera>): Nullable<PickingInfo> {\r\n    if (!PickingInfo) {\r\n        return null;\r\n    }\r\n    const result = InternalPick(\r\n        scene,\r\n        (world) => {\r\n            if (!scene._tempPickingRay) {\r\n                scene._tempPickingRay = Ray.Zero();\r\n            }\r\n\r\n            CreatePickingRayToRef(scene, x, y, world, scene._tempPickingRay, camera || null);\r\n            return scene._tempPickingRay;\r\n        },\r\n        predicate,\r\n        fastCheck,\r\n        true\r\n    );\r\n    if (result) {\r\n        result.ray = CreatePickingRay(scene, x, y, Matrix.Identity(), camera || null);\r\n    }\r\n    return result;\r\n}\r\n\r\n/** Launch a ray to try to pick a mesh in the scene\r\n * @param scene defines the scene to use for the picking\r\n * @param x position on screen\r\n * @param y position on screen\r\n * @param predicate Predicate function used to determine eligible meshes. Can be set to null. In this case, a mesh must be enabled, visible and with isPickable set to true. thinInstanceIndex is -1 when the mesh is non-instanced\r\n * @param fastCheck defines if the first intersection will be used (and not the closest)\r\n * @param camera to use for computing the picking ray. Can be set to null. In this case, the scene.activeCamera will be used\r\n * @param trianglePredicate defines an optional predicate used to select faces when a mesh intersection is detected\r\n * @param _enableDistantPicking defines if picking should handle large values for mesh position/scaling (false by default)\r\n * @returns a PickingInfo\r\n */\r\nexport function Pick(\r\n    scene: Scene,\r\n    x: number,\r\n    y: number,\r\n    predicate?: MeshPredicate,\r\n    fastCheck?: boolean,\r\n    camera?: Nullable<Camera>,\r\n    trianglePredicate?: TrianglePickingPredicate,\r\n    _enableDistantPicking = false\r\n): PickingInfo {\r\n    const result = InternalPick(\r\n        scene,\r\n        (world, enableDistantPicking) => {\r\n            if (!scene._tempPickingRay) {\r\n                scene._tempPickingRay = Ray.Zero();\r\n            }\r\n\r\n            CreatePickingRayToRef(scene, x, y, world, scene._tempPickingRay, camera || null, false, enableDistantPicking);\r\n            return scene._tempPickingRay;\r\n        },\r\n        predicate,\r\n        fastCheck,\r\n        false,\r\n        trianglePredicate\r\n    );\r\n    if (result) {\r\n        result.ray = CreatePickingRay(scene, x, y, Matrix.Identity(), camera || null);\r\n    }\r\n    return result;\r\n}\r\n\r\n/**\r\n * Use the given ray to pick a mesh in the scene. A mesh triangle can be picked both from its front and back sides,\r\n * irrespective of orientation.\r\n * @param scene defines the scene to use for the picking\r\n * @param ray The ray to use to pick meshes\r\n * @param predicate Predicate function used to determine eligible meshes. Can be set to null. In this case, a mesh must have isPickable set to true. thinInstanceIndex is -1 when the mesh is non-instanced\r\n * @param fastCheck defines if the first intersection will be used (and not the closest)\r\n * @param trianglePredicate defines an optional predicate used to select faces when a mesh intersection is detected\r\n * @returns a PickingInfo\r\n */\r\nexport function PickWithRay(scene: Scene, ray: Ray, predicate?: MeshPredicate, fastCheck?: boolean, trianglePredicate?: TrianglePickingPredicate): Nullable<PickingInfo> {\r\n    const result = InternalPick(\r\n        scene,\r\n        (world) => {\r\n            if (!scene._pickWithRayInverseMatrix) {\r\n                scene._pickWithRayInverseMatrix = Matrix.Identity();\r\n            }\r\n            world.invertToRef(scene._pickWithRayInverseMatrix);\r\n\r\n            if (!scene._cachedRayForTransform) {\r\n                scene._cachedRayForTransform = Ray.Zero();\r\n            }\r\n\r\n            Ray.TransformToRef(ray, scene._pickWithRayInverseMatrix, scene._cachedRayForTransform);\r\n            return scene._cachedRayForTransform;\r\n        },\r\n        predicate,\r\n        fastCheck,\r\n        false,\r\n        trianglePredicate\r\n    );\r\n    if (result) {\r\n        result.ray = ray;\r\n    }\r\n    return result;\r\n}\r\n\r\n/**\r\n * Launch a ray to try to pick a mesh in the scene. A mesh triangle can be picked both from its front and back sides,\r\n * irrespective of orientation.\r\n * @param scene defines the scene to use for the picking\r\n * @param x X position on screen\r\n * @param y Y position on screen\r\n * @param predicate Predicate function used to determine eligible meshes and instances. Can be set to null. In this case, a mesh must be enabled, visible and with isPickable set to true. thinInstanceIndex is -1 when the mesh is non-instanced\r\n * @param camera camera to use for computing the picking ray. Can be set to null. In this case, the scene.activeCamera will be used\r\n * @param trianglePredicate defines an optional predicate used to select faces when a mesh intersection is detected\r\n * @returns an array of PickingInfo\r\n */\r\nexport function MultiPick(scene: Scene, x: number, y: number, predicate?: MeshPredicate, camera?: Camera, trianglePredicate?: TrianglePickingPredicate): Nullable<PickingInfo[]> {\r\n    return InternalMultiPick(scene, (world) => CreatePickingRay(scene, x, y, world, camera || null), predicate, trianglePredicate);\r\n}\r\n\r\n/**\r\n * Launch a ray to try to pick a mesh in the scene\r\n * @param scene defines the scene to use for the picking\r\n * @param ray Ray to use\r\n * @param predicate Predicate function used to determine eligible meshes and instances. Can be set to null. In this case, a mesh must be enabled, visible and with isPickable set to true. thinInstanceIndex is -1 when the mesh is non-instanced\r\n * @param trianglePredicate defines an optional predicate used to select faces when a mesh intersection is detected\r\n * @returns an array of PickingInfo\r\n */\r\nexport function MultiPickWithRay(scene: Scene, ray: Ray, predicate?: MeshPredicate, trianglePredicate?: TrianglePickingPredicate): Nullable<PickingInfo[]> {\r\n    return InternalMultiPick(\r\n        scene,\r\n        (world) => {\r\n            if (!scene._pickWithRayInverseMatrix) {\r\n                scene._pickWithRayInverseMatrix = Matrix.Identity();\r\n            }\r\n            world.invertToRef(scene._pickWithRayInverseMatrix);\r\n\r\n            if (!scene._cachedRayForTransform) {\r\n                scene._cachedRayForTransform = Ray.Zero();\r\n            }\r\n\r\n            Ray.TransformToRef(ray, scene._pickWithRayInverseMatrix, scene._cachedRayForTransform);\r\n            return scene._cachedRayForTransform;\r\n        },\r\n        predicate,\r\n        trianglePredicate\r\n    );\r\n}\r\n\r\n/**\r\n * Gets a ray in the forward direction from the camera.\r\n * @param camera Defines the camera to use to get the ray from\r\n * @param length Defines the length of the ray to create\r\n * @param transform Defines the transform to apply to the ray, by default the world matrix is used to create a workd space ray\r\n * @param origin Defines the start point of the ray which defaults to the camera position\r\n * @returns the forward ray\r\n */\r\nexport function GetForwardRay(camera: Camera, length = 100, transform?: Matrix, origin?: Vector3): Ray {\r\n    return GetForwardRayToRef(camera, new Ray(Vector3.Zero(), Vector3.Zero(), length), length, transform, origin);\r\n}\r\n\r\n/**\r\n * Gets a ray in the forward direction from the camera.\r\n * @param camera Defines the camera to use to get the ray from\r\n * @param refRay the ray to (re)use when setting the values\r\n * @param length Defines the length of the ray to create\r\n * @param transform Defines the transform to apply to the ray, by default the world matrx is used to create a workd space ray\r\n * @param origin Defines the start point of the ray which defaults to the camera position\r\n * @returns the forward ray\r\n */\r\nexport function GetForwardRayToRef(camera: Camera, refRay: Ray, length = 100, transform?: Matrix, origin?: Vector3): Ray {\r\n    if (!transform) {\r\n        transform = camera.getWorldMatrix();\r\n    }\r\n    refRay.length = length;\r\n\r\n    if (origin) {\r\n        refRay.origin.copyFrom(origin);\r\n    } else {\r\n        refRay.origin.copyFrom(camera.position);\r\n    }\r\n    const forward = TmpVectors.Vector3[2];\r\n    forward.set(0, 0, camera._scene.useRightHandedSystem ? -1 : 1);\r\n    const worldForward = TmpVectors.Vector3[3];\r\n    Vector3.TransformNormalToRef(forward, transform, worldForward);\r\n    Vector3.NormalizeToRef(worldForward, refRay.direction);\r\n\r\n    return refRay;\r\n}\r\n\r\n/**\r\n * Initialize the minimal interdependecies between the Ray and Scene and Camera\r\n * @param sceneClass defines the scene prototype to use\r\n * @param cameraClass defines the camera prototype to use\r\n */\r\nexport function AddRayExtensions(sceneClass: typeof Scene, cameraClass: typeof Camera): void {\r\n    if (cameraClass) {\r\n        cameraClass.prototype.getForwardRay = function (length = 100, transform?: Matrix, origin?: Vector3): Ray {\r\n            return GetForwardRayToRef(this, new Ray(Vector3.Zero(), Vector3.Zero(), length), length, transform, origin);\r\n        };\r\n\r\n        cameraClass.prototype.getForwardRayToRef = function (refRay: Ray, length = 100, transform?: Matrix, origin?: Vector3): Ray {\r\n            return GetForwardRayToRef(this, refRay, length, transform, origin);\r\n        };\r\n    }\r\n\r\n    if (!sceneClass) {\r\n        return;\r\n    }\r\n\r\n    _ImportHelper._IsPickingAvailable = true;\r\n\r\n    sceneClass.prototype.createPickingRay = function (x: number, y: number, world: Nullable<Matrix>, camera: Nullable<Camera>, cameraViewSpace = false): Ray {\r\n        return CreatePickingRay(this, x, y, world, camera, cameraViewSpace);\r\n    };\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,OAAO,EAAE,OAAO,EAAE,mCAAkC;AACpD,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,gCAA+B;AACrE,OAAO,EAAE,UAAU,EAAE,8BAA6B;AAClD,OAAO,EAAE,gBAAgB,EAAE,MAAM,gCAAgC,CAAC;AAMlE,OAAO,EAAE,WAAW,EAAE,qCAAoC;AAC1D,OAAO,EAAE,WAAW,EAAE,kCAAiC;AAIvD,OAAO,EAAE,aAAa,EAAE,4BAA2B;;;;;;;;AAkC5C,MAAM,oBAAoB,GAA0B;IACvD,qBAAqB,EAAE,SAAS;CACnC,CAAC;AAKI,MAAO,GAAG;IAuBZ,UAAU;IAEV;;;OAGG,CACI,KAAK,GAAA;QACR,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IAC7E,CAAC;IAED;;;;;;;OAOG,CACI,mBAAmB,CAAC,OAA+B,EAAE,OAA+B,EAAkC;mCAAhC,iEAA+B,CAAC;QACzH,MAAM,UAAU,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,GAAG,oBAAoB,EAAE,OAAO,CAAC,CAAC,GAAG,oBAAoB,EAAE,OAAO,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC;QAC3J,MAAM,UAAU,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,GAAG,oBAAoB,EAAE,OAAO,CAAC,CAAC,GAAG,oBAAoB,EAAE,OAAO,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC;QAC3J,IAAI,CAAC,GAAG,GAAG,CAAC;QACZ,IAAI,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC;QAChC,IAAI,GAAW,CAAC;QAChB,IAAI,GAAW,CAAC;QAChB,IAAI,GAAW,CAAC;QAChB,IAAI,IAAY,CAAC;QACjB,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,EAAE,CAAC;YACzC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,EAAE,CAAC;gBAC/D,OAAO,KAAK,CAAC;YACjB,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;YAC7B,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;YAC3C,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;YAC3C,IAAI,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACpB,GAAG,GAAG,QAAQ,CAAC;YACnB,CAAC;YAED,IAAI,GAAG,GAAG,GAAG,EAAE,CAAC;gBACZ,IAAI,GAAG,GAAG,CAAC;gBACX,GAAG,GAAG,GAAG,CAAC;gBACV,GAAG,GAAG,IAAI,CAAC;YACf,CAAC;YAED,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YACrB,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;YAEnC,IAAI,CAAC,GAAG,QAAQ,EAAE,CAAC;gBACf,OAAO,KAAK,CAAC;YACjB,CAAC;QACL,CAAC;QAED,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,EAAE,CAAC;YACzC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,EAAE,CAAC;gBAC/D,OAAO,KAAK,CAAC;YACjB,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;YAC7B,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;YAC3C,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;YAE3C,IAAI,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACpB,GAAG,GAAG,QAAQ,CAAC;YACnB,CAAC;YAED,IAAI,GAAG,GAAG,GAAG,EAAE,CAAC;gBACZ,IAAI,GAAG,GAAG,CAAC;gBACX,GAAG,GAAG,GAAG,CAAC;gBACV,GAAG,GAAG,IAAI,CAAC;YACf,CAAC;YAED,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YACrB,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;YAEnC,IAAI,CAAC,GAAG,QAAQ,EAAE,CAAC;gBACf,OAAO,KAAK,CAAC;YACjB,CAAC;QACL,CAAC;QAED,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,EAAE,CAAC;YACzC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,EAAE,CAAC;gBAC/D,OAAO,KAAK,CAAC;YACjB,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;YAC7B,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;YAC3C,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;YAE3C,IAAI,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACpB,GAAG,GAAG,QAAQ,CAAC;YACnB,CAAC;YAED,IAAI,GAAG,GAAG,GAAG,EAAE,CAAC;gBACZ,IAAI,GAAG,GAAG,CAAC;gBACX,GAAG,GAAG,GAAG,CAAC;gBACV,GAAG,GAAG,IAAI,CAAC;YACf,CAAC;YAED,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YACrB,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;YAEnC,IAAI,CAAC,GAAG,QAAQ,EAAE,CAAC;gBACf,OAAO,KAAK,CAAC;YACjB,CAAC;QACL,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG,CACI,aAAa,CAAC,GAA+B,EAAkC;YAAhC,wFAA+B,CAAC;QAClF,OAAO,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAC;IACpF,CAAC;IAED;;;;;OAKG,CACI,gBAAgB,CAAC,MAAqC,EAAkC;mCAAhC,iEAA+B,CAAC;QAC3F,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;QAC1C,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;QAC1C,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;QAC1C,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACnC,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,GAAG,oBAAoB,CAAC;QACpD,MAAM,EAAE,GAAG,MAAM,GAAG,MAAM,CAAC;QAE3B,IAAI,IAAI,IAAI,EAAE,EAAE,CAAC;YACb,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,GAAG,EAAE,CAAC;YACZ,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC;QAE9B,OAAO,IAAI,IAAI,EAAE,CAAC;IACtB,CAAC;IAED;;;;;;OAMG,CACI,kBAAkB,CAAC,OAA+B,EAAE,OAA+B,EAAE,OAA+B,EAAA;QACvH,MAAM,KAAK,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QACjC,MAAM,KAAK,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QACjC,MAAM,IAAI,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAChC,MAAM,IAAI,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAChC,MAAM,IAAI,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAEhC,OAAO,CAAC,aAAa,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACtC,OAAO,CAAC,aAAa,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;0KACtC,UAAO,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAChD,MAAM,GAAG,oKAAG,WAAO,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAErC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC;YACZ,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC;QAEvB,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAEzC,MAAM,EAAE,oKAAG,WAAO,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,MAAM,CAAC;QAE5C,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YAChD,OAAO,IAAI,CAAC;QAChB,CAAC;0KAED,UAAO,CAAC,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAEtC,MAAM,EAAE,qKAAG,UAAO,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,GAAG,MAAM,CAAC;QAEtD,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YACrD,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,6DAA6D;QAC7D,MAAM,QAAQ,qKAAG,UAAO,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,MAAM,CAAC;QACnD,IAAI,QAAQ,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,6KAAI,mBAAgB,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAC;IAC3D,CAAC;IAED;;;;OAIG,CACI,eAAe,CAAC,KAA2B,EAAA;QAC9C,IAAI,QAAgB,CAAC;QACrB,MAAM,OAAO,qKAAG,UAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAC1D,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,mBAAmB,EAAE,CAAC;YAC1C,OAAO,IAAI,CAAC;QAChB,CAAC,MAAM,CAAC;YACJ,MAAM,OAAO,qKAAG,UAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YACvD,QAAQ,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC;YAC1C,IAAI,QAAQ,GAAG,GAAG,EAAE,CAAC;gBACjB,IAAI,QAAQ,GAAG,CAAC,mBAAmB,EAAE,CAAC;oBAClC,OAAO,IAAI,CAAC;gBAChB,CAAC,MAAM,CAAC;oBACJ,OAAO,CAAC,CAAC;gBACb,CAAC;YACL,CAAC;YAED,OAAO,QAAQ,CAAC;QACpB,CAAC;IACL,CAAC;IACD;;;;;OAKG,CACI,cAAc,CAAC,IAAY,EAAoB;qBAAlB,iEAAiB,CAAC;QAClD,OAAQ,IAAI,EAAE,CAAC;YACX,KAAK,GAAG,CAAC;gBAAC,CAAC;oBACP,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;oBACtD,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;wBACR,OAAO,IAAI,CAAC;oBAChB,CAAC;oBACD,OAAO,sKAAI,UAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC7G,CAAC;YACD,KAAK,GAAG,CAAC;gBAAC,CAAC;oBACP,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;oBACtD,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;wBACR,OAAO,IAAI,CAAC;oBAChB,CAAC;oBACD,OAAO,sKAAI,UAAO,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC7G,CAAC;YACD,KAAK,GAAG,CAAC;gBAAC,CAAC;oBACP,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;oBACtD,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;wBACR,OAAO,IAAI,CAAC;oBAChB,CAAC;oBACD,OAAO,sKAAI,UAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;gBAC7G,CAAC;YACD;gBACI,OAAO,IAAI,CAAC;QACpB,CAAC;IACL,CAAC;IAED;;;;;;;;;;OAUG,CACI,cAAc,CACjB,IAAiC,EACjC,SAAmB,EACnB,iBAA4C,EAGpB;+BAFxB,gBAAgB,iDAAG,KAAK,EACxB,UAAmB,oEACnB,gBAAgB,iDAAG,KAAK;QAExB,MAAM,EAAE,qKAAG,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAEhC,IAAI,CAAC,cAAc,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;QAEtC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAC/C,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QAC3C,CAAC;QAED,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,UAAU,EAAE,gBAAgB,CAAC,CAAC;IACvH,CAAC;IAED;;;;;;OAMG,CACI,gBAAgB,CAAC,MAA0C,EAAE,SAAmB,EAAE,OAA4B,EAAA;QACjH,IAAI,OAAO,EAAE,CAAC;YACV,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;QACvB,CAAC,MAAM,CAAC;YACJ,OAAO,GAAG,EAAE,CAAC;QACjB,CAAC;QAED,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;YACrC,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;YAE3D,IAAI,QAAQ,CAAC,GAAG,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC3B,CAAC;QACL,CAAC;QAED,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAEvC,OAAO,OAAO,CAAC;IACnB,CAAC;IAEO,mBAAmB,CAAC,YAAwC,EAAE,YAAwC,EAAA;QAC1G,IAAI,YAAY,CAAC,QAAQ,GAAG,YAAY,CAAC,QAAQ,EAAE,CAAC;YAChD,OAAO,CAAC,CAAC,CAAC;QACd,CAAC,MAAM,IAAI,YAAY,CAAC,QAAQ,GAAG,YAAY,CAAC,QAAQ,EAAE,CAAC;YACvD,OAAO,CAAC,CAAC;QACb,CAAC,MAAM,CAAC;YACJ,OAAO,CAAC,CAAC;QACb,CAAC;IACL,CAAC;IAKD;;;;;;OAMG,CACH,mBAAmB,CAAC,IAA4B,EAAE,IAA4B,EAAE,SAAiB,EAAA;QAC7F,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;QACtB,MAAM,CAAC,qKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAChC,MAAM,KAAK,qKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACpC,MAAM,CAAC,qKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAChC,MAAM,CAAC,oKAAG,cAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAEhC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QAE5B,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAErB,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEzB,MAAM,CAAC,qKAAG,UAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,cAAc;QAC3C,MAAM,CAAC,qKAAG,UAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5B,MAAM,CAAC,qKAAG,UAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,cAAc;QAC3C,MAAM,CAAC,qKAAG,UAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5B,MAAM,CAAC,qKAAG,UAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5B,MAAM,YAAY,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,cAAc;QAClD,IAAI,EAAU,EACV,EAAE,GAAG,YAAY,CAAC,CAAC,oCAAoC;QAC3D,IAAI,EAAU,EACV,EAAE,GAAG,YAAY,CAAC,CAAC,oCAAoC;QAE3D,wDAAwD;QACxD,IAAI,YAAY,GAAG,GAAG,CAAC,SAAS,EAAE,CAAC;YAC/B,gCAAgC;YAChC,EAAE,GAAG,GAAG,CAAC,CAAC,qCAAqC;YAC/C,EAAE,GAAG,GAAG,CAAC,CAAC,4CAA4C;YACtD,EAAE,GAAG,CAAC,CAAC;YACP,EAAE,GAAG,CAAC,CAAC;QACX,CAAC,MAAM,CAAC;YACJ,+CAA+C;YAC/C,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACnB,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACnB,IAAI,EAAE,GAAG,GAAG,EAAE,CAAC;gBACX,oCAAoC;gBACpC,EAAE,GAAG,GAAG,CAAC;gBACT,EAAE,GAAG,CAAC,CAAC;gBACP,EAAE,GAAG,CAAC,CAAC;YACX,CAAC,MAAM,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC;gBACjB,oCAAoC;gBACpC,EAAE,GAAG,EAAE,CAAC;gBACR,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;gBACX,EAAE,GAAG,CAAC,CAAC;YACX,CAAC;QACL,CAAC;QAED,IAAI,EAAE,GAAG,GAAG,EAAE,CAAC;YACX,oCAAoC;YACpC,EAAE,GAAG,GAAG,CAAC;YACT,6BAA6B;YAC7B,IAAI,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC;gBACX,EAAE,GAAG,GAAG,CAAC;YACb,CAAC,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;gBAChB,EAAE,GAAG,EAAE,CAAC;YACZ,CAAC,MAAM,CAAC;gBACJ,EAAE,GAAG,CAAC,CAAC,CAAC;gBACR,EAAE,GAAG,CAAC,CAAC;YACX,CAAC;QACL,CAAC,MAAM,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC;YACjB,oCAAoC;YACpC,EAAE,GAAG,EAAE,CAAC;YACR,6BAA6B;YAC7B,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,CAAC;gBACf,EAAE,GAAG,CAAC,CAAC;YACX,CAAC,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;gBACpB,EAAE,GAAG,EAAE,CAAC;YACZ,CAAC,MAAM,CAAC;gBACJ,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;gBACZ,EAAE,GAAG,CAAC,CAAC;YACX,CAAC;QACL,CAAC;QACD,2CAA2C;QAC3C,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;QACxD,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;QAExD,+CAA+C;QAC/C,MAAM,GAAG,qKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,UAAU,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;QACtB,MAAM,GAAG,qKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,UAAU,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;QACtB,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAClB,MAAM,EAAE,oKAAG,cAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACjC,GAAG,CAAC,aAAa,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,oBAAoB;QAEhD,MAAM,aAAa,GAAG,EAAE,GAAG,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC,aAAa,EAAE,GAAG,SAAS,GAAG,SAAS,CAAC,CAAC,6BAA6B;QAE9H,IAAI,aAAa,EAAE,CAAC;YAChB,OAAO,GAAG,CAAC,MAAM,EAAE,CAAC;QACxB,CAAC;QACD,OAAO,CAAC,CAAC,CAAC;IACd,CAAC;IAED;;;;;;;;;;;OAWG,CACI,MAAM,CACT,CAAS,EACT,CAAS,EACT,aAAqB,EACrB,cAAsB,EACtB,KAA4B,EAC5B,IAA2B,EAC3B,UAAiC,EACI;YAArC,wFAAgC,KAAK;QAErC,IAAI,oBAAoB,EAAE,CAAC;YACvB,mGAAmG;YACnG,2GAA2G;YAC3G,yGAAyG;YACzG,6EAA6E;YAC7E,uEAAuE;YACvE,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC;gBACnB,GAAG,CAAC,WAAW,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;YACjC,CAAC;YAED,GAAG,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE,aAAa,EAAE,cAAc,mKAAE,UAAM,CAAC,gBAAgB,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;YAElH,MAAM,EAAE,oKAAG,cAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAChC,KAAK,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YACtB,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;QAClD,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE,aAAa,EAAE,cAAc,EAAE,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;QACzF,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,UAAU;IACV;;;OAGG,CACI,MAAM,CAAC,IAAI,GAAA;QACd,OAAO,IAAI,GAAG,mKAAC,UAAO,CAAC,IAAI,EAAE,EAAE,4KAAO,CAAC,IAAI,EAAE,CAAC,CAAC;IACnD,CAAC;IAED;;;;;;;;;;OAUG,CACI,MAAM,CAAC,SAAS,CACnB,CAAS,EACT,CAAS,EACT,aAAqB,EACrB,cAAsB,EACtB,KAA4B,EAC5B,IAA2B,EAC3B,UAAiC,EAAA;QAEjC,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;QAE1B,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,aAAa,EAAE,cAAc,EAAE,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;IACvF,CAAC;IAED;;;;;;;OAOG,CACI,MAAM,CAAC,eAAe,CAAC,MAAe,EAAE,GAAY,EAAwD;oBAAtD,mOAA+B,SAAM,CAAC,gBAAgB;QAC/G,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,sKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,sKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACnE,OAAO,GAAG,CAAC,iBAAiB,CAAC,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;IAC7D,CAAC;IAED;;;;;;;;OAQG,CACI,MAAM,CAAC,iBAAiB,CAAC,MAAe,EAAE,GAAY,EAAE,MAAW,EAAwD;oBAAtD,mOAA+B,SAAM,CAAC,gBAAgB;QAC9H,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC/B,MAAM,SAAS,GAAG,GAAG,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;QAC9D,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;QAC5G,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;QACvB,MAAM,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;QAE7B,OAAO,GAAG,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;IACrD,CAAC;IAED;;;;;OAKG,CACI,MAAM,CAAC,SAAS,CAAC,GAAuB,EAAE,MAA6B,EAAA;QAC1E,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,sKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,sKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACnE,GAAG,CAAC,cAAc,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;QAExC,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;;OAMG,CACI,MAAM,CAAC,cAAc,CAAC,GAAuB,EAAE,MAA6B,EAAE,MAAW,EAAA;0KAC5F,UAAO,CAAC,yBAAyB,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;0KACrE,UAAO,CAAC,oBAAoB,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;QACtE,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;QAC3B,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;QAE7B,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC;QAC7B,MAAM,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC;QAEzB,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC;YAC5B,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;YACtB,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC;YACb,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC;YACb,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC;YACb,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC;QACzB,CAAC;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;;;;;OASG,CACI,iBAAiB,CACpB,OAAc,EACd,OAAc,EACd,aAAqB,EACrB,cAAsB,EACtB,KAA4B,EAC5B,IAA2B,EAC3B,UAAiC,EAAA;QAEjC,MAAM,MAAM,qKAAG,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACpC,KAAK,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAClC,MAAM,CAAC,aAAa,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QACzC,MAAM,CAAC,MAAM,EAAE,CAAC;QAEhB,MAAM,MAAM,oKAAG,cAAW,CAAC,iBAAiB,CAAC;QAC7C,MAAM,gBAAgB,GAAG,+KAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC/C,gBAAgB,CAAC,CAAC,GAAG,AAAC,OAAO,GAAG,aAAa,CAAC,EAAG,CAAC,GAAG,CAAC,CAAC;QACvD,gBAAgB,CAAC,CAAC,GAAG,CAAC,CAAE,AAAD,OAAQ,GAAG,cAAc,CAAC,EAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAC3D,gBAAgB,CAAC,CAAC,oDAAG,MAAM,CAAE,qBAAqB,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,kDAAC,MAAM,CAAE,eAAe,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE1F,uFAAuF;QACvF,MAAM,eAAe,qKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,CAAC;QACjH,MAAM,QAAQ,qKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACvC,MAAM,OAAO,GAAG,+KAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;0KACtC,UAAO,CAAC,yBAAyB,CAAC,gBAAgB,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;yKACtE,WAAO,CAAC,yBAAyB,CAAC,eAAe,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;QAEpE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAC/B,OAAO,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAChD,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;IAC/B,CAAC;IA9nBD;;;;;;OAMG,CACH,YACI,iBAAA,EAAmB,CACZ,MAAe,EACtB,cAAA,EAAgB,CACT,SAAkB,EACzB,yCAAA,EAA2C,CACpC,SAAiB,MAAM,CAAC,SAAS,EACxC,6HAAA,EAA+H,CACxH,+KAAkB,UAAO,CAAA;QANzB,IAAA,CAAA,MAAM,GAAN,MAAM,CAAS;QAEf,IAAA,CAAA,SAAS,GAAT,SAAS,CAAS;QAElB,IAAA,CAAA,MAAM,GAAN,MAAM,CAA2B;QAEjC,IAAA,CAAA,OAAO,GAAP,OAAO,CAAkB;IACjC,CAAC;;AApBoB,IAAA,WAAW,GAAG,8KAAA,AAAU,EAAC,CAAC,oKAAE,UAAO,CAAC,IAAI,CAAC,CAAC;AACnD,IAAA,WAAW,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;AA0VzB,IAAA,SAAS,GAAG,UAAU,CAAC;AACvB,IAAA,KAAK,GAAG,IAAI,CAAC;AAmT1B,SAAU,gBAAgB,CAAC,KAAY,EAAE,CAAS,EAAE,CAAS,EAAE,KAAuB,EAAE,MAAwB;0BAAE,eAAe,kDAAG,KAAK;IAC3I,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;IAE1B,qBAAqB,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,eAAe,CAAC,CAAC;IAE3E,OAAO,MAAM,CAAC;AAClB,CAAC;AAcK,SAAU,qBAAqB,CACjC,KAAY,EACZ,CAAS,EACT,CAAS,EACT,KAAuB,EACvB,MAAW,EACX,MAAwB;0BACxB,eAAe,kDAAG,KAAK,yBACvB,oBAAoB,6CAAG,KAAK;IAE5B,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;IAEjC,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,YAAa,CAAC,EAAE,CAAC;QAC7C,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,MAAM,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC;IACvC,MAAM,YAAY,GAAG,MAAM,CAAC,eAAe,EAAE,CAAC;IAC9C,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,cAAc,EAAE,EAAE,YAAY,CAAC,CAAC;IAEvG,6CAA6C;IAC7C,MAAM,QAAQ,GAAG,CAAC,GAAG,MAAM,CAAC,uBAAuB,EAAE,CAAC;IACtD,CAAC,GAAG,CAAC,GAAG,QAAQ,GAAG,EAAE,CAAC;IACtB,CAAC,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,YAAY,GAAG,EAAE,GAAG,MAAM,CAAC,CAAC;IAEhD,MAAM,CAAC,MAAM,CACT,CAAC,EACD,CAAC,EACD,KAAK,EACL,MAAM,EACN,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,mKAAC,SAAM,CAAC,gBAAgB,EACvC,eAAe,CAAC,CAAC,mKAAC,SAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,MAAM,CAAC,aAAa,EAAE,EAClE,MAAM,CAAC,mBAAmB,EAAE,EAC5B,oBAAoB,CACvB,CAAC;IACF,OAAO,KAAK,CAAC;AACjB,CAAC;AAUK,SAAU,6BAA6B,CAAC,KAAY,EAAE,CAAS,EAAE,CAAS,EAAE,MAAe;IAC7F,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;IAE1B,kCAAkC,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;IAEhE,OAAO,MAAM,CAAC;AAClB,CAAC;AAWK,SAAU,kCAAkC,CAAC,KAAY,EAAE,CAAS,EAAE,CAAS,EAAE,MAAW,EAAE,MAAe;IAC/G,IAAI,qKAAC,cAAW,EAAE,CAAC;QACf,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;IAEjC,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,YAAa,CAAC,EAAE,CAAC;QAC7C,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;IAC7C,CAAC;IAED,MAAM,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC;IACvC,MAAM,YAAY,GAAG,MAAM,CAAC,eAAe,EAAE,CAAC;IAC9C,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,cAAc,EAAE,EAAE,YAAY,CAAC,CAAC;IACvG,MAAM,QAAQ,qKAAG,SAAM,CAAC,QAAQ,EAAE,CAAC;IAEnC,6CAA6C;IAC7C,MAAM,QAAQ,GAAG,CAAC,GAAG,MAAM,CAAC,uBAAuB,EAAE,CAAC;IACtD,CAAC,GAAG,CAAC,GAAG,QAAQ,GAAG,EAAE,CAAC;IACtB,CAAC,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,YAAY,GAAG,EAAE,GAAG,MAAM,CAAC,CAAC;IAChD,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,CAAC,mBAAmB,EAAE,CAAC,CAAC;IACrF,OAAO,KAAK,CAAC;AACjB,CAAC;AAED,SAAS,mBAAmB,CACxB,WAAkC,EAClC,WAAkE,EAClE,IAAkB,EAClB,KAAa,EACb,SAAmB,EACnB,gBAA0B,EAC1B,iBAA4C,EAC5C,gBAA0B;IAE1B,MAAM,GAAG,GAAG,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;IAE1D,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,SAAS,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,KAAK,EAAE,gBAAgB,CAAC,CAAC;IAC7G,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;QACzB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,IAAI,CAAC,SAAS,IAAI,WAAW,IAAI,IAAI,IAAI,MAAM,CAAC,QAAQ,IAAI,WAAW,CAAC,QAAQ,EAAE,CAAC;QAC/E,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,OAAO,MAAM,CAAC;AAClB,CAAC;AAED,SAAS,YAAY,CACjB,KAAY,EACZ,WAAkE,EAClE,SAAyB,EACzB,SAAmB,EACnB,gBAA0B,EAC1B,iBAA4C;IAE5C,IAAI,WAAW,GAAG,IAAI,CAAC;IAEvB,MAAM,2BAA2B,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,aAAa,IAAI,KAAK,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,sBAAsB,KAAK,KAAK,CAAC,YAAY,CAAC,CAAC;IACrJ,MAAM,aAAa,GAAG,KAAK,CAAC,sBAAsB,IAAI,KAAK,CAAC,YAAY,CAAC;IACzE,MAAM,MAAM,GAAG,oBAAoB,CAAC,qBAAqB,IAAI,mBAAmB,CAAC;IAEjF,IAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,CAAE,CAAC;QACnE,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAErC,IAAI,SAAS,EAAE,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBACvB,SAAS;YACb,CAAC;QACL,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YAClE,SAAS;QACb,CAAC;QAED,MAAM,YAAY,GAAG,2BAA2B,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACxF,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC;QAEnE,IAAI,IAAI,CAAC,gBAAgB,IAAK,IAAa,CAAC,yBAAyB,EAAE,CAAC;YACpE,8EAA8E;YAC9E,MAAM,MAAM,GAAG,MAAM,CAAC,WAAW,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,iBAAiB,CAAC,CAAC;YAC5F,IAAI,MAAM,EAAE,CAAC;gBACT,IAAI,gBAAgB,EAAE,CAAC;oBACnB,iEAAiE;oBACjE,OAAO,MAAM,CAAC;gBAClB,CAAC;gBACD,MAAM,SAAS,qKAAG,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBACvC,MAAM,YAAY,GAAI,IAAa,CAAC,4BAA4B,EAAE,CAAC;gBACnE,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;oBACvD,IAAI,SAAS,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC;wBACvC,SAAS;oBACb,CAAC;oBACD,MAAM,UAAU,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;oBACvC,UAAU,CAAC,aAAa,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;oBAC3C,MAAM,MAAM,GAAG,MAAM,CAAC,WAAW,EAAE,WAAW,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,IAAI,CAAC,CAAC;oBAEvH,IAAI,MAAM,EAAE,CAAC;wBACT,WAAW,GAAG,MAAM,CAAC;wBACrB,WAAW,CAAC,iBAAiB,GAAG,KAAK,CAAC;wBAEtC,IAAI,SAAS,EAAE,CAAC;4BACZ,OAAO,WAAW,CAAC;wBACvB,CAAC;oBACL,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,MAAM,MAAM,GAAG,MAAM,CAAC,WAAW,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,gBAAgB,EAAE,iBAAiB,CAAC,CAAC;YAE7G,IAAI,MAAM,EAAE,CAAC;gBACT,WAAW,GAAG,MAAM,CAAC;gBAErB,IAAI,SAAS,EAAE,CAAC;oBACZ,OAAO,WAAW,CAAC;gBACvB,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED,OAAO,WAAW,IAAI,wKAAI,cAAW,EAAE,CAAC;AAC5C,CAAC;AAED,SAAS,iBAAiB,CACtB,KAAY,EACZ,WAAkE,EAClE,SAAyB,EACzB,iBAA4C;IAE5C,IAAI,qKAAC,cAAW,EAAE,CAAC;QACf,OAAO,IAAI,CAAC;IAChB,CAAC;IACD,MAAM,YAAY,GAAkB,EAAE,CAAC;IACvC,MAAM,2BAA2B,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,aAAa,IAAI,KAAK,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,sBAAsB,KAAK,KAAK,CAAC,YAAY,CAAC,CAAC;IACrJ,MAAM,aAAa,GAAG,KAAK,CAAC,sBAAsB,IAAI,KAAK,CAAC,YAAY,CAAC;IACzE,MAAM,MAAM,GAAG,oBAAoB,CAAC,qBAAqB,IAAI,mBAAmB,CAAC;IAEjF,IAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,CAAE,CAAC;QACnE,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAErC,IAAI,SAAS,EAAE,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBACvB,SAAS;YACb,CAAC;QACL,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YAClE,SAAS;QACb,CAAC;QAED,MAAM,YAAY,GAAG,2BAA2B,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACxF,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC;QAEnE,IAAI,IAAI,CAAC,gBAAgB,IAAK,IAAa,CAAC,yBAAyB,EAAE,CAAC;YACpE,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,iBAAiB,CAAC,CAAC;YACrF,IAAI,MAAM,EAAE,CAAC;gBACT,MAAM,SAAS,qKAAG,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBACvC,MAAM,YAAY,GAAI,IAAa,CAAC,4BAA4B,EAAE,CAAC;gBACnE,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;oBACvD,IAAI,SAAS,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC;wBACvC,SAAS;oBACb,CAAC;oBACD,MAAM,UAAU,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;oBACvC,UAAU,CAAC,aAAa,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;oBAC3C,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAiB,EAAE,IAAI,CAAC,CAAC;oBAEjG,IAAI,MAAM,EAAE,CAAC;wBACT,MAAM,CAAC,iBAAiB,GAAG,KAAK,CAAC;wBACjC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBAC9B,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAiB,CAAC,CAAC;YAEvF,IAAI,MAAM,EAAE,CAAC;gBACT,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC9B,CAAC;QACL,CAAC;IACL,CAAC;IAED,OAAO,YAAY,CAAC;AACxB,CAAC;AAWK,SAAU,oBAAoB,CAAC,KAAY,EAAE,CAAS,EAAE,CAAS,EAAE,SAAyB,EAAE,SAAmB,EAAE,MAAyB;IAC9I,IAAI,qKAAC,cAAW,EAAE,CAAC;QACf,OAAO,IAAI,CAAC;IAChB,CAAC;IACD,MAAM,MAAM,GAAG,YAAY,CACvB,KAAK,EACL,CAAC,KAAK,EAAE,EAAE;QACN,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;YACzB,KAAK,CAAC,eAAe,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;QACvC,CAAC;QAED,qBAAqB,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,eAAe,EAAE,MAAM,IAAI,IAAI,CAAC,CAAC;QACjF,OAAO,KAAK,CAAC,eAAe,CAAC;IACjC,CAAC,EACD,SAAS,EACT,SAAS,EACT,IAAI,CACP,CAAC;IACF,IAAI,MAAM,EAAE,CAAC;QACT,MAAM,CAAC,GAAG,GAAG,gBAAgB,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,oKAAE,SAAM,CAAC,QAAQ,EAAE,EAAE,MAAM,IAAI,IAAI,CAAC,CAAC;IAClF,CAAC;IACD,OAAO,MAAM,CAAC;AAClB,CAAC;AAaK,SAAU,IAAI,CAChB,KAAY,EACZ,CAAS,EACT,CAAS,EACT,SAAyB,EACzB,SAAmB,EACnB,MAAyB,EACzB,iBAA4C;gCAC5C,qBAAqB,4CAAG,KAAK;IAE7B,MAAM,MAAM,GAAG,YAAY,CACvB,KAAK,EACL,CAAC,KAAK,EAAE,oBAAoB,EAAE,EAAE;QAC5B,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;YACzB,KAAK,CAAC,eAAe,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;QACvC,CAAC;QAED,qBAAqB,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,eAAe,EAAE,MAAM,IAAI,IAAI,EAAE,KAAK,EAAE,oBAAoB,CAAC,CAAC;QAC9G,OAAO,KAAK,CAAC,eAAe,CAAC;IACjC,CAAC,EACD,SAAS,EACT,SAAS,EACT,KAAK,EACL,iBAAiB,CACpB,CAAC;IACF,IAAI,MAAM,EAAE,CAAC;QACT,MAAM,CAAC,GAAG,GAAG,gBAAgB,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,2KAAM,CAAC,QAAQ,EAAE,EAAE,MAAM,IAAI,IAAI,CAAC,CAAC;IAClF,CAAC;IACD,OAAO,MAAM,CAAC;AAClB,CAAC;AAYK,SAAU,WAAW,CAAC,KAAY,EAAE,GAAQ,EAAE,SAAyB,EAAE,SAAmB,EAAE,iBAA4C;IAC5I,MAAM,MAAM,GAAG,YAAY,CACvB,KAAK,EACL,CAAC,KAAK,EAAE,EAAE;QACN,IAAI,CAAC,KAAK,CAAC,yBAAyB,EAAE,CAAC;YACnC,KAAK,CAAC,yBAAyB,qKAAG,SAAM,CAAC,QAAQ,EAAE,CAAC;QACxD,CAAC;QACD,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAEnD,IAAI,CAAC,KAAK,CAAC,sBAAsB,EAAE,CAAC;YAChC,KAAK,CAAC,sBAAsB,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;QAC9C,CAAC;QAED,GAAG,CAAC,cAAc,CAAC,GAAG,EAAE,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,sBAAsB,CAAC,CAAC;QACvF,OAAO,KAAK,CAAC,sBAAsB,CAAC;IACxC,CAAC,EACD,SAAS,EACT,SAAS,EACT,KAAK,EACL,iBAAiB,CACpB,CAAC;IACF,IAAI,MAAM,EAAE,CAAC;QACT,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC;IACrB,CAAC;IACD,OAAO,MAAM,CAAC;AAClB,CAAC;AAaK,SAAU,SAAS,CAAC,KAAY,EAAE,CAAS,EAAE,CAAS,EAAE,SAAyB,EAAE,MAAe,EAAE,iBAA4C;IAClJ,OAAO,iBAAiB,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,CAAG,CAAD,eAAiB,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,IAAI,IAAI,CAAC,EAAE,SAAS,EAAE,iBAAiB,CAAC,CAAC;AACnI,CAAC;AAUK,SAAU,gBAAgB,CAAC,KAAY,EAAE,GAAQ,EAAE,SAAyB,EAAE,iBAA4C;IAC5H,OAAO,iBAAiB,CACpB,KAAK,EACL,CAAC,KAAK,EAAE,EAAE;QACN,IAAI,CAAC,KAAK,CAAC,yBAAyB,EAAE,CAAC;YACnC,KAAK,CAAC,yBAAyB,qKAAG,SAAM,CAAC,QAAQ,EAAE,CAAC;QACxD,CAAC;QACD,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAEnD,IAAI,CAAC,KAAK,CAAC,sBAAsB,EAAE,CAAC;YAChC,KAAK,CAAC,sBAAsB,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;QAC9C,CAAC;QAED,GAAG,CAAC,cAAc,CAAC,GAAG,EAAE,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,sBAAsB,CAAC,CAAC;QACvF,OAAO,KAAK,CAAC,sBAAsB,CAAC;IACxC,CAAC,EACD,SAAS,EACT,iBAAiB,CACpB,CAAC;AACN,CAAC;AAUK,SAAU,aAAa,CAAC,MAAc;iBAAE,MAAM,2DAAG,GAAG,EAAE,SAAkB,iDAAE,MAAgB;IAC5F,OAAO,kBAAkB,CAAC,MAAM,EAAE,IAAI,GAAG,mKAAC,UAAO,CAAC,IAAI,EAAE,oKAAE,UAAO,CAAC,IAAI,EAAE,EAAE,MAAM,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;AAClH,CAAC;AAWK,SAAU,kBAAkB,CAAC,MAAc,EAAE,MAAW;QAAE,MAAM,oEAAG,GAAG,EAAE,SAAkB,iDAAE,MAAgB;IAC9G,IAAI,CAAC,SAAS,EAAE,CAAC;QACb,SAAS,GAAG,MAAM,CAAC,cAAc,EAAE,CAAC;IACxC,CAAC;IACD,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;IAEvB,IAAI,MAAM,EAAE,CAAC;QACT,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACnC,CAAC,MAAM,CAAC;QACJ,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC5C,CAAC;IACD,MAAM,OAAO,qKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IACtC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/D,MAAM,YAAY,qKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3C,4KAAO,CAAC,oBAAoB,CAAC,OAAO,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC;sKAC/D,UAAO,CAAC,cAAc,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;IAEvD,OAAO,MAAM,CAAC;AAClB,CAAC;AAOK,SAAU,gBAAgB,CAAC,UAAwB,EAAE,WAA0B;IACjF,IAAI,WAAW,EAAE,CAAC;QACd,WAAW,CAAC,SAAS,CAAC,aAAa,GAAG;yBAAU,MAAM,2DAAG,GAAG,EAAE,SAAkB,iDAAE,MAAgB;YAC9F,OAAO,kBAAkB,CAAC,IAAI,EAAE,IAAI,GAAG,mKAAC,UAAO,CAAC,IAAI,EAAE,oKAAE,UAAO,CAAC,IAAI,EAAE,EAAE,MAAM,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;QAChH,CAAC,CAAC;QAEF,WAAW,CAAC,SAAS,CAAC,kBAAkB,GAAG,SAAU,MAAW;yBAAE,MAAM,2DAAG,GAAG,EAAE,SAAkB,iDAAE,MAAgB;YAChH,OAAO,kBAAkB,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;QACvE,CAAC,CAAC;IACN,CAAC;IAED,IAAI,CAAC,UAAU,EAAE,CAAC;QACd,OAAO;IACX,CAAC;+JAED,gBAAa,CAAC,mBAAmB,GAAG,IAAI,CAAC;IAEzC,UAAU,CAAC,SAAS,CAAC,gBAAgB,GAAG,SAAU,CAAS,EAAE,CAAS,EAAE,KAAuB,EAAE,MAAwB;8BAAE,eAAe,kDAAG,KAAK;QAC9I,OAAO,gBAAgB,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,eAAe,CAAC,CAAC;IACxE,CAAC,CAAC;AACN,CAAC", "debugId": null}}, {"offset": {"line": 1509, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Culling/ray.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Culling/ray.ts"], "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport type { Matrix } from \"../Maths/math.vector\";\r\nimport type { PickingInfo } from \"../Collisions/pickingInfo\";\r\n\r\nimport { Scene } from \"../scene\";\r\nimport { Camera } from \"../Cameras/camera\";\r\n\r\nimport type { MeshPredicate, TrianglePickingPredicate, <PERSON> } from \"./ray.core\";\r\nimport {\r\n    AddRayExtensions,\r\n    CreatePickingRayInCameraSpace,\r\n    CreatePickingRayInCameraSpaceToRef,\r\n    CreatePickingRayToRef,\r\n    MultiPick,\r\n    MultiPickWithRay,\r\n    Pick,\r\n    PickWithBoundingInfo,\r\n    PickWithRay,\r\n} from \"./ray.core\";\r\n\r\nexport * from \"./ray.core\";\r\n\r\n// Picking\r\nAddRayExtensions(Scene, Camera);\r\n\r\nScene.prototype.createPickingRayToRef = function (\r\n    x: number,\r\n    y: number,\r\n    world: Nullable<Matrix>,\r\n    result: Ray,\r\n    camera: Nullable<Camera>,\r\n    cameraViewSpace = false,\r\n    enableDistantPicking = false\r\n): Scene {\r\n    return CreatePickingRayToRef(this, x, y, world, result, camera, cameraViewSpace, enableDistantPicking);\r\n};\r\n\r\nScene.prototype.createPickingRayInCameraSpace = function (x: number, y: number, camera?: Camera): Ray {\r\n    return CreatePickingRayInCameraSpace(this, x, y, camera);\r\n};\r\n\r\nScene.prototype.createPickingRayInCameraSpaceToRef = function (x: number, y: number, result: Ray, camera?: Camera): Scene {\r\n    return CreatePickingRayInCameraSpaceToRef(this, x, y, result, camera);\r\n};\r\n\r\nScene.prototype.pickWithBoundingInfo = function (x: number, y: number, predicate?: MeshPredicate, fastCheck?: boolean, camera?: Nullable<Camera>): Nullable<PickingInfo> {\r\n    return PickWithBoundingInfo(this, x, y, predicate, fastCheck, camera);\r\n};\r\n\r\nScene.prototype.pick = function (\r\n    x: number,\r\n    y: number,\r\n    predicate?: MeshPredicate,\r\n    fastCheck?: boolean,\r\n    camera?: Nullable<Camera>,\r\n    trianglePredicate?: TrianglePickingPredicate,\r\n    _enableDistantPicking = false\r\n): PickingInfo {\r\n    return Pick(this, x, y, predicate, fastCheck, camera, trianglePredicate, _enableDistantPicking);\r\n};\r\n\r\nScene.prototype.pickWithRay = function (ray: Ray, predicate?: MeshPredicate, fastCheck?: boolean, trianglePredicate?: TrianglePickingPredicate): Nullable<PickingInfo> {\r\n    return PickWithRay(this, ray, predicate, fastCheck, trianglePredicate);\r\n};\r\n\r\nScene.prototype.multiPick = function (x: number, y: number, predicate?: MeshPredicate, camera?: Camera, trianglePredicate?: TrianglePickingPredicate): Nullable<PickingInfo[]> {\r\n    return MultiPick(this, x, y, predicate, camera, trianglePredicate);\r\n};\r\n\r\nScene.prototype.multiPickWithRay = function (ray: Ray, predicate?: MeshPredicate, trianglePredicate?: TrianglePickingPredicate): Nullable<PickingInfo[]> {\r\n    return MultiPickWithRay(this, ray, predicate, trianglePredicate);\r\n};\r\n"], "names": [], "mappings": ";AAIA,OAAO,EAAE,KAAK,EAAE,MAAM,UAAU,CAAC;AACjC,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAG3C,OAAO,EACH,gBAAgB,EAChB,6BAA6B,EAC7B,kCAAkC,EAClC,qBAAqB,EACrB,SAAS,EACT,gBAAgB,EAChB,IAAI,EACJ,oBAAoB,EACpB,WAAW,GACd,MAAM,YAAY,CAAC;;;;;AAIpB,UAAU;qKACV,mBAAA,AAAgB,kJAAC,QAAK,8JAAE,SAAM,CAAC,CAAC;gJAEhC,QAAK,CAAC,SAAS,CAAC,qBAAqB,GAAG,SACpC,CAAS,EACT,CAAS,EACT,KAAuB,EACvB,MAAW,EACX,MAAwB;0BACxB,eAAe,kDAAG,KAAK,yBACvB,oBAAoB,6CAAG,KAAK;IAE5B,WAAO,yLAAA,AAAqB,EAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,eAAe,EAAE,oBAAoB,CAAC,CAAC;AAC3G,CAAC,CAAC;gJAEF,QAAK,CAAC,SAAS,CAAC,6BAA6B,GAAG,SAAU,CAAS,EAAE,CAAS,EAAE,MAAe;IAC3F,4KAAO,gCAAA,AAA6B,EAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;AAC7D,CAAC,CAAC;AAEF,wJAAK,CAAC,SAAS,CAAC,kCAAkC,GAAG,SAAU,CAAS,EAAE,CAAS,EAAE,MAAW,EAAE,MAAe;IAC7G,OAAO,0MAAA,AAAkC,EAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;AAC1E,CAAC,CAAC;gJAEF,QAAK,CAAC,SAAS,CAAC,oBAAoB,GAAG,SAAU,CAAS,EAAE,CAAS,EAAE,SAAyB,EAAE,SAAmB,EAAE,MAAyB;IAC5I,4KAAO,uBAAA,AAAoB,EAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;AAC1E,CAAC,CAAC;gJAEF,QAAK,CAAC,SAAS,CAAC,IAAI,GAAG,SACnB,CAAS,EACT,CAAS,EACT,SAAyB,EACzB,SAAmB,EACnB,MAAyB,EACzB,iBAA4C;gCAC5C,qBAAqB,4CAAG,KAAK;IAE7B,WAAO,wKAAA,AAAI,EAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,iBAAiB,EAAE,qBAAqB,CAAC,CAAC;AACpG,CAAC,CAAC;gJAEF,QAAK,CAAC,SAAS,CAAC,WAAW,GAAG,SAAU,GAAQ,EAAE,SAAyB,EAAE,SAAmB,EAAE,iBAA4C;IAC1I,4KAAO,cAAA,AAAW,EAAC,IAAI,EAAE,GAAG,EAAE,SAAS,EAAE,SAAS,EAAE,iBAAiB,CAAC,CAAC;AAC3E,CAAC,CAAC;gJAEF,QAAK,CAAC,SAAS,CAAC,SAAS,GAAG,SAAU,CAAS,EAAE,CAAS,EAAE,SAAyB,EAAE,MAAe,EAAE,iBAA4C;IAChJ,4KAAO,YAAA,AAAS,EAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,iBAAiB,CAAC,CAAC;AACvE,CAAC,CAAC;gJAEF,QAAK,CAAC,SAAS,CAAC,gBAAgB,GAAG,SAAU,GAAQ,EAAE,SAAyB,EAAE,iBAA4C;IAC1H,4KAAO,mBAAA,AAAgB,EAAC,IAAI,EAAE,GAAG,EAAE,SAAS,EAAE,iBAAiB,CAAC,CAAC;AACrE,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 1558, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Culling/Helper/boundingInfoHelper.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Culling/Helper/boundingInfoHelper.ts"], "sourcesContent": ["import type { AbstractMesh } from \"core/Meshes/abstractMesh\";\r\nimport type { AbstractEngine } from \"core/Engines/abstractEngine\";\r\nimport type { IBoundingInfoHelperPlatform } from \"./IBoundingInfoHelperPlatform\";\r\nimport type { ThinEngine } from \"core/Engines\";\r\nimport { Logger } from \"core/Misc/logger\";\r\n\r\n/**\r\n * Utility class to help with bounding info management\r\n * Warning: using the BoundingInfoHelper class may be slower than executing calculations on the CPU!\r\n * This will happen if there are a lot of meshes / few vertices (like with the BrainStem model)\r\n * The BoundingInfoHelper will perform better if there are few meshes / a lot of vertices\r\n *  https://playground.babylonjs.com/#QPOERJ#9 : WebGL\r\n *  https://playground.babylonjs.com/#QPOERJ#10 : WebGPU\r\n */\r\nexport class BoundingInfoHelper {\r\n    private _platform: IBoundingInfoHelperPlatform;\r\n    private _engine: AbstractEngine;\r\n\r\n    /**\r\n     * Creates a new BoundingInfoHelper\r\n     * @param engine defines the engine to use\r\n     */\r\n    public constructor(engine: AbstractEngine) {\r\n        this._engine = engine;\r\n    }\r\n\r\n    private async _initializePlatformAsync() {\r\n        if (!this._platform) {\r\n            if (this._engine.getCaps().supportComputeShaders) {\r\n                const module = await import(\"./computeShaderBoundingHelper\");\r\n                this._platform = new module.ComputeShaderBoundingHelper(this._engine);\r\n            } else if (this._engine.getCaps().supportTransformFeedbacks) {\r\n                const module = await import(\"./transformFeedbackBoundingHelper\");\r\n                this._platform = new module.TransformFeedbackBoundingHelper(this._engine as ThinEngine);\r\n            } else {\r\n                throw new Error(\"Your engine does not support Compute Shaders or Transform Feedbacks\");\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Compute the bounding info of a mesh / array of meshes using shaders\r\n     * @param target defines the mesh(es) to update\r\n     * @returns a promise that resolves when the bounding info is/are computed\r\n     */\r\n    public async computeAsync(target: AbstractMesh | AbstractMesh[]): Promise<void> {\r\n        await this._initializePlatformAsync();\r\n        return await this._platform.processAsync(target);\r\n    }\r\n\r\n    /**\r\n     * Register a mesh / array of meshes to be processed per batch\r\n     * This method must be called before calling batchProcess (which can be called several times) and batchFetchResultsAsync\r\n     * @param target defines the mesh(es) to be processed per batch\r\n     * @returns a promise that resolves when the initialization is done\r\n     */\r\n    public async batchInitializeAsync(target: AbstractMesh | AbstractMesh[]): Promise<void> {\r\n        await this._initializePlatformAsync();\r\n        return await this._platform.registerMeshListAsync(target);\r\n    }\r\n\r\n    /**\r\n     * Processes meshes registered with batchRegisterAsync\r\n     * If called multiple times, the second, third, etc calls will perform a union of the bounding boxes calculated in the previous calls\r\n     */\r\n    public batchProcess(): void {\r\n        if (this._platform === null) {\r\n            Logger.Warn(\"Helper is not initialized. Skipping batch.\");\r\n            return;\r\n        }\r\n        this._platform.processMeshList();\r\n    }\r\n\r\n    /**\r\n     * Update the bounding info of the meshes registered with batchRegisterAsync, after batchProcess has been called once or several times\r\n     * @returns a promise that resolves when the bounding info is/are computed\r\n     */\r\n    public async batchFetchResultsAsync(): Promise<void> {\r\n        await this._initializePlatformAsync();\r\n        return await this._platform.fetchResultsForMeshListAsync();\r\n    }\r\n\r\n    /**\r\n     * Dispose and release associated resources\r\n     */\r\n    public dispose(): void {\r\n        if (this._platform) {\r\n            this._platform.dispose();\r\n        }\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AAIA,OAAO,EAAE,MAAM,EAAE,6BAAyB;;AAUpC,MAAO,kBAAkB;IAYnB,KAAK,CAAC,wBAAwB,GAAA;QAClC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAClB,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,qBAAqB,EAAE,CAAC;gBAC/C,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,+BAA+B,CAAC,CAAC;gBAC7D,IAAI,CAAC,SAAS,GAAG,IAAI,MAAM,CAAC,2BAA2B,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC1E,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,yBAAyB,EAAE,CAAC;gBAC1D,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,mCAAmC,CAAC,CAAC;gBACjE,IAAI,CAAC,SAAS,GAAG,IAAI,MAAM,CAAC,+BAA+B,CAAC,IAAI,CAAC,OAAqB,CAAC,CAAC;YAC5F,CAAC,MAAM,CAAC;gBACJ,MAAM,IAAI,KAAK,CAAC,qEAAqE,CAAC,CAAC;YAC3F,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;OAIG,CACI,KAAK,CAAC,YAAY,CAAC,MAAqC,EAAA;QAC3D,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;QACtC,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;IACrD,CAAC;IAED;;;;;OAKG,CACI,KAAK,CAAC,oBAAoB,CAAC,MAAqC,EAAA;QACnE,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;QACtC,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;IAC9D,CAAC;IAED;;;OAGG,CACI,YAAY,GAAA;QACf,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,EAAE,CAAC;qKAC1B,SAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;YAC1D,OAAO;QACX,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,CAAC;IACrC,CAAC;IAED;;;OAGG,CACI,KAAK,CAAC,sBAAsB,GAAA;QAC/B,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;QACtC,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,4BAA4B,EAAE,CAAC;IAC/D,CAAC;IAED;;OAEG,CACI,OAAO,GAAA;QACV,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QAC7B,CAAC;IACL,CAAC;IAvED;;;OAGG,CACH,YAAmB,MAAsB,CAAA;QACrC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IAC1B,CAAC;CAkEJ", "debugId": null}}, {"offset": {"line": 1629, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Culling/Helper/transformFeedbackBoundingHelper.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Culling/Helper/transformFeedbackBoundingHelper.ts"], "sourcesContent": ["import type { Effect } from \"core/Materials/effect\";\r\nimport type { ThinEngine } from \"core/Engines/thinEngine\";\r\nimport { VertexBuffer, Buffer } from \"core/Buffers/buffer\";\r\nimport type { Engine } from \"core/Engines/engine\";\r\nimport { Constants } from \"core/Engines/constants\";\r\nimport type { Nullable } from \"core/types\";\r\nimport type { AbstractMesh } from \"core/Meshes/abstractMesh\";\r\nimport {\r\n    BindBonesParameters,\r\n    BindMorphTargetParameters,\r\n    PrepareAttributesForBakedVertexAnimation,\r\n    PrepareDefinesAndAttributesForMorphTargets,\r\n} from \"core/Materials/materialHelper.functions\";\r\nimport type { Mesh } from \"core/Meshes/mesh\";\r\nimport type { IBoundingInfoHelperPlatform } from \"./IBoundingInfoHelperPlatform\";\r\nimport { extractMinAndMax } from \"core/Maths/math.functions\";\r\nimport { Vector3 } from \"core/Maths/math.vector\";\r\n\r\nimport \"../../Shaders/gpuTransform.vertex\";\r\nimport \"../../Shaders/gpuTransform.fragment\";\r\n\r\n/** @internal */\r\nexport class TransformFeedbackBoundingHelper implements IBoundingInfoHelperPlatform {\r\n    private static _Min = new Vector3();\r\n    private static _Max = new Vector3();\r\n\r\n    private _engine: Nullable<ThinEngine>;\r\n    private _buffers: { [key: number]: Buffer } = {};\r\n    private _effects: { [key: string]: Effect } = {};\r\n    private _meshList: AbstractMesh[];\r\n    private _meshListCounter = 0;\r\n\r\n    /**\r\n     * Creates a new TransformFeedbackBoundingHelper\r\n     * @param engine defines the engine to use\r\n     */\r\n    constructor(engine: ThinEngine) {\r\n        this._engine = engine;\r\n    }\r\n\r\n    /** @internal */\r\n    // eslint-disable-next-line @typescript-eslint/promise-function-async, no-restricted-syntax\r\n    public processAsync(meshes: AbstractMesh | AbstractMesh[]): Promise<void> {\r\n        if (!Array.isArray(meshes)) {\r\n            meshes = [meshes];\r\n        }\r\n\r\n        this._meshListCounter = 0;\r\n\r\n        this._processMeshList(meshes);\r\n\r\n        return Promise.resolve();\r\n    }\r\n\r\n    private _processMeshList(meshes: AbstractMesh[]) {\r\n        const parallelShaderCompile = this._engine!.getCaps().parallelShaderCompile;\r\n\r\n        this._engine!.getCaps().parallelShaderCompile = undefined;\r\n\r\n        for (let i = 0; i < meshes.length; ++i) {\r\n            const mesh = meshes[i];\r\n            const vertexCount = mesh.getTotalVertices();\r\n\r\n            if (vertexCount === 0 || !(mesh as Mesh).getVertexBuffer || !(mesh as Mesh).getVertexBuffer(VertexBuffer.PositionKind)) {\r\n                continue;\r\n            }\r\n\r\n            // Get correct effect\r\n            let computeEffect: Effect;\r\n            const defines: string[] = [];\r\n            const attribs = [VertexBuffer.PositionKind];\r\n\r\n            // Bones\r\n            if (mesh && mesh.useBones && mesh.computeBonesUsingShaders && mesh.skeleton) {\r\n                attribs.push(VertexBuffer.MatricesIndicesKind);\r\n                attribs.push(VertexBuffer.MatricesWeightsKind);\r\n                if (mesh.numBoneInfluencers > 4) {\r\n                    attribs.push(VertexBuffer.MatricesIndicesExtraKind);\r\n                    attribs.push(VertexBuffer.MatricesWeightsExtraKind);\r\n                }\r\n                defines.push(\"#define NUM_BONE_INFLUENCERS \" + mesh.numBoneInfluencers);\r\n                defines.push(\"#define BONETEXTURE \" + mesh.skeleton.isUsingTextureForMatrices);\r\n                defines.push(\"#define BonesPerMesh \" + (mesh.skeleton.bones.length + 1));\r\n            } else {\r\n                defines.push(\"#define NUM_BONE_INFLUENCERS 0\");\r\n            }\r\n\r\n            // Morph\r\n            const numMorphInfluencers = mesh.morphTargetManager\r\n                ? PrepareDefinesAndAttributesForMorphTargets(\r\n                      mesh.morphTargetManager,\r\n                      defines,\r\n                      attribs,\r\n                      mesh,\r\n                      true, // usePositionMorph\r\n                      false, // useNormalMorph\r\n                      false, // useTangentMorph\r\n                      false, // useUVMorph\r\n                      false, // useUV2Morph\r\n                      false // useColorMorph\r\n                  )\r\n                : 0;\r\n\r\n            // Baked Vertex Animation\r\n            const bvaManager = (<Mesh>mesh).bakedVertexAnimationManager;\r\n            if (bvaManager && bvaManager.isEnabled) {\r\n                defines.push(\"#define BAKED_VERTEX_ANIMATION_TEXTURE\");\r\n                PrepareAttributesForBakedVertexAnimation(attribs, mesh, defines);\r\n            }\r\n\r\n            const join = defines.join(\"\\n\");\r\n            if (!this._effects[join]) {\r\n                const uniforms = [\r\n                    \"boneTextureWidth\",\r\n                    \"mBones\",\r\n                    \"morphTargetInfluences\",\r\n                    \"morphTargetCount\",\r\n                    \"morphTargetTextureInfo\",\r\n                    \"morphTargetTextureIndices\",\r\n                    \"bakedVertexAnimationSettings\",\r\n                    \"bakedVertexAnimationTextureSizeInverted\",\r\n                    \"bakedVertexAnimationTime\",\r\n                ];\r\n                const samplers = [\"boneSampler\", \"morphTargets\", \"bakedVertexAnimationTexture\"];\r\n\r\n                const computeEffectOptions = {\r\n                    attributes: attribs,\r\n                    uniformsNames: uniforms,\r\n                    uniformBuffersNames: [],\r\n                    samplers: samplers,\r\n                    defines: join,\r\n                    fallbacks: null,\r\n                    onCompiled: null,\r\n                    onError: null,\r\n                    indexParameters: { maxSimultaneousMorphTargets: numMorphInfluencers },\r\n                    maxSimultaneousLights: 0,\r\n                    transformFeedbackVaryings: [\"outPosition\"],\r\n                };\r\n                computeEffect = this._engine!.createEffect(\"gpuTransform\", computeEffectOptions, this._engine!);\r\n                this._effects[join] = computeEffect;\r\n            } else {\r\n                computeEffect = this._effects[join];\r\n            }\r\n\r\n            this._compute(mesh, computeEffect);\r\n        }\r\n\r\n        this._engine!.getCaps().parallelShaderCompile = parallelShaderCompile;\r\n    }\r\n\r\n    private _compute(mesh: AbstractMesh, effect: Effect): void {\r\n        const engine = this._engine as Engine;\r\n\r\n        // Buffer\r\n        let targetBuffer: Buffer;\r\n        const vertexCount = mesh.getTotalVertices();\r\n\r\n        if (!this._buffers[mesh.uniqueId]) {\r\n            const targetData = new Float32Array(vertexCount * 3);\r\n            targetBuffer = new Buffer(mesh.getEngine(), targetData, true, 3);\r\n            this._buffers[mesh.uniqueId] = targetBuffer;\r\n        } else {\r\n            targetBuffer = this._buffers[mesh.uniqueId];\r\n        }\r\n\r\n        // Bind\r\n        effect.getEngine().enableEffect(effect);\r\n        (mesh as Mesh)._bindDirect(effect, null, true);\r\n\r\n        // Bones\r\n        BindBonesParameters(mesh, effect);\r\n\r\n        // Morph targets\r\n        BindMorphTargetParameters(mesh, effect);\r\n        if (mesh.morphTargetManager && mesh.morphTargetManager.isUsingTextureForTargets) {\r\n            mesh.morphTargetManager._bind(effect);\r\n        }\r\n\r\n        // BVA\r\n        const bvaManager = (<Mesh>mesh).bakedVertexAnimationManager;\r\n\r\n        if (bvaManager && bvaManager.isEnabled) {\r\n            mesh.bakedVertexAnimationManager?.bind(effect, false);\r\n        }\r\n\r\n        // Update\r\n        const arrayBuffer = targetBuffer.getData()! as Float32Array;\r\n        engine.bindTransformFeedbackBuffer(targetBuffer.getBuffer());\r\n        engine.setRasterizerState(false);\r\n        engine.beginTransformFeedback(true);\r\n        engine.drawArraysType(Constants.MATERIAL_PointFillMode, 0, vertexCount);\r\n        engine.endTransformFeedback();\r\n        engine.setRasterizerState(true);\r\n        engine.readTransformFeedbackBuffer(arrayBuffer);\r\n        engine.bindTransformFeedbackBuffer(null);\r\n\r\n        // Update mesh\r\n        if (this._meshListCounter === 0) {\r\n            mesh._refreshBoundingInfo(arrayBuffer, null);\r\n        } else {\r\n            const bb = mesh.getBoundingInfo().boundingBox;\r\n            const extend = extractMinAndMax(arrayBuffer, 0, vertexCount);\r\n\r\n            TransformFeedbackBoundingHelper._Min.copyFrom(bb.minimum).minimizeInPlace(extend.minimum);\r\n            TransformFeedbackBoundingHelper._Max.copyFrom(bb.maximum).maximizeInPlace(extend.maximum);\r\n\r\n            mesh._refreshBoundingInfoDirect({ minimum: TransformFeedbackBoundingHelper._Min, maximum: TransformFeedbackBoundingHelper._Max });\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    // eslint-disable-next-line @typescript-eslint/promise-function-async, no-restricted-syntax\r\n    public registerMeshListAsync(meshes: AbstractMesh | AbstractMesh[]): Promise<void> {\r\n        if (!Array.isArray(meshes)) {\r\n            meshes = [meshes];\r\n        }\r\n\r\n        this._meshList = meshes;\r\n        this._meshListCounter = 0;\r\n\r\n        return Promise.resolve();\r\n    }\r\n\r\n    /** @internal */\r\n    public processMeshList(): void {\r\n        if (this._meshList.length === 0) {\r\n            return;\r\n        }\r\n\r\n        this._processMeshList(this._meshList);\r\n        this._meshListCounter++;\r\n    }\r\n\r\n    /** @internal */\r\n    // eslint-disable-next-line @typescript-eslint/promise-function-async, no-restricted-syntax\r\n    public fetchResultsForMeshListAsync(): Promise<void> {\r\n        this._meshListCounter = 0;\r\n\r\n        return Promise.resolve();\r\n    }\r\n\r\n    /** @internal */\r\n    public dispose(): void {\r\n        for (const key in this._buffers) {\r\n            this._buffers[key].dispose();\r\n        }\r\n        this._buffers = {};\r\n        this._effects = {};\r\n        this._engine = null;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AAEA,OAAO,EAAE,YAAY,EAAE,MAAM,EAAE,gCAA4B;AAK3D,OAAO,EACH,mBAAmB,EACnB,yBAAyB,EACzB,wCAAwC,EACxC,0CAA0C,GAC7C,oDAAgD;AAGjD,OAAO,EAAE,gBAAgB,EAAE,sCAAkC;AAC7D,OAAO,EAAE,OAAO,EAAE,mCAA+B;AAEjD,OAAO,mCAAmC,CAAC;AAC3C,OAAO,qCAAqC,CAAC;;;;;;;AAGvC,MAAO,+BAA+B;IAkBxC,cAAA,EAAgB,CAChB,2FAA2F;IACpF,YAAY,CAAC,MAAqC,EAAA;QACrD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YACzB,MAAM,GAAG;gBAAC,MAAM;aAAC,CAAC;QACtB,CAAC;QAED,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;QAE1B,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAE9B,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;IAEO,gBAAgB,CAAC,MAAsB,EAAA;QAC3C,MAAM,qBAAqB,GAAG,IAAI,CAAC,OAAQ,CAAC,OAAO,EAAE,CAAC,qBAAqB,CAAC;QAE5E,IAAI,CAAC,OAAQ,CAAC,OAAO,EAAE,CAAC,qBAAqB,GAAG,SAAS,CAAC;QAE1D,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,CAAE,CAAC;YACrC,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACvB,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAE5C,IAAI,WAAW,KAAK,CAAC,IAAI,CAAE,IAAa,CAAC,eAAe,IAAI,CAAE,IAAa,CAAC,eAAe,CAAC,2KAAY,CAAC,YAAY,CAAC,EAAE,CAAC;gBACrH,SAAS;YACb,CAAC;YAED,qBAAqB;YACrB,IAAI,aAAqB,CAAC;YAC1B,MAAM,OAAO,GAAa,EAAE,CAAC;YAC7B,MAAM,OAAO,GAAG;4KAAC,eAAY,CAAC,YAAY;aAAC,CAAC;YAE5C,QAAQ;YACR,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,wBAAwB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC1E,OAAO,CAAC,IAAI,6JAAC,eAAY,CAAC,mBAAmB,CAAC,CAAC;gBAC/C,OAAO,CAAC,IAAI,6JAAC,eAAY,CAAC,mBAAmB,CAAC,CAAC;gBAC/C,IAAI,IAAI,CAAC,kBAAkB,GAAG,CAAC,EAAE,CAAC;oBAC9B,OAAO,CAAC,IAAI,6JAAC,eAAY,CAAC,wBAAwB,CAAC,CAAC;oBACpD,OAAO,CAAC,IAAI,4JAAC,gBAAY,CAAC,wBAAwB,CAAC,CAAC;gBACxD,CAAC;gBACD,OAAO,CAAC,IAAI,CAAC,+BAA+B,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBACxE,OAAO,CAAC,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,QAAQ,CAAC,yBAAyB,CAAC,CAAC;gBAC/E,OAAO,CAAC,IAAI,CAAC,uBAAuB,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7E,CAAC,MAAM,CAAC;gBACJ,OAAO,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YACnD,CAAC;YAED,QAAQ;YACR,MAAM,mBAAmB,GAAG,IAAI,CAAC,kBAAkB,0LAC7C,6CAA0C,AAA1C,EACI,IAAI,CAAC,kBAAkB,EACvB,OAAO,EACP,OAAO,EACP,IAAI,EACJ,IAAI,EAAE,AACN,KAAK,EAAE,AACP,KAAK,EAAE,AACP,KAHyB,AAGpB,EAAE,AACP,GAHwB,EAGnB,EAAE,AACP,IAHyB,CAGpB,CAAC,AAFc,QACC,QACC;gBAE1B,CAAC,CAAC;YAER,yBAAyB;YACzB,MAAM,UAAU,GAAU,IAAK,CAAC,2BAA2B,CAAC;YAC5D,IAAI,UAAU,IAAI,UAAU,CAAC,SAAS,EAAE,CAAC;gBACrC,OAAO,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;uMACvD,2CAAA,AAAwC,EAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;YACrE,CAAC;YAED,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBACvB,MAAM,QAAQ,GAAG;oBACb,kBAAkB;oBAClB,QAAQ;oBACR,uBAAuB;oBACvB,kBAAkB;oBAClB,wBAAwB;oBACxB,2BAA2B;oBAC3B,8BAA8B;oBAC9B,yCAAyC;oBACzC,0BAA0B;iBAC7B,CAAC;gBACF,MAAM,QAAQ,GAAG;oBAAC,aAAa;oBAAE,cAAc;oBAAE,6BAA6B;iBAAC,CAAC;gBAEhF,MAAM,oBAAoB,GAAG;oBACzB,UAAU,EAAE,OAAO;oBACnB,aAAa,EAAE,QAAQ;oBACvB,mBAAmB,EAAE,EAAE;oBACvB,QAAQ,EAAE,QAAQ;oBAClB,OAAO,EAAE,IAAI;oBACb,SAAS,EAAE,IAAI;oBACf,UAAU,EAAE,IAAI;oBAChB,OAAO,EAAE,IAAI;oBACb,eAAe,EAAE;wBAAE,2BAA2B,EAAE,mBAAmB;oBAAA,CAAE;oBACrE,qBAAqB,EAAE,CAAC;oBACxB,yBAAyB,EAAE;wBAAC,aAAa;qBAAC;iBAC7C,CAAC;gBACF,aAAa,GAAG,IAAI,CAAC,OAAQ,CAAC,YAAY,CAAC,cAAc,EAAE,oBAAoB,EAAE,IAAI,CAAC,OAAQ,CAAC,CAAC;gBAChG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC;YACxC,CAAC,MAAM,CAAC;gBACJ,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACxC,CAAC;YAED,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;QACvC,CAAC;QAED,IAAI,CAAC,OAAQ,CAAC,OAAO,EAAE,CAAC,qBAAqB,GAAG,qBAAqB,CAAC;IAC1E,CAAC;IAEO,QAAQ,CAAC,IAAkB,EAAE,MAAc,EAAA;QAC/C,MAAM,MAAM,GAAG,IAAI,CAAC,OAAiB,CAAC;QAEtC,SAAS;QACT,IAAI,YAAoB,CAAC;QACzB,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAE5C,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAChC,MAAM,UAAU,GAAG,IAAI,YAAY,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;YACrD,YAAY,GAAG,gKAAI,SAAM,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;YACjE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,YAAY,CAAC;QAChD,CAAC,MAAM,CAAC;YACJ,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAChD,CAAC;QAED,OAAO;QACP,MAAM,CAAC,SAAS,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QACvC,IAAa,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAE/C,QAAQ;+LACR,sBAAA,AAAmB,EAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAElC,gBAAgB;+LAChB,4BAAA,AAAyB,EAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QACxC,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,wBAAwB,EAAE,CAAC;YAC9E,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAC1C,CAAC;QAED,MAAM;QACN,MAAM,UAAU,GAAU,IAAK,CAAC,2BAA2B,CAAC;QAE5D,IAAI,UAAU,IAAI,UAAU,CAAC,SAAS,EAAE,CAAC;;sDAChC,2BAA2B,cAAhC,IAAI,sFAA8B,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAC1D,CAAC;QAED,SAAS;QACT,MAAM,WAAW,GAAG,YAAY,CAAC,OAAO,EAAmB,CAAC;QAC5D,MAAM,CAAC,2BAA2B,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,CAAC;QAC7D,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;QACjC,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;QACpC,MAAM,CAAC,cAAc,CAAC,GAAA,GAAA,GAAS,CAAC,sBAAsB,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC;QACxE,MAAM,CAAC,oBAAoB,EAAE,CAAC;QAC9B,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAChC,MAAM,CAAC,2BAA2B,CAAC,WAAW,CAAC,CAAC;QAChD,MAAM,CAAC,2BAA2B,CAAC,IAAI,CAAC,CAAC;QAEzC,cAAc;QACd,IAAI,IAAI,CAAC,gBAAgB,KAAK,CAAC,EAAE,CAAC;YAC9B,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QACjD,CAAC,MAAM,CAAC;YACJ,MAAM,EAAE,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,WAAW,CAAC;YAC9C,MAAM,MAAM,4KAAG,mBAAgB,AAAhB,EAAiB,WAAW,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC;YAE7D,+BAA+B,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAC1F,+BAA+B,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAE1F,IAAI,CAAC,0BAA0B,CAAC;gBAAE,OAAO,EAAE,+BAA+B,CAAC,IAAI;gBAAE,OAAO,EAAE,+BAA+B,CAAC,IAAI;YAAA,CAAE,CAAC,CAAC;QACtI,CAAC;IACL,CAAC;IAED,cAAA,EAAgB,CAChB,2FAA2F;IACpF,qBAAqB,CAAC,MAAqC,EAAA;QAC9D,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YACzB,MAAM,GAAG;gBAAC,MAAM;aAAC,CAAC;QACtB,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QACxB,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;QAE1B,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;IAED,cAAA,EAAgB,CACT,eAAe,GAAA;QAClB,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9B,OAAO;QACX,CAAC;QAED,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACtC,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC5B,CAAC;IAED,cAAA,EAAgB,CAChB,2FAA2F;IACpF,4BAA4B,GAAA;QAC/B,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;QAE1B,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;IAED,cAAA,EAAgB,CACT,OAAO,GAAA;QACV,IAAK,MAAM,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAE,CAAC;YAC9B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC;QACjC,CAAC;QACD,IAAI,CAAC,QAAQ,GAAG,CAAA,CAAE,CAAC;QACnB,IAAI,CAAC,QAAQ,GAAG,CAAA,CAAE,CAAC;QACnB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACxB,CAAC;IAzND;;;OAGG,CACH,YAAY,MAAkB,CAAA;QATtB,IAAA,CAAA,QAAQ,GAA8B,CAAA,CAAE,CAAC;QACzC,IAAA,CAAA,QAAQ,GAA8B,CAAA,CAAE,CAAC;QAEzC,IAAA,CAAA,gBAAgB,GAAG,CAAC,CAAC;QAOzB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IAC1B,CAAC;;AAfc,gCAAA,IAAI,GAAG,sKAAI,UAAO,EAAE,AAAhB,CAAiB;AACrB,gCAAA,IAAI,GAAG,sKAAI,UAAO,EAAE,AAAhB,CAAiB", "debugId": null}}, {"offset": {"line": 1837, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Culling/Helper/computeShaderBoundingHelper.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Culling/Helper/computeShaderBoundingHelper.ts"], "sourcesContent": ["import type { IBoundingInfoHelperPlatform } from \"./IBoundingInfoHelperPlatform\";\r\nimport type { AbstractMesh } from \"core/Meshes/abstractMesh\";\r\nimport { ComputeShader } from \"core/Compute/computeShader\";\r\nimport { StorageBuffer } from \"core/Buffers/storageBuffer\";\r\nimport type { WebGPUEngine } from \"core/Engines/webgpuEngine\";\r\nimport type { AbstractEngine } from \"core/Engines/abstractEngine\";\r\nimport type { Mesh } from \"core/Meshes/mesh\";\r\nimport { VertexBuffer } from \"core/Buffers/buffer\";\r\nimport { Vector3 } from \"core/Maths/math.vector\";\r\nimport { UniformBuffer } from \"core/Materials/uniformBuffer\";\r\nimport type { DataBuffer } from \"core/Buffers/dataBuffer\";\r\nimport type { ComputeBindingMapping } from \"core/Engines/Extensions/engine.computeShader\";\r\n\r\nimport \"../../ShadersWGSL/boundingInfo.compute\";\r\nimport { _RetryWithInterval } from \"core/Misc/timingTools\";\r\n\r\n/** @internal */\r\nexport class ComputeShaderBoundingHelper implements IBoundingInfoHelperPlatform {\r\n    private _engine: AbstractEngine;\r\n    private _computeShadersCache: { [key: string]: ComputeShader } = {};\r\n    private _positionBuffers: { [key: number]: StorageBuffer } = {};\r\n    private _indexBuffers: { [key: number]: StorageBuffer } = {};\r\n    private _weightBuffers: { [key: number]: StorageBuffer } = {};\r\n    private _indexExtraBuffers: { [key: number]: StorageBuffer } = {};\r\n    private _weightExtraBuffers: { [key: number]: StorageBuffer } = {};\r\n    private _morphTargetInfluenceBuffers: { [key: number]: StorageBuffer } = {};\r\n    private _morphTargetTextureIndexBuffers: { [key: number]: StorageBuffer } = {};\r\n    private _ubos: UniformBuffer[] = [];\r\n    private _uboIndex: number = 0;\r\n    private _processedMeshes: AbstractMesh[] = [];\r\n    private _computeShaders: ComputeShader[][] = [];\r\n    private _uniqueComputeShaders: Set<ComputeShader> = new Set();\r\n    private _resultBuffers: StorageBuffer[] = [];\r\n\r\n    /**\r\n     * Creates a new ComputeShaderBoundingHelper\r\n     * @param engine defines the engine to use\r\n     */\r\n    constructor(engine: AbstractEngine) {\r\n        this._engine = engine;\r\n    }\r\n\r\n    private _getComputeShader(defines: string[], hasBones: boolean, hasMorphs: boolean) {\r\n        let computeShader: ComputeShader;\r\n        const join = defines.join(\"\\n\");\r\n\r\n        if (!this._computeShadersCache[join]) {\r\n            const bindingsMapping: ComputeBindingMapping = {\r\n                positionBuffer: { group: 0, binding: 0 },\r\n                resultBuffer: { group: 0, binding: 1 },\r\n                settings: { group: 0, binding: 7 },\r\n            };\r\n\r\n            if (hasBones) {\r\n                bindingsMapping.boneSampler = { group: 0, binding: 2 };\r\n                bindingsMapping.indexBuffer = { group: 0, binding: 3 };\r\n                bindingsMapping.weightBuffer = { group: 0, binding: 4 };\r\n                bindingsMapping.indexExtraBuffer = { group: 0, binding: 5 };\r\n                bindingsMapping.weightExtraBuffer = { group: 0, binding: 6 };\r\n            }\r\n            if (hasMorphs) {\r\n                bindingsMapping.morphTargets = { group: 0, binding: 8 };\r\n                bindingsMapping.morphTargetInfluences = { group: 0, binding: 9 };\r\n                bindingsMapping.morphTargetTextureIndices = { group: 0, binding: 10 };\r\n            }\r\n\r\n            computeShader = new ComputeShader(`boundingInfoCompute${hasBones ? \"_bones\" : \"\"}${hasMorphs ? \"_morphs\" : \"\"}`, this._engine, \"boundingInfo\", {\r\n                bindingsMapping,\r\n                defines: defines,\r\n            });\r\n            this._computeShadersCache[join] = computeShader;\r\n        } else {\r\n            computeShader = this._computeShadersCache[join];\r\n        }\r\n\r\n        return computeShader;\r\n    }\r\n\r\n    private _getUBO() {\r\n        if (this._uboIndex >= this._ubos.length) {\r\n            const ubo = new UniformBuffer(this._engine);\r\n            ubo.addFloat3(\"morphTargetTextureInfo\", 0, 0, 0);\r\n            ubo.addUniform(\"morphTargetCount\", 1);\r\n            ubo.addUniform(\"indexResult\", 1);\r\n            this._ubos.push(ubo);\r\n        }\r\n\r\n        return this._ubos[this._uboIndex++];\r\n    }\r\n\r\n    private _extractDataAndLink(computeShader: ComputeShader, mesh: Mesh, kind: string, stride: number, name: string, storageUnit: { [key: number]: StorageBuffer }) {\r\n        let buffer: StorageBuffer;\r\n        const vertexCount = mesh.getTotalVertices();\r\n        if (!storageUnit[mesh.uniqueId]) {\r\n            const dataArray = mesh.getVertexBuffer(kind)?.getFloatData(vertexCount);\r\n            buffer = new StorageBuffer(this._engine as WebGPUEngine, Float32Array.BYTES_PER_ELEMENT * vertexCount * stride);\r\n            buffer.update(dataArray!);\r\n\r\n            storageUnit[mesh.uniqueId] = buffer;\r\n        } else {\r\n            buffer = storageUnit[mesh.uniqueId];\r\n        }\r\n\r\n        computeShader.setStorageBuffer(name, buffer);\r\n    }\r\n\r\n    private _prepareStorage(computeShader: ComputeShader, name: string, id: number, storageUnit: { [key: number]: StorageBuffer }, numInfluencers: number, data: Float32Array) {\r\n        let buffer: StorageBuffer;\r\n        if (!storageUnit[id]) {\r\n            buffer = new StorageBuffer(this._engine as WebGPUEngine, Float32Array.BYTES_PER_ELEMENT * numInfluencers);\r\n\r\n            storageUnit[id] = buffer;\r\n        } else {\r\n            buffer = storageUnit[id];\r\n        }\r\n        buffer.update(data);\r\n\r\n        computeShader.setStorageBuffer(name, buffer);\r\n    }\r\n\r\n    /** @internal */\r\n    public async processAsync(meshes: AbstractMesh | AbstractMesh[]): Promise<void> {\r\n        await this.registerMeshListAsync(meshes);\r\n        this.processMeshList();\r\n        await this.fetchResultsForMeshListAsync();\r\n    }\r\n\r\n    /** @internal */\r\n    // eslint-disable-next-line @typescript-eslint/promise-function-async, no-restricted-syntax\r\n    public registerMeshListAsync(meshes: AbstractMesh | AbstractMesh[]): Promise<void> {\r\n        this._disposeForMeshList();\r\n\r\n        if (!Array.isArray(meshes)) {\r\n            meshes = [meshes];\r\n        }\r\n\r\n        let maxNumInfluencers = 0;\r\n        for (let i = 0; i < meshes.length; i++) {\r\n            const mesh = meshes[i];\r\n            const vertexCount = mesh.getTotalVertices();\r\n\r\n            if (vertexCount === 0 || !(mesh as Mesh).getVertexBuffer || !(mesh as Mesh).getVertexBuffer(VertexBuffer.PositionKind)) {\r\n                continue;\r\n            }\r\n\r\n            this._processedMeshes.push(mesh);\r\n\r\n            const manager = (<Mesh>mesh).morphTargetManager;\r\n            if (manager && manager.supportsPositions) {\r\n                maxNumInfluencers = Math.max(maxNumInfluencers, manager.numTargets);\r\n            }\r\n        }\r\n\r\n        for (let i = 0; i < this._processedMeshes.length; i++) {\r\n            const mesh = this._processedMeshes[i];\r\n            let defines = [\"\"];\r\n\r\n            let hasBones = false;\r\n            if (mesh && mesh.useBones && mesh.computeBonesUsingShaders && mesh.skeleton) {\r\n                defines.push(\"#define NUM_BONE_INFLUENCERS \" + mesh.numBoneInfluencers);\r\n                hasBones = true;\r\n            }\r\n\r\n            const computeShaderWithoutMorph = this._getComputeShader(defines, hasBones, false);\r\n\r\n            this._uniqueComputeShaders.add(computeShaderWithoutMorph);\r\n\r\n            const manager = (<Mesh>mesh).morphTargetManager;\r\n            if (manager && manager.supportsPositions) {\r\n                defines = defines.slice();\r\n                defines.push(\"#define MORPHTARGETS\");\r\n                defines.push(\"#define NUM_MORPH_INFLUENCERS \" + maxNumInfluencers);\r\n\r\n                const computeShaderWithMorph = this._getComputeShader(defines, hasBones, true);\r\n\r\n                this._uniqueComputeShaders.add(computeShaderWithMorph);\r\n                this._computeShaders.push([computeShaderWithoutMorph, computeShaderWithMorph]);\r\n            } else {\r\n                this._computeShaders.push([computeShaderWithoutMorph, computeShaderWithoutMorph]);\r\n            }\r\n\r\n            // Pre-build the ubos, as they won't change if there's no morph targets\r\n            const ubo = this._getUBO();\r\n            ubo.updateUInt(\"indexResult\", i);\r\n\r\n            ubo.update();\r\n        }\r\n\r\n        return new Promise((resolve) => {\r\n            _RetryWithInterval(() => {\r\n                const iterator = this._uniqueComputeShaders.keys();\r\n                for (let key = iterator.next(); key.done !== true; key = iterator.next()) {\r\n                    const computeShader = key.value;\r\n                    if (!computeShader.isReady()) {\r\n                        return false;\r\n                    }\r\n                }\r\n                return true;\r\n            }, resolve);\r\n        });\r\n    }\r\n\r\n    /** @internal */\r\n    public processMeshList(): void {\r\n        if (this._processedMeshes.length === 0) {\r\n            return;\r\n        }\r\n\r\n        this._uboIndex = 0;\r\n\r\n        const resultDataSize = 8 * this._processedMeshes.length;\r\n        const resultData = new Float32Array(resultDataSize);\r\n\r\n        const resultBuffer = new StorageBuffer(this._engine as WebGPUEngine, Float32Array.BYTES_PER_ELEMENT * resultDataSize);\r\n        this._resultBuffers.push(resultBuffer);\r\n\r\n        for (let i = 0; i < this._processedMeshes.length; i++) {\r\n            resultData[i * 8 + 0] = Number.POSITIVE_INFINITY;\r\n            resultData[i * 8 + 1] = Number.POSITIVE_INFINITY;\r\n            resultData[i * 8 + 2] = Number.POSITIVE_INFINITY;\r\n\r\n            resultData[i * 8 + 3] = Number.NEGATIVE_INFINITY;\r\n            resultData[i * 8 + 4] = Number.NEGATIVE_INFINITY;\r\n            resultData[i * 8 + 5] = Number.NEGATIVE_INFINITY;\r\n        }\r\n\r\n        resultBuffer.update(resultData);\r\n\r\n        for (let i = 0; i < this._processedMeshes.length; i++) {\r\n            const mesh = this._processedMeshes[i];\r\n            const vertexCount = mesh.getTotalVertices();\r\n\r\n            const [computeShaderWithoutMorph, computeShaderWithMorph] = this._computeShaders[i];\r\n\r\n            const manager = (<Mesh>mesh).morphTargetManager;\r\n            const hasMorphs = manager && manager.numInfluencers > 0 && manager.supportsPositions;\r\n            const computeShader = hasMorphs ? computeShaderWithMorph : computeShaderWithoutMorph;\r\n\r\n            this._extractDataAndLink(computeShader, mesh as Mesh, VertexBuffer.PositionKind, 3, \"positionBuffer\", this._positionBuffers);\r\n\r\n            // Bones\r\n            if (mesh && mesh.useBones && mesh.computeBonesUsingShaders && mesh.skeleton && mesh.skeleton.useTextureToStoreBoneMatrices) {\r\n                this._extractDataAndLink(computeShader, mesh as Mesh, VertexBuffer.MatricesIndicesKind, 4, \"indexBuffer\", this._indexBuffers);\r\n                this._extractDataAndLink(computeShader, mesh as Mesh, VertexBuffer.MatricesWeightsKind, 4, \"weightBuffer\", this._weightBuffers);\r\n                const boneSampler = mesh.skeleton.getTransformMatrixTexture(mesh);\r\n                computeShader.setTexture(\"boneSampler\", boneSampler!, false);\r\n                if (mesh.numBoneInfluencers > 4) {\r\n                    this._extractDataAndLink(computeShader, mesh as Mesh, VertexBuffer.MatricesIndicesExtraKind, 4, \"indexExtraBuffer\", this._indexExtraBuffers);\r\n                    this._extractDataAndLink(computeShader, mesh as Mesh, VertexBuffer.MatricesWeightsExtraKind, 4, \"weightExtraBuffer\", this._weightExtraBuffers);\r\n                }\r\n            }\r\n\r\n            const ubo = this._getUBO();\r\n\r\n            // Morphs\r\n            if (hasMorphs) {\r\n                const morphTargets = manager._targetStoreTexture;\r\n                computeShader.setTexture(\"morphTargets\", morphTargets!, false);\r\n\r\n                this._prepareStorage(computeShader, \"morphTargetInfluences\", mesh.uniqueId, this._morphTargetInfluenceBuffers, manager.numInfluencers, manager.influences);\r\n                this._prepareStorage(\r\n                    computeShader,\r\n                    \"morphTargetTextureIndices\",\r\n                    mesh.uniqueId,\r\n                    this._morphTargetTextureIndexBuffers,\r\n                    manager.numInfluencers,\r\n                    manager._morphTargetTextureIndices\r\n                );\r\n\r\n                ubo.updateFloat3(\"morphTargetTextureInfo\", manager._textureVertexStride, manager._textureWidth, manager._textureHeight);\r\n                ubo.updateFloat(\"morphTargetCount\", manager.numInfluencers);\r\n                ubo.update();\r\n            }\r\n\r\n            computeShader.setStorageBuffer(\"resultBuffer\", resultBuffer);\r\n\r\n            computeShader.setUniformBuffer(\"settings\", ubo);\r\n\r\n            // Dispatch\r\n            computeShader.dispatch(Math.ceil(vertexCount / 256));\r\n\r\n            this._engine.flushFramebuffer();\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public async fetchResultsForMeshListAsync(): Promise<void> {\r\n        return await new Promise((resolve) => {\r\n            const buffers: DataBuffer[] = [];\r\n            let size = 0;\r\n            for (let i = 0; i < this._resultBuffers.length; i++) {\r\n                const buffer = this._resultBuffers[i].getBuffer();\r\n                buffers.push(buffer);\r\n                size += buffer.capacity;\r\n            }\r\n\r\n            const resultData = new Float32Array(size / Float32Array.BYTES_PER_ELEMENT);\r\n\r\n            const minimum = Vector3.Zero();\r\n            const maximum = Vector3.Zero();\r\n\r\n            const minmax = { minimum, maximum };\r\n\r\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises, github/no-then\r\n            (this._engine as WebGPUEngine).readFromMultipleStorageBuffers(buffers, 0, undefined, resultData, true).then(() => {\r\n                let resultDataOffset = 0;\r\n                for (let j = 0; j < this._resultBuffers.length; j++) {\r\n                    for (let i = 0; i < this._processedMeshes.length; i++) {\r\n                        const mesh = this._processedMeshes[i];\r\n\r\n                        Vector3.FromArrayToRef(resultData, resultDataOffset + i * 8, minimum);\r\n                        Vector3.FromArrayToRef(resultData, resultDataOffset + i * 8 + 3, maximum);\r\n\r\n                        if (j > 0) {\r\n                            minimum.minimizeInPlace(mesh.getBoundingInfo().minimum);\r\n                            maximum.maximizeInPlace(mesh.getBoundingInfo().maximum);\r\n                        }\r\n\r\n                        mesh._refreshBoundingInfoDirect(minmax);\r\n                    }\r\n\r\n                    resultDataOffset += 8 * this._processedMeshes.length;\r\n                }\r\n\r\n                for (const resultBuffer of this._resultBuffers) {\r\n                    resultBuffer.dispose();\r\n                }\r\n\r\n                this._resultBuffers = [];\r\n                this._uboIndex = 0;\r\n\r\n                resolve();\r\n            });\r\n        });\r\n    }\r\n\r\n    private _disposeCache(storageUnit: { [key: number]: StorageBuffer }) {\r\n        for (const key in storageUnit) {\r\n            storageUnit[key].dispose();\r\n        }\r\n    }\r\n\r\n    private _disposeForMeshList() {\r\n        for (const resultBuffer of this._resultBuffers) {\r\n            resultBuffer.dispose();\r\n        }\r\n        this._resultBuffers = [];\r\n        this._processedMeshes = [];\r\n        this._computeShaders = [];\r\n        this._uniqueComputeShaders = new Set();\r\n    }\r\n\r\n    /** @internal */\r\n    public dispose(): void {\r\n        this._disposeCache(this._positionBuffers);\r\n        this._positionBuffers = {};\r\n        this._disposeCache(this._indexBuffers);\r\n        this._indexBuffers = {};\r\n        this._disposeCache(this._weightBuffers);\r\n        this._weightBuffers = {};\r\n        this._disposeCache(this._morphTargetInfluenceBuffers);\r\n        this._morphTargetInfluenceBuffers = {};\r\n        this._disposeCache(this._morphTargetTextureIndexBuffers);\r\n        this._morphTargetTextureIndexBuffers = {};\r\n        for (const ubo of this._ubos) {\r\n            ubo.dispose();\r\n        }\r\n        this._ubos = [];\r\n        this._computeShadersCache = {};\r\n        this._engine = undefined!;\r\n        this._disposeForMeshList();\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AAEA,OAAO,EAAE,aAAa,EAAE,uCAAmC;AAC3D,OAAO,EAAE,aAAa,EAAE,uCAAmC;AAI3D,OAAO,EAAE,YAAY,EAAE,gCAA4B;AACnD,OAAO,EAAE,OAAO,EAAE,mCAA+B;AACjD,OAAO,EAAE,aAAa,EAAE,yCAAqC;AAI7D,OAAO,wCAAwC,CAAC;AAChD,OAAO,EAAE,kBAAkB,EAAE,kCAA8B;;;;;;;;AAGrD,MAAO,2BAA2B;IAyB5B,iBAAiB,CAAC,OAAiB,EAAE,QAAiB,EAAE,SAAkB,EAAA;QAC9E,IAAI,aAA4B,CAAC;QACjC,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEhC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,CAAC;YACnC,MAAM,eAAe,GAA0B;gBAC3C,cAAc,EAAE;oBAAE,KAAK,EAAE,CAAC;oBAAE,OAAO,EAAE,CAAC;gBAAA,CAAE;gBACxC,YAAY,EAAE;oBAAE,KAAK,EAAE,CAAC;oBAAE,OAAO,EAAE,CAAC;gBAAA,CAAE;gBACtC,QAAQ,EAAE;oBAAE,KAAK,EAAE,CAAC;oBAAE,OAAO,EAAE,CAAC;gBAAA,CAAE;aACrC,CAAC;YAEF,IAAI,QAAQ,EAAE,CAAC;gBACX,eAAe,CAAC,WAAW,GAAG;oBAAE,KAAK,EAAE,CAAC;oBAAE,OAAO,EAAE,CAAC;gBAAA,CAAE,CAAC;gBACvD,eAAe,CAAC,WAAW,GAAG;oBAAE,KAAK,EAAE,CAAC;oBAAE,OAAO,EAAE,CAAC;gBAAA,CAAE,CAAC;gBACvD,eAAe,CAAC,YAAY,GAAG;oBAAE,KAAK,EAAE,CAAC;oBAAE,OAAO,EAAE,CAAC;gBAAA,CAAE,CAAC;gBACxD,eAAe,CAAC,gBAAgB,GAAG;oBAAE,KAAK,EAAE,CAAC;oBAAE,OAAO,EAAE,CAAC;gBAAA,CAAE,CAAC;gBAC5D,eAAe,CAAC,iBAAiB,GAAG;oBAAE,KAAK,EAAE,CAAC;oBAAE,OAAO,EAAE,CAAC;gBAAA,CAAE,CAAC;YACjE,CAAC;YACD,IAAI,SAAS,EAAE,CAAC;gBACZ,eAAe,CAAC,YAAY,GAAG;oBAAE,KAAK,EAAE,CAAC;oBAAE,OAAO,EAAE,CAAC;gBAAA,CAAE,CAAC;gBACxD,eAAe,CAAC,qBAAqB,GAAG;oBAAE,KAAK,EAAE,CAAC;oBAAE,OAAO,EAAE,CAAC;gBAAA,CAAE,CAAC;gBACjE,eAAe,CAAC,yBAAyB,GAAG;oBAAE,KAAK,EAAE,CAAC;oBAAE,OAAO,EAAE,EAAE;gBAAA,CAAE,CAAC;YAC1E,CAAC;YAED,aAAa,GAAG,uKAAI,gBAAa,CAAC,sBAAiD,OAA3B,EAAoC,CAAC,CAAC,IAA9B,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,EAA6B,CAAE,kBAAhB,SAAS,CAAC,CAAC,CAAC,EAAE,GAAI,IAAI,CAAC,OAAO,EAAE,cAAc,EAAE;gBAC3I,eAAe;gBACf,OAAO,EAAE,OAAO;aACnB,CAAC,CAAC;YACH,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC;QACpD,CAAC,MAAM,CAAC;YACJ,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;QACpD,CAAC;QAED,OAAO,aAAa,CAAC;IACzB,CAAC;IAEO,OAAO,GAAA;QACX,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YACtC,MAAM,GAAG,GAAG,yKAAI,gBAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC5C,GAAG,CAAC,SAAS,CAAC,wBAAwB,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACjD,GAAG,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;YACtC,GAAG,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;YACjC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACzB,CAAC;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;IACxC,CAAC;IAEO,mBAAmB,CAAC,aAA4B,EAAE,IAAU,EAAE,IAAY,EAAE,MAAc,EAAE,IAAY,EAAE,WAA6C,EAAA;QAC3J,IAAI,MAAqB,CAAC;QAC1B,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC5C,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;;YAC9B,MAAM,SAAS,4BAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,gFAAE,YAAY,CAAC,WAAW,CAAC,CAAC;YACxE,MAAM,GAAG,uKAAI,gBAAa,CAAC,IAAI,CAAC,OAAuB,EAAE,YAAY,CAAC,iBAAiB,GAAG,WAAW,GAAG,MAAM,CAAC,CAAC;YAChH,MAAM,CAAC,MAAM,CAAC,SAAU,CAAC,CAAC;YAE1B,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;QACxC,CAAC,MAAM,CAAC;YACJ,MAAM,GAAG,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACxC,CAAC;QAED,aAAa,CAAC,gBAAgB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACjD,CAAC;IAEO,eAAe,CAAC,aAA4B,EAAE,IAAY,EAAE,EAAU,EAAE,WAA6C,EAAE,cAAsB,EAAE,IAAkB,EAAA;QACrK,IAAI,MAAqB,CAAC;QAC1B,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE,CAAC;YACnB,MAAM,GAAG,uKAAI,gBAAa,CAAC,IAAI,CAAC,OAAuB,EAAE,YAAY,CAAC,iBAAiB,GAAG,cAAc,CAAC,CAAC;YAE1G,WAAW,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC;QAC7B,CAAC,MAAM,CAAC;YACJ,MAAM,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC;QAC7B,CAAC;QACD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAEpB,aAAa,CAAC,gBAAgB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACjD,CAAC;IAED,cAAA,EAAgB,CACT,KAAK,CAAC,YAAY,CAAC,MAAqC,EAAA;QAC3D,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;QACzC,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,MAAM,IAAI,CAAC,4BAA4B,EAAE,CAAC;IAC9C,CAAC;IAED,cAAA,EAAgB,CAChB,2FAA2F;IACpF,qBAAqB,CAAC,MAAqC,EAAA;QAC9D,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE3B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YACzB,MAAM,GAAG;gBAAC,MAAM;aAAC,CAAC;QACtB,CAAC;QAED,IAAI,iBAAiB,GAAG,CAAC,CAAC;QAC1B,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;YACrC,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACvB,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAE5C,IAAI,WAAW,KAAK,CAAC,IAAI,CAAE,IAAa,CAAC,eAAe,IAAI,CAAE,IAAa,CAAC,eAAe,6JAAC,eAAY,CAAC,YAAY,CAAC,EAAE,CAAC;gBACrH,SAAS;YACb,CAAC;YAED,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEjC,MAAM,OAAO,GAAU,IAAK,CAAC,kBAAkB,CAAC;YAChD,IAAI,OAAO,IAAI,OAAO,CAAC,iBAAiB,EAAE,CAAC;gBACvC,iBAAiB,GAAG,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;YACxE,CAAC;QACL,CAAC;QAED,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;YACpD,MAAM,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;YACtC,IAAI,OAAO,GAAG;gBAAC,EAAE;aAAC,CAAC;YAEnB,IAAI,QAAQ,GAAG,KAAK,CAAC;YACrB,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,wBAAwB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC1E,OAAO,CAAC,IAAI,CAAC,+BAA+B,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBACxE,QAAQ,GAAG,IAAI,CAAC;YACpB,CAAC;YAED,MAAM,yBAAyB,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;YAEnF,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;YAE1D,MAAM,OAAO,GAAU,IAAK,CAAC,kBAAkB,CAAC;YAChD,IAAI,OAAO,IAAI,OAAO,CAAC,iBAAiB,EAAE,CAAC;gBACvC,OAAO,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;gBAC1B,OAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBACrC,OAAO,CAAC,IAAI,CAAC,gCAAgC,GAAG,iBAAiB,CAAC,CAAC;gBAEnE,MAAM,sBAAsB,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;gBAE/E,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;gBACvD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;oBAAC,yBAAyB;oBAAE,sBAAsB;iBAAC,CAAC,CAAC;YACnF,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;oBAAC,yBAAyB;oBAAE,yBAAyB;iBAAC,CAAC,CAAC;YACtF,CAAC;YAED,uEAAuE;YACvE,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YAC3B,GAAG,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;YAEjC,GAAG,CAAC,MAAM,EAAE,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;8KAC3B,qBAAA,AAAkB,EAAC,GAAG,EAAE;gBACpB,MAAM,QAAQ,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,CAAC;gBACnD,IAAK,IAAI,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,KAAK,IAAI,EAAE,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAE,CAAC;oBACvE,MAAM,aAAa,GAAG,GAAG,CAAC,KAAK,CAAC;oBAChC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE,CAAC;wBAC3B,OAAO,KAAK,CAAC;oBACjB,CAAC;gBACL,CAAC;gBACD,OAAO,IAAI,CAAC;YAChB,CAAC,EAAE,OAAO,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC;IACP,CAAC;IAED,cAAA,EAAgB,CACT,eAAe,GAAA;QAClB,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;QAEnB,MAAM,cAAc,GAAG,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC;QACxD,MAAM,UAAU,GAAG,IAAI,YAAY,CAAC,cAAc,CAAC,CAAC;QAEpD,MAAM,YAAY,GAAG,uKAAI,gBAAa,CAAC,IAAI,CAAC,OAAuB,EAAE,YAAY,CAAC,iBAAiB,GAAG,cAAc,CAAC,CAAC;QACtH,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAEvC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;YACpD,UAAU,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,iBAAiB,CAAC;YACjD,UAAU,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,iBAAiB,CAAC;YACjD,UAAU,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,iBAAiB,CAAC;YAEjD,UAAU,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,iBAAiB,CAAC;YACjD,UAAU,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,iBAAiB,CAAC;YACjD,UAAU,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,iBAAiB,CAAC;QACrD,CAAC;QAED,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAEhC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;YACpD,MAAM,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAE5C,MAAM,CAAC,yBAAyB,EAAE,sBAAsB,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAEpF,MAAM,OAAO,GAAU,IAAK,CAAC,kBAAkB,CAAC;YAChD,MAAM,SAAS,GAAG,OAAO,IAAI,OAAO,CAAC,cAAc,GAAG,CAAC,IAAI,OAAO,CAAC,iBAAiB,CAAC;YACrF,MAAM,aAAa,GAAG,SAAS,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,yBAAyB,CAAC;YAErF,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,IAAY,6JAAE,gBAAY,CAAC,YAAY,EAAE,CAAC,EAAE,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAE7H,QAAQ;YACR,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,wBAAwB,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,6BAA6B,EAAE,CAAC;gBACzH,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,IAAY,EAAE,2KAAY,CAAC,mBAAmB,EAAE,CAAC,EAAE,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;gBAC9H,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,IAAY,8JAAE,eAAY,CAAC,mBAAmB,EAAE,CAAC,EAAE,cAAc,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;gBAChI,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC;gBAClE,aAAa,CAAC,UAAU,CAAC,aAAa,EAAE,WAAY,EAAE,KAAK,CAAC,CAAC;gBAC7D,IAAI,IAAI,CAAC,kBAAkB,GAAG,CAAC,EAAE,CAAC;oBAC9B,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,IAAY,8JAAE,eAAY,CAAC,wBAAwB,EAAE,CAAC,EAAE,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;oBAC7I,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,IAAY,8JAAE,eAAY,CAAC,wBAAwB,EAAE,CAAC,EAAE,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;gBACnJ,CAAC;YACL,CAAC;YAED,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YAE3B,SAAS;YACT,IAAI,SAAS,EAAE,CAAC;gBACZ,MAAM,YAAY,GAAG,OAAO,CAAC,mBAAmB,CAAC;gBACjD,aAAa,CAAC,UAAU,CAAC,cAAc,EAAE,YAAa,EAAE,KAAK,CAAC,CAAC;gBAE/D,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE,uBAAuB,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,4BAA4B,EAAE,OAAO,CAAC,cAAc,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;gBAC3J,IAAI,CAAC,eAAe,CAChB,aAAa,EACb,2BAA2B,EAC3B,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,+BAA+B,EACpC,OAAO,CAAC,cAAc,EACtB,OAAO,CAAC,0BAA0B,CACrC,CAAC;gBAEF,GAAG,CAAC,YAAY,CAAC,wBAAwB,EAAE,OAAO,CAAC,oBAAoB,EAAE,OAAO,CAAC,aAAa,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC;gBACxH,GAAG,CAAC,WAAW,CAAC,kBAAkB,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC;gBAC5D,GAAG,CAAC,MAAM,EAAE,CAAC;YACjB,CAAC;YAED,aAAa,CAAC,gBAAgB,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;YAE7D,aAAa,CAAC,gBAAgB,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;YAEhD,WAAW;YACX,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC,CAAC,CAAC;YAErD,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC;QACpC,CAAC;IACL,CAAC;IAED,cAAA,EAAgB,CACT,KAAK,CAAC,4BAA4B,GAAA;QACrC,OAAO,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YACjC,MAAM,OAAO,GAAiB,EAAE,CAAC;YACjC,IAAI,IAAI,GAAG,CAAC,CAAC;YACb,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;gBAClD,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC;gBAClD,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACrB,IAAI,IAAI,MAAM,CAAC,QAAQ,CAAC;YAC5B,CAAC;YAED,MAAM,UAAU,GAAG,IAAI,YAAY,CAAC,IAAI,GAAG,YAAY,CAAC,iBAAiB,CAAC,CAAC;YAE3E,MAAM,OAAO,oKAAG,WAAO,CAAC,IAAI,EAAE,CAAC;YAC/B,MAAM,OAAO,qKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;YAE/B,MAAM,MAAM,GAAG;gBAAE,OAAO;gBAAE,OAAO;YAAA,CAAE,CAAC;YAEpC,mFAAmF;YAClF,IAAI,CAAC,OAAwB,CAAC,8BAA8B,CAAC,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;gBAC7G,IAAI,gBAAgB,GAAG,CAAC,CAAC;gBACzB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;oBAClD,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;wBACpD,MAAM,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;0LAEtC,UAAO,CAAC,cAAc,CAAC,UAAU,EAAE,gBAAgB,GAAG,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC;0LACtE,UAAO,CAAC,cAAc,CAAC,UAAU,EAAE,gBAAgB,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC;wBAE1E,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;4BACR,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,OAAO,CAAC,CAAC;4BACxD,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,OAAO,CAAC,CAAC;wBAC5D,CAAC;wBAED,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC,CAAC;oBAC5C,CAAC;oBAED,gBAAgB,IAAI,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC;gBACzD,CAAC;gBAED,KAAK,MAAM,YAAY,IAAI,IAAI,CAAC,cAAc,CAAE,CAAC;oBAC7C,YAAY,CAAC,OAAO,EAAE,CAAC;gBAC3B,CAAC;gBAED,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;gBACzB,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;gBAEnB,OAAO,EAAE,CAAC;YACd,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,aAAa,CAAC,WAA6C,EAAA;QAC/D,IAAK,MAAM,GAAG,IAAI,WAAW,CAAE,CAAC;YAC5B,WAAW,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC;QAC/B,CAAC;IACL,CAAC;IAEO,mBAAmB,GAAA;QACvB,KAAK,MAAM,YAAY,IAAI,IAAI,CAAC,cAAc,CAAE,CAAC;YAC7C,YAAY,CAAC,OAAO,EAAE,CAAC;QAC3B,CAAC;QACD,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAC1B,IAAI,CAAC,qBAAqB,GAAG,IAAI,GAAG,EAAE,CAAC;IAC3C,CAAC;IAED,cAAA,EAAgB,CACT,OAAO,GAAA;QACV,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC1C,IAAI,CAAC,gBAAgB,GAAG,CAAA,CAAE,CAAC;QAC3B,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACvC,IAAI,CAAC,aAAa,GAAG,CAAA,CAAE,CAAC;QACxB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACxC,IAAI,CAAC,cAAc,GAAG,CAAA,CAAE,CAAC;QACzB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;QACtD,IAAI,CAAC,4BAA4B,GAAG,CAAA,CAAE,CAAC;QACvC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QACzD,IAAI,CAAC,+BAA+B,GAAG,CAAA,CAAE,CAAC;QAC1C,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,KAAK,CAAE,CAAC;YAC3B,GAAG,CAAC,OAAO,EAAE,CAAC;QAClB,CAAC;QACD,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;QAChB,IAAI,CAAC,oBAAoB,GAAG,CAAA,CAAE,CAAC;QAC/B,IAAI,CAAC,OAAO,GAAG,SAAU,CAAC;QAC1B,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IAjVD;;;OAGG,CACH,YAAY,MAAsB,CAAA;QAnB1B,IAAA,CAAA,oBAAoB,GAAqC,CAAA,CAAE,CAAC;QAC5D,IAAA,CAAA,gBAAgB,GAAqC,CAAA,CAAE,CAAC;QACxD,IAAA,CAAA,aAAa,GAAqC,CAAA,CAAE,CAAC;QACrD,IAAA,CAAA,cAAc,GAAqC,CAAA,CAAE,CAAC;QACtD,IAAA,CAAA,kBAAkB,GAAqC,CAAA,CAAE,CAAC;QAC1D,IAAA,CAAA,mBAAmB,GAAqC,CAAA,CAAE,CAAC;QAC3D,IAAA,CAAA,4BAA4B,GAAqC,CAAA,CAAE,CAAC;QACpE,IAAA,CAAA,+BAA+B,GAAqC,CAAA,CAAE,CAAC;QACvE,IAAA,CAAA,KAAK,GAAoB,EAAE,CAAC;QAC5B,IAAA,CAAA,SAAS,GAAW,CAAC,CAAC;QACtB,IAAA,CAAA,gBAAgB,GAAmB,EAAE,CAAC;QACtC,IAAA,CAAA,eAAe,GAAsB,EAAE,CAAC;QACxC,IAAA,CAAA,qBAAqB,GAAuB,IAAI,GAAG,EAAE,CAAC;QACtD,IAAA,CAAA,cAAc,GAAoB,EAAE,CAAC;QAOzC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IAC1B,CAAC;CA4UJ", "debugId": null}}, {"offset": {"line": 2181, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Culling/Octrees/octreeBlock.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Culling/Octrees/octreeBlock.ts"], "sourcesContent": ["import type { SmartArrayNoDuplicate } from \"../../Misc/smartArray\";\r\nimport { Vector3 } from \"../../Maths/math.vector\";\r\nimport type { <PERSON> } from \"../../Culling/ray\";\r\nimport { BoundingBox } from \"../../Culling/boundingBox\";\r\nimport type { Plane } from \"../../Maths/math.plane\";\r\n\r\n/**\r\n * Contains an array of blocks representing the octree\r\n */\r\nexport interface IOctreeContainer<T> {\r\n    /**\r\n     * Blocks within the octree\r\n     */\r\n    blocks: Array<OctreeBlock<T>>;\r\n}\r\n\r\n/**\r\n * Class used to store a cell in an octree\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/optimizeOctrees\r\n */\r\nexport class OctreeBlock<T> {\r\n    /**\r\n     * Gets the content of the current block\r\n     */\r\n    public entries: T[] = [];\r\n\r\n    /**\r\n     * Gets the list of block children\r\n     */\r\n    public blocks: Array<OctreeBlock<T>>;\r\n\r\n    private _depth: number;\r\n    private _maxDepth: number;\r\n    private _capacity: number;\r\n    private _minPoint: Vector3;\r\n    private _maxPoint: Vector3;\r\n    private _boundingVectors = new Array<Vector3>();\r\n    private _creationFunc: (entry: T, block: OctreeBlock<T>) => void;\r\n\r\n    /**\r\n     * Creates a new block\r\n     * @param minPoint defines the minimum vector (in world space) of the block's bounding box\r\n     * @param maxPoint defines the maximum vector (in world space) of the block's bounding box\r\n     * @param capacity defines the maximum capacity of this block (if capacity is reached the block will be split into sub blocks)\r\n     * @param depth defines the current depth of this block in the octree\r\n     * @param maxDepth defines the maximal depth allowed (beyond this value, the capacity is ignored)\r\n     * @param creationFunc defines a callback to call when an element is added to the block\r\n     */\r\n    constructor(minPoint: Vector3, maxPoint: Vector3, capacity: number, depth: number, maxDepth: number, creationFunc: (entry: T, block: OctreeBlock<T>) => void) {\r\n        this._capacity = capacity;\r\n        this._depth = depth;\r\n        this._maxDepth = maxDepth;\r\n        this._creationFunc = creationFunc;\r\n\r\n        this._minPoint = minPoint;\r\n        this._maxPoint = maxPoint;\r\n\r\n        this._boundingVectors.push(minPoint.clone());\r\n        this._boundingVectors.push(maxPoint.clone());\r\n\r\n        this._boundingVectors.push(minPoint.clone());\r\n        this._boundingVectors[2].x = maxPoint.x;\r\n\r\n        this._boundingVectors.push(minPoint.clone());\r\n        this._boundingVectors[3].y = maxPoint.y;\r\n\r\n        this._boundingVectors.push(minPoint.clone());\r\n        this._boundingVectors[4].z = maxPoint.z;\r\n\r\n        this._boundingVectors.push(maxPoint.clone());\r\n        this._boundingVectors[5].z = minPoint.z;\r\n\r\n        this._boundingVectors.push(maxPoint.clone());\r\n        this._boundingVectors[6].x = minPoint.x;\r\n\r\n        this._boundingVectors.push(maxPoint.clone());\r\n        this._boundingVectors[7].y = minPoint.y;\r\n    }\r\n\r\n    // Property\r\n\r\n    /**\r\n     * Gets the maximum capacity of this block (if capacity is reached the block will be split into sub blocks)\r\n     */\r\n    public get capacity(): number {\r\n        return this._capacity;\r\n    }\r\n\r\n    /**\r\n     * Gets the minimum vector (in world space) of the block's bounding box\r\n     */\r\n    public get minPoint(): Vector3 {\r\n        return this._minPoint;\r\n    }\r\n\r\n    /**\r\n     * Gets the maximum vector (in world space) of the block's bounding box\r\n     */\r\n    public get maxPoint(): Vector3 {\r\n        return this._maxPoint;\r\n    }\r\n\r\n    // Methods\r\n\r\n    /**\r\n     * Add a new element to this block\r\n     * @param entry defines the element to add\r\n     */\r\n    public addEntry(entry: T): void {\r\n        if (this.blocks) {\r\n            for (let index = 0; index < this.blocks.length; index++) {\r\n                const block = this.blocks[index];\r\n                block.addEntry(entry);\r\n            }\r\n            return;\r\n        }\r\n\r\n        this._creationFunc(entry, this);\r\n\r\n        if (this.entries.length > this.capacity && this._depth < this._maxDepth) {\r\n            this.createInnerBlocks();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Remove an element from this block\r\n     * @param entry defines the element to remove\r\n     */\r\n    public removeEntry(entry: T): void {\r\n        if (this.blocks) {\r\n            for (let index = 0; index < this.blocks.length; index++) {\r\n                const block = this.blocks[index];\r\n                block.removeEntry(entry);\r\n            }\r\n            return;\r\n        }\r\n\r\n        const entryIndex = this.entries.indexOf(entry);\r\n\r\n        if (entryIndex > -1) {\r\n            this.entries.splice(entryIndex, 1);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Add an array of elements to this block\r\n     * @param entries defines the array of elements to add\r\n     */\r\n    public addEntries(entries: T[]): void {\r\n        for (let index = 0; index < entries.length; index++) {\r\n            const mesh = entries[index];\r\n            this.addEntry(mesh);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Test if the current block intersects the frustum planes and if yes, then add its content to the selection array\r\n     * @param frustumPlanes defines the frustum planes to test\r\n     * @param selection defines the array to store current content if selection is positive\r\n     * @param allowDuplicate defines if the selection array can contains duplicated entries\r\n     */\r\n    public select(frustumPlanes: Plane[], selection: SmartArrayNoDuplicate<T>, allowDuplicate?: boolean): void {\r\n        if (BoundingBox.IsInFrustum(this._boundingVectors, frustumPlanes)) {\r\n            if (this.blocks) {\r\n                for (let index = 0; index < this.blocks.length; index++) {\r\n                    const block = this.blocks[index];\r\n                    block.select(frustumPlanes, selection, allowDuplicate);\r\n                }\r\n                return;\r\n            }\r\n\r\n            if (allowDuplicate) {\r\n                selection.concat(this.entries);\r\n            } else {\r\n                selection.concatWithNoDuplicate(this.entries);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Test if the current block intersect with the given bounding sphere and if yes, then add its content to the selection array\r\n     * @param sphereCenter defines the bounding sphere center\r\n     * @param sphereRadius defines the bounding sphere radius\r\n     * @param selection defines the array to store current content if selection is positive\r\n     * @param allowDuplicate defines if the selection array can contains duplicated entries\r\n     */\r\n    public intersects(sphereCenter: Vector3, sphereRadius: number, selection: SmartArrayNoDuplicate<T>, allowDuplicate?: boolean): void {\r\n        if (BoundingBox.IntersectsSphere(this._minPoint, this._maxPoint, sphereCenter, sphereRadius)) {\r\n            if (this.blocks) {\r\n                for (let index = 0; index < this.blocks.length; index++) {\r\n                    const block = this.blocks[index];\r\n                    block.intersects(sphereCenter, sphereRadius, selection, allowDuplicate);\r\n                }\r\n                return;\r\n            }\r\n\r\n            if (allowDuplicate) {\r\n                selection.concat(this.entries);\r\n            } else {\r\n                selection.concatWithNoDuplicate(this.entries);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Test if the current block intersect with the given ray and if yes, then add its content to the selection array\r\n     * @param ray defines the ray to test with\r\n     * @param selection defines the array to store current content if selection is positive\r\n     */\r\n    public intersectsRay(ray: Ray, selection: SmartArrayNoDuplicate<T>): void {\r\n        if (ray.intersectsBoxMinMax(this._minPoint, this._maxPoint)) {\r\n            if (this.blocks) {\r\n                for (let index = 0; index < this.blocks.length; index++) {\r\n                    const block = this.blocks[index];\r\n                    block.intersectsRay(ray, selection);\r\n                }\r\n                return;\r\n            }\r\n            selection.concatWithNoDuplicate(this.entries);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Subdivide the content into child blocks (this block will then be empty)\r\n     */\r\n    public createInnerBlocks(): void {\r\n        OctreeBlock._CreateBlocks(this._minPoint, this._maxPoint, this.entries, this._capacity, this._depth, this._maxDepth, this, this._creationFunc);\r\n        this.entries.splice(0);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public static _CreateBlocks<T>(\r\n        worldMin: Vector3,\r\n        worldMax: Vector3,\r\n        entries: T[],\r\n        maxBlockCapacity: number,\r\n        currentDepth: number,\r\n        maxDepth: number,\r\n        target: IOctreeContainer<T>,\r\n        creationFunc: (entry: T, block: OctreeBlock<T>) => void\r\n    ): void {\r\n        target.blocks = new Array<OctreeBlock<T>>();\r\n        const blockSize = new Vector3((worldMax.x - worldMin.x) / 2, (worldMax.y - worldMin.y) / 2, (worldMax.z - worldMin.z) / 2);\r\n\r\n        // Segmenting space\r\n        for (let x = 0; x < 2; x++) {\r\n            for (let y = 0; y < 2; y++) {\r\n                for (let z = 0; z < 2; z++) {\r\n                    const localMin = worldMin.add(blockSize.multiplyByFloats(x, y, z));\r\n                    const localMax = worldMin.add(blockSize.multiplyByFloats(x + 1, y + 1, z + 1));\r\n\r\n                    const block = new OctreeBlock<T>(localMin, localMax, maxBlockCapacity, currentDepth + 1, maxDepth, creationFunc);\r\n                    block.addEntries(entries);\r\n                    target.blocks.push(block);\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AACA,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAElD,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;;;AAiBlD,MAAO,WAAW;IA2DpB,WAAW;IAEX;;OAEG,CACH,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;OAEG,CACH,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;OAEG,CACH,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED,UAAU;IAEV;;;OAGG,CACI,QAAQ,CAAC,KAAQ,EAAA;QACpB,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBACtD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACjC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAC1B,CAAC;YACD,OAAO;QACX,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAEhC,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;YACtE,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC7B,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,WAAW,CAAC,KAAQ,EAAA;QACvB,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBACtD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACjC,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAC7B,CAAC;YACD,OAAO;QACX,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAE/C,IAAI,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC;YAClB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;QACvC,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,UAAU,CAAC,OAAY,EAAA;QAC1B,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAClD,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;YAC5B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACxB,CAAC;IACL,CAAC;IAED;;;;;OAKG,CACI,MAAM,CAAC,aAAsB,EAAE,SAAmC,EAAE,cAAwB,EAAA;QAC/F,oKAAI,eAAW,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,EAAE,aAAa,CAAC,EAAE,CAAC;YAChE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBACd,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;oBACtD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;oBACjC,KAAK,CAAC,MAAM,CAAC,aAAa,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;gBAC3D,CAAC;gBACD,OAAO;YACX,CAAC;YAED,IAAI,cAAc,EAAE,CAAC;gBACjB,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACnC,CAAC,MAAM,CAAC;gBACJ,SAAS,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAClD,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;;;OAMG,CACI,UAAU,CAAC,YAAqB,EAAE,YAAoB,EAAE,SAAmC,EAAE,cAAwB,EAAA;QACxH,qKAAI,cAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,YAAY,EAAE,YAAY,CAAC,EAAE,CAAC;YAC3F,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBACd,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;oBACtD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;oBACjC,KAAK,CAAC,UAAU,CAAC,YAAY,EAAE,YAAY,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;gBAC5E,CAAC;gBACD,OAAO;YACX,CAAC;YAED,IAAI,cAAc,EAAE,CAAC;gBACjB,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACnC,CAAC,MAAM,CAAC;gBACJ,SAAS,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAClD,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;OAIG,CACI,aAAa,CAAC,GAAQ,EAAE,SAAmC,EAAA;QAC9D,IAAI,GAAG,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YAC1D,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBACd,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;oBACtD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;oBACjC,KAAK,CAAC,aAAa,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;gBACxC,CAAC;gBACD,OAAO;YACX,CAAC;YACD,SAAS,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAClD,CAAC;IACL,CAAC;IAED;;OAEG,CACI,iBAAiB,GAAA;QACpB,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAC/I,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACI,MAAM,CAAC,aAAa,CACvB,QAAiB,EACjB,QAAiB,EACjB,OAAY,EACZ,gBAAwB,EACxB,YAAoB,EACpB,QAAgB,EAChB,MAA2B,EAC3B,YAAuD,EAAA;QAEvD,MAAM,CAAC,MAAM,GAAG,IAAI,KAAK,EAAkB,CAAC;QAC5C,MAAM,SAAS,GAAG,sKAAI,UAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAE3H,mBAAmB;QACnB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;YACzB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;gBACzB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;oBACzB,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oBACnE,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;oBAE/E,MAAM,KAAK,GAAG,IAAI,WAAW,CAAI,QAAQ,EAAE,QAAQ,EAAE,gBAAgB,EAAE,YAAY,GAAG,CAAC,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;oBACjH,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;oBAC1B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC9B,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IA5ND;;;;;;;;OAQG,CACH,YAAY,QAAiB,EAAE,QAAiB,EAAE,QAAgB,EAAE,KAAa,EAAE,QAAgB,EAAE,YAAuD,CAAA;QA3B5J;;WAEG,CACI,IAAA,CAAA,OAAO,GAAQ,EAAE,CAAC;QAYjB,IAAA,CAAA,gBAAgB,GAAG,IAAI,KAAK,EAAW,CAAC;QAa5C,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAElC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAE1B,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC;QAC7C,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC;QAE7C,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC;QAC7C,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;QAExC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC;QAC7C,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;QAExC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC;QAC7C,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;QAExC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC;QAC7C,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;QAExC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC;QAC7C,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;QAExC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC;QAC7C,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;IAC5C,CAAC;CAuLJ", "debugId": null}}, {"offset": {"line": 2369, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Culling/Octrees/octree.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Culling/Octrees/octree.ts"], "sourcesContent": ["import type { SmartArray } from \"../../Misc/smartArray\";\r\nimport { SmartArrayNoDuplicate } from \"../../Misc/smartArray\";\r\nimport type { Vector3 } from \"../../Maths/math.vector\";\r\nimport type { SubMesh } from \"../../Meshes/subMesh\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport type { Ray } from \"../../Culling/ray\";\r\nimport { OctreeBlock } from \"./octreeBlock\";\r\nimport type { Plane } from \"../../Maths/math.plane\";\r\n\r\n/**\r\n * Octrees are a really powerful data structure that can quickly select entities based on space coordinates.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/optimizeOctrees\r\n */\r\nexport class Octree<T> {\r\n    /**\r\n     * Blocks within the octree containing objects\r\n     */\r\n    public blocks: Array<OctreeBlock<T>>;\r\n    /**\r\n     * Content stored in the octree\r\n     */\r\n    public dynamicContent: T[] = [];\r\n\r\n    private _maxBlockCapacity: number;\r\n    private _selectionContent: SmartArrayNoDuplicate<T>;\r\n    private _creationFunc: (entry: T, block: OctreeBlock<T>) => void;\r\n\r\n    /**\r\n     * Creates a octree\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/optimizeOctrees\r\n     * @param creationFunc function to be used to instantiate the octree\r\n     * @param maxBlockCapacity defines the maximum number of meshes you want on your octree's leaves (default: 64)\r\n     * @param maxDepth defines the maximum depth (sub-levels) for your octree. Default value is 2, which means 8 8 8 = 512 blocks :) (This parameter takes precedence over capacity.)\r\n     */\r\n    constructor(\r\n        creationFunc: (entry: T, block: OctreeBlock<T>) => void,\r\n        maxBlockCapacity?: number,\r\n        /** [2] Defines the maximum depth (sub-levels) for your octree. Default value is 2, which means 8 8 8 = 512 blocks :) (This parameter takes precedence over capacity.) */\r\n        public maxDepth = 2\r\n    ) {\r\n        this._maxBlockCapacity = maxBlockCapacity || 64;\r\n        this._selectionContent = new SmartArrayNoDuplicate<T>(1024);\r\n        this._creationFunc = creationFunc;\r\n    }\r\n\r\n    // Methods\r\n    /**\r\n     * Updates the octree by adding blocks for the passed in meshes within the min and max world parameters\r\n     * @param worldMin worldMin for the octree blocks var blockSize = new Vector3((worldMax.x - worldMin.x) / 2, (worldMax.y - worldMin.y) / 2, (worldMax.z - worldMin.z) / 2);\r\n     * @param worldMax worldMax for the octree blocks var blockSize = new Vector3((worldMax.x - worldMin.x) / 2, (worldMax.y - worldMin.y) / 2, (worldMax.z - worldMin.z) / 2);\r\n     * @param entries meshes to be added to the octree blocks\r\n     */\r\n    public update(worldMin: Vector3, worldMax: Vector3, entries: T[]): void {\r\n        OctreeBlock._CreateBlocks(worldMin, worldMax, entries, this._maxBlockCapacity, 0, this.maxDepth, this, this._creationFunc);\r\n    }\r\n\r\n    /**\r\n     * Adds a mesh to the octree\r\n     * @param entry Mesh to add to the octree\r\n     */\r\n    public addMesh(entry: T): void {\r\n        for (let index = 0; index < this.blocks.length; index++) {\r\n            const block = this.blocks[index];\r\n            block.addEntry(entry);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Remove an element from the octree\r\n     * @param entry defines the element to remove\r\n     */\r\n    public removeMesh(entry: T): void {\r\n        for (let index = 0; index < this.blocks.length; index++) {\r\n            const block = this.blocks[index];\r\n            block.removeEntry(entry);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Selects an array of meshes within the frustum\r\n     * @param frustumPlanes The frustum planes to use which will select all meshes within it\r\n     * @param allowDuplicate If duplicate objects are allowed in the resulting object array\r\n     * @returns array of meshes within the frustum\r\n     */\r\n    public select(frustumPlanes: Plane[], allowDuplicate?: boolean): SmartArray<T> {\r\n        this._selectionContent.reset();\r\n\r\n        for (let index = 0; index < this.blocks.length; index++) {\r\n            const block = this.blocks[index];\r\n            block.select(frustumPlanes, this._selectionContent, allowDuplicate);\r\n        }\r\n\r\n        if (allowDuplicate) {\r\n            this._selectionContent.concat(this.dynamicContent);\r\n        } else {\r\n            this._selectionContent.concatWithNoDuplicate(this.dynamicContent);\r\n        }\r\n\r\n        return this._selectionContent;\r\n    }\r\n\r\n    /**\r\n     * Test if the octree intersect with the given bounding sphere and if yes, then add its content to the selection array\r\n     * @param sphereCenter defines the bounding sphere center\r\n     * @param sphereRadius defines the bounding sphere radius\r\n     * @param allowDuplicate defines if the selection array can contains duplicated entries\r\n     * @returns an array of objects that intersect the sphere\r\n     */\r\n    public intersects(sphereCenter: Vector3, sphereRadius: number, allowDuplicate?: boolean): SmartArray<T> {\r\n        this._selectionContent.reset();\r\n\r\n        for (let index = 0; index < this.blocks.length; index++) {\r\n            const block = this.blocks[index];\r\n            block.intersects(sphereCenter, sphereRadius, this._selectionContent, allowDuplicate);\r\n        }\r\n\r\n        if (allowDuplicate) {\r\n            this._selectionContent.concat(this.dynamicContent);\r\n        } else {\r\n            this._selectionContent.concatWithNoDuplicate(this.dynamicContent);\r\n        }\r\n\r\n        return this._selectionContent;\r\n    }\r\n\r\n    /**\r\n     * Test if the octree intersect with the given ray and if yes, then add its content to resulting array\r\n     * @param ray defines the ray to test with\r\n     * @returns array of intersected objects\r\n     */\r\n    public intersectsRay(ray: Ray): SmartArray<T> {\r\n        this._selectionContent.reset();\r\n\r\n        for (let index = 0; index < this.blocks.length; index++) {\r\n            const block = this.blocks[index];\r\n            block.intersectsRay(ray, this._selectionContent);\r\n        }\r\n\r\n        this._selectionContent.concatWithNoDuplicate(this.dynamicContent);\r\n\r\n        return this._selectionContent;\r\n    }\r\n\r\n    /**\r\n     * Adds a mesh into the octree block if it intersects the block\r\n     * @param entry defines the mesh to try to add to the block\r\n     * @param block defines the block where the mesh should be added\r\n     */\r\n    public static CreationFuncForMeshes = (entry: AbstractMesh, block: OctreeBlock<AbstractMesh>): void => {\r\n        const boundingInfo = entry.getBoundingInfo();\r\n        if (!entry.isBlocked && boundingInfo.boundingBox.intersectsMinMax(block.minPoint, block.maxPoint)) {\r\n            block.entries.push(entry);\r\n        }\r\n    };\r\n\r\n    /**\r\n     * Adds a submesh into the octree block if it intersects the block\r\n     * @param entry defines the submesh to try to add to the block\r\n     * @param block defines the block where the submesh should be added\r\n     */\r\n    public static CreationFuncForSubMeshes = (entry: SubMesh, block: OctreeBlock<SubMesh>): void => {\r\n        const boundingInfo = entry.getBoundingInfo();\r\n        if (boundingInfo.boundingBox.intersectsMinMax(block.minPoint, block.maxPoint)) {\r\n            block.entries.push(entry);\r\n        }\r\n    };\r\n}\r\n"], "names": [], "mappings": ";;;AACA,OAAO,EAAE,qBAAqB,EAAE,MAAM,uBAAuB,CAAC;AAK9D,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;;;AAOtC,MAAO,MAAM;IAgCf,UAAU;IACV;;;;;OAKG,CACI,MAAM,CAAC,QAAiB,EAAE,QAAiB,EAAE,OAAY,EAAA;oLAC5D,cAAW,CAAC,aAAa,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,CAAC,iBAAiB,EAAE,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IAC/H,CAAC;IAED;;;OAGG,CACI,OAAO,CAAC,KAAQ,EAAA;QACnB,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACtD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACjC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,UAAU,CAAC,KAAQ,EAAA;QACtB,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACtD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACjC,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC7B,CAAC;IACL,CAAC;IAED;;;;;OAKG,CACI,MAAM,CAAC,aAAsB,EAAE,cAAwB,EAAA;QAC1D,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAE/B,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACtD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACjC,KAAK,CAAC,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,cAAc,EAAE,CAAC;YACjB,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACvD,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACtE,CAAC;QAED,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED;;;;;;OAMG,CACI,UAAU,CAAC,YAAqB,EAAE,YAAoB,EAAE,cAAwB,EAAA;QACnF,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAE/B,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACtD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACjC,KAAK,CAAC,UAAU,CAAC,YAAY,EAAE,YAAY,EAAE,IAAI,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC;QACzF,CAAC;QAED,IAAI,cAAc,EAAE,CAAC;YACjB,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACvD,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACtE,CAAC;QAED,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED;;;;OAIG,CACI,aAAa,CAAC,GAAQ,EAAA;QACzB,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAE/B,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACtD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACjC,KAAK,CAAC,aAAa,CAAC,GAAG,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAElE,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAlHD;;;;;;OAMG,CACH,YACI,YAAuD,EACvD,gBAAyB,EACzB,uKAAA,EAAyK,CAClK,WAAW,CAAC,CAAA;QAAZ,IAAA,CAAA,QAAQ,GAAR,QAAQ,CAAI;QApBvB;;WAEG,CACI,IAAA,CAAA,cAAc,GAAQ,EAAE,CAAC;QAmB5B,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,IAAI,EAAE,CAAC;QAChD,IAAI,CAAC,iBAAiB,GAAG,iKAAI,wBAAqB,CAAI,IAAI,CAAC,CAAC;QAC5D,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;IACtC,CAAC;;AAoGD;;;;GAIG,CACW,OAAA,qBAAqB,GAAG,CAAC,KAAmB,EAAE,KAAgC,EAAQ,EAAE;IAClG,MAAM,YAAY,GAAG,KAAK,CAAC,eAAe,EAAE,CAAC;IAC7C,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI,YAAY,CAAC,WAAW,CAAC,gBAAgB,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;QAChG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC9B,CAAC;AACL,CAAC,AALkC,CAKjC;AAEF;;;;GAIG,CACW,OAAA,wBAAwB,GAAG,CAAC,KAAc,EAAE,KAA2B,EAAQ,EAAE;IAC3F,MAAM,YAAY,GAAG,KAAK,CAAC,eAAe,EAAE,CAAC;IAC7C,IAAI,YAAY,CAAC,WAAW,CAAC,gBAAgB,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC5E,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC9B,CAAC;AACL,CAAC,AALqC,CAKpC", "debugId": null}}, {"offset": {"line": 2494, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Culling/Octrees/octreeSceneComponent.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Culling/Octrees/octreeSceneComponent.ts"], "sourcesContent": ["import type { ISmartArrayLike } from \"../../Misc/smartArray\";\r\nimport { Scene } from \"../../scene\";\r\nimport { Vector3 } from \"../../Maths/math.vector\";\r\nimport type { SubMesh } from \"../../Meshes/subMesh\";\r\nimport { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport { Ray } from \"../../Culling/ray\";\r\nimport { SceneComponentConstants } from \"../../sceneComponent\";\r\n\r\nimport { Octree } from \"./octree\";\r\nimport { EngineStore } from \"../../Engines/engineStore\";\r\n\r\nimport type { Collider } from \"../../Collisions/collider\";\r\n\r\ndeclare module \"../../scene\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface Scene {\r\n        /**\r\n         * @internal\r\n         * Backing Filed\r\n         */\r\n        _selectionOctree: Octree<AbstractMesh>;\r\n\r\n        /**\r\n         * Gets the octree used to boost mesh selection (picking)\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/optimizeOctrees\r\n         */\r\n        selectionOctree: Octree<AbstractMesh>;\r\n\r\n        /**\r\n         * Creates or updates the octree used to boost selection (picking)\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/optimizeOctrees\r\n         * @param maxCapacity defines the maximum capacity per leaf\r\n         * @param maxDepth defines the maximum depth of the octree\r\n         * @returns an octree of AbstractMesh\r\n         */\r\n        createOrUpdateSelectionOctree(maxCapacity?: number, maxDepth?: number): Octree<AbstractMesh>;\r\n    }\r\n}\r\n\r\nScene.prototype.createOrUpdateSelectionOctree = function (maxCapacity = 64, maxDepth = 2): Octree<AbstractMesh> {\r\n    let component = this._getComponent(SceneComponentConstants.NAME_OCTREE);\r\n    if (!component) {\r\n        component = new OctreeSceneComponent(this);\r\n        this._addComponent(component);\r\n    }\r\n\r\n    if (!this._selectionOctree) {\r\n        this._selectionOctree = new Octree<AbstractMesh>(Octree.CreationFuncForMeshes, maxCapacity, maxDepth);\r\n    }\r\n\r\n    const worldExtends = this.getWorldExtends();\r\n\r\n    // Update octree\r\n    this._selectionOctree.update(worldExtends.min, worldExtends.max, this.meshes);\r\n\r\n    return this._selectionOctree;\r\n};\r\n\r\nObject.defineProperty(Scene.prototype, \"selectionOctree\", {\r\n    get: function (this: Scene) {\r\n        return this._selectionOctree;\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n\r\ndeclare module \"../../Meshes/abstractMesh\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractMesh {\r\n        /**\r\n         * @internal\r\n         * Backing Field\r\n         */\r\n        _submeshesOctree: Octree<SubMesh>;\r\n\r\n        /**\r\n         * This function will create an octree to help to select the right submeshes for rendering, picking and collision computations.\r\n         * Please note that you must have a decent number of submeshes to get performance improvements when using an octree\r\n         * @param maxCapacity defines the maximum size of each block (64 by default)\r\n         * @param maxDepth defines the maximum depth to use (no more than 2 levels by default)\r\n         * @returns the new octree\r\n         * @see https://www.babylonjs-playground.com/#NA4OQ#12\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/optimizeOctrees\r\n         */\r\n        createOrUpdateSubmeshesOctree(maxCapacity?: number, maxDepth?: number): Octree<SubMesh>;\r\n    }\r\n}\r\n\r\n/**\r\n * This function will create an octree to help to select the right submeshes for rendering, picking and collision computations.\r\n * Please note that you must have a decent number of submeshes to get performance improvements when using an octree\r\n * @param maxCapacity defines the maximum size of each block (64 by default)\r\n * @param maxDepth defines the maximum depth to use (no more than 2 levels by default)\r\n * @returns the new octree\r\n * @see https://www.babylonjs-playground.com/#NA4OQ#12\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/optimizeOctrees\r\n */\r\nAbstractMesh.prototype.createOrUpdateSubmeshesOctree = function (maxCapacity = 64, maxDepth = 2): Octree<SubMesh> {\r\n    const scene = this.getScene();\r\n    let component = scene._getComponent(SceneComponentConstants.NAME_OCTREE);\r\n    if (!component) {\r\n        component = new OctreeSceneComponent(scene);\r\n        scene._addComponent(component);\r\n    }\r\n\r\n    if (!this._submeshesOctree) {\r\n        this._submeshesOctree = new Octree<SubMesh>(Octree.CreationFuncForSubMeshes, maxCapacity, maxDepth);\r\n    }\r\n\r\n    this.computeWorldMatrix(true);\r\n\r\n    const boundingInfo = this.getBoundingInfo();\r\n\r\n    // Update octree\r\n    const bbox = boundingInfo.boundingBox;\r\n    this._submeshesOctree.update(bbox.minimumWorld, bbox.maximumWorld, this.subMeshes);\r\n\r\n    return this._submeshesOctree;\r\n};\r\n\r\n/**\r\n * Defines the octree scene component responsible to manage any octrees\r\n * in a given scene.\r\n */\r\nexport class OctreeSceneComponent {\r\n    /**\r\n     * The component name help to identify the component in the list of scene components.\r\n     */\r\n    public readonly name = SceneComponentConstants.NAME_OCTREE;\r\n\r\n    /**\r\n     * The scene the component belongs to.\r\n     */\r\n    public scene: Scene;\r\n\r\n    /**\r\n     * Indicates if the meshes have been checked to make sure they are isEnabled()\r\n     */\r\n    public readonly checksIsEnabled = true;\r\n\r\n    /**\r\n     * Creates a new instance of the component for the given scene\r\n     * @param scene Defines the scene to register the component in\r\n     */\r\n    constructor(scene?: Scene) {\r\n        scene = scene || <Scene>EngineStore.LastCreatedScene;\r\n        if (!scene) {\r\n            return;\r\n        }\r\n        this.scene = scene;\r\n\r\n        this.scene.getActiveMeshCandidates = () => this.getActiveMeshCandidates();\r\n        this.scene.getActiveSubMeshCandidates = (mesh: AbstractMesh) => this.getActiveSubMeshCandidates(mesh);\r\n        this.scene.getCollidingSubMeshCandidates = (mesh: AbstractMesh, collider: Collider) => this.getCollidingSubMeshCandidates(mesh, collider);\r\n        this.scene.getIntersectingSubMeshCandidates = (mesh: AbstractMesh, localRay: Ray) => this.getIntersectingSubMeshCandidates(mesh, localRay);\r\n    }\r\n\r\n    /**\r\n     * Registers the component in a given scene\r\n     */\r\n    public register(): void {\r\n        this.scene.onMeshRemovedObservable.add((mesh: AbstractMesh) => {\r\n            const sceneOctree = this.scene.selectionOctree;\r\n            if (sceneOctree !== undefined && sceneOctree !== null) {\r\n                const index = sceneOctree.dynamicContent.indexOf(mesh);\r\n\r\n                if (index !== -1) {\r\n                    sceneOctree.dynamicContent.splice(index, 1);\r\n                }\r\n            }\r\n        });\r\n\r\n        this.scene.onMeshImportedObservable.add((mesh: AbstractMesh) => {\r\n            const sceneOctree = this.scene.selectionOctree;\r\n            if (sceneOctree !== undefined && sceneOctree !== null) {\r\n                sceneOctree.addMesh(mesh);\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Return the list of active meshes\r\n     * @returns the list of active meshes\r\n     */\r\n    public getActiveMeshCandidates(): ISmartArrayLike<AbstractMesh> {\r\n        return this.scene._selectionOctree?.select(this.scene.frustumPlanes) || this.scene._getDefaultMeshCandidates();\r\n    }\r\n\r\n    /**\r\n     * Return the list of active sub meshes\r\n     * @param mesh The mesh to get the candidates sub meshes from\r\n     * @returns the list of active sub meshes\r\n     */\r\n    public getActiveSubMeshCandidates(mesh: AbstractMesh): ISmartArrayLike<SubMesh> {\r\n        if (mesh._submeshesOctree && mesh.useOctreeForRenderingSelection) {\r\n            const intersections = mesh._submeshesOctree.select(this.scene.frustumPlanes);\r\n            return intersections;\r\n        }\r\n        return this.scene._getDefaultSubMeshCandidates(mesh);\r\n    }\r\n\r\n    private _tempRay = new Ray(Vector3.Zero(), new Vector3(1, 1, 1));\r\n    /**\r\n     * Return the list of sub meshes intersecting with a given local ray\r\n     * @param mesh defines the mesh to find the submesh for\r\n     * @param localRay defines the ray in local space\r\n     * @returns the list of intersecting sub meshes\r\n     */\r\n    public getIntersectingSubMeshCandidates(mesh: AbstractMesh, localRay: Ray): ISmartArrayLike<SubMesh> {\r\n        if (mesh._submeshesOctree && mesh.useOctreeForPicking) {\r\n            Ray.TransformToRef(localRay, mesh.getWorldMatrix(), this._tempRay);\r\n            const intersections = mesh._submeshesOctree.intersectsRay(this._tempRay);\r\n\r\n            return intersections;\r\n        }\r\n        return this.scene._getDefaultSubMeshCandidates(mesh);\r\n    }\r\n\r\n    /**\r\n     * Return the list of sub meshes colliding with a collider\r\n     * @param mesh defines the mesh to find the submesh for\r\n     * @param collider defines the collider to evaluate the collision against\r\n     * @returns the list of colliding sub meshes\r\n     */\r\n    public getCollidingSubMeshCandidates(mesh: AbstractMesh, collider: Collider): ISmartArrayLike<SubMesh> {\r\n        if (mesh._submeshesOctree && mesh.useOctreeForCollisions) {\r\n            const radius = collider._velocityWorldLength + Math.max(collider._radius.x, collider._radius.y, collider._radius.z);\r\n            const intersections = mesh._submeshesOctree.intersects(collider._basePointWorld, radius);\r\n\r\n            return intersections;\r\n        }\r\n        return this.scene._getDefaultSubMeshCandidates(mesh);\r\n    }\r\n\r\n    /**\r\n     * Rebuilds the elements related to this component in case of\r\n     * context lost for instance.\r\n     */\r\n    public rebuild(): void {\r\n        // Nothing to do here.\r\n    }\r\n\r\n    /**\r\n     * Disposes the component and the associated resources.\r\n     */\r\n    public dispose(): void {\r\n        // Nothing to do here.\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AACA,OAAO,EAAE,KAAK,EAAE,MAAM,aAAa,CAAC;AACpC,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAElD,OAAO,EAAE,YAAY,EAAE,MAAM,2BAA2B,CAAC;AACzD,OAAO,EAAE,GAAG,EAAE,MAAM,mBAAmB,CAAC;;AACxC,OAAO,EAAE,uBAAuB,EAAE,MAAM,sBAAsB,CAAC;AAE/D,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAClC,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;;;;;;;;gJA8BxD,QAAK,CAAC,SAAS,CAAC,6BAA6B,GAAG;sBAAU,WAAW,sDAAG,EAAE,aAAE,QAAQ,yDAAG,CAAC;IACpF,IAAI,SAAS,GAAG,IAAI,CAAC,aAAa,0JAAC,0BAAuB,CAAC,WAAW,CAAC,CAAC;IACxE,IAAI,CAAC,SAAS,EAAE,CAAC;QACb,SAAS,GAAG,IAAI,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAC3C,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;IAClC,CAAC;IAED,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACzB,IAAI,CAAC,gBAAgB,GAAG,2KAAI,SAAM,wKAAe,SAAM,CAAC,qBAAqB,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;IAC1G,CAAC;IAED,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;IAE5C,gBAAgB;IAChB,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,EAAE,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IAE9E,OAAO,IAAI,CAAC,gBAAgB,CAAC;AACjC,CAAC,CAAC;AAEF,MAAM,CAAC,cAAc,iJAAC,QAAK,CAAC,SAAS,EAAE,iBAAiB,EAAE;IACtD,GAAG,EAAE;QACD,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;AAwBH;;;;;;;;GAQG,kKACH,eAAY,CAAC,SAAS,CAAC,6BAA6B,GAAG;sBAAU,WAAW,sDAAG,EAAE,aAAE,QAAQ,yDAAG,CAAC;IAC3F,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;IAC9B,IAAI,SAAS,GAAG,KAAK,CAAC,aAAa,0JAAC,0BAAuB,CAAC,WAAW,CAAC,CAAC;IACzE,IAAI,CAAC,SAAS,EAAE,CAAC;QACb,SAAS,GAAG,IAAI,oBAAoB,CAAC,KAAK,CAAC,CAAC;QAC5C,KAAK,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;IACnC,CAAC;IAED,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACzB,IAAI,CAAC,gBAAgB,GAAG,IAAI,gLAAM,wKAAU,SAAM,CAAC,wBAAwB,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;IACxG,CAAC;IAED,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;IAE9B,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;IAE5C,gBAAgB;IAChB,MAAM,IAAI,GAAG,YAAY,CAAC,WAAW,CAAC;IACtC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;IAEnF,OAAO,IAAI,CAAC,gBAAgB,CAAC;AACjC,CAAC,CAAC;AAMI,MAAO,oBAAoB;IAiC7B;;OAEG,CACI,QAAQ,GAAA;QACX,IAAI,CAAC,KAAK,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC,IAAkB,EAAE,EAAE;YAC1D,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC;YAC/C,IAAI,WAAW,KAAK,SAAS,IAAI,WAAW,KAAK,IAAI,EAAE,CAAC;gBACpD,MAAM,KAAK,GAAG,WAAW,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBAEvD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;oBACf,WAAW,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBAChD,CAAC;YACL,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC,IAAkB,EAAE,EAAE;YAC3D,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC;YAC/C,IAAI,WAAW,KAAK,SAAS,IAAI,WAAW,KAAK,IAAI,EAAE,CAAC;gBACpD,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC9B,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG,CACI,uBAAuB,GAAA;;QAC1B,4CAAW,CAAC,KAAK,CAAC,gBAAgB,cAA3B,gFAA6B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,KAAI,IAAI,CAAC,KAAK,CAAC,yBAAyB,EAAE,CAAC;IACnH,CAAC;IAED;;;;OAIG,CACI,0BAA0B,CAAC,IAAkB,EAAA;QAChD,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,8BAA8B,EAAE,CAAC;YAC/D,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YAC7E,OAAO,aAAa,CAAC;QACzB,CAAC;QACD,OAAO,IAAI,CAAC,KAAK,CAAC,4BAA4B,CAAC,IAAI,CAAC,CAAC;IACzD,CAAC;IAGD;;;;;OAKG,CACI,gCAAgC,CAAC,IAAkB,EAAE,QAAa,EAAA;QACrE,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;6KACpD,MAAG,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YACnE,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAEzE,OAAO,aAAa,CAAC;QACzB,CAAC;QACD,OAAO,IAAI,CAAC,KAAK,CAAC,4BAA4B,CAAC,IAAI,CAAC,CAAC;IACzD,CAAC;IAED;;;;;OAKG,CACI,6BAA6B,CAAC,IAAkB,EAAE,QAAkB,EAAA;QACvE,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;YACvD,MAAM,MAAM,GAAG,QAAQ,CAAC,oBAAoB,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACpH,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,QAAQ,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;YAEzF,OAAO,aAAa,CAAC;QACzB,CAAC;QACD,OAAO,IAAI,CAAC,KAAK,CAAC,4BAA4B,CAAC,IAAI,CAAC,CAAC;IACzD,CAAC;IAED;;;OAGG,CACI,OAAO,GAAA;IACV,sBAAsB;IAC1B,CAAC;IAED;;OAEG,CACI,OAAO,GAAA;IACV,sBAAsB;IAC1B,CAAC;IA3GD;;;OAGG,CACH,YAAY,KAAa,CAAA;QAnBzB;;WAEG,CACa,IAAA,CAAA,IAAI,4JAAG,0BAAuB,CAAC,WAAW,CAAC;QAO3D;;WAEG,CACa,IAAA,CAAA,eAAe,GAAG,IAAI,CAAC;QA+D/B,IAAA,CAAA,QAAQ,GAAG,qKAAI,MAAG,mKAAC,UAAO,CAAC,IAAI,EAAE,EAAE,sKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAxD7D,KAAK,GAAG,KAAK,qKAAW,cAAW,CAAC,gBAAgB,CAAC;QACrD,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,OAAO;QACX,CAAC;QACD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QAEnB,IAAI,CAAC,KAAK,CAAC,uBAAuB,GAAG,GAAG,CAAG,CAAD,GAAK,CAAC,uBAAuB,EAAE,CAAC;QAC1E,IAAI,CAAC,KAAK,CAAC,0BAA0B,GAAG,CAAC,IAAkB,EAAE,CAAG,CAAD,GAAK,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC;QACtG,IAAI,CAAC,KAAK,CAAC,6BAA6B,GAAG,CAAC,IAAkB,EAAE,QAAkB,EAAE,CAAG,CAAD,GAAK,CAAC,6BAA6B,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAC1I,IAAI,CAAC,KAAK,CAAC,gCAAgC,GAAG,CAAC,IAAkB,EAAE,QAAa,EAAE,CAAG,CAAD,GAAK,CAAC,gCAAgC,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IAC/I,CAAC;CA6FJ", "debugId": null}}, {"offset": {"line": 2661, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Culling/Octrees/index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Culling/Octrees/index.ts"], "sourcesContent": ["export * from \"./octree\";\r\nexport * from \"./octreeBlock\";\r\nexport * from \"./octreeSceneComponent\";\r\n"], "names": [], "mappings": ";AAAA,cAAc,UAAU,CAAC;AACzB,cAAc,eAAe,CAAC;AAC9B,cAAc,wBAAwB,CAAC", "debugId": null}}, {"offset": {"line": 2681, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Culling/index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Culling/index.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-restricted-imports */\r\nexport * from \"./boundingBox\";\r\nexport * from \"./boundingInfo\";\r\nexport * from \"./Helper/boundingInfoHelper\";\r\nexport * from \"./Helper/transformFeedbackBoundingHelper\";\r\nexport * from \"./Helper/computeShaderBoundingHelper\";\r\nexport * from \"./boundingSphere\";\r\nexport * from \"./Octrees/index\";\r\nexport * from \"./ray\";\r\n"], "names": [], "mappings": "AAAA,2DAAA,EAA6D;AAC7D,cAAc,eAAe,CAAC;AAC9B,cAAc,gBAAgB,CAAC;AAC/B,cAAc,6BAA6B,CAAC;AAC5C,cAAc,0CAA0C,CAAC;AACzD,cAAc,sCAAsC,CAAC;AACrD,cAAc,kBAAkB,CAAC;AACjC,cAAc,iBAAiB,CAAC;AAChC,cAAc,OAAO,CAAC", "debugId": null}}]}