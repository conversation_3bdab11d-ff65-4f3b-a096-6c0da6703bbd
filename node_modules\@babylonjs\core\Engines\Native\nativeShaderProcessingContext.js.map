{"version": 3, "file": "nativeShaderProcessingContext.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/Native/nativeShaderProcessingContext.ts"], "names": [], "mappings": "AAIA;;GAEG;AACH,MAAM,OAAO,6BAA6B;IAA1C;QACW,yCAAoC,GAA+B,EAAE,CAAC;QACtE,2BAAsB,GAA+B,EAAE,CAAC;QACxD,uBAAkB,GAAG,EAAE,CAAC;IACnC,CAAC;CAAA", "sourcesContent": ["/* eslint-disable babylonjs/available */\r\n/* eslint-disable jsdoc/require-jsdoc */\r\nimport type { _IShaderProcessingContext } from \"../Processors/shaderProcessingOptions\";\r\n\r\n/**\r\n * @internal\r\n */\r\nexport class NativeShaderProcessingContext implements _IShaderProcessingContext {\r\n    public vertexBufferKindToNumberOfComponents: { [kind: string]: number } = {};\r\n    public remappedAttributeNames: { [name: string]: string } = {};\r\n    public injectInVertexMain = \"\";\r\n}\r\n"]}