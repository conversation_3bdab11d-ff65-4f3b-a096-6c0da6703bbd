{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialBlockConnectionPointTypes.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Enums/nodeMaterialBlockConnectionPointTypes.ts"], "sourcesContent": ["/**\r\n * Defines the kind of connection point for node based material\r\n */\r\nexport enum NodeMaterialBlockConnectionPointTypes {\r\n    /** Float */\r\n    Float = 0x0001,\r\n    /** Int */\r\n    Int = 0x0002,\r\n    /** Vector2 */\r\n    Vector2 = 0x0004,\r\n    /** Vector3 */\r\n    Vector3 = 0x0008,\r\n    /** Vector4 */\r\n    Vector4 = 0x0010,\r\n    /** Color3 */\r\n    Color3 = 0x0020,\r\n    /** Color4 */\r\n    Color4 = 0x0040,\r\n    /** Matrix */\r\n    Matrix = 0x0080,\r\n    /** Custom object */\r\n    Object = 0x0100,\r\n    /** Detect type based on connection */\r\n    AutoDetect = 0x0400,\r\n    /** Output type that will be defined by input type */\r\n    BasedOnInput = 0x0800,\r\n    /** Bitmask of all types */\r\n    All = 0x0fff,\r\n}\r\n"], "names": [], "mappings": "AAAA;;GAEG;;;AACH,IAAY,qCAyBX;AAzBD,CAAA,SAAY,qCAAqC;IAC7C,UAAA,EAAY,CACZ,qCAAA,CAAA,qCAAA,CAAA,QAAA,GAAA,EAAA,GAAA,OAAc,CAAA;IACd,QAAA,EAAU,CACV,qCAAA,CAAA,qCAAA,CAAA,MAAA,GAAA,EAAA,GAAA,KAAY,CAAA;IACZ,YAAA,EAAc,CACd,qCAAA,CAAA,qCAAA,CAAA,UAAA,GAAA,EAAA,GAAA,SAAgB,CAAA;IAChB,YAAA,EAAc,CACd,qCAAA,CAAA,qCAAA,CAAA,UAAA,GAAA,EAAA,GAAA,SAAgB,CAAA;IAChB,YAAA,EAAc,CACd,qCAAA,CAAA,qCAAA,CAAA,UAAA,GAAA,GAAA,GAAA,SAAgB,CAAA;IAChB,WAAA,EAAa,CACb,qCAAA,CAAA,qCAAA,CAAA,SAAA,GAAA,GAAA,GAAA,QAAe,CAAA;IACf,WAAA,EAAa,CACb,qCAAA,CAAA,qCAAA,CAAA,SAAA,GAAA,GAAA,GAAA,QAAe,CAAA;IACf,WAAA,EAAa,CACb,qCAAA,CAAA,qCAAA,CAAA,SAAA,GAAA,IAAA,GAAA,QAAe,CAAA;IACf,kBAAA,EAAoB,CACpB,qCAAA,CAAA,qCAAA,CAAA,SAAA,GAAA,IAAA,GAAA,QAAe,CAAA;IACf,oCAAA,EAAsC,CACtC,qCAAA,CAAA,qCAAA,CAAA,aAAA,GAAA,KAAA,GAAA,YAAmB,CAAA;IACnB,mDAAA,EAAqD,CACrD,qCAAA,CAAA,qCAAA,CAAA,eAAA,GAAA,KAAA,GAAA,cAAqB,CAAA;IACrB,yBAAA,EAA2B,CAC3B,qCAAA,CAAA,qCAAA,CAAA,MAAA,GAAA,KAAA,GAAA,KAAY,CAAA;AAChB,CAAC,EAzBW,qCAAqC,IAAA,CAArC,qCAAqC,GAAA,CAAA,CAAA,GAyBhD", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialBlockTargets.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Enums/nodeMaterialBlockTargets.ts"], "sourcesContent": ["/**\r\n * Enum used to define the target of a block\r\n */\r\nexport enum NodeMaterialBlockTargets {\r\n    /** Vertex shader */\r\n    Vertex = 1,\r\n    /** Fragment shader */\r\n    Fragment = 2,\r\n    /** Neutral */\r\n    Neutral = 4,\r\n    /** Vertex and Fragment */\r\n    VertexAndFragment = Vertex | Fragment,\r\n}\r\n"], "names": [], "mappings": "AAAA;;GAEG;;;AACH,IAAY,wBASX;AATD,CAAA,SAAY,wBAAwB;IAChC,kBAAA,EAAoB,CACpB,wBAAA,CAAA,wBAAA,CAAA,SAAA,GAAA,EAAA,GAAA,QAAU,CAAA;IACV,oBAAA,EAAsB,CACtB,wBAAA,CAAA,wBAAA,CAAA,WAAA,GAAA,EAAA,GAAA,UAAY,CAAA;IACZ,YAAA,EAAc,CACd,wBAAA,CAAA,wBAAA,CAAA,UAAA,GAAA,EAAA,GAAA,SAAW,CAAA;IACX,wBAAA,EAA0B,CAC1B,wBAAA,CAAA,wBAAA,CAAA,oBAAA,GAAA,EAAA,GAAA,mBAAqC,CAAA;AACzC,CAAC,EATW,wBAAwB,IAAA,CAAxB,wBAAwB,GAAA,CAAA,CAAA,GASnC", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialModes.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Enums/nodeMaterialModes.ts"], "sourcesContent": ["/**\r\n * Enum used to define the material modes\r\n */\r\nexport enum NodeMaterialModes {\r\n    /** Regular material */\r\n    Material = 0,\r\n    /** For post process */\r\n    PostProcess = 1,\r\n    /** For particle system */\r\n    Particle = 2,\r\n    /** For procedural texture */\r\n    ProceduralTexture = 3,\r\n    /** For gaussian splatting */\r\n    GaussianSplatting = 4,\r\n    /** For SFE */\r\n    SFE = 5,\r\n}\r\n"], "names": [], "mappings": "AAAA;;GAEG;;;AACH,IAAY,iBAaX;AAbD,CAAA,SAAY,iBAAiB;IACzB,qBAAA,EAAuB,CACvB,iBAAA,CAAA,iBAAA,CAAA,WAAA,GAAA,EAAA,GAAA,UAAY,CAAA;IACZ,qBAAA,EAAuB,CACvB,iBAAA,CAAA,iBAAA,CAAA,cAAA,GAAA,EAAA,GAAA,aAAe,CAAA;IACf,wBAAA,EAA0B,CAC1B,iBAAA,CAAA,iBAAA,CAAA,WAAA,GAAA,EAAA,GAAA,UAAY,CAAA;IACZ,2BAAA,EAA6B,CAC7B,iBAAA,CAAA,iBAAA,CAAA,oBAAA,GAAA,EAAA,GAAA,mBAAqB,CAAA;IACrB,2BAAA,EAA6B,CAC7B,iBAAA,CAAA,iBAAA,CAAA,oBAAA,GAAA,EAAA,GAAA,mBAAqB,CAAA;IACrB,YAAA,EAAc,CACd,iBAAA,CAAA,iBAAA,CAAA,MAAA,GAAA,EAAA,GAAA,KAAO,CAAA;AACX,CAAC,EAbW,iBAAiB,IAAA,CAAjB,iBAAiB,GAAA,CAAA,CAAA,GAa5B", "debugId": null}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialSystemValues.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Enums/nodeMaterialSystemValues.ts"], "sourcesContent": ["/**\r\n * Enum used to define system values e.g. values automatically provided by the system\r\n */\r\nexport enum NodeMaterialSystemValues {\r\n    /** World */\r\n    World = 1,\r\n    /** View */\r\n    View = 2,\r\n    /** Projection */\r\n    Projection = 3,\r\n    /** ViewProjection */\r\n    ViewProjection = 4,\r\n    /** WorldView */\r\n    WorldView = 5,\r\n    /** WorldViewProjection */\r\n    WorldViewProjection = 6,\r\n    /** CameraPosition */\r\n    CameraPosition = 7,\r\n    /** Fog Color */\r\n    FogColor = 8,\r\n    /** Delta time */\r\n    DeltaTime = 9,\r\n    /** Camera parameters */\r\n    CameraParameters = 10,\r\n    /** Material alpha */\r\n    MaterialAlpha = 11,\r\n}\r\n"], "names": [], "mappings": "AAAA;;GAEG;;;AACH,IAAY,wBAuBX;AAvBD,CAAA,SAAY,wBAAwB;IAChC,UAAA,EAAY,CACZ,wBAAA,CAAA,wBAAA,CAAA,QAAA,GAAA,EAAA,GAAA,OAAS,CAAA;IACT,SAAA,EAAW,CACX,wBAAA,CAAA,wBAAA,CAAA,OAAA,GAAA,EAAA,GAAA,MAAQ,CAAA;IACR,eAAA,EAAiB,CACjB,wBAAA,CAAA,wBAAA,CAAA,aAAA,GAAA,EAAA,GAAA,YAAc,CAAA;IACd,mBAAA,EAAqB,CACrB,wBAAA,CAAA,wBAAA,CAAA,iBAAA,GAAA,EAAA,GAAA,gBAAkB,CAAA;IAClB,cAAA,EAAgB,CAChB,wBAAA,CAAA,wBAAA,CAAA,YAAA,GAAA,EAAA,GAAA,WAAa,CAAA;IACb,wBAAA,EAA0B,CAC1B,wBAAA,CAAA,wBAAA,CAAA,sBAAA,GAAA,EAAA,GAAA,qBAAuB,CAAA;IACvB,mBAAA,EAAqB,CACrB,wBAAA,CAAA,wBAAA,CAAA,iBAAA,GAAA,EAAA,GAAA,gBAAkB,CAAA;IAClB,cAAA,EAAgB,CAChB,wBAAA,CAAA,wBAAA,CAAA,WAAA,GAAA,EAAA,GAAA,UAAY,CAAA;IACZ,eAAA,EAAiB,CACjB,wBAAA,CAAA,wBAAA,CAAA,YAAA,GAAA,EAAA,GAAA,WAAa,CAAA;IACb,sBAAA,EAAwB,CACxB,wBAAA,CAAA,wBAAA,CAAA,mBAAA,GAAA,GAAA,GAAA,kBAAqB,CAAA;IACrB,mBAAA,EAAqB,CACrB,wBAAA,CAAA,wBAAA,CAAA,gBAAA,GAAA,GAAA,GAAA,eAAkB,CAAA;AACtB,CAAC,EAvBW,wBAAwB,IAAA,CAAxB,wBAAwB,GAAA,CAAA,CAAA,GAuBnC", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialBlockConnectionPointMode.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Enums/nodeMaterialBlockConnectionPointMode.ts"], "sourcesContent": ["/**\r\n * Enum defining the mode of a NodeMaterialBlockConnectionPoint\r\n */\r\nexport const enum NodeMaterialBlockConnectionPointMode {\r\n    /** Value is an uniform */\r\n    Uniform,\r\n    /** Value is a mesh attribute */\r\n    Attribute,\r\n    /** Value is a varying between vertex and fragment shaders */\r\n    Varying,\r\n    /** Mode is undefined */\r\n    Undefined,\r\n}\r\n"], "names": [], "mappings": "AAAA;;GAEG;;;AACH,IAAkB,oCASjB;AATD,CAAA,SAAkB,oCAAoC;IAClD,wBAAA,EAA0B,CAC1B,oCAAA,CAAA,oCAAA,CAAA,UAAA,GAAA,EAAA,GAAA,SAAO,CAAA;IACP,8BAAA,EAAgC,CAChC,oCAAA,CAAA,oCAAA,CAAA,YAAA,GAAA,EAAA,GAAA,WAAS,CAAA;IACT,2DAAA,EAA6D,CAC7D,oCAAA,CAAA,oCAAA,CAAA,UAAA,GAAA,EAAA,GAAA,SAAO,CAAA;IACP,sBAAA,EAAwB,CACxB,oCAAA,CAAA,oCAAA,CAAA,YAAA,GAAA,EAAA,GAAA,WAAS,CAAA;AACb,CAAC,EATiB,oCAAoC,IAAA,CAApC,oCAAoC,GAAA,CAAA,CAAA,GASrD", "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Enums/index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Enums/index.ts"], "sourcesContent": ["export * from \"./nodeMaterialBlockTargets\";\r\nexport * from \"./nodeMaterialBlockConnectionPointTypes\";\r\nexport * from \"./nodeMaterialBlockConnectionPointMode\";\r\nexport * from \"./nodeMaterialSystemValues\";\r\nexport * from \"./nodeMaterialModes\";\r\n"], "names": [], "mappings": ";AAAA,cAAc,4BAA4B,CAAC;AAC3C,cAAc,yCAAyC,CAAC;AACxD,cAAc,wCAAwC,CAAC;AACvD,cAAc,4BAA4B,CAAC;AAC3C,cAAc,qBAAqB,CAAC", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterialBuildState.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/nodeMaterialBuildState.ts"], "sourcesContent": ["import { NodeMaterialBlockConnectionPointTypes } from \"./Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport { NodeMaterialBlockTargets } from \"./Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterialBuildStateSharedData } from \"./nodeMaterialBuildStateSharedData\";\r\nimport { ShaderLanguage } from \"../shaderLanguage\";\r\nimport type { NodeMaterialConnectionPoint } from \"./nodeMaterialBlockConnectionPoint\";\r\nimport { ShaderStore as EngineShaderStore } from \"../../Engines/shaderStore\";\r\nimport { Constants } from \"../../Engines/constants\";\r\nimport type { NodeMaterialBlock } from \"./nodeMaterialBlock\";\r\nimport { Process } from \"core/Engines/Processors/shaderProcessor\";\r\nimport type { _IProcessingOptions } from \"core/Engines/Processors/shaderProcessingOptions\";\r\nimport { WebGLShaderProcessor } from \"core/Engines/WebGL/webGLShaderProcessors\";\r\nimport { Logger } from \"core/Misc/logger\";\r\n\r\n/**\r\n * Class used to store node based material build state\r\n */\r\nexport class NodeMaterialBuildState {\r\n    /** Gets or sets a boolean indicating if the current state can emit uniform buffers */\r\n    public supportUniformBuffers = false;\r\n    /**\r\n     * Gets the list of emitted attributes\r\n     */\r\n    public attributes: string[] = [];\r\n    /**\r\n     * Gets the list of emitted uniforms\r\n     */\r\n    public uniforms: string[] = [];\r\n    /**\r\n     * Gets the list of emitted constants\r\n     */\r\n    public constants: string[] = [];\r\n    /**\r\n     * Gets the list of emitted samplers\r\n     */\r\n    public samplers: string[] = [];\r\n    /**\r\n     * Gets the list of emitted functions\r\n     */\r\n    public functions: { [key: string]: string } = {};\r\n    /**\r\n     * Gets the list of emitted extensions\r\n     */\r\n    public extensions: { [key: string]: string } = {};\r\n    /**\r\n     * Gets the list of emitted prePass outputs - if using the prepass\r\n     */\r\n    public prePassOutput: { [key: string]: string } = {};\r\n\r\n    /**\r\n     * Gets the target of the compilation state\r\n     */\r\n    public target: NodeMaterialBlockTargets;\r\n    /**\r\n     * Gets the list of emitted counters\r\n     */\r\n    public counters: { [key: string]: number } = {};\r\n\r\n    /**\r\n     * Shared data between multiple NodeMaterialBuildState instances\r\n     */\r\n    public sharedData: NodeMaterialBuildStateSharedData;\r\n\r\n    /** @internal */\r\n    public _terminalBlocks: Set<NodeMaterialBlock> = new Set();\r\n\r\n    /** @internal */\r\n    public _vertexState: NodeMaterialBuildState;\r\n\r\n    /** @internal */\r\n    public _attributeDeclaration = \"\";\r\n    /** @internal */\r\n    public _uniformDeclaration = \"\";\r\n    /** @internal */\r\n    public _constantDeclaration = \"\";\r\n    /** @internal */\r\n    public _samplerDeclaration = \"\";\r\n    /** @internal */\r\n    public _varyingTransfer = \"\";\r\n    /** @internal */\r\n    public _injectAtEnd = \"\";\r\n    /** @internal */\r\n    public _injectAtTop = \"\";\r\n    /** @internal */\r\n    public _customEntryHeader = \"\";\r\n    /** @internal */\r\n    private _repeatableContentAnchorIndex = 0;\r\n    /** @internal */\r\n    public _builtCompilationString = \"\";\r\n\r\n    /**\r\n     * Gets the emitted compilation strings\r\n     */\r\n    public compilationString = \"\";\r\n\r\n    /**\r\n     * Gets the current shader language to use\r\n     */\r\n    public get shaderLanguage() {\r\n        return this.sharedData.nodeMaterial.shaderLanguage;\r\n    }\r\n\r\n    /** Gets suffix to add behind type casting */\r\n    public get fSuffix() {\r\n        return this.shaderLanguage === ShaderLanguage.WGSL ? \"f\" : \"\";\r\n    }\r\n\r\n    /**\r\n     * Returns the processed, compiled shader code\r\n     * @param defines defines to use for the shader processing\r\n     * @returns the raw shader code used by the engine\r\n     */\r\n    public async getProcessedShaderAsync(defines: string): Promise<string> {\r\n        if (!this._builtCompilationString) {\r\n            Logger.Error(\"getProcessedShaderAsync: Shader not built yet.\");\r\n            return \"\";\r\n        }\r\n\r\n        const engine = this.sharedData.nodeMaterial.getScene().getEngine();\r\n        const options: _IProcessingOptions = {\r\n            defines: defines.split(\"\\n\"),\r\n            indexParameters: undefined,\r\n            isFragment: this.target === NodeMaterialBlockTargets.Fragment,\r\n            shouldUseHighPrecisionShader: engine._shouldUseHighPrecisionShader,\r\n            processor: engine._getShaderProcessor(this.shaderLanguage),\r\n            supportsUniformBuffers: engine.supportsUniformBuffers,\r\n            shadersRepository: EngineShaderStore.GetShadersRepository(this.shaderLanguage),\r\n            includesShadersStore: EngineShaderStore.GetIncludesShadersStore(this.shaderLanguage),\r\n            version: (engine.version * 100).toString(),\r\n            platformName: engine.shaderPlatformName,\r\n            processingContext: null,\r\n            isNDCHalfZRange: engine.isNDCHalfZRange,\r\n            useReverseDepthBuffer: engine.useReverseDepthBuffer,\r\n        };\r\n\r\n        // Export WebGL2 shaders with WebGL1 syntax for max compatibility\r\n        if (!engine.isWebGPU && engine.version > 1.0) {\r\n            options.processor = new WebGLShaderProcessor();\r\n        }\r\n\r\n        return await new Promise((resolve) => {\r\n            Process(\r\n                this._builtCompilationString,\r\n                options,\r\n                (migratedCode, _) => {\r\n                    resolve(migratedCode);\r\n                },\r\n                engine\r\n            );\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Finalize the compilation strings\r\n     * @param state defines the current compilation state\r\n     */\r\n    public finalize(state: NodeMaterialBuildState) {\r\n        const emitComments = state.sharedData.emitComments;\r\n        const isFragmentMode = this.target === NodeMaterialBlockTargets.Fragment;\r\n\r\n        let entryPointString = `\\n${emitComments ? \"//Entry point\\n\" : \"\"}`;\r\n        if (this._customEntryHeader) {\r\n            entryPointString += this._customEntryHeader;\r\n        } else if (this.shaderLanguage === ShaderLanguage.WGSL) {\r\n            if (isFragmentMode) {\r\n                entryPointString += `@fragment\\nfn main(input: FragmentInputs) -> FragmentOutputs {\\n${this.sharedData.varyingInitializationsFragment}`;\r\n            } else {\r\n                entryPointString += `@vertex\\nfn main(input: VertexInputs) -> FragmentInputs{\\n`;\r\n            }\r\n        } else {\r\n            entryPointString += `void main(void) {\\n`;\r\n        }\r\n\r\n        this.compilationString = entryPointString + this.compilationString;\r\n\r\n        if (this._constantDeclaration) {\r\n            this.compilationString = `\\n${emitComments ? \"//Constants\\n\" : \"\"}${this._constantDeclaration}\\n${this.compilationString}`;\r\n        }\r\n\r\n        let functionCode = \"\";\r\n        for (const functionName in this.functions) {\r\n            functionCode += this.functions[functionName] + `\\n`;\r\n        }\r\n        this.compilationString = `\\n${functionCode}\\n${this.compilationString}`;\r\n\r\n        if (!isFragmentMode && this._varyingTransfer) {\r\n            this.compilationString = `${this.compilationString}\\n${this._varyingTransfer}`;\r\n        }\r\n\r\n        if (this._injectAtEnd) {\r\n            this.compilationString = `${this.compilationString}\\n${this._injectAtEnd}`;\r\n        }\r\n\r\n        this.compilationString = `${this.compilationString}\\n}`;\r\n\r\n        if (this.sharedData.varyingDeclaration) {\r\n            this.compilationString = `\\n${emitComments ? \"//Varyings\\n\" : \"\"}${isFragmentMode ? this.sharedData.varyingDeclarationFragment : this.sharedData.varyingDeclaration}\\n${this.compilationString}`;\r\n        }\r\n\r\n        if (this._samplerDeclaration) {\r\n            this.compilationString = `\\n${emitComments ? \"//Samplers\\n\" : \"\"}${this._samplerDeclaration}\\n${this.compilationString}`;\r\n        }\r\n\r\n        if (this._uniformDeclaration) {\r\n            this.compilationString = `\\n${emitComments ? \"//Uniforms\\n\" : \"\"}${this._uniformDeclaration}\\n${this.compilationString}`;\r\n        }\r\n\r\n        if (this._attributeDeclaration && !isFragmentMode) {\r\n            this.compilationString = `\\n${emitComments ? \"//Attributes\\n\" : \"\"}${this._attributeDeclaration}\\n${this.compilationString}`;\r\n        }\r\n\r\n        if (this.shaderLanguage !== ShaderLanguage.WGSL) {\r\n            this.compilationString = \"precision highp float;\\n\" + this.compilationString;\r\n            this.compilationString = \"#if defined(WEBGL2) || defined(WEBGPU)\\nprecision highp sampler2DArray;\\n#endif\\n\" + this.compilationString;\r\n\r\n            if (isFragmentMode) {\r\n                this.compilationString =\r\n                    \"#if defined(PREPASS)\\r\\n#extension GL_EXT_draw_buffers : require\\r\\nlayout(location = 0) out highp vec4 glFragData[SCENE_MRT_COUNT];\\r\\nhighp vec4 gl_FragColor;\\r\\n#endif\\r\\n\" +\r\n                    this.compilationString;\r\n            }\r\n\r\n            for (const extensionName in this.extensions) {\r\n                const extension = this.extensions[extensionName];\r\n                this.compilationString = `\\n${extension}\\n${this.compilationString}`;\r\n            }\r\n        }\r\n\r\n        if (this._injectAtTop) {\r\n            this.compilationString = `${this._injectAtTop}\\n${this.compilationString}`;\r\n        }\r\n\r\n        this._builtCompilationString = this.compilationString;\r\n    }\r\n\r\n    /** @internal */\r\n    public get _repeatableContentAnchor(): string {\r\n        return `###___ANCHOR${this._repeatableContentAnchorIndex++}___###`;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getFreeVariableName(prefix: string): string {\r\n        prefix = this.sharedData.formatConfig.formatVariablename(prefix);\r\n\r\n        if (this.sharedData.variableNames[prefix] === undefined) {\r\n            this.sharedData.variableNames[prefix] = 0;\r\n\r\n            // Check reserved words\r\n            if (prefix === \"output\" || prefix === \"texture\") {\r\n                return prefix + this.sharedData.variableNames[prefix];\r\n            }\r\n\r\n            return prefix;\r\n        } else {\r\n            this.sharedData.variableNames[prefix]++;\r\n        }\r\n\r\n        return prefix + this.sharedData.variableNames[prefix];\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getFreeDefineName(prefix: string): string {\r\n        if (this.sharedData.defineNames[prefix] === undefined) {\r\n            this.sharedData.defineNames[prefix] = 0;\r\n        } else {\r\n            this.sharedData.defineNames[prefix]++;\r\n        }\r\n\r\n        return prefix + this.sharedData.defineNames[prefix];\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _excludeVariableName(name: string) {\r\n        this.sharedData.variableNames[name] = 0;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _emit2DSampler(name: string, define = \"\", force = false, annotation?: string, unsignedSampler?: boolean, precision?: string) {\r\n        if (this.samplers.indexOf(name) < 0 || force) {\r\n            if (define) {\r\n                this._samplerDeclaration += `#if ${define}\\n`;\r\n            }\r\n\r\n            if (this.shaderLanguage === ShaderLanguage.WGSL) {\r\n                const unsignedSamplerPrefix = unsignedSampler ? \"u\" : \"f\";\r\n                this._samplerDeclaration += `var ${name + Constants.AUTOSAMPLERSUFFIX}: sampler;\\n`;\r\n                this._samplerDeclaration += `var ${name}: texture_2d<${unsignedSamplerPrefix}32>;\\n`;\r\n            } else {\r\n                const unsignedSamplerPrefix = unsignedSampler ? \"u\" : \"\";\r\n                const precisionDecl = precision ?? \"\";\r\n                this._samplerDeclaration += `uniform ${precisionDecl} ${unsignedSamplerPrefix}sampler2D ${name}; ${annotation ? annotation : \"\"}\\n`;\r\n            }\r\n\r\n            if (define) {\r\n                this._samplerDeclaration += `#endif\\n`;\r\n            }\r\n\r\n            if (!force) {\r\n                this.samplers.push(name);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _emitCubeSampler(name: string, define = \"\", force = false) {\r\n        if (this.samplers.indexOf(name) < 0 || force) {\r\n            if (define) {\r\n                this._samplerDeclaration += `#if ${define}\\n`;\r\n            }\r\n\r\n            if (this.shaderLanguage === ShaderLanguage.WGSL) {\r\n                this._samplerDeclaration += `var ${name + Constants.AUTOSAMPLERSUFFIX}: sampler;\\n`;\r\n                this._samplerDeclaration += `var ${name}: texture_cube<f32>;\\n`;\r\n            } else {\r\n                this._samplerDeclaration += `uniform samplerCube ${name};\\n`;\r\n            }\r\n\r\n            if (define) {\r\n                this._samplerDeclaration += `#endif\\n`;\r\n            }\r\n\r\n            if (!force) {\r\n                this.samplers.push(name);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _emit2DArraySampler(name: string) {\r\n        if (this.samplers.indexOf(name) < 0) {\r\n            this._samplerDeclaration += `uniform sampler2DArray ${name};\\n`;\r\n            this.samplers.push(name);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getGLType(type: NodeMaterialBlockConnectionPointTypes): string {\r\n        switch (type) {\r\n            case NodeMaterialBlockConnectionPointTypes.Float:\r\n                return \"float\";\r\n            case NodeMaterialBlockConnectionPointTypes.Int:\r\n                return \"int\";\r\n            case NodeMaterialBlockConnectionPointTypes.Vector2:\r\n                return \"vec2\";\r\n            case NodeMaterialBlockConnectionPointTypes.Color3:\r\n            case NodeMaterialBlockConnectionPointTypes.Vector3:\r\n                return \"vec3\";\r\n            case NodeMaterialBlockConnectionPointTypes.Color4:\r\n            case NodeMaterialBlockConnectionPointTypes.Vector4:\r\n                return \"vec4\";\r\n            case NodeMaterialBlockConnectionPointTypes.Matrix:\r\n                return \"mat4\";\r\n        }\r\n\r\n        return \"\";\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getShaderType(type: NodeMaterialBlockConnectionPointTypes) {\r\n        const isWGSL = this.shaderLanguage === ShaderLanguage.WGSL;\r\n\r\n        switch (type) {\r\n            case NodeMaterialBlockConnectionPointTypes.Float:\r\n                return isWGSL ? \"f32\" : \"float\";\r\n            case NodeMaterialBlockConnectionPointTypes.Int:\r\n                return isWGSL ? \"i32\" : \"int\";\r\n            case NodeMaterialBlockConnectionPointTypes.Vector2:\r\n                return isWGSL ? \"vec2f\" : \"vec2\";\r\n            case NodeMaterialBlockConnectionPointTypes.Color3:\r\n            case NodeMaterialBlockConnectionPointTypes.Vector3:\r\n                return isWGSL ? \"vec3f\" : \"vec3\";\r\n            case NodeMaterialBlockConnectionPointTypes.Color4:\r\n            case NodeMaterialBlockConnectionPointTypes.Vector4:\r\n                return isWGSL ? \"vec4f\" : \"vec4\";\r\n            case NodeMaterialBlockConnectionPointTypes.Matrix:\r\n                return isWGSL ? \"mat4x4f\" : \"mat4\";\r\n        }\r\n\r\n        return \"\";\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _emitExtension(name: string, extension: string, define: string = \"\") {\r\n        if (this.extensions[name]) {\r\n            return;\r\n        }\r\n\r\n        if (define) {\r\n            extension = `#if ${define}\\n${extension}\\n#endif`;\r\n        }\r\n        this.extensions[name] = extension;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _emitFunction(name: string, code: string, comments: string) {\r\n        if (this.functions[name]) {\r\n            return;\r\n        }\r\n\r\n        if (this.sharedData.emitComments) {\r\n            code = comments + `\\n` + code;\r\n        }\r\n\r\n        this.functions[name] = code;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _emitCodeFromInclude(\r\n        includeName: string,\r\n        comments: string,\r\n        options?: {\r\n            replaceStrings?: { search: RegExp; replace: string }[];\r\n            repeatKey?: string;\r\n            substitutionVars?: string;\r\n        }\r\n    ) {\r\n        const store = EngineShaderStore.GetIncludesShadersStore(this.shaderLanguage);\r\n\r\n        if (options && options.repeatKey) {\r\n            return `#include<${includeName}>${options.substitutionVars ? \"(\" + options.substitutionVars + \")\" : \"\"}[0..${options.repeatKey}]\\n`;\r\n        }\r\n\r\n        let code = store[includeName] + \"\\n\";\r\n\r\n        if (this.sharedData.emitComments) {\r\n            code = comments + `\\n` + code;\r\n        }\r\n\r\n        if (!options) {\r\n            return code;\r\n        }\r\n\r\n        if (options.replaceStrings) {\r\n            for (let index = 0; index < options.replaceStrings.length; index++) {\r\n                const replaceString = options.replaceStrings[index];\r\n                code = code.replace(replaceString.search, replaceString.replace);\r\n            }\r\n        }\r\n\r\n        return code;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _emitFunctionFromInclude(\r\n        includeName: string,\r\n        comments: string,\r\n        options?: {\r\n            repeatKey?: string;\r\n            substitutionVars?: string;\r\n            removeAttributes?: boolean;\r\n            removeUniforms?: boolean;\r\n            removeVaryings?: boolean;\r\n            removeIfDef?: boolean;\r\n            replaceStrings?: { search: RegExp; replace: string }[];\r\n        },\r\n        storeKey: string = \"\"\r\n    ) {\r\n        const key = includeName + storeKey;\r\n        if (this.functions[key]) {\r\n            return;\r\n        }\r\n        const store = EngineShaderStore.GetIncludesShadersStore(this.shaderLanguage);\r\n\r\n        if (!options || (!options.removeAttributes && !options.removeUniforms && !options.removeVaryings && !options.removeIfDef && !options.replaceStrings)) {\r\n            if (options && options.repeatKey) {\r\n                this.functions[key] = `#include<${includeName}>${options.substitutionVars ? \"(\" + options.substitutionVars + \")\" : \"\"}[0..${options.repeatKey}]\\n`;\r\n            } else {\r\n                this.functions[key] = `#include<${includeName}>${options?.substitutionVars ? \"(\" + options?.substitutionVars + \")\" : \"\"}\\n`;\r\n            }\r\n\r\n            if (this.sharedData.emitComments) {\r\n                this.functions[key] = comments + `\\n` + this.functions[key];\r\n            }\r\n\r\n            return;\r\n        }\r\n\r\n        this.functions[key] = store[includeName];\r\n\r\n        if (this.sharedData.emitComments) {\r\n            this.functions[key] = comments + `\\n` + this.functions[key];\r\n        }\r\n\r\n        if (options.removeIfDef) {\r\n            this.functions[key] = this.functions[key].replace(/^\\s*?#ifdef.+$/gm, \"\");\r\n            this.functions[key] = this.functions[key].replace(/^\\s*?#endif.*$/gm, \"\");\r\n            this.functions[key] = this.functions[key].replace(/^\\s*?#else.*$/gm, \"\");\r\n            this.functions[key] = this.functions[key].replace(/^\\s*?#elif.*$/gm, \"\");\r\n        }\r\n\r\n        if (options.removeAttributes) {\r\n            this.functions[key] = this.functions[key].replace(/\\s*?attribute .+?;/g, \"\\n\");\r\n        }\r\n\r\n        if (options.removeUniforms) {\r\n            this.functions[key] = this.functions[key].replace(/\\s*?uniform .*?;/g, \"\\n\");\r\n        }\r\n\r\n        if (options.removeVaryings) {\r\n            this.functions[key] = this.functions[key].replace(/\\s*?(varying|in) .+?;/g, \"\\n\");\r\n        }\r\n\r\n        if (options.replaceStrings) {\r\n            for (let index = 0; index < options.replaceStrings.length; index++) {\r\n                const replaceString = options.replaceStrings[index];\r\n                this.functions[key] = this.functions[key].replace(replaceString.search, replaceString.replace);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _registerTempVariable(name: string) {\r\n        if (this.sharedData.temps.indexOf(name) !== -1) {\r\n            return false;\r\n        }\r\n\r\n        this.sharedData.temps.push(name);\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _emitVaryingFromString(name: string, type: NodeMaterialBlockConnectionPointTypes, define: string = \"\", notDefine = false) {\r\n        if (this.sharedData.varyings.indexOf(name) !== -1) {\r\n            return false;\r\n        }\r\n\r\n        this.sharedData.varyings.push(name);\r\n\r\n        const shaderType = this._getShaderType(type);\r\n\r\n        const emitCode = (forFragment = false) => {\r\n            let code = \"\";\r\n            if (define) {\r\n                if (define.startsWith(\"defined(\")) {\r\n                    code += `#if ${define}\\n`;\r\n                } else {\r\n                    code += `${notDefine ? \"#ifndef\" : \"#ifdef\"} ${define}\\n`;\r\n                }\r\n            }\r\n            if (this.shaderLanguage === ShaderLanguage.WGSL) {\r\n                switch (shaderType) {\r\n                    case \"mat4x4f\":\r\n                        // We can't pass a matrix as a varying in WGSL, so we need to split it into 4 vectors\r\n                        code += `varying ${name}_r0: vec4f;\\n`;\r\n                        code += `varying ${name}_r1: vec4f;\\n`;\r\n                        code += `varying ${name}_r2: vec4f;\\n`;\r\n                        code += `varying ${name}_r3: vec4f;\\n`;\r\n\r\n                        if (forFragment) {\r\n                            code += `var<private> ${name}: mat4x4f;\\n`;\r\n                            this.sharedData.varyingInitializationsFragment += `${name} = mat4x4f(fragmentInputs.${name}_r0, fragmentInputs.${name}_r1, fragmentInputs.${name}_r2, fragmentInputs.${name}_r3);\\n`;\r\n                        }\r\n                        break;\r\n                    default:\r\n                        code += `varying ${name}: ${shaderType};\\n`;\r\n                        break;\r\n                }\r\n            } else {\r\n                code += `varying ${shaderType} ${name};\\n`;\r\n            }\r\n            if (define) {\r\n                code += `#endif\\n`;\r\n            }\r\n            return code;\r\n        };\r\n\r\n        if (this.shaderLanguage === ShaderLanguage.WGSL) {\r\n            this.sharedData.varyingDeclaration += emitCode(false);\r\n            this.sharedData.varyingDeclarationFragment += emitCode(true);\r\n        } else {\r\n            const code = emitCode();\r\n            this.sharedData.varyingDeclaration += code;\r\n            this.sharedData.varyingDeclarationFragment += code;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getVaryingName(name: string): string {\r\n        if (this.shaderLanguage === ShaderLanguage.WGSL) {\r\n            return (this.target !== NodeMaterialBlockTargets.Fragment ? \"vertexOutputs.\" : \"fragmentInputs.\") + name;\r\n        }\r\n\r\n        return name;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _emitUniformFromString(name: string, type: NodeMaterialBlockConnectionPointTypes, define: string = \"\", notDefine = false) {\r\n        if (this.uniforms.indexOf(name) !== -1) {\r\n            return;\r\n        }\r\n\r\n        this.uniforms.push(name);\r\n\r\n        if (define) {\r\n            if (define.startsWith(\"defined(\")) {\r\n                this._uniformDeclaration += `#if ${define}\\n`;\r\n            } else {\r\n                this._uniformDeclaration += `${notDefine ? \"#ifndef\" : \"#ifdef\"} ${define}\\n`;\r\n            }\r\n        }\r\n        if (this.sharedData.formatConfig.getUniformAnnotation) {\r\n            this._uniformDeclaration += this.sharedData.formatConfig.getUniformAnnotation(name);\r\n        }\r\n        const shaderType = this._getShaderType(type);\r\n        if (this.shaderLanguage === ShaderLanguage.WGSL) {\r\n            this._uniformDeclaration += `uniform ${name}: ${shaderType};\\n`;\r\n        } else {\r\n            this._uniformDeclaration += `uniform ${shaderType} ${name};\\n`;\r\n        }\r\n        if (define) {\r\n            this._uniformDeclaration += `#endif\\n`;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _generateTernary(trueStatement: string, falseStatement: string, condition: string) {\r\n        if (this.shaderLanguage === ShaderLanguage.WGSL) {\r\n            return `select(${falseStatement}, ${trueStatement}, ${condition})`;\r\n        }\r\n\r\n        return `(${condition}) ? ${trueStatement} : ${falseStatement}`;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _emitFloat(value: number) {\r\n        if (value.toString() === value.toFixed(0)) {\r\n            return `${value}.0`;\r\n        }\r\n\r\n        return value.toString();\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _declareOutput(output: NodeMaterialConnectionPoint, isConst?: boolean): string {\r\n        return this._declareLocalVar(output.associatedVariableName, output.type, isConst);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _declareLocalVar(name: string, type: NodeMaterialBlockConnectionPointTypes, isConst?: boolean): string {\r\n        if (this.shaderLanguage === ShaderLanguage.WGSL) {\r\n            return `${isConst ? \"const\" : \"var\"} ${name}: ${this._getShaderType(type)}`;\r\n        } else {\r\n            return `${isConst ? \"const \" : \"\"}${this._getShaderType(type)} ${name}`;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _samplerCubeFunc() {\r\n        if (this.shaderLanguage === ShaderLanguage.WGSL) {\r\n            return \"textureSample\";\r\n        }\r\n        return \"textureCube\";\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _samplerFunc() {\r\n        if (this.shaderLanguage === ShaderLanguage.WGSL) {\r\n            return \"textureSample\";\r\n        }\r\n        return \"texture2D\";\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _samplerLODFunc() {\r\n        if (this.shaderLanguage === ShaderLanguage.WGSL) {\r\n            return \"textureSampleLevel\";\r\n        }\r\n        return \"texture2DLodEXT\";\r\n    }\r\n\r\n    public _toLinearSpace(output: NodeMaterialConnectionPoint) {\r\n        if (this.shaderLanguage === ShaderLanguage.WGSL) {\r\n            if (output.type === NodeMaterialBlockConnectionPointTypes.Color3 || output.type === NodeMaterialBlockConnectionPointTypes.Vector3) {\r\n                return `toLinearSpaceVec3(${output.associatedVariableName})`;\r\n            }\r\n\r\n            return `toLinearSpace(${output.associatedVariableName})`;\r\n        }\r\n        return `toLinearSpace(${output.associatedVariableName})`;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _generateTextureSample(uv: string, samplerName: string) {\r\n        if (this.shaderLanguage === ShaderLanguage.WGSL) {\r\n            return `${this._samplerFunc()}(${samplerName},${samplerName + Constants.AUTOSAMPLERSUFFIX}, ${uv})`;\r\n        }\r\n        return `${this._samplerFunc()}(${samplerName}, ${uv})`;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _generateTextureSampleLOD(uv: string, samplerName: string, lod: string) {\r\n        if (this.shaderLanguage === ShaderLanguage.WGSL) {\r\n            return `${this._samplerLODFunc()}(${samplerName},${samplerName + Constants.AUTOSAMPLERSUFFIX}, ${uv}, ${lod})`;\r\n        }\r\n        return `${this._samplerLODFunc()}(${samplerName}, ${uv}, ${lod})`;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _generateTextureSampleCube(uv: string, samplerName: string) {\r\n        if (this.shaderLanguage === ShaderLanguage.WGSL) {\r\n            return `${this._samplerCubeFunc()}(${samplerName},${samplerName + Constants.AUTOSAMPLERSUFFIX}, ${uv})`;\r\n        }\r\n        return `${this._samplerCubeFunc()}(${samplerName}, ${uv})`;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _generateTextureSampleCubeLOD(uv: string, samplerName: string, lod: string) {\r\n        if (this.shaderLanguage === ShaderLanguage.WGSL) {\r\n            return `${this._samplerCubeFunc()}(${samplerName},${samplerName + Constants.AUTOSAMPLERSUFFIX}, ${uv}, ${lod})`;\r\n        }\r\n        return `${this._samplerCubeFunc()}(${samplerName}, ${uv}, ${lod})`;\r\n    }\r\n\r\n    private _convertVariableDeclarationToWGSL(type: string, dest: string, source: string): string {\r\n        return source.replace(new RegExp(`(${type})\\\\s+(\\\\w+)`, \"g\"), `var $2: ${dest}`);\r\n    }\r\n\r\n    private _convertVariableConstructorsToWGSL(type: string, dest: string, source: string): string {\r\n        return source.replace(new RegExp(`(${type})\\\\(`, \"g\"), ` ${dest}(`);\r\n    }\r\n\r\n    private _convertOutParametersToWGSL(source: string): string {\r\n        return source.replace(new RegExp(`out\\\\s+var\\\\s+(\\\\w+)\\\\s*:\\\\s*(\\\\w+)`, \"g\"), `$1: ptr<function, $2>`);\r\n    }\r\n\r\n    private _convertTernaryOperandsToWGSL(source: string): string {\r\n        return source.replace(new RegExp(`\\\\[(.*?)\\\\?(.*?):(.*)\\\\]`, \"g\"), (match, condition, trueCase, falseCase) => `select(${falseCase}, ${trueCase}, ${condition})`);\r\n    }\r\n\r\n    private _convertModOperatorsToWGSL(source: string): string {\r\n        return source.replace(new RegExp(`mod\\\\((.+?),\\\\s*(.+?)\\\\)`, \"g\"), (match, left, right) => `((${left})%(${right}))`);\r\n    }\r\n\r\n    private _convertConstToWGSL(source: string): string {\r\n        return source.replace(new RegExp(`const var`, \"g\"), `const`);\r\n    }\r\n\r\n    private _convertInnerFunctionsToWGSL(source: string): string {\r\n        return source.replace(new RegExp(`inversesqrt`, \"g\"), `inverseSqrt`);\r\n    }\r\n\r\n    private _convertFunctionsToWGSL(source: string): string {\r\n        const regex = /var\\s+(\\w+)\\s*:\\s*(\\w+)\\((.*)\\)/g;\r\n\r\n        let match: RegExpMatchArray | null;\r\n        while ((match = regex.exec(source)) !== null) {\r\n            const funcName = match[1];\r\n            const funcType = match[2];\r\n            const params = match[3]; // All parameters as a single string\r\n\r\n            // Processing the parameters to match 'name: type' format\r\n            const formattedParams = params.replace(/var\\s/g, \"\");\r\n\r\n            // Constructing the final output string\r\n            source = source.replace(match[0], `fn ${funcName}(${formattedParams}) -> ${funcType}`);\r\n        }\r\n        return source;\r\n    }\r\n\r\n    public _babylonSLtoWGSL(code: string) {\r\n        // variable declarations\r\n        code = this._convertVariableDeclarationToWGSL(\"void\", \"voidnull\", code);\r\n        code = this._convertVariableDeclarationToWGSL(\"bool\", \"bool\", code);\r\n        code = this._convertVariableDeclarationToWGSL(\"int\", \"i32\", code);\r\n        code = this._convertVariableDeclarationToWGSL(\"uint\", \"u32\", code);\r\n        code = this._convertVariableDeclarationToWGSL(\"float\", \"f32\", code);\r\n        code = this._convertVariableDeclarationToWGSL(\"vec2\", \"vec2f\", code);\r\n        code = this._convertVariableDeclarationToWGSL(\"vec3\", \"vec3f\", code);\r\n        code = this._convertVariableDeclarationToWGSL(\"vec4\", \"vec4f\", code);\r\n        code = this._convertVariableDeclarationToWGSL(\"mat2\", \"mat2x2f\", code);\r\n        code = this._convertVariableDeclarationToWGSL(\"mat3\", \"mat3x3f\", code);\r\n        code = this._convertVariableDeclarationToWGSL(\"mat4\", \"mat4x4f\", code);\r\n\r\n        // Type constructors\r\n        code = this._convertVariableConstructorsToWGSL(\"float\", \"f32\", code);\r\n        code = this._convertVariableConstructorsToWGSL(\"vec2\", \"vec2f\", code);\r\n        code = this._convertVariableConstructorsToWGSL(\"vec3\", \"vec3f\", code);\r\n        code = this._convertVariableConstructorsToWGSL(\"vec4\", \"vec4f\", code);\r\n        code = this._convertVariableConstructorsToWGSL(\"mat2\", \"mat2x2f\", code);\r\n        code = this._convertVariableConstructorsToWGSL(\"mat3\", \"mat3x3f\", code);\r\n        code = this._convertVariableConstructorsToWGSL(\"mat4\", \"mat4x4f\", code);\r\n\r\n        // Ternary operands\r\n        code = this._convertTernaryOperandsToWGSL(code);\r\n\r\n        // Mod operators\r\n        code = this._convertModOperatorsToWGSL(code);\r\n\r\n        // Const\r\n        code = this._convertConstToWGSL(code);\r\n\r\n        // Inner functions\r\n        code = this._convertInnerFunctionsToWGSL(code);\r\n\r\n        // Out paramters\r\n        code = this._convertOutParametersToWGSL(code);\r\n        code = code.replace(/\\[\\*\\]/g, \"*\");\r\n\r\n        // Functions\r\n        code = this._convertFunctionsToWGSL(code);\r\n\r\n        // Remove voidnull\r\n        code = code.replace(/\\s->\\svoidnull/g, \"\");\r\n\r\n        // Derivatives\r\n        code = code.replace(/dFdx/g, \"dpdx\");\r\n        code = code.replace(/dFdy/g, \"dpdy\");\r\n\r\n        return code;\r\n    }\r\n\r\n    private _convertTernaryOperandsToGLSL(source: string): string {\r\n        return source.replace(new RegExp(`\\\\[(.+?)\\\\?(.+?):(.+)\\\\]`, \"g\"), (match, condition, trueCase, falseCase) => `${condition} ? ${trueCase} : ${falseCase}`);\r\n    }\r\n\r\n    public _babylonSLtoGLSL(code: string) {\r\n        /** Remove BSL specifics */\r\n        code = code.replace(/\\[\\*\\]/g, \"\");\r\n        code = this._convertTernaryOperandsToGLSL(code);\r\n\r\n        return code;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,qCAAqC,EAAE,MAAM,+CAA+C,CAAC;AACtG,OAAO,EAAE,wBAAwB,EAAE,MAAM,kCAAkC,CAAC;AAI5E,OAAO,EAAE,WAAW,IAAI,iBAAiB,EAAE,MAAM,2BAA2B,CAAC;AAG7E,OAAO,EAAE,OAAO,EAAE,oDAAgD;AAElE,OAAO,EAAE,oBAAoB,EAAE,qDAAiD;AAChF,OAAO,EAAE,MAAM,EAAE,6BAAyB;;;;;;;AAKpC,MAAO,sBAAsB;IA8E/B;;OAEG,CACH,IAAW,cAAc,GAAA;QACrB,OAAO,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,cAAc,CAAC;IACvD,CAAC;IAED,2CAAA,EAA6C,CAC7C,IAAW,OAAO,GAAA;QACd,OAAO,IAAI,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,CAAC,CAAC,EAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;IAClE,CAAC;IAED;;;;OAIG,CACI,KAAK,CAAC,uBAAuB,CAAC,OAAe,EAAA;QAChD,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC;qKAChC,SAAM,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAC;YAC/D,OAAO,EAAE,CAAC;QACd,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC;QACnE,MAAM,OAAO,GAAwB;YACjC,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;YAC5B,eAAe,EAAE,SAAS;YAC1B,UAAU,EAAE,IAAI,CAAC,MAAM,sMAAK,2BAAwB,CAAC,QAAQ;YAC7D,4BAA4B,EAAE,MAAM,CAAC,6BAA6B;YAClE,SAAS,EAAE,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,cAAc,CAAC;YAC1D,sBAAsB,EAAE,MAAM,CAAC,sBAAsB;YACrD,iBAAiB,mKAAE,cAAiB,CAAC,oBAAoB,CAAC,IAAI,CAAC,cAAc,CAAC;YAC9E,oBAAoB,mKAAE,cAAiB,CAAC,uBAAuB,CAAC,IAAI,CAAC,cAAc,CAAC;YACpF,OAAO,EAAE,CAAC,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC,CAAC,QAAQ,EAAE;YAC1C,YAAY,EAAE,MAAM,CAAC,kBAAkB;YACvC,iBAAiB,EAAE,IAAI;YACvB,eAAe,EAAE,MAAM,CAAC,eAAe;YACvC,qBAAqB,EAAE,MAAM,CAAC,qBAAqB;SACtD,CAAC;QAEF,iEAAiE;QACjE,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC;YAC3C,OAAO,CAAC,SAAS,GAAG,wLAAI,uBAAoB,EAAE,CAAC;QACnD,CAAC;QAED,OAAO,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;mMACjC,UAAO,AAAP,EACI,IAAI,CAAC,uBAAuB,EAC5B,OAAO,EACP,CAAC,YAAY,EAAE,CAAC,EAAE,EAAE;gBAChB,OAAO,CAAC,YAAY,CAAC,CAAC;YAC1B,CAAC,EACD,MAAM,CACT,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG,CACI,QAAQ,CAAC,KAA6B,EAAA;QACzC,MAAM,YAAY,GAAG,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC;QACnD,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,sMAAK,2BAAwB,CAAC,QAAQ,CAAC;QAEzE,IAAI,gBAAgB,GAAG,KAA0C,CAAE,CAAC,KAAxC,YAAY,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE;QACjE,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,gBAAgB,IAAI,IAAI,CAAC,kBAAkB,CAAC;QAChD,CAAC,MAAM,IAAI,IAAI,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;YACrD,IAAI,cAAc,EAAE,CAAC;gBACjB,gBAAgB,IAAI,mEAAiH,CAAE,CAAC,KAAjD,IAAI,CAAC,UAAU,CAAC,8BAA8B;YACzI,CAAC,MAAM,CAAC;gBACJ,gBAAgB,IAAI,2DAA4D,CAAC;YACrF,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,gBAAgB,IAAI,oBAAqB,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAEnE,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,IAAI,CAAC,iBAAiB,GAAG,KAA2C,OAAtC,YAAY,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,aAAO,CAAC,oBAAoB,EAAA,MAA2B,CAAE,CAAC,KAAzB,IAAI,CAAC,iBAAiB;QAC5H,CAAC;QAED,IAAI,YAAY,GAAG,EAAE,CAAC;QACtB,IAAK,MAAM,YAAY,IAAI,IAAI,CAAC,SAAS,CAAE,CAAC;YACxC,YAAY,IAAI,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,GAAG,GAAI,CAAC;QACxD,CAAC;QACD,IAAI,CAAC,iBAAiB,GAAG,YAAK,YAAY,EAAA,MAA2B,CAAE,CAAC,KAAzB,IAAI,CAAC,iBAAiB;QAErE,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3C,IAAI,CAAC,iBAAiB,GAAG,UAAG,IAAI,CAAC,iBAAiB,EAAA,MAA0B,CAAE,CAAC,KAAxB,IAAI,CAAC,gBAAgB;QAChF,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,iBAAiB,GAAG,UAAG,IAAI,CAAC,iBAAiB,EAAA,MAAsB,CAAE,CAAC,KAApB,IAAI,CAAC,YAAY;QAC5E,CAAC;QAED,IAAI,CAAC,iBAAiB,GAAG,GAAyB,OAAtB,IAAI,CAAC,iBAAiB,EAAA,IAAK,CAAC;QAExD,IAAI,IAAI,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAC;YACrC,IAAI,CAAC,iBAAiB,GAAG,YAAK,YAAY,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,EAAwG,OAArG,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,0BAA0B,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,kBAAkB,EAAA,MAA2B,CAAE,CAAC,SAArB,CAAC,iBAAiB;QAClM,CAAC;QAED,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,IAAI,CAAC,iBAAiB,GAAG,YAAK,YAAY,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,EAAgC,OAA7B,IAAI,CAAC,mBAAmB,EAAA,MAA2B,CAAE,CAAC,SAArB,CAAC,iBAAiB;QAC1H,CAAC;QAED,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,IAAI,CAAC,iBAAiB,GAAG,YAAK,YAAY,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,SAAG,IAAI,CAAC,mBAAmB,EAAA,MAA2B,CAAE,CAAC,KAAzB,IAAI,CAAC,iBAAiB;QAC1H,CAAC;QAED,IAAI,IAAI,CAAC,qBAAqB,IAAI,CAAC,cAAc,EAAE,CAAC;YAChD,IAAI,CAAC,iBAAiB,GAAG,YAAK,YAAY,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,SAAG,IAAI,CAAC,qBAAqB,EAAA,MAA2B,CAAE,CAAC,KAAzB,IAAI,CAAC,iBAAiB;QAC9H,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;YAC9C,IAAI,CAAC,iBAAiB,GAAG,0BAA0B,GAAG,IAAI,CAAC,iBAAiB,CAAC;YAC7E,IAAI,CAAC,iBAAiB,GAAG,mFAAmF,GAAG,IAAI,CAAC,iBAAiB,CAAC;YAEtI,IAAI,cAAc,EAAE,CAAC;gBACjB,IAAI,CAAC,iBAAiB,GAClB,gLAAgL,GAChL,IAAI,CAAC,iBAAiB,CAAC;YAC/B,CAAC;YAED,IAAK,MAAM,aAAa,IAAI,IAAI,CAAC,UAAU,CAAE,CAAC;gBAC1C,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;gBACjD,IAAI,CAAC,iBAAiB,GAAG,YAAK,SAAS,EAAA,MAA2B,CAAE,CAAC,KAAzB,IAAI,CAAC,iBAAiB;YACtE,CAAC;QACL,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,iBAAiB,GAAG,UAAG,IAAI,CAAC,YAAY,EAAA,MAA2B,CAAE,CAAC,KAAzB,IAAI,CAAC,iBAAiB;QAC5E,CAAC;QAED,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,iBAAiB,CAAC;IAC1D,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,wBAAwB,GAAA;QAC/B,OAAO,eAAmD,OAApC,IAAI,CAAC,6BAA6B,EAAE,EAAA,OAAQ,CAAC;IACvE,CAAC;IAED;;OAEG,CACI,oBAAoB,CAAC,MAAc,EAAA;QACtC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QAEjE,IAAI,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,SAAS,EAAE,CAAC;YACtD,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAE1C,uBAAuB;YACvB,IAAI,MAAM,KAAK,QAAQ,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;gBAC9C,OAAO,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;YAC1D,CAAC;YAED,OAAO,MAAM,CAAC;QAClB,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC;QAC5C,CAAC;QAED,OAAO,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG,CACI,kBAAkB,CAAC,MAAc,EAAA;QACpC,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,SAAS,EAAE,CAAC;YACpD,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAC5C,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,CAAC;QAC1C,CAAC;QAED,OAAO,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG,CACI,oBAAoB,CAAC,IAAY,EAAA;QACpC,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG,CACI,cAAc,CAAC,IAAY,EAAgG;qBAA9F,MAAM,2DAAG,EAAE,UAAE,KAAK,4DAAG,KAAK,EAAE,UAAmB,iDAAE,eAAyB,iDAAE,SAAkB;QAC9H,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,EAAE,CAAC;YAC3C,IAAI,MAAM,EAAE,CAAC;gBACT,IAAI,CAAC,mBAAmB,IAAI,OAAa,OAAN,MAAM,EAAA,GAAI,CAAC;YAClD,CAAC;YAED,IAAI,IAAI,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;gBAC9C,MAAM,qBAAqB,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;gBAC1D,IAAI,CAAC,mBAAmB,IAAI,cAAO,IAAI,GAAG,QAAS,GAAC,iBAAiB,cAAc,CAAC;gBACpF,IAAI,CAAC,mBAAmB,IAAI,cAAO,IAAI,EAAA,iBAAqC,OAArB,AAA6B,CAAC,oBAAT,EAAA;YAChF,CAAC,MAAM,CAAC;gBACJ,MAAM,qBAAqB,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;gBACzD,MAAM,aAAa,gDAAG,SAAS,GAAI,EAAE,CAAC;gBACtC,IAAI,CAAC,mBAAmB,IAAI,kBAAW,aAAa,EAAA,YAAI,qBAAqB,EAAA,qBAAa,IAAI,EAAA,MAAiC,OAA5B,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,EAAA,GAAI,CAAC;YACxI,CAAC;YAED,IAAI,MAAM,EAAE,CAAC;gBACT,IAAI,CAAC,mBAAmB,IAAI,SAAU,CAAC;YAC3C,CAAC;YAED,IAAI,CAAC,KAAK,EAAE,CAAC;gBACT,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7B,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG,CACI,gBAAgB,CAAC,IAAY,EAA4B;qBAA1B,MAAM,2DAAG,EAAE,UAAE,KAAK,4DAAG,KAAK;QAC5D,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,EAAE,CAAC;YAC3C,IAAI,MAAM,EAAE,CAAC;gBACT,IAAI,CAAC,mBAAmB,IAAI,OAAa,OAAN,MAAM,EAAA,GAAI,CAAC;YAClD,CAAC;YAED,IAAI,IAAI,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;gBAC9C,IAAI,CAAC,mBAAmB,IAAI,cAAO,IAAI,GAAG,QAAS,GAAC,iBAAiB,cAAc,CAAC;gBACpF,IAAI,CAAC,mBAAmB,IAAI,OAAW,OAAJ,IAAI,EAAA,uBAAwB,CAAC;YACpE,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,mBAAmB,IAAI,uBAA2B,OAAJ,IAAI,EAAA,IAAK,CAAC;YACjE,CAAC;YAED,IAAI,MAAM,EAAE,CAAC;gBACT,IAAI,CAAC,mBAAmB,IAAI,SAAU,CAAC;YAC3C,CAAC;YAED,IAAI,CAAC,KAAK,EAAE,CAAC;gBACT,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7B,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG,CACI,mBAAmB,CAAC,IAAY,EAAA;QACnC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;YAClC,IAAI,CAAC,mBAAmB,IAAI,0BAA8B,OAAJ,IAAI,EAAA,IAAK,CAAC;YAChE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7B,CAAC;IACL,CAAC;IAED;;OAEG,CACI,UAAU,CAAC,IAA2C,EAAA;QACzD,OAAQ,IAAI,EAAE,CAAC;YACX,mNAAK,wCAAqC,CAAC,KAAK;gBAC5C,OAAO,OAAO,CAAC;YACnB,mNAAK,wCAAqC,CAAC,GAAG;gBAC1C,OAAO,KAAK,CAAC;YACjB,kNAAK,yCAAqC,CAAC,OAAO;gBAC9C,OAAO,MAAM,CAAC;YAClB,mNAAK,wCAAqC,CAAC,MAAM,CAAC;YAClD,mNAAK,wCAAqC,CAAC,OAAO;gBAC9C,OAAO,MAAM,CAAC;YAClB,mNAAK,wCAAqC,CAAC,MAAM,CAAC;YAClD,mNAAK,wCAAqC,CAAC,OAAO;gBAC9C,OAAO,MAAM,CAAC;YAClB,mNAAK,wCAAqC,CAAC,MAAM;gBAC7C,OAAO,MAAM,CAAC;QACtB,CAAC;QAED,OAAO,EAAE,CAAC;IACd,CAAC;IAED;;OAEG,CACI,cAAc,CAAC,IAA2C,EAAA;QAC7D,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,CAAC;QAE3D,OAAQ,IAAI,EAAE,CAAC;YACX,mNAAK,wCAAqC,CAAC,KAAK;gBAC5C,OAAO,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC;YACpC,mNAAK,wCAAqC,CAAC,GAAG;gBAC1C,OAAO,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;YAClC,mNAAK,wCAAqC,CAAC,OAAO;gBAC9C,OAAO,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;YACrC,mNAAK,wCAAqC,CAAC,MAAM,CAAC;YAClD,mNAAK,wCAAqC,CAAC,OAAO;gBAC9C,OAAO,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;YACrC,mNAAK,wCAAqC,CAAC,MAAM,CAAC;YAClD,mNAAK,wCAAqC,CAAC,OAAO;gBAC9C,OAAO,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;YACrC,mNAAK,wCAAqC,CAAC,MAAM;gBAC7C,OAAO,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC;QAC3C,CAAC;QAED,OAAO,EAAE,CAAC;IACd,CAAC;IAED;;OAEG,CACI,cAAc,CAAC,IAAY,EAAE,SAAiB,EAAqB;qBAAnB,iEAAiB,EAAE;QACtE,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YACxB,OAAO;QACX,CAAC;QAED,IAAI,MAAM,EAAE,CAAC;YACT,SAAS,GAAG,cAAO,MAAM,EAAA,MAAc,OAAT,SAAS,EAAA,SAAU,CAAC;QACtD,CAAC;QACD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC;IACtC,CAAC;IAED;;OAEG,CACI,aAAa,CAAC,IAAY,EAAE,IAAY,EAAE,QAAgB,EAAA;QAC7D,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;YACvB,OAAO;QACX,CAAC;QAED,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;YAC/B,IAAI,GAAG,QAAQ,GAAG,GAAI,IAAG,IAAI,CAAC;QAClC,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;IAChC,CAAC;IAED;;OAEG,CACI,oBAAoB,CACvB,WAAmB,EACnB,QAAgB,EAChB,OAIC,EAAA;QAED,MAAM,KAAK,oKAAG,cAAiB,CAAC,uBAAuB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAE7E,IAAI,OAAO,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YAC/B,OAAO,mBAAY,WAAW,EAAA,KAA+E,OAA3E,AAAkF,OAA3E,CAAC,gBAAgB,CAAC,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,gBAAgB,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,EAAA,QAAwB,IAAK,CAAC,UAAf,SAAS,EAAA;QAClI,CAAC;QAED,IAAI,IAAI,GAAG,KAAK,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;QAErC,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;YAC/B,IAAI,GAAG,QAAQ,GAAG,GAAI,IAAG,IAAI,CAAC;QAClC,CAAC;QAED,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;YACzB,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,OAAO,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBACjE,MAAM,aAAa,GAAG,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBACpD,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,EAAE,aAAa,CAAC,OAAO,CAAC,CAAC;YACrE,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG,CACI,wBAAwB,CAC3B,WAAmB,EACnB,QAAgB,EAChB,OAQC,EACoB;uBAArB,iEAAmB,EAAE;QAErB,MAAM,GAAG,GAAG,WAAW,GAAG,QAAQ,CAAC;QACnC,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC;YACtB,OAAO;QACX,CAAC;QACD,MAAM,KAAK,oKAAG,cAAiB,CAAC,uBAAuB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAE7E,IAAI,CAAC,OAAO,IAAI,AAAC,CAAC,OAAO,CAAC,gBAAgB,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAE,CAAC;YACnJ,IAAI,OAAO,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;gBAC/B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,mBAAY,WAAW,EAAA,YAAI,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,gBAAgB,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,EAAA,QAAwB,OAAjB,OAAO,CAAC,SAAS,EAAA,IAAK,CAAC;YACvJ,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,mBAAY,WAAW,EAAA,KAA0E,GAAI,CAAC,sDAA3E,OAAO,CAAE,gBAAgB,CAAC,CAAC,EAAC,GAAG,sDAAG,OAAO,CAAE,gBAAgB,IAAG,GAAG,CAAC,CAAC,CAAC,EAAE,EAAA;YAC3H,CAAC;YAED,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;gBAC/B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,QAAQ,GAAG,GAAI,IAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YAChE,CAAC;YAED,OAAO;QACX,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC,CAAC;QAEzC,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;YAC/B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,QAAQ,GAAG,GAAI,IAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YACtB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAC;YAC1E,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAC;YAC1E,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC;YACzE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC;QAC7E,CAAC;QAED,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAC3B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC;QACnF,CAAC;QAED,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;YACzB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;QACjF,CAAC;QAED,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;YACzB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,wBAAwB,EAAE,IAAI,CAAC,CAAC;QACtF,CAAC;QAED,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;YACzB,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,OAAO,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBACjE,MAAM,aAAa,GAAG,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBACpD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,EAAE,aAAa,CAAC,OAAO,CAAC,CAAC;YACnG,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG,CACI,qBAAqB,CAAC,IAAY,EAAA;QACrC,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YAC7C,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG,CACI,sBAAsB,CAAC,IAAY,EAAE,IAA2C,EAAwC;qBAAtC,iEAAiB,EAAE,cAAE,SAAS,wDAAG,KAAK;;QAC3H,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YAChD,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEpC,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAE7C,MAAM,QAAQ,GAAG;gBAAC,WAAW,oEAAG,KAAK,EAAE,EAAE;YACrC,IAAI,IAAI,GAAG,EAAE,CAAC;YACd,IAAI,MAAM,EAAE,CAAC;gBACT,IAAI,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;oBAChC,IAAI,IAAI,OAAa,OAAN,MAAM,EAAA,GAAI,CAAC;gBAC9B,CAAC,MAAM,CAAC;oBACJ,IAAI,IAAI,UAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,EAAA,KAAU,OAAN,MAAM,EAAA,GAAI,CAAC;gBAC9D,CAAC;YACL,CAAC;YACD,IAAI,IAAI,EAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;gBAC9C,OAAQ,UAAU,EAAE,CAAC;oBACjB,KAAK,SAAS;wBACV,qFAAqF;wBACrF,IAAI,IAAI,WAAe,OAAJ,IAAI,EAAA,cAAe,CAAC;wBACvC,IAAI,IAAI,WAAe,OAAJ,IAAI,EAAA,cAAe,CAAC;wBACvC,IAAI,IAAI,WAAe,OAAJ,IAAI,EAAA,cAAe,CAAC;wBACvC,IAAI,IAAI,WAAe,OAAJ,IAAI,EAAA,cAAe,CAAC;wBAEvC,IAAI,WAAW,EAAE,CAAC;4BACd,IAAI,IAAI,gBAAoB,OAAJ,IAAI,EAAA,AAAc,CAAC;4BAC3C,IAAI,EAAC,UAAU,CAAC,8BAA8B,IAAI,UAAG,IAAI,EAAA,qCAA6B,IAAI,EAAA,+BAAuB,IAAI,EAAA,+BAAuB,IAAI,EAAA,wBAA2B,OAAJ,IAAI,EAAA,QAAS,CAAC;wBACzL,CAAC;wBACD,MAAM;oBACV;wBACI,IAAI,IAAI,kBAAW,IAAI,EAAA,MAAe,OAAV,UAAU,EAAA,IAAK,CAAC;wBAC5C,MAAM;gBACd,CAAC;YACL,CAAC,MAAM,CAAC;gBACJ,IAAI,IAAI,kBAAW,UAAU,EAAA,KAAQ,OAAJ,IAAI,EAAA,IAAK,CAAC;YAC/C,CAAC;YACD,IAAI,MAAM,EAAE,CAAC;gBACT,IAAI,IAAI,SAAU,CAAC;YACvB,CAAC;YACD,OAAO,IAAI,CAAC;QAChB,CAAC,CAAC;QAEF,IAAI,IAAI,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;YAC9C,IAAI,CAAC,UAAU,CAAC,kBAAkB,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC;YACtD,IAAI,CAAC,UAAU,CAAC,0BAA0B,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC;QACjE,CAAC,MAAM,CAAC;YACJ,MAAM,IAAI,GAAG,QAAQ,EAAE,CAAC;YACxB,IAAI,CAAC,UAAU,CAAC,kBAAkB,IAAI,IAAI,CAAC;YAC3C,IAAI,CAAC,UAAU,CAAC,0BAA0B,IAAI,IAAI,CAAC;QACvD,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG,CACI,eAAe,CAAC,IAAY,EAAA;QAC/B,IAAI,IAAI,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;YAC9C,OAAO,CAAC,IAAI,CAAC,MAAM,sMAAK,2BAAwB,CAAC,QAAQ,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC;QAC7G,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG,CACI,sBAAsB,CAAC,IAAY,EAAE,IAA2C,EAAwC;qBAAtC,iEAAiB,EAAE,cAAE,SAAS,wDAAG,KAAK;QAC3H,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YACrC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEzB,IAAI,MAAM,EAAE,CAAC;YACT,IAAI,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;gBAChC,IAAI,CAAC,mBAAmB,IAAI,OAAa,OAAN,MAAM,EAAA,GAAI,CAAC;YAClD,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,mBAAmB,IAAI,UAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,EAAA,KAAU,OAAN,MAAM,EAAA,GAAI,CAAC;YAClF,CAAC;QACL,CAAC;QACD,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,oBAAoB,EAAE,CAAC;YACpD,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;QACxF,CAAC;QACD,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAC7C,IAAI,IAAI,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;YAC9C,IAAI,CAAC,mBAAmB,IAAI,kBAAW,IAAI,EAAA,MAAe,OAAV,UAAU,EAAA,IAAK,CAAC;QACpE,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,mBAAmB,IAAI,kBAAW,UAAU,EAAA,KAAQ,OAAJ,IAAI,EAAA,IAAK,CAAC;QACnE,CAAC;QACD,IAAI,MAAM,EAAE,CAAC;YACT,IAAI,CAAC,mBAAmB,IAAI,SAAU,CAAC;QAC3C,CAAC;IACL,CAAC;IAED;;OAEG,CACI,gBAAgB,CAAC,aAAqB,EAAE,cAAsB,EAAE,SAAiB,EAAA;QACpF,IAAI,IAAI,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;YAC9C,OAAO,iBAAU,cAAc,EAAA,aAAK,aAAa,EAAA,MAAc,OAAT,SAAS,EAAA,EAAG,CAAC;QACvE,CAAC;QAED,OAAO,WAAI,SAAS,EAAA,eAAO,aAAa,EAAA,OAAoB,CAAE,CAAC,KAAjB,cAAc;IAChE,CAAC;IAED;;OAEG,CACI,UAAU,CAAC,KAAa,EAAA;QAC3B,IAAI,KAAK,CAAC,QAAQ,EAAE,KAAK,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;YACxC,OAAO,GAAQ,OAAL,KAAK,EAAA,GAAI,CAAC;QACxB,CAAC;QAED,OAAO,KAAK,CAAC,QAAQ,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG,CACI,cAAc,CAAC,MAAmC,EAAE,OAAiB,EAAA;QACxE,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,sBAAsB,EAAE,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACtF,CAAC;IAED;;OAEG,CACI,gBAAgB,CAAC,IAAY,EAAE,IAA2C,EAAE,OAAiB,EAAA;QAChG,IAAI,IAAI,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;YAC9C,OAAO,UAAG,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAA,YAAI,IAAI,EAAA,MAA8B,CAAE,CAAC,KAA5B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;QAC7E,CAAC,MAAM,CAAC;YACJ,OAAO,GAA6B,OAA1B,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,aAAO,CAAC,cAAc,CAAC,IAAI,CAAC,EAAA,KAAQ,CAAE,CAAC,KAAP,IAAI;QACzE,CAAC;IACL,CAAC;IAED;;OAEG,CACI,gBAAgB,GAAA;QACnB,IAAI,IAAI,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;YAC9C,OAAO,eAAe,CAAC;QAC3B,CAAC;QACD,OAAO,aAAa,CAAC;IACzB,CAAC;IAED;;OAEG,CACI,YAAY,GAAA;QACf,IAAI,IAAI,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;YAC9C,OAAO,eAAe,CAAC;QAC3B,CAAC;QACD,OAAO,WAAW,CAAC;IACvB,CAAC;IAED;;OAEG,CACI,eAAe,GAAA;QAClB,IAAI,IAAI,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;YAC9C,OAAO,oBAAoB,CAAC;QAChC,CAAC;QACD,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IAEM,cAAc,CAAC,MAAmC,EAAA;QACrD,IAAI,IAAI,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;YAC9C,IAAI,MAAM,CAAC,IAAI,mNAAK,wCAAqC,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,kNAAK,yCAAqC,CAAC,OAAO,EAAE,CAAC;gBAChI,OAAO,qBAAkD,OAA7B,MAAM,CAAC,sBAAsB,EAAA,EAAG,CAAC;YACjE,CAAC;YAED,OAAO,iBAA8C,OAA7B,MAAM,CAAC,sBAAsB,EAAA,EAAG,CAAC;QAC7D,CAAC;QACD,OAAO,iBAA8C,OAA7B,MAAM,CAAC,sBAAsB,EAAA,EAAG,CAAC;IAC7D,CAAC;IAED;;OAEG,CACI,sBAAsB,CAAC,EAAU,EAAE,WAAmB,EAAA;QACzD,IAAI,IAAI,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;YAC9C,OAAO,UAAG,IAAI,CAAC,YAAY,EAAE,EAAA,KAAmB,OAAf,IAA0B,GAAG,IAAlB,EAAA,KAA4B,qBAAV,QAAS,GAAC,MAAA,OAAA,IAAA,UAAiB,KAAK,EAAE,GAAG,CAAC;QACxG,CAAC;QACD,OAAO,UAAG,IAAI,CAAC,YAAY,EAAE,EAAA,KAAoB,EAAE,KAAlB,WAAW,EAAA,MAAO,EAAG,CAAC,QAAJ;IACvD,CAAC;IAED;;OAEG,CACI,yBAAyB,CAAC,EAAU,EAAE,WAAmB,EAAE,GAAW,EAAA;QACzE,IAAI,IAAI,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;YAC9C,OAAO,UAAG,IAAI,CAAC,eAAe,EAAE,EAAA,YAAI,WAAW,EAAA,KAA4B,OAAxB,WAAW,GAAG,QAAS,GAAC,MAAA,OAAA,IAAA,MAAA,EAAiB,KAAjB,AAAsB,EAAE,GAAxB,EAA6B,GAAG,GAAG,CAAC;QACnH,CAAC;QACD,OAAO,UAAG,IAAI,CAAC,eAAe,EAAE,EAAA,KAAoB,EAAE,KAAlB,WAAW,EAAA,MAAY,GAAG,QAAR,MAAQ,YAAA,EAAG,CAAC;IACtE,CAAC;IAED;;OAEG,CACI,0BAA0B,CAAC,EAAU,EAAE,WAAmB,EAAA;QAC7D,IAAI,IAAI,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;YAC9C,OAAO,UAAG,IAAI,CAAC,gBAAgB,EAAE,EAAA,YAAI,WAAW,EAAA,KAA4B,OAAxB,WAAW,GAAG,QAAS,GAAC,MAAA,OAAA,IAAA,UAAiB,KAAK,EAAE,GAAG,CAAC;QAC5G,CAAC;QACD,OAAO,UAAG,IAAI,CAAC,gBAAgB,EAAE,EAAA,YAAI,WAAW,EAAA,MAAO,OAAF,EAAE,EAAA,EAAG,CAAC;IAC/D,CAAC;IAED;;OAEG,CACI,6BAA6B,CAAC,EAAU,EAAE,WAAmB,EAAE,GAAW,EAAA;QAC7E,IAAI,IAAI,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;YAC9C,OAAO,UAAG,IAAI,CAAC,gBAAgB,EAAE,EAAA,YAAI,WAAW,EAAA,KAA4B,OAAxB,WAAW,GAAG,QAAS,GAAC,MAAA,OAAA,IAAA,MAAA,OAAA,KAAA,EAAiB,KAAK,EAAE,KAAK,GAAG,GAAG,CAAC;QACpH,CAAC;QACD,OAAO,UAAG,IAAI,CAAC,gBAAgB,EAAE,EAAA,YAAI,WAAW,EAAA,aAAK,EAAE,EAAA,MAAQ,OAAH,GAAG,EAAA,EAAG,CAAC;IACvE,CAAC;IAEO,iCAAiC,CAAC,IAAY,EAAE,IAAY,EAAE,MAAc,EAAA;QAChF,OAAO,MAAM,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,IAAQ,OAAJ,IAAI,CAAa,CAAb,gBAAe,GAAG,CAAC,EAAE,WAAe,CAAE,CAAC,CAAC,IAAR,IAAI;IACjF,CAAC;IAEO,kCAAkC,CAAC,IAAY,EAAE,IAAY,EAAE,MAAc,EAAA;QACjF,OAAO,MAAM,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,IAAQ,OAAJ,IAAI,EAAA,KAAM,IAAE,GAAG,CAAC,EAAE,IAAQ,EAAG,CAAC,CAAC,GAAT,IAAI,EAAA;IACnE,CAAC;IAEO,2BAA2B,CAAC,MAAc,EAAA;QAC9C,OAAO,MAAM,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,oCAAqC,GAAE,GAAG,CAAC,EAAE,sBAAuB,CAAC,CAAC;IAC3G,CAAC;IAEO,6BAA6B,CAAC,MAAc,EAAA;QAChD,OAAO,MAAM,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,yBAA0B,GAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAG,CAAD,gBAAW,SAAS,EAAA,aAAK,QAAQ,EAAA,MAAc,OAAT,SAAS,EAAA,EAAG,CAAC,CAAC;IACrK,CAAC;IAEO,0BAA0B,CAAC,MAAc,EAAA;QAC7C,OAAO,MAAM,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,yBAA0B,GAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,CAAG,CAAD,WAAM,IAAI,EAAA,OAAW,OAAL,KAAK,EAAA,GAAI,CAAC,CAAC;IACzH,CAAC;IAEO,mBAAmB,CAAC,MAAc,EAAA;QACtC,OAAO,MAAM,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,UAAW,GAAE,GAAG,CAAC,EAAE,MAAO,CAAC,CAAC;IACjE,CAAC;IAEO,4BAA4B,CAAC,MAAc,EAAA;QAC/C,OAAO,MAAM,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,YAAa,GAAE,GAAG,CAAC,EAAE,YAAa,CAAC,CAAC;IACzE,CAAC;IAEO,uBAAuB,CAAC,MAAc,EAAA;QAC1C,MAAM,KAAK,GAAG,kCAAkC,CAAC;QAEjD,IAAI,KAA8B,CAAC;QACnC,MAAO,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,IAAI,CAAE,CAAC;YAC3C,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAC1B,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAC1B,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,oCAAoC;YAE7D,yDAAyD;YACzD,MAAM,eAAe,GAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;YAErD,uCAAuC;YACvC,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,aAAM,QAAQ,EAAA,YAAI,eAAe,EAAA,SAAgB,CAAE,CAAC,CAAC,IAAZ,QAAQ;QACvF,CAAC;QACD,OAAO,MAAM,CAAC;IAClB,CAAC;IAEM,gBAAgB,CAAC,IAAY,EAAA;QAChC,wBAAwB;QACxB,IAAI,GAAG,IAAI,CAAC,iCAAiC,CAAC,MAAM,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;QACxE,IAAI,GAAG,IAAI,CAAC,iCAAiC,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;QACpE,IAAI,GAAG,IAAI,CAAC,iCAAiC,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAClE,IAAI,GAAG,IAAI,CAAC,iCAAiC,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QACnE,IAAI,GAAG,IAAI,CAAC,iCAAiC,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QACpE,IAAI,GAAG,IAAI,CAAC,iCAAiC,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QACrE,IAAI,GAAG,IAAI,CAAC,iCAAiC,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QACrE,IAAI,GAAG,IAAI,CAAC,iCAAiC,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QACrE,IAAI,GAAG,IAAI,CAAC,iCAAiC,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;QACvE,IAAI,GAAG,IAAI,CAAC,iCAAiC,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;QACvE,IAAI,GAAG,IAAI,CAAC,iCAAiC,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;QAEvE,oBAAoB;QACpB,IAAI,GAAG,IAAI,CAAC,kCAAkC,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QACrE,IAAI,GAAG,IAAI,CAAC,kCAAkC,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QACtE,IAAI,GAAG,IAAI,CAAC,kCAAkC,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QACtE,IAAI,GAAG,IAAI,CAAC,kCAAkC,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QACtE,IAAI,GAAG,IAAI,CAAC,kCAAkC,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;QACxE,IAAI,GAAG,IAAI,CAAC,kCAAkC,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;QACxE,IAAI,GAAG,IAAI,CAAC,kCAAkC,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;QAExE,mBAAmB;QACnB,IAAI,GAAG,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,CAAC;QAEhD,gBAAgB;QAChB,IAAI,GAAG,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC;QAE7C,QAAQ;QACR,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;QAEtC,kBAAkB;QAClB,IAAI,GAAG,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,CAAC;QAE/C,gBAAgB;QAChB,IAAI,GAAG,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,CAAC;QAC9C,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;QAEpC,YAAY;QACZ,IAAI,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;QAE1C,kBAAkB;QAClB,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC;QAE3C,cAAc;QACd,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QACrC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAErC,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,6BAA6B,CAAC,MAAc,EAAA;QAChD,OAAO,MAAM,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,yBAA0B,GAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAG,CAAD,EAAmB,OAAf,CAAuB,QAAd,EAAA,wBAAc,OAAe,CAAE,CAAC,CAAC,IAAb,SAAS;IAC3J,CAAC;IAEM,gBAAgB,CAAC,IAAY,EAAA;QAChC,yBAAA,EAA2B,CAC3B,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;QACnC,IAAI,GAAG,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,CAAC;QAEhD,OAAO,IAAI,CAAC;IAChB,CAAC;IA31BL,aAAA;QACI,oFAAA,EAAsF,CAC/E,IAAA,CAAA,qBAAqB,GAAG,KAAK,CAAC;QACrC;;WAEG,CACI,IAAA,CAAA,UAAU,GAAa,EAAE,CAAC;QACjC;;WAEG,CACI,IAAA,CAAA,QAAQ,GAAa,EAAE,CAAC;QAC/B;;WAEG,CACI,IAAA,CAAA,SAAS,GAAa,EAAE,CAAC;QAChC;;WAEG,CACI,IAAA,CAAA,QAAQ,GAAa,EAAE,CAAC;QAC/B;;WAEG,CACI,IAAA,CAAA,SAAS,GAA8B,CAAA,CAAE,CAAC;QACjD;;WAEG,CACI,IAAA,CAAA,UAAU,GAA8B,CAAA,CAAE,CAAC;QAClD;;WAEG,CACI,IAAA,CAAA,aAAa,GAA8B,CAAA,CAAE,CAAC;QAMrD;;WAEG,CACI,IAAA,CAAA,QAAQ,GAA8B,CAAA,CAAE,CAAC;QAOhD,cAAA,EAAgB,CACT,IAAA,CAAA,eAAe,GAA2B,IAAI,GAAG,EAAE,CAAC;QAK3D,cAAA,EAAgB,CACT,IAAA,CAAA,qBAAqB,GAAG,EAAE,CAAC;QAClC,cAAA,EAAgB,CACT,IAAA,CAAA,mBAAmB,GAAG,EAAE,CAAC;QAChC,cAAA,EAAgB,CACT,IAAA,CAAA,oBAAoB,GAAG,EAAE,CAAC;QACjC,cAAA,EAAgB,CACT,IAAA,CAAA,mBAAmB,GAAG,EAAE,CAAC;QAChC,cAAA,EAAgB,CACT,IAAA,CAAA,gBAAgB,GAAG,EAAE,CAAC;QAC7B,cAAA,EAAgB,CACT,IAAA,CAAA,YAAY,GAAG,EAAE,CAAC;QACzB,cAAA,EAAgB,CACT,IAAA,CAAA,YAAY,GAAG,EAAE,CAAC;QACzB,cAAA,EAAgB,CACT,IAAA,CAAA,kBAAkB,GAAG,EAAE,CAAC;QAC/B,cAAA,EAAgB,CACR,IAAA,CAAA,6BAA6B,GAAG,CAAC,CAAC;QAC1C,cAAA,EAAgB,CACT,IAAA,CAAA,uBAAuB,GAAG,EAAE,CAAC;QAEpC;;WAEG,CACI,IAAA,CAAA,iBAAiB,GAAG,EAAE,CAAC;IAgxBlC,CAAC;CAAA", "debugId": null}}, {"offset": {"line": 797, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterialBuildStateSharedData.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/nodeMaterialBuildStateSharedData.ts"], "sourcesContent": ["import type { NodeMaterialConnectionPoint } from \"./nodeMaterialBlockConnectionPoint\";\r\nimport type { NodeMaterialBlock } from \"./nodeMaterialBlock\";\r\nimport type { InputBlock } from \"./Blocks/Input/inputBlock\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { Immutable, Nullable } from \"../../types\";\r\nimport type { NodeMaterial, NodeMaterialTextureBlocks } from \"./nodeMaterial\";\r\nimport { Logger } from \"core/Misc/logger\";\r\n\r\n/**\r\n * Class used to store shared data between 2 NodeMaterialBuildState\r\n */\r\nexport class NodeMaterialBuildStateSharedData {\r\n    /**\r\n     * The node material we are currently building\r\n     */\r\n    public nodeMaterial: NodeMaterial;\r\n\r\n    /**\r\n     * Gets the list of emitted varyings\r\n     */\r\n    public temps: string[] = [];\r\n\r\n    /**\r\n     * Gets the list of emitted varyings\r\n     */\r\n    public varyings: string[] = [];\r\n\r\n    /**\r\n     * Gets the varying declaration string (for vertex shader)\r\n     */\r\n    public varyingDeclaration = \"\";\r\n\r\n    /**\r\n     * Gets the varying declaration string (for fragment shader)\r\n     * This is potentially different from varyingDeclaration only in WebGPU\r\n     */\r\n    public varyingDeclarationFragment = \"\";\r\n\r\n    /**\r\n     * Gets the varying initialization string (for fragment shader)\r\n     * Only used in WebGPU, to reconstruct the varying values from the vertex shader if their types is mat4x4f\r\n     */\r\n    public varyingInitializationsFragment = \"\";\r\n\r\n    /**\r\n     * List of the fragment output nodes\r\n     */\r\n    public fragmentOutputNodes: Immutable<Array<NodeMaterialBlock>>;\r\n\r\n    /**\r\n     * Input blocks\r\n     */\r\n    public inputBlocks: InputBlock[] = [];\r\n\r\n    /**\r\n     * Input blocks\r\n     */\r\n    public textureBlocks: NodeMaterialTextureBlocks[] = [];\r\n\r\n    /**\r\n     * Bindable blocks (Blocks that need to set data to the effect)\r\n     */\r\n    public bindableBlocks: NodeMaterialBlock[] = [];\r\n\r\n    /**\r\n     * Bindable blocks (Blocks that need to set data to the effect) that will always be called (by bindForSubMesh), contrary to bindableBlocks that won't be called if _mustRebind() returns false\r\n     */\r\n    public forcedBindableBlocks: NodeMaterialBlock[] = [];\r\n\r\n    /**\r\n     * List of blocks that can provide a compilation fallback\r\n     */\r\n    public blocksWithFallbacks: NodeMaterialBlock[] = [];\r\n\r\n    /**\r\n     * List of blocks that can provide a define update\r\n     */\r\n    public blocksWithDefines: NodeMaterialBlock[] = [];\r\n\r\n    /**\r\n     * List of blocks that can provide a repeatable content\r\n     */\r\n    public repeatableContentBlocks: NodeMaterialBlock[] = [];\r\n\r\n    /**\r\n     * List of blocks that can provide a dynamic list of uniforms\r\n     */\r\n    public dynamicUniformBlocks: NodeMaterialBlock[] = [];\r\n\r\n    /**\r\n     * List of blocks that can block the isReady function for the material\r\n     */\r\n    public blockingBlocks: NodeMaterialBlock[] = [];\r\n\r\n    /**\r\n     * Gets the list of animated inputs\r\n     */\r\n    public animatedInputs: InputBlock[] = [];\r\n\r\n    /**\r\n     * Configurations used to format the generated code\r\n     */\r\n    public formatConfig = {\r\n        getUniformAnnotation: null as Nullable<(name: string) => string>,\r\n        formatVariablename: (name: string) => name.replace(/[^a-zA-Z_]+/g, \"\"),\r\n    };\r\n\r\n    /**\r\n     * Build Id used to avoid multiple recompilations\r\n     */\r\n    public buildId: number;\r\n\r\n    /** List of emitted variables */\r\n    public variableNames: { [key: string]: number } = {};\r\n\r\n    /** List of emitted defines */\r\n    public defineNames: { [key: string]: number } = {};\r\n\r\n    /** Should emit comments? */\r\n    public emitComments: boolean;\r\n\r\n    /** Emit build activity */\r\n    public verbose: boolean;\r\n\r\n    /** Gets or sets the hosting scene */\r\n    public scene: Scene;\r\n\r\n    /**\r\n     * Gets the compilation hints emitted at compilation time\r\n     */\r\n    public hints = {\r\n        needWorldViewMatrix: false,\r\n        needWorldViewProjectionMatrix: false,\r\n        needAlphaBlending: false,\r\n        needAlphaTesting: false,\r\n    };\r\n\r\n    /**\r\n     * List of compilation checks\r\n     */\r\n    public checks = {\r\n        emitVertex: false,\r\n        emitFragment: false,\r\n        notConnectedNonOptionalInputs: new Array<NodeMaterialConnectionPoint>(),\r\n        customErrors: new Array<string>(),\r\n    };\r\n\r\n    /**\r\n     * Is vertex program allowed to be empty?\r\n     */\r\n    public allowEmptyVertexProgram: boolean = false;\r\n\r\n    /** Creates a new shared data */\r\n    public constructor() {\r\n        // Exclude usual attributes from free variable names\r\n        this.variableNames[\"position\"] = 0;\r\n        this.variableNames[\"normal\"] = 0;\r\n        this.variableNames[\"tangent\"] = 0;\r\n        this.variableNames[\"uv\"] = 0;\r\n        this.variableNames[\"uv2\"] = 0;\r\n        this.variableNames[\"uv3\"] = 0;\r\n        this.variableNames[\"uv4\"] = 0;\r\n        this.variableNames[\"uv5\"] = 0;\r\n        this.variableNames[\"uv6\"] = 0;\r\n        this.variableNames[\"color\"] = 0;\r\n        this.variableNames[\"matricesIndices\"] = 0;\r\n        this.variableNames[\"matricesWeights\"] = 0;\r\n        this.variableNames[\"matricesIndicesExtra\"] = 0;\r\n        this.variableNames[\"matricesWeightsExtra\"] = 0;\r\n        this.variableNames[\"diffuseBase\"] = 0;\r\n        this.variableNames[\"specularBase\"] = 0;\r\n        this.variableNames[\"worldPos\"] = 0;\r\n        this.variableNames[\"shadow\"] = 0;\r\n        this.variableNames[\"view\"] = 0;\r\n\r\n        // Exclude known varyings\r\n        this.variableNames[\"vTBN\"] = 0;\r\n\r\n        // Exclude defines\r\n        this.defineNames[\"MAINUV0\"] = 0;\r\n        this.defineNames[\"MAINUV1\"] = 0;\r\n        this.defineNames[\"MAINUV2\"] = 0;\r\n        this.defineNames[\"MAINUV3\"] = 0;\r\n        this.defineNames[\"MAINUV4\"] = 0;\r\n        this.defineNames[\"MAINUV5\"] = 0;\r\n        this.defineNames[\"MAINUV6\"] = 0;\r\n        this.defineNames[\"MAINUV7\"] = 0;\r\n    }\r\n\r\n    /**\r\n     * Push a new error to the build state, avoiding exceptions that can break the build process\r\n     * @param message defines the error message to push\r\n     */\r\n    public raiseBuildError(message: string) {\r\n        if (this.checks.customErrors.indexOf(message) !== -1) {\r\n            this.checks.customErrors.push(message);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Emits console errors and exceptions if there is a failing check\r\n     * @returns true if all checks pass\r\n     */\r\n    public emitErrors() {\r\n        let errorMessage = \"\";\r\n\r\n        if (!this.checks.emitVertex && !this.allowEmptyVertexProgram) {\r\n            errorMessage += \"NodeMaterial does not have a vertex output. You need to at least add a block that generates a position value.\\n\";\r\n        }\r\n        if (!this.checks.emitFragment) {\r\n            errorMessage += \"NodeMaterial does not have a fragment output. You need to at least add a block that generates a color value.\\n\";\r\n        }\r\n        for (const notConnectedInput of this.checks.notConnectedNonOptionalInputs) {\r\n            errorMessage += `input ${notConnectedInput.name} from block ${\r\n                notConnectedInput.ownerBlock.name\r\n            }[${notConnectedInput.ownerBlock.getClassName()}] is not connected and is not optional.\\n`;\r\n        }\r\n        for (const customError of this.checks.customErrors) {\r\n            errorMessage += customError + \"\\n\";\r\n        }\r\n\r\n        if (errorMessage) {\r\n            errorMessage = \"Node material build failed: \\n\" + errorMessage;\r\n            Logger.Error(errorMessage);\r\n            this.nodeMaterial.onBuildErrorObservable.notifyObservers(errorMessage);\r\n            return false;\r\n        }\r\n\r\n        return true;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AAMA,OAAO,EAAE,MAAM,EAAE,6BAAyB;;AAKpC,MAAO,gCAAgC;IAkLzC;;;OAGG,CACI,eAAe,CAAC,OAAe,EAAA;QAClC,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YACnD,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC3C,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,UAAU,GAAA;QACb,IAAI,YAAY,GAAG,EAAE,CAAC;QAEtB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC3D,YAAY,IAAI,iHAAiH,CAAC;QACtI,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;YAC5B,YAAY,IAAI,gHAAgH,CAAC;QACrI,CAAC;QACD,KAAK,MAAM,iBAAiB,IAAI,IAAI,CAAC,MAAM,CAAC,6BAA6B,CAAE,CAAC;YACxE,YAAY,IAAI,SACZ,OADqB,UACJ,OADqB,CAAC,IAAI,EAAA,yCACzB,UAAU,CAAC,IACjC,EAAA,KAA+C,OAA3C,iBAAiB,CAAC,UAAU,CAAC,YAAY,EAAE,EAAA,0CAA2C,CAAC;QAC/F,CAAC;QACD,KAAK,MAAM,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAE,CAAC;YACjD,YAAY,IAAI,WAAW,GAAG,IAAI,CAAC;QACvC,CAAC;QAED,IAAI,YAAY,EAAE,CAAC;YACf,YAAY,GAAG,gCAAgC,GAAG,YAAY,CAAC;qKAC/D,SAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YAC3B,IAAI,CAAC,YAAY,CAAC,sBAAsB,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;YACvE,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IA7ED,8BAAA,EAAgC,CAChC,aAAA;QAxIA;;WAEG,CACI,IAAA,CAAA,KAAK,GAAa,EAAE,CAAC;QAE5B;;WAEG,CACI,IAAA,CAAA,QAAQ,GAAa,EAAE,CAAC;QAE/B;;WAEG,CACI,IAAA,CAAA,kBAAkB,GAAG,EAAE,CAAC;QAE/B;;;WAGG,CACI,IAAA,CAAA,0BAA0B,GAAG,EAAE,CAAC;QAEvC;;;WAGG,CACI,IAAA,CAAA,8BAA8B,GAAG,EAAE,CAAC;QAO3C;;WAEG,CACI,IAAA,CAAA,WAAW,GAAiB,EAAE,CAAC;QAEtC;;WAEG,CACI,IAAA,CAAA,aAAa,GAAgC,EAAE,CAAC;QAEvD;;WAEG,CACI,IAAA,CAAA,cAAc,GAAwB,EAAE,CAAC;QAEhD;;WAEG,CACI,IAAA,CAAA,oBAAoB,GAAwB,EAAE,CAAC;QAEtD;;WAEG,CACI,IAAA,CAAA,mBAAmB,GAAwB,EAAE,CAAC;QAErD;;WAEG,CACI,IAAA,CAAA,iBAAiB,GAAwB,EAAE,CAAC;QAEnD;;WAEG,CACI,IAAA,CAAA,uBAAuB,GAAwB,EAAE,CAAC;QAEzD;;WAEG,CACI,IAAA,CAAA,oBAAoB,GAAwB,EAAE,CAAC;QAEtD;;WAEG,CACI,IAAA,CAAA,cAAc,GAAwB,EAAE,CAAC;QAEhD;;WAEG,CACI,IAAA,CAAA,cAAc,GAAiB,EAAE,CAAC;QAEzC;;WAEG,CACI,IAAA,CAAA,YAAY,GAAG;YAClB,oBAAoB,EAAE,IAA0C;YAChE,kBAAkB,EAAE,CAAC,IAAY,EAAE,CAAG,CAAD,GAAK,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;SACzE,CAAC;QAOF,8BAAA,EAAgC,CACzB,IAAA,CAAA,aAAa,GAA8B,CAAA,CAAE,CAAC;QAErD,4BAAA,EAA8B,CACvB,IAAA,CAAA,WAAW,GAA8B,CAAA,CAAE,CAAC;QAWnD;;WAEG,CACI,IAAA,CAAA,KAAK,GAAG;YACX,mBAAmB,EAAE,KAAK;YAC1B,6BAA6B,EAAE,KAAK;YACpC,iBAAiB,EAAE,KAAK;YACxB,gBAAgB,EAAE,KAAK;SAC1B,CAAC;QAEF;;WAEG,CACI,IAAA,CAAA,MAAM,GAAG;YACZ,UAAU,EAAE,KAAK;YACjB,YAAY,EAAE,KAAK;YACnB,6BAA6B,EAAE,IAAI,KAAK,EAA+B;YACvE,YAAY,EAAE,IAAI,KAAK,EAAU;SACpC,CAAC;QAEF;;WAEG,CACI,IAAA,CAAA,uBAAuB,GAAY,KAAK,CAAC;QAI5C,oDAAoD;QACpD,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QACnC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QACjC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAClC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC7B,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAChC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;QAC1C,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;QAC1C,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC;QAC/C,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC;QAC/C,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;QACtC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;QACvC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QACnC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QACjC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAE/B,yBAAyB;QACzB,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAE/B,kBAAkB;QAClB,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAChC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAChC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAChC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAChC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAChC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAChC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAChC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;IACpC,CAAC;CA2CJ", "debugId": null}}, {"offset": {"line": 948, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterialBlockConnectionPoint.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/nodeMaterialBlockConnectionPoint.ts"], "sourcesContent": ["import { NodeMaterialBlockConnectionPointTypes } from \"./Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport { NodeMaterialBlockTargets } from \"./Enums/nodeMaterialBlockTargets\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { InputBlock } from \"./Blocks/Input/inputBlock\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport type { NodeMaterialBlock } from \"./nodeMaterialBlock\";\r\n\r\n/**\r\n * Enum used to define the compatibility state between two connection points\r\n */\r\nexport const enum NodeMaterialConnectionPointCompatibilityStates {\r\n    /** Points are compatibles */\r\n    Compatible,\r\n    /** Points are incompatible because of their types */\r\n    TypeIncompatible,\r\n    /** Points are incompatible because of their targets (vertex vs fragment) */\r\n    TargetIncompatible,\r\n    /** Points are incompatible because they are in the same hierarchy **/\r\n    HierarchyIssue,\r\n}\r\n\r\n/**\r\n * Defines the direction of a connection point\r\n */\r\nexport const enum NodeMaterialConnectionPointDirection {\r\n    /** Input */\r\n    Input,\r\n    /** Output */\r\n    Output,\r\n}\r\n\r\n/**\r\n * Defines a connection point for a block\r\n */\r\nexport class NodeMaterialConnectionPoint {\r\n    /**\r\n     * Checks if two types are equivalent\r\n     * @param type1 type 1 to check\r\n     * @param type2 type 2 to check\r\n     * @returns true if both types are equivalent, else false\r\n     */\r\n    public static AreEquivalentTypes(type1: number, type2: number): boolean {\r\n        switch (type1) {\r\n            case NodeMaterialBlockConnectionPointTypes.Vector3: {\r\n                if (type2 === NodeMaterialBlockConnectionPointTypes.Color3) {\r\n                    return true;\r\n                }\r\n                break;\r\n            }\r\n            case NodeMaterialBlockConnectionPointTypes.Vector4: {\r\n                if (type2 === NodeMaterialBlockConnectionPointTypes.Color4) {\r\n                    return true;\r\n                }\r\n                break;\r\n            }\r\n            case NodeMaterialBlockConnectionPointTypes.Color3: {\r\n                if (type2 === NodeMaterialBlockConnectionPointTypes.Vector3) {\r\n                    return true;\r\n                }\r\n                break;\r\n            }\r\n            case NodeMaterialBlockConnectionPointTypes.Color4: {\r\n                if (type2 === NodeMaterialBlockConnectionPointTypes.Vector4) {\r\n                    return true;\r\n                }\r\n                break;\r\n            }\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /** @internal */\r\n    public _preventBubbleUp = false;\r\n\r\n    /** @internal */\r\n    public readonly _ownerBlock: NodeMaterialBlock;\r\n\r\n    private _connectedPointBackingField: Nullable<NodeMaterialConnectionPoint> = null;\r\n    private _connectedPointTypeChangedObserver: Nullable<Observer<NodeMaterialBlockConnectionPointTypes>>;\r\n\r\n    private get _connectedPoint(): Nullable<NodeMaterialConnectionPoint> {\r\n        return this._connectedPointBackingField;\r\n    }\r\n\r\n    private set _connectedPoint(value: Nullable<NodeMaterialConnectionPoint>) {\r\n        if (this._connectedPointBackingField === value) {\r\n            return;\r\n        }\r\n\r\n        this._connectedPointTypeChangedObserver?.remove();\r\n        this._updateTypeDependentState(() => (this._connectedPointBackingField = value));\r\n        if (this._connectedPointBackingField) {\r\n            this._connectedPointTypeChangedObserver = this._connectedPointBackingField.onTypeChangedObservable.add(() => {\r\n                this._notifyTypeChanged();\r\n            });\r\n        }\r\n    }\r\n\r\n    private readonly _endpoints = new Array<NodeMaterialConnectionPoint>();\r\n    private _associatedVariableName: string;\r\n    private readonly _direction: NodeMaterialConnectionPointDirection;\r\n\r\n    /** @internal */\r\n    public _redirectedSource: Nullable<NodeMaterialConnectionPoint> = null;\r\n\r\n    private _typeConnectionSourceBackingField: Nullable<NodeMaterialConnectionPoint> = null;\r\n    private _typeConnectionSourceTypeChangedObserver: Nullable<Observer<NodeMaterialBlockConnectionPointTypes>>;\r\n\r\n    /** @internal */\r\n    public get _typeConnectionSource(): Nullable<NodeMaterialConnectionPoint> {\r\n        return this._typeConnectionSourceBackingField;\r\n    }\r\n\r\n    /** @internal */\r\n    public set _typeConnectionSource(value: Nullable<NodeMaterialConnectionPoint>) {\r\n        if (this._typeConnectionSourceBackingField === value) {\r\n            return;\r\n        }\r\n\r\n        this._typeConnectionSourceTypeChangedObserver?.remove();\r\n        this._updateTypeDependentState(() => (this._typeConnectionSourceBackingField = value));\r\n        if (this._typeConnectionSourceBackingField) {\r\n            this._typeConnectionSourceTypeChangedObserver = this._typeConnectionSourceBackingField.onTypeChangedObservable.add(() => {\r\n                this._notifyTypeChanged();\r\n            });\r\n        }\r\n    }\r\n\r\n    private _defaultConnectionPointTypeBackingField: Nullable<NodeMaterialBlockConnectionPointTypes> = null;\r\n\r\n    /** @internal */\r\n    public get _defaultConnectionPointType(): Nullable<NodeMaterialBlockConnectionPointTypes> {\r\n        return this._defaultConnectionPointTypeBackingField;\r\n    }\r\n\r\n    /** @internal */\r\n    public set _defaultConnectionPointType(value: Nullable<NodeMaterialBlockConnectionPointTypes>) {\r\n        this._updateTypeDependentState(() => (this._defaultConnectionPointTypeBackingField = value));\r\n    }\r\n\r\n    /** @internal */\r\n    public _isMainLinkSource = false;\r\n\r\n    private _linkedConnectionSourceBackingField: Nullable<NodeMaterialConnectionPoint> = null;\r\n    private _linkedConnectionSourceTypeChangedObserver: Nullable<Observer<NodeMaterialBlockConnectionPointTypes>>;\r\n\r\n    /** @internal */\r\n    public get _linkedConnectionSource(): Nullable<NodeMaterialConnectionPoint> {\r\n        return this._linkedConnectionSourceBackingField;\r\n    }\r\n\r\n    /** @internal */\r\n    public set _linkedConnectionSource(value: Nullable<NodeMaterialConnectionPoint>) {\r\n        if (this._linkedConnectionSourceBackingField === value) {\r\n            return;\r\n        }\r\n\r\n        this._linkedConnectionSourceTypeChangedObserver?.remove();\r\n        this._updateTypeDependentState(() => (this._linkedConnectionSourceBackingField = value));\r\n        this._isMainLinkSource = false;\r\n        if (this._linkedConnectionSourceBackingField) {\r\n            this._linkedConnectionSourceTypeChangedObserver = this._linkedConnectionSourceBackingField.onTypeChangedObservable.add(() => {\r\n                this._notifyTypeChanged();\r\n            });\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public _acceptedConnectionPointType: Nullable<NodeMaterialConnectionPoint> = null;\r\n\r\n    private _type = NodeMaterialBlockConnectionPointTypes.Float;\r\n\r\n    /** @internal */\r\n    public _enforceAssociatedVariableName = false;\r\n\r\n    /** @internal */\r\n    public _forPostBuild = false;\r\n\r\n    /** Gets the direction of the point */\r\n    public get direction() {\r\n        return this._direction;\r\n    }\r\n\r\n    /** Indicates that this connection point needs dual validation before being connected to another point */\r\n    public needDualDirectionValidation: boolean = false;\r\n\r\n    /**\r\n     * Gets or sets the additional types supported by this connection point\r\n     */\r\n    public acceptedConnectionPointTypes: NodeMaterialBlockConnectionPointTypes[] = [];\r\n\r\n    /**\r\n     * Gets or sets the additional types excluded by this connection point\r\n     */\r\n    public excludedConnectionPointTypes: NodeMaterialBlockConnectionPointTypes[] = [];\r\n\r\n    /**\r\n     * Observable triggered when this point is connected\r\n     */\r\n    public readonly onConnectionObservable = new Observable<NodeMaterialConnectionPoint>();\r\n\r\n    /**\r\n     * Observable triggered when this point is disconnected\r\n     */\r\n    public readonly onDisconnectionObservable = new Observable<NodeMaterialConnectionPoint>();\r\n\r\n    /**\r\n     * Observable triggered when the type of the connection point is changed\r\n     */\r\n    public readonly onTypeChangedObservable = new Observable<NodeMaterialBlockConnectionPointTypes>();\r\n    private _isTypeChangeObservableNotifying = false;\r\n\r\n    /**\r\n     * Gets the declaration variable name in the shader\r\n     */\r\n    public get declarationVariableName(): string {\r\n        if (this._ownerBlock.isInput) {\r\n            return (this._ownerBlock as InputBlock).declarationVariableName;\r\n        }\r\n\r\n        if ((!this._enforceAssociatedVariableName || !this._associatedVariableName) && this._connectedPoint) {\r\n            return this._connectedPoint.declarationVariableName;\r\n        }\r\n\r\n        return this._associatedVariableName;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the associated variable name in the shader\r\n     */\r\n    public get associatedVariableName(): string {\r\n        if (this._ownerBlock.isInput) {\r\n            return (this._ownerBlock as InputBlock).associatedVariableName;\r\n        }\r\n\r\n        if ((!this._enforceAssociatedVariableName || !this._associatedVariableName) && this._connectedPoint) {\r\n            return this._connectedPoint.associatedVariableName;\r\n        }\r\n\r\n        return this._associatedVariableName;\r\n    }\r\n\r\n    public set associatedVariableName(value: string) {\r\n        this._associatedVariableName = value;\r\n    }\r\n\r\n    /** Get the inner type (ie AutoDetect for instance instead of the inferred one) */\r\n    public get innerType() {\r\n        if (this._linkedConnectionSource && !this._isMainLinkSource && this._linkedConnectionSource.isConnected) {\r\n            return this.type;\r\n        }\r\n        return this._type;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the connection point type (default is float)\r\n     */\r\n    public get type(): NodeMaterialBlockConnectionPointTypes {\r\n        if (this._type === NodeMaterialBlockConnectionPointTypes.AutoDetect) {\r\n            if (this._ownerBlock.isInput) {\r\n                return (this._ownerBlock as InputBlock).type;\r\n            }\r\n\r\n            if (this._connectedPoint) {\r\n                return this._connectedPoint.type;\r\n            }\r\n\r\n            if (this._linkedConnectionSource) {\r\n                if (this._linkedConnectionSource.isConnected) {\r\n                    if (this._linkedConnectionSource.connectedPoint!._redirectedSource && this._linkedConnectionSource.connectedPoint!._redirectedSource.isConnected) {\r\n                        return this._linkedConnectionSource.connectedPoint!._redirectedSource.type;\r\n                    }\r\n                    return this._linkedConnectionSource.type;\r\n                }\r\n                if (this._linkedConnectionSource._defaultConnectionPointType) {\r\n                    return this._linkedConnectionSource._defaultConnectionPointType;\r\n                }\r\n            }\r\n\r\n            if (this._defaultConnectionPointType) {\r\n                return this._defaultConnectionPointType;\r\n            }\r\n        }\r\n\r\n        if (this._type === NodeMaterialBlockConnectionPointTypes.BasedOnInput) {\r\n            if (this._typeConnectionSource) {\r\n                if (!this._typeConnectionSource.isConnected && this._defaultConnectionPointType) {\r\n                    return this._defaultConnectionPointType;\r\n                }\r\n                return this._typeConnectionSource.type;\r\n            } else if (this._defaultConnectionPointType) {\r\n                return this._defaultConnectionPointType;\r\n            }\r\n        }\r\n\r\n        return this._type;\r\n    }\r\n\r\n    public set type(value: NodeMaterialBlockConnectionPointTypes) {\r\n        this._updateTypeDependentState(() => (this._type = value));\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the connection point name\r\n     */\r\n    public readonly name: string;\r\n\r\n    /**\r\n     * Gets or sets the connection point name\r\n     */\r\n    public displayName: string;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating that this connection point can be omitted\r\n     */\r\n    public isOptional: boolean;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating that this connection point is exposed on a frame\r\n     */\r\n    public isExposedOnFrame: boolean = false;\r\n\r\n    /**\r\n     * Gets or sets number indicating the position that the port is exposed to on a frame\r\n     */\r\n    public exposedPortPosition: number = -1;\r\n\r\n    /**\r\n     * Gets or sets a string indicating that this uniform must be defined under a #ifdef\r\n     */\r\n    public define: string;\r\n\r\n    /** @internal */\r\n    public _prioritizeVertex = false;\r\n\r\n    private _target: NodeMaterialBlockTargets = NodeMaterialBlockTargets.VertexAndFragment;\r\n\r\n    /** Gets or sets the target of that connection point */\r\n    public get target(): NodeMaterialBlockTargets {\r\n        if (!this._prioritizeVertex || !this._ownerBlock) {\r\n            return this._target;\r\n        }\r\n\r\n        if (this._target !== NodeMaterialBlockTargets.VertexAndFragment) {\r\n            return this._target;\r\n        }\r\n\r\n        if (this._ownerBlock.target === NodeMaterialBlockTargets.Fragment) {\r\n            return NodeMaterialBlockTargets.Fragment;\r\n        }\r\n\r\n        return NodeMaterialBlockTargets.Vertex;\r\n    }\r\n\r\n    public set target(value: NodeMaterialBlockTargets) {\r\n        this._target = value;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating that the current point is connected to another NodeMaterialBlock\r\n     */\r\n    public get isConnected(): boolean {\r\n        return this.connectedPoint !== null || this.hasEndpoints;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating that the current point is connected to an input block\r\n     */\r\n    public get isConnectedToInputBlock(): boolean {\r\n        return this.connectedPoint !== null && this.connectedPoint.ownerBlock.isInput;\r\n    }\r\n\r\n    /**\r\n     * Gets a the connected input block (if any)\r\n     */\r\n    public get connectInputBlock(): Nullable<InputBlock> {\r\n        if (!this.isConnectedToInputBlock) {\r\n            return null;\r\n        }\r\n\r\n        return this.connectedPoint!.ownerBlock as InputBlock;\r\n    }\r\n\r\n    /** Get the other side of the connection (if any) */\r\n    public get connectedPoint(): Nullable<NodeMaterialConnectionPoint> {\r\n        return this._connectedPoint;\r\n    }\r\n\r\n    /** Get the block that owns this connection point */\r\n    public get ownerBlock(): NodeMaterialBlock {\r\n        return this._ownerBlock;\r\n    }\r\n\r\n    /** Get the block connected on the other side of this connection (if any) */\r\n    public get sourceBlock(): Nullable<NodeMaterialBlock> {\r\n        if (!this._connectedPoint) {\r\n            return null;\r\n        }\r\n\r\n        return this._connectedPoint.ownerBlock;\r\n    }\r\n\r\n    /** Get the block connected on the endpoints of this connection (if any) */\r\n    public get connectedBlocks(): Array<NodeMaterialBlock> {\r\n        if (this._endpoints.length === 0) {\r\n            return [];\r\n        }\r\n\r\n        return this._endpoints.map((e) => e.ownerBlock);\r\n    }\r\n\r\n    /** Gets the list of connected endpoints */\r\n    public get endpoints() {\r\n        return this._endpoints;\r\n    }\r\n\r\n    /** Gets a boolean indicating if that output point is connected to at least one input */\r\n    public get hasEndpoints(): boolean {\r\n        return this._endpoints && this._endpoints.length > 0;\r\n    }\r\n\r\n    /** Gets a boolean indicating that this connection has a path to the vertex output*/\r\n    public get isDirectlyConnectedToVertexOutput(): boolean {\r\n        if (!this.hasEndpoints) {\r\n            return false;\r\n        }\r\n\r\n        for (const endpoint of this._endpoints) {\r\n            if (endpoint.ownerBlock.target === NodeMaterialBlockTargets.Vertex) {\r\n                return true;\r\n            }\r\n\r\n            if (endpoint.ownerBlock.target === NodeMaterialBlockTargets.Neutral || endpoint.ownerBlock.target === NodeMaterialBlockTargets.VertexAndFragment) {\r\n                if (endpoint.ownerBlock.outputs.some((o) => o.isDirectlyConnectedToVertexOutput)) {\r\n                    return true;\r\n                }\r\n            }\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /** Gets a boolean indicating that this connection will be used in the vertex shader */\r\n    public get isConnectedInVertexShader(): boolean {\r\n        if (this.target === NodeMaterialBlockTargets.Vertex) {\r\n            return true;\r\n        }\r\n\r\n        if (!this.hasEndpoints) {\r\n            return false;\r\n        }\r\n\r\n        for (const endpoint of this._endpoints) {\r\n            if (endpoint.ownerBlock.target === NodeMaterialBlockTargets.Vertex) {\r\n                return true;\r\n            }\r\n\r\n            if (endpoint.target === NodeMaterialBlockTargets.Vertex) {\r\n                return true;\r\n            }\r\n\r\n            if (endpoint.ownerBlock.target === NodeMaterialBlockTargets.Neutral || endpoint.ownerBlock.target === NodeMaterialBlockTargets.VertexAndFragment) {\r\n                if (endpoint.ownerBlock.outputs.some((o) => o.isConnectedInVertexShader)) {\r\n                    return true;\r\n                }\r\n            }\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /** Gets a boolean indicating that this connection will be used in the fragment shader */\r\n    public get isConnectedInFragmentShader(): boolean {\r\n        if (this.target === NodeMaterialBlockTargets.Fragment) {\r\n            return true;\r\n        }\r\n\r\n        if (!this.hasEndpoints) {\r\n            return false;\r\n        }\r\n\r\n        for (const endpoint of this._endpoints) {\r\n            if (endpoint.ownerBlock.target === NodeMaterialBlockTargets.Fragment) {\r\n                return true;\r\n            }\r\n\r\n            if (endpoint.ownerBlock.target === NodeMaterialBlockTargets.Neutral || endpoint.ownerBlock.target === NodeMaterialBlockTargets.VertexAndFragment) {\r\n                if (endpoint.ownerBlock.isConnectedInFragmentShader()) {\r\n                    return true;\r\n                }\r\n            }\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Creates a block suitable to be used as an input for this input point.\r\n     * If null is returned, a block based on the point type will be created.\r\n     * @returns The returned string parameter is the name of the output point of NodeMaterialBlock (first parameter of the returned array) that can be connected to the input\r\n     */\r\n    public createCustomInputBlock(): Nullable<[NodeMaterialBlock, string]> {\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Creates a new connection point\r\n     * @param name defines the connection point name\r\n     * @param ownerBlock defines the block hosting this connection point\r\n     * @param direction defines the direction of the connection point\r\n     */\r\n    public constructor(name: string, ownerBlock: NodeMaterialBlock, direction: NodeMaterialConnectionPointDirection) {\r\n        this._ownerBlock = ownerBlock;\r\n        this.name = name;\r\n        this._direction = direction;\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name e.g. \"NodeMaterialConnectionPoint\"\r\n     * @returns the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"NodeMaterialConnectionPoint\";\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if the current point can be connected to another point\r\n     * @param connectionPoint defines the other connection point\r\n     * @returns a boolean\r\n     */\r\n    public canConnectTo(connectionPoint: NodeMaterialConnectionPoint) {\r\n        return this.checkCompatibilityState(connectionPoint) === NodeMaterialConnectionPointCompatibilityStates.Compatible;\r\n    }\r\n\r\n    /**\r\n     * Gets a number indicating if the current point can be connected to another point\r\n     * @param connectionPoint defines the other connection point\r\n     * @returns a number defining the compatibility state\r\n     */\r\n    public checkCompatibilityState(connectionPoint: NodeMaterialConnectionPoint): NodeMaterialConnectionPointCompatibilityStates {\r\n        const ownerBlock = this._ownerBlock;\r\n        const otherBlock = connectionPoint.ownerBlock;\r\n\r\n        if (ownerBlock.target === NodeMaterialBlockTargets.Fragment) {\r\n            // Let's check we are not going reverse\r\n\r\n            if (otherBlock.target === NodeMaterialBlockTargets.Vertex) {\r\n                return NodeMaterialConnectionPointCompatibilityStates.TargetIncompatible;\r\n            }\r\n\r\n            for (const output of otherBlock.outputs) {\r\n                if (output.ownerBlock.target != NodeMaterialBlockTargets.Neutral && output.isConnectedInVertexShader) {\r\n                    return NodeMaterialConnectionPointCompatibilityStates.TargetIncompatible;\r\n                }\r\n            }\r\n        }\r\n\r\n        if (this.type !== connectionPoint.type && connectionPoint.innerType !== NodeMaterialBlockConnectionPointTypes.AutoDetect) {\r\n            // Equivalents\r\n            if (NodeMaterialConnectionPoint.AreEquivalentTypes(this.type, connectionPoint.type)) {\r\n                return NodeMaterialConnectionPointCompatibilityStates.Compatible;\r\n            }\r\n\r\n            // Accepted types\r\n            if (\r\n                (connectionPoint.acceptedConnectionPointTypes && connectionPoint.acceptedConnectionPointTypes.indexOf(this.type) !== -1) ||\r\n                (connectionPoint._acceptedConnectionPointType && NodeMaterialConnectionPoint.AreEquivalentTypes(connectionPoint._acceptedConnectionPointType.type, this.type))\r\n            ) {\r\n                return NodeMaterialConnectionPointCompatibilityStates.Compatible;\r\n            } else {\r\n                return NodeMaterialConnectionPointCompatibilityStates.TypeIncompatible;\r\n            }\r\n        }\r\n\r\n        // Excluded\r\n        if (connectionPoint.excludedConnectionPointTypes && connectionPoint.excludedConnectionPointTypes.indexOf(this.type) !== -1) {\r\n            return NodeMaterialConnectionPointCompatibilityStates.TypeIncompatible;\r\n        }\r\n\r\n        // Check hierarchy\r\n        let targetBlock = otherBlock;\r\n        let sourceBlock = ownerBlock;\r\n        if (this.direction === NodeMaterialConnectionPointDirection.Input) {\r\n            targetBlock = ownerBlock;\r\n            sourceBlock = otherBlock;\r\n        }\r\n\r\n        if (targetBlock.isAnAncestorOf(sourceBlock)) {\r\n            return NodeMaterialConnectionPointCompatibilityStates.HierarchyIssue;\r\n        }\r\n\r\n        return NodeMaterialConnectionPointCompatibilityStates.Compatible;\r\n    }\r\n\r\n    /**\r\n     * Connect this point to another connection point\r\n     * @param connectionPoint defines the other connection point\r\n     * @param ignoreConstraints defines if the system will ignore connection type constraints (default is false)\r\n     * @returns the current connection point\r\n     */\r\n    public connectTo(connectionPoint: NodeMaterialConnectionPoint, ignoreConstraints = false): NodeMaterialConnectionPoint {\r\n        if (!ignoreConstraints && !this.canConnectTo(connectionPoint)) {\r\n            // eslint-disable-next-line no-throw-literal\r\n            throw `Cannot connect these two connectors. source: \"${this.ownerBlock.name}\".${this.name}, target: \"${connectionPoint.ownerBlock.name}\".${connectionPoint.name}`;\r\n        }\r\n\r\n        this._endpoints.push(connectionPoint);\r\n        connectionPoint._connectedPoint = this;\r\n\r\n        this._enforceAssociatedVariableName = false;\r\n\r\n        this.onConnectionObservable.notifyObservers(connectionPoint);\r\n        connectionPoint.onConnectionObservable.notifyObservers(this);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Disconnect this point from one of his endpoint\r\n     * @param endpoint defines the other connection point\r\n     * @returns the current connection point\r\n     */\r\n    public disconnectFrom(endpoint: NodeMaterialConnectionPoint): NodeMaterialConnectionPoint {\r\n        const index = this._endpoints.indexOf(endpoint);\r\n\r\n        if (index === -1) {\r\n            return this;\r\n        }\r\n\r\n        this._endpoints.splice(index, 1);\r\n        endpoint._connectedPoint = null;\r\n        this._enforceAssociatedVariableName = false;\r\n        endpoint._enforceAssociatedVariableName = false;\r\n\r\n        this.onDisconnectionObservable.notifyObservers(endpoint);\r\n        endpoint.onDisconnectionObservable.notifyObservers(this);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Fill the list of excluded connection point types with all types other than those passed in the parameter\r\n     * @param mask Types (ORed values of NodeMaterialBlockConnectionPointTypes) that are allowed, and thus will not be pushed to the excluded list\r\n     */\r\n    public addExcludedConnectionPointFromAllowedTypes(mask: number): void {\r\n        let bitmask = 1;\r\n        while (bitmask < NodeMaterialBlockConnectionPointTypes.All) {\r\n            if (!(mask & bitmask)) {\r\n                this.excludedConnectionPointTypes.push(bitmask);\r\n            }\r\n            bitmask = bitmask << 1;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Serializes this point in a JSON representation\r\n     * @param isInput defines if the connection point is an input (default is true)\r\n     * @returns the serialized point object\r\n     */\r\n    public serialize(isInput = true): any {\r\n        const serializationObject: any = {};\r\n\r\n        serializationObject.name = this.name;\r\n        if (this.displayName) {\r\n            serializationObject.displayName = this.displayName;\r\n        }\r\n\r\n        if (isInput && this.connectedPoint) {\r\n            serializationObject.inputName = this.name;\r\n            serializationObject.targetBlockId = this.connectedPoint.ownerBlock.uniqueId;\r\n            serializationObject.targetConnectionName = this.connectedPoint.name;\r\n            serializationObject.isExposedOnFrame = true;\r\n            serializationObject.exposedPortPosition = this.exposedPortPosition;\r\n        }\r\n\r\n        if (this.isExposedOnFrame || this.exposedPortPosition >= 0) {\r\n            serializationObject.isExposedOnFrame = true;\r\n            serializationObject.exposedPortPosition = this.exposedPortPosition;\r\n        }\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * Release resources\r\n     */\r\n    public dispose() {\r\n        this.onConnectionObservable.clear();\r\n        this.onDisconnectionObservable.clear();\r\n        this.onTypeChangedObservable.clear();\r\n\r\n        this._connectedPoint = null;\r\n        this._typeConnectionSource = null;\r\n        this._linkedConnectionSource = null;\r\n    }\r\n\r\n    private _updateTypeDependentState(update: () => void) {\r\n        const previousType = this.type;\r\n        update();\r\n        if (this.type !== previousType) {\r\n            this._notifyTypeChanged();\r\n        }\r\n    }\r\n\r\n    private _notifyTypeChanged() {\r\n        // Disallow re-entrancy\r\n        if (this._isTypeChangeObservableNotifying) {\r\n            return;\r\n        }\r\n\r\n        this._isTypeChangeObservableNotifying = true;\r\n        this.onTypeChangedObservable.notifyObservers(this.type);\r\n        this._isTypeChangeObservableNotifying = false;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA,OAAO,EAAE,qCAAqC,EAAE,MAAM,+CAA+C,CAAC;AACtG,OAAO,EAAE,wBAAwB,EAAE,MAAM,kCAAkC,CAAC;AAG5E,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;;;;AAOnD,IAAkB,8CASjB;AATD,CAAA,SAAkB,8CAA8C;IAC5D,2BAAA,EAA6B,CAC7B,8CAAA,CAAA,8CAAA,CAAA,aAAA,GAAA,EAAA,GAAA,YAAU,CAAA;IACV,mDAAA,EAAqD,CACrD,8CAAA,CAAA,8CAAA,CAAA,mBAAA,GAAA,EAAA,GAAA,kBAAgB,CAAA;IAChB,0EAAA,EAA4E,CAC5E,8CAAA,CAAA,8CAAA,CAAA,qBAAA,GAAA,EAAA,GAAA,oBAAkB,CAAA;IAClB,oEAAA,EAAsE,CACtE,8CAAA,CAAA,8CAAA,CAAA,iBAAA,GAAA,EAAA,GAAA,gBAAc,CAAA;AAClB,CAAC,EATiB,8CAA8C,IAAA,CAA9C,8CAA8C,GAAA,CAAA,CAAA,GAS/D;AAKD,IAAkB,oCAKjB;AALD,CAAA,SAAkB,oCAAoC;IAClD,UAAA,EAAY,CACZ,oCAAA,CAAA,oCAAA,CAAA,QAAA,GAAA,EAAA,GAAA,OAAK,CAAA;IACL,WAAA,EAAa,CACb,oCAAA,CAAA,oCAAA,CAAA,SAAA,GAAA,EAAA,GAAA,QAAM,CAAA;AACV,CAAC,EALiB,oCAAoC,IAAA,CAApC,oCAAoC,GAAA,CAAA,CAAA,GAKrD;AAKK,MAAO,2BAA2B;IACpC;;;;;OAKG,CACI,MAAM,CAAC,kBAAkB,CAAC,KAAa,EAAE,KAAa,EAAA;QACzD,OAAQ,KAAK,EAAE,CAAC;YACZ,kNAAK,yCAAqC,CAAC,OAAO,CAAC;gBAAC,CAAC;oBACjD,IAAI,KAAK,mNAAK,wCAAqC,CAAC,MAAM,EAAE,CAAC;wBACzD,OAAO,IAAI,CAAC;oBAChB,CAAC;oBACD,MAAM;gBACV,CAAC;YACD,KAAK,sPAAqC,CAAC,OAAO,CAAC;gBAAC,CAAC;oBACjD,IAAI,KAAK,mNAAK,wCAAqC,CAAC,MAAM,EAAE,CAAC;wBACzD,OAAO,IAAI,CAAC;oBAChB,CAAC;oBACD,MAAM;gBACV,CAAC;YACD,mNAAK,wCAAqC,CAAC,MAAM,CAAC;gBAAC,CAAC;oBAChD,IAAI,KAAK,mNAAK,wCAAqC,CAAC,OAAO,EAAE,CAAC;wBAC1D,OAAO,IAAI,CAAC;oBAChB,CAAC;oBACD,MAAM;gBACV,CAAC;YACD,mNAAK,wCAAqC,CAAC,MAAM,CAAC;gBAAC,CAAC;oBAChD,IAAI,KAAK,mNAAK,wCAAqC,CAAC,OAAO,EAAE,CAAC;wBAC1D,OAAO,IAAI,CAAC;oBAChB,CAAC;oBACD,MAAM;gBACV,CAAC;QACL,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAWD,IAAY,eAAe,GAAA;QACvB,OAAO,IAAI,CAAC,2BAA2B,CAAC;IAC5C,CAAC;IAED,IAAY,eAAe,CAAC,KAA4C,EAAA;YAKpE;QAJA,IAAI,IAAI,CAAC,2BAA2B,KAAK,KAAK,EAAE,CAAC;YAC7C,OAAO;QACX,CAAC;wDAEG,CAAC,kCAAkC,sHAAE,MAAM,EAAE,CAAC;QAClD,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAI,CAAF,CAAC,EAAK,CAAC,2BAA2B,GAAG,KAAK,CAAC,CAAC,CAAC;QACjF,IAAI,IAAI,CAAC,2BAA2B,EAAE,CAAC;YACnC,IAAI,CAAC,kCAAkC,GAAG,IAAI,CAAC,2BAA2B,CAAC,uBAAuB,CAAC,GAAG,CAAC,GAAG,EAAE;gBACxG,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC9B,CAAC,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAYD,cAAA,EAAgB,CAChB,IAAW,qBAAqB,GAAA;QAC5B,OAAO,IAAI,CAAC,iCAAiC,CAAC;IAClD,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,qBAAqB,CAAC,KAA4C,EAAA;;QACzE,IAAI,IAAI,CAAC,iCAAiC,KAAK,KAAK,EAAE,CAAC;YACnD,OAAO;QACX,CAAC;8DAEG,CAAC,wCAAwC,mFAA7C,+CAA+C,MAAM,EAAE,CAAC;QACxD,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAI,CAAF,CAAC,EAAK,CAAC,iCAAiC,GAAG,KAAK,CAAC,CAAC,CAAC;QACvF,IAAI,IAAI,CAAC,iCAAiC,EAAE,CAAC;YACzC,IAAI,CAAC,wCAAwC,GAAG,IAAI,CAAC,iCAAiC,CAAC,uBAAuB,CAAC,GAAG,CAAC,GAAG,EAAE;gBACpH,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC9B,CAAC,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAID,cAAA,EAAgB,CAChB,IAAW,2BAA2B,GAAA;QAClC,OAAO,IAAI,CAAC,uCAAuC,CAAC;IACxD,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,2BAA2B,CAAC,KAAsD,EAAA;QACzF,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAI,CAAF,CAAC,EAAK,CAAC,uCAAuC,GAAG,KAAK,CAAC,CAAC,CAAC;IACjG,CAAC;IAQD,cAAA,EAAgB,CAChB,IAAW,uBAAuB,GAAA;QAC9B,OAAO,IAAI,CAAC,mCAAmC,CAAC;IACpD,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,uBAAuB,CAAC,KAA4C,EAAA;YAK3E;QAJA,IAAI,IAAI,CAAC,mCAAmC,KAAK,KAAK,EAAE,CAAC;YACrD,OAAO;QACX,CAAC;gEAEG,CAAC,0CAA0C,sIAAE,MAAM,EAAE,CAAC;QAC1D,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAI,CAAF,CAAC,EAAK,CAAC,mCAAmC,GAAG,KAAK,CAAC,CAAC,CAAC;QACzF,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;QAC/B,IAAI,IAAI,CAAC,mCAAmC,EAAE,CAAC;YAC3C,IAAI,CAAC,0CAA0C,GAAG,IAAI,CAAC,mCAAmC,CAAC,uBAAuB,CAAC,GAAG,CAAC,GAAG,EAAE;gBACxH,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC9B,CAAC,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAaD,oCAAA,EAAsC,CACtC,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IA+BD;;OAEG,CACH,IAAW,uBAAuB,GAAA;QAC9B,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;YAC3B,OAAQ,IAAI,CAAC,WAA0B,CAAC,uBAAuB,CAAC;QACpE,CAAC;QAED,IAAI,CAAC,CAAC,IAAI,CAAC,8BAA8B,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YAClG,OAAO,IAAI,CAAC,eAAe,CAAC,uBAAuB,CAAC;QACxD,CAAC;QAED,OAAO,IAAI,CAAC,uBAAuB,CAAC;IACxC,CAAC;IAED;;OAEG,CACH,IAAW,sBAAsB,GAAA;QAC7B,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;YAC3B,OAAQ,IAAI,CAAC,WAA0B,CAAC,sBAAsB,CAAC;QACnE,CAAC;QAED,IAAI,CAAC,CAAC,IAAI,CAAC,8BAA8B,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YAClG,OAAO,IAAI,CAAC,eAAe,CAAC,sBAAsB,CAAC;QACvD,CAAC;QAED,OAAO,IAAI,CAAC,uBAAuB,CAAC;IACxC,CAAC;IAED,IAAW,sBAAsB,CAAC,KAAa,EAAA;QAC3C,IAAI,CAAC,uBAAuB,GAAG,KAAK,CAAC;IACzC,CAAC;IAED,gFAAA,EAAkF,CAClF,IAAW,SAAS,GAAA;QAChB,IAAI,IAAI,CAAC,uBAAuB,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,CAAC;YACtG,OAAO,IAAI,CAAC,IAAI,CAAC;QACrB,CAAC;QACD,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED;;OAEG,CACH,IAAW,IAAI,GAAA;QACX,IAAI,IAAI,CAAC,KAAK,mNAAK,wCAAqC,CAAC,UAAU,EAAE,CAAC;YAClE,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;gBAC3B,OAAQ,IAAI,CAAC,WAA0B,CAAC,IAAI,CAAC;YACjD,CAAC;YAED,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvB,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;YACrC,CAAC;YAED,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBAC/B,IAAI,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,CAAC;oBAC3C,IAAI,IAAI,CAAC,uBAAuB,CAAC,cAAe,CAAC,iBAAiB,IAAI,IAAI,CAAC,uBAAuB,CAAC,cAAe,CAAC,iBAAiB,CAAC,WAAW,EAAE,CAAC;wBAC/I,OAAO,IAAI,CAAC,uBAAuB,CAAC,cAAe,CAAC,iBAAiB,CAAC,IAAI,CAAC;oBAC/E,CAAC;oBACD,OAAO,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC;gBAC7C,CAAC;gBACD,IAAI,IAAI,CAAC,uBAAuB,CAAC,2BAA2B,EAAE,CAAC;oBAC3D,OAAO,IAAI,CAAC,uBAAuB,CAAC,2BAA2B,CAAC;gBACpE,CAAC;YACL,CAAC;YAED,IAAI,IAAI,CAAC,2BAA2B,EAAE,CAAC;gBACnC,OAAO,IAAI,CAAC,2BAA2B,CAAC;YAC5C,CAAC;QACL,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,mNAAK,wCAAqC,CAAC,YAAY,EAAE,CAAC;YACpE,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBAC7B,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,WAAW,IAAI,IAAI,CAAC,2BAA2B,EAAE,CAAC;oBAC9E,OAAO,IAAI,CAAC,2BAA2B,CAAC;gBAC5C,CAAC;gBACD,OAAO,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC;YAC3C,CAAC,MAAM,IAAI,IAAI,CAAC,2BAA2B,EAAE,CAAC;gBAC1C,OAAO,IAAI,CAAC,2BAA2B,CAAC;YAC5C,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,IAAW,IAAI,CAAC,KAA4C,EAAA;QACxD,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAI,CAAF,CAAC,EAAK,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC;IAC/D,CAAC;IAqCD,qDAAA,EAAuD,CACvD,IAAW,MAAM,GAAA;QACb,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YAC/C,OAAO,IAAI,CAAC,OAAO,CAAC;QACxB,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,sMAAK,2BAAwB,CAAC,iBAAiB,EAAE,CAAC;YAC9D,OAAO,IAAI,CAAC,OAAO,CAAC;QACxB,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,sMAAK,2BAAwB,CAAC,QAAQ,EAAE,CAAC;YAChE,OAAO,4NAAwB,CAAC,QAAQ,CAAC;QAC7C,CAAC;QAED,wMAAO,2BAAwB,CAAC,MAAM,CAAC;IAC3C,CAAC;IAED,IAAW,MAAM,CAAC,KAA+B,EAAA;QAC7C,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;IACzB,CAAC;IAED;;OAEG,CACH,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,cAAc,KAAK,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC;IAC7D,CAAC;IAED;;OAEG,CACH,IAAW,uBAAuB,GAAA;QAC9B,OAAO,IAAI,CAAC,cAAc,KAAK,IAAI,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,OAAO,CAAC;IAClF,CAAC;IAED;;OAEG,CACH,IAAW,iBAAiB,GAAA;QACxB,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAChC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,IAAI,CAAC,cAAe,CAAC,UAAwB,CAAC;IACzD,CAAC;IAED,kDAAA,EAAoD,CACpD,IAAW,cAAc,GAAA;QACrB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED,kDAAA,EAAoD,CACpD,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,0EAAA,EAA4E,CAC5E,IAAW,WAAW,GAAA;QAClB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YACxB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC;IAC3C,CAAC;IAED,yEAAA,EAA2E,CAC3E,IAAW,eAAe,GAAA;QACtB,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/B,OAAO,EAAE,CAAC;QACd,CAAC;QAED,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,UAAU,CAAC,CAAC;IACpD,CAAC;IAED,yCAAA,EAA2C,CAC3C,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED,sFAAA,EAAwF,CACxF,IAAW,YAAY,GAAA;QACnB,OAAO,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;IACzD,CAAC;IAED,kFAAA,EAAoF,CACpF,IAAW,iCAAiC,GAAA;QACxC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACrB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,UAAU,CAAE,CAAC;YACrC,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,sMAAK,2BAAwB,CAAC,MAAM,EAAE,CAAC;gBACjE,OAAO,IAAI,CAAC;YAChB,CAAC;YAED,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,sMAAK,2BAAwB,CAAC,OAAO,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,sMAAK,2BAAwB,CAAC,iBAAiB,EAAE,CAAC;gBAC/I,IAAI,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,iCAAiC,CAAC,EAAE,CAAC;oBAC/E,OAAO,IAAI,CAAC;gBAChB,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,qFAAA,EAAuF,CACvF,IAAW,yBAAyB,GAAA;QAChC,IAAI,IAAI,CAAC,MAAM,sMAAK,2BAAwB,CAAC,MAAM,EAAE,CAAC;YAClD,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACrB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,UAAU,CAAE,CAAC;YACrC,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,qMAAK,4BAAwB,CAAC,MAAM,EAAE,CAAC;gBACjE,OAAO,IAAI,CAAC;YAChB,CAAC;YAED,IAAI,QAAQ,CAAC,MAAM,sMAAK,2BAAwB,CAAC,MAAM,EAAE,CAAC;gBACtD,OAAO,IAAI,CAAC;YAChB,CAAC;YAED,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,sMAAK,2BAAwB,CAAC,OAAO,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,sMAAK,2BAAwB,CAAC,iBAAiB,EAAE,CAAC;gBAC/I,IAAI,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,yBAAyB,CAAC,EAAE,CAAC;oBACvE,OAAO,IAAI,CAAC;gBAChB,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,uFAAA,EAAyF,CACzF,IAAW,2BAA2B,GAAA;QAClC,IAAI,IAAI,CAAC,MAAM,KAAK,4NAAwB,CAAC,QAAQ,EAAE,CAAC;YACpD,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACrB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,UAAU,CAAE,CAAC;YACrC,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,sMAAK,2BAAwB,CAAC,QAAQ,EAAE,CAAC;gBACnE,OAAO,IAAI,CAAC;YAChB,CAAC;YAED,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,sMAAK,2BAAwB,CAAC,OAAO,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,sMAAK,2BAAwB,CAAC,iBAAiB,EAAE,CAAC;gBAC/I,IAAI,QAAQ,CAAC,UAAU,CAAC,2BAA2B,EAAE,EAAE,CAAC;oBACpD,OAAO,IAAI,CAAC;gBAChB,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG,CACI,sBAAsB,GAAA;QACzB,OAAO,IAAI,CAAC;IAChB,CAAC;IAcD;;;OAGG,CACI,YAAY,GAAA;QACf,OAAO,6BAA6B,CAAC;IACzC,CAAC;IAED;;;;OAIG,CACI,YAAY,CAAC,eAA4C,EAAA;QAC5D,OAAO,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,KAAA,EAAA,6DAAA,EAA8D,CAAC;IACvH,CAAC;IAED;;;;OAIG,CACI,uBAAuB,CAAC,eAA4C,EAAA;QACvE,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;QACpC,MAAM,UAAU,GAAG,eAAe,CAAC,UAAU,CAAC;QAE9C,IAAI,UAAU,CAAC,MAAM,sMAAK,2BAAwB,CAAC,QAAQ,EAAE,CAAC;YAC1D,uCAAuC;YAEvC,IAAI,UAAU,CAAC,MAAM,sMAAK,2BAAwB,CAAC,MAAM,EAAE,CAAC;gBACxD,OAAA,EAAA,qEAAA,GAAyE;YAC7E,CAAC;YAED,KAAK,MAAM,MAAM,IAAI,UAAU,CAAC,OAAO,CAAE,CAAC;gBACtC,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,qMAAI,2BAAwB,CAAC,OAAO,IAAI,MAAM,CAAC,yBAAyB,EAAE,CAAC;oBACnG,OAAA,EAAA,qEAAA,GAAyE;gBAC7E,CAAC;YACL,CAAC;QACL,CAAC;QAED,IAAI,IAAI,CAAC,IAAI,KAAK,eAAe,CAAC,IAAI,IAAI,eAAe,CAAC,SAAS,mNAAK,wCAAqC,CAAC,UAAU,EAAE,CAAC;YACvH,cAAc;YACd,IAAI,2BAA2B,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,EAAE,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC;gBAClF,OAAA,EAAA,6DAAA,GAAiE;YACrE,CAAC;YAED,iBAAiB;YACjB,IACI,AAAC,eAAe,CAAC,4BAA4B,IAAI,eAAe,CAAC,4BAA4B,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GACvH,eAAe,CAAC,4BAA4B,IAAI,2BAA2B,CAAC,kBAAkB,CAAC,eAAe,CAAC,4BAA4B,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAChK,CAAC;gBACC,OAAA,EAAA,6DAAA,GAAiE;YACrE,CAAC,MAAM,CAAC;gBACJ,OAAA,EAAA,mEAAA,GAAuE;YAC3E,CAAC;QACL,CAAC;QAED,WAAW;QACX,IAAI,eAAe,CAAC,4BAA4B,IAAI,eAAe,CAAC,4BAA4B,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YACzH,OAAA,EAAA,mEAAA,GAAuE;QAC3E,CAAC;QAED,kBAAkB;QAClB,IAAI,WAAW,GAAG,UAAU,CAAC;QAC7B,IAAI,WAAW,GAAG,UAAU,CAAC;QAC7B,IAAI,IAAI,CAAC,SAAS,KAAA,EAAA,8CAAA,EAA+C,GAAE,CAAC;YAChE,WAAW,GAAG,UAAU,CAAC;YACzB,WAAW,GAAG,UAAU,CAAC;QAC7B,CAAC;QAED,IAAI,WAAW,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE,CAAC;YAC1C,OAAA,EAAA,iEAAA,GAAqE;QACzE,CAAC;QAED,OAAA,EAAA,6DAAA,GAAiE;IACrE,CAAC;IAED;;;;;OAKG,CACI,SAAS,CAAC,eAA4C,EAA2B;gCAAzB,iBAAiB,gDAAG,KAAK;QACpF,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,EAAE,CAAC;YAC5D,4CAA4C;YAC5C,MAAM,wDAAiD,IAAI,CAAC,UAAU,CAAC,IAAI,EAAA,aAAK,IAAI,CAAC,IAAI,EAAA,eAAkD,OAApC,QAAmD,OAApC,CAAC,UAAU,CAAC,IAAI,EAAA,MAAyB,CAAE,CAAC,qBAAP,IAAI;QACnK,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACtC,eAAe,CAAC,eAAe,GAAG,IAAI,CAAC;QAEvC,IAAI,CAAC,8BAA8B,GAAG,KAAK,CAAC;QAE5C,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;QAC7D,eAAe,CAAC,sBAAsB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAE7D,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,cAAc,CAAC,QAAqC,EAAA;QACvD,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAEhD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACf,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACjC,QAAQ,CAAC,eAAe,GAAG,IAAI,CAAC;QAChC,IAAI,CAAC,8BAA8B,GAAG,KAAK,CAAC;QAC5C,QAAQ,CAAC,8BAA8B,GAAG,KAAK,CAAC;QAEhD,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QACzD,QAAQ,CAAC,yBAAyB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAEzD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG,CACI,0CAA0C,CAAC,IAAY,EAAA;QAC1D,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,MAAO,OAAO,iNAAG,wCAAqC,CAAC,GAAG,CAAE,CAAC;YACzD,IAAI,CAAC,CAAC,IAAI,GAAG,OAAO,CAAC,EAAE,CAAC;gBACpB,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACpD,CAAC;YACD,OAAO,GAAG,OAAO,IAAI,CAAC,CAAC;QAC3B,CAAC;IACL,CAAC;IAED;;;;OAIG,CACI,SAAS,GAAe;sBAAd,OAAO,0DAAG,IAAI;QAC3B,MAAM,mBAAmB,GAAQ,CAAA,CAAE,CAAC;QAEpC,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACrC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,mBAAmB,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACvD,CAAC;QAED,IAAI,OAAO,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACjC,mBAAmB,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC;YAC1C,mBAAmB,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,QAAQ,CAAC;YAC5E,mBAAmB,CAAC,oBAAoB,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;YACpE,mBAAmB,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAC5C,mBAAmB,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;QACvE,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,mBAAmB,IAAI,CAAC,EAAE,CAAC;YACzD,mBAAmB,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAC5C,mBAAmB,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;QACvE,CAAC;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;OAEG,CACI,OAAO,GAAA;QACV,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;QACpC,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;QACvC,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAC;QAErC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;QAClC,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;IACxC,CAAC;IAEO,yBAAyB,CAAC,MAAkB,EAAA;QAChD,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC;QAC/B,MAAM,EAAE,CAAC;QACT,IAAI,IAAI,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;YAC7B,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC9B,CAAC;IACL,CAAC;IAEO,kBAAkB,GAAA;QACtB,uBAAuB;QACvB,IAAI,IAAI,CAAC,gCAAgC,EAAE,CAAC;YACxC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,gCAAgC,GAAG,IAAI,CAAC;QAC7C,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxD,IAAI,CAAC,gCAAgC,GAAG,KAAK,CAAC;IAClD,CAAC;IAhND;;;;;OAKG,CACH,YAAmB,IAAY,EAAE,UAA6B,EAAE,SAA+C,CAAA;QAxb/G,cAAA,EAAgB,CACT,IAAA,CAAA,gBAAgB,GAAG,KAAK,CAAC;QAKxB,IAAA,CAAA,2BAA2B,GAA0C,IAAI,CAAC;QAqBjE,IAAA,CAAA,UAAU,GAAG,IAAI,KAAK,EAA+B,CAAC;QAIvE,cAAA,EAAgB,CACT,IAAA,CAAA,iBAAiB,GAA0C,IAAI,CAAC;QAE/D,IAAA,CAAA,iCAAiC,GAA0C,IAAI,CAAC;QAuBhF,IAAA,CAAA,uCAAuC,GAAoD,IAAI,CAAC;QAYxG,cAAA,EAAgB,CACT,IAAA,CAAA,iBAAiB,GAAG,KAAK,CAAC;QAEzB,IAAA,CAAA,mCAAmC,GAA0C,IAAI,CAAC;QAwB1F,cAAA,EAAgB,CACT,IAAA,CAAA,4BAA4B,GAA0C,IAAI,CAAC;QAE1E,IAAA,CAAA,KAAK,iNAAG,wCAAqC,CAAC,KAAK,CAAC;QAE5D,cAAA,EAAgB,CACT,IAAA,CAAA,8BAA8B,GAAG,KAAK,CAAC;QAE9C,cAAA,EAAgB,CACT,IAAA,CAAA,aAAa,GAAG,KAAK,CAAC;QAO7B,uGAAA,EAAyG,CAClG,IAAA,CAAA,2BAA2B,GAAY,KAAK,CAAC;QAEpD;;WAEG,CACI,IAAA,CAAA,4BAA4B,GAA4C,EAAE,CAAC;QAElF;;WAEG,CACI,IAAA,CAAA,4BAA4B,GAA4C,EAAE,CAAC;QAElF;;WAEG,CACa,IAAA,CAAA,sBAAsB,GAAG,iKAAI,aAAU,EAA+B,CAAC;QAEvF;;WAEG,CACa,IAAA,CAAA,yBAAyB,GAAG,iKAAI,aAAU,EAA+B,CAAC;QAE1F;;WAEG,CACa,IAAA,CAAA,uBAAuB,GAAG,gKAAI,cAAU,EAAyC,CAAC;QAC1F,IAAA,CAAA,gCAAgC,GAAG,KAAK,CAAC;QA2GjD;;WAEG,CACI,IAAA,CAAA,gBAAgB,GAAY,KAAK,CAAC;QAEzC;;WAEG,CACI,IAAA,CAAA,mBAAmB,GAAW,CAAC,CAAC,CAAC;QAOxC,cAAA,EAAgB,CACT,IAAA,CAAA,iBAAiB,GAAG,KAAK,CAAC;QAEzB,IAAA,CAAA,OAAO,oMAA6B,2BAAwB,CAAC,iBAAiB,CAAC;QAiLnF,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;IAChC,CAAC;CAuMJ", "debugId": null}}, {"offset": {"line": 1468, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterialBlock.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/nodeMaterialBlock.ts"], "sourcesContent": ["import { NodeMaterialBlockConnectionPointTypes } from \"./Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"./nodeMaterialBuildState\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { NodeMaterialConnectionPoint, NodeMaterialConnectionPointDirection } from \"./nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialBlockTargets } from \"./Enums/nodeMaterialBlockTargets\";\r\nimport type { Effect } from \"../effect\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport type { Mesh } from \"../../Meshes/mesh\";\r\nimport type { SubMesh } from \"../../Meshes/subMesh\";\r\nimport type { NodeMaterial, NodeMaterialDefines } from \"./nodeMaterial\";\r\nimport type { InputBlock } from \"./Blocks/Input/inputBlock\";\r\nimport { UniqueIdGenerator } from \"../../Misc/uniqueIdGenerator\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { GetClass } from \"../../Misc/typeStore\";\r\nimport type { EffectFallbacks } from \"../effectFallbacks\";\r\nimport { Logger } from \"core/Misc/logger\";\r\nimport { ShaderLanguage } from \"../shaderLanguage\";\r\nimport { Observable } from \"core/Misc/observable\";\r\nimport type { NodeMaterialTeleportOutBlock } from \"./Blocks/Teleport/teleportOutBlock\";\r\n\r\n/**\r\n * Defines a block that can be used inside a node based material\r\n */\r\nexport class NodeMaterialBlock {\r\n    private _buildId: number;\r\n    private _buildTarget: NodeMaterialBlockTargets;\r\n    protected _target: NodeMaterialBlockTargets;\r\n    private _isFinalMerger = false;\r\n    private _isInput = false;\r\n    private _isLoop = false;\r\n    private _isTeleportOut = false;\r\n    private _isTeleportIn = false;\r\n    private _name = \"\";\r\n    protected _isUnique = false;\r\n    protected _codeIsReady = true;\r\n    /** @internal */\r\n    public _isFinalOutput = false;\r\n\r\n    /** @internal */\r\n    public get _isFinalOutputAndActive() {\r\n        return this._isFinalOutput;\r\n    }\r\n\r\n    /** @internal */\r\n    public get _hasPrecedence() {\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Observable raised when the block code is ready (if the code loading is async)\r\n     */\r\n    public onCodeIsReadyObservable = new Observable<NodeMaterialBlock>();\r\n\r\n    /** Gets or sets a boolean indicating that only one input can be connected at a time */\r\n    public inputsAreExclusive = false;\r\n\r\n    /** @internal */\r\n    public _codeVariableName = \"\";\r\n\r\n    /** @internal */\r\n    public _inputs = new Array<NodeMaterialConnectionPoint>();\r\n    /** @internal */\r\n    public _outputs = new Array<NodeMaterialConnectionPoint>();\r\n\r\n    /** @internal */\r\n    public _preparationId: number;\r\n\r\n    /** @internal */\r\n    public readonly _originalTargetIsNeutral: boolean;\r\n\r\n    /**\r\n     * Gets the name of the block\r\n     */\r\n    public get name(): string {\r\n        return this._name;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating that this block has is code ready to be used\r\n     */\r\n    public get codeIsReady() {\r\n        return this._codeIsReady;\r\n    }\r\n\r\n    /**\r\n     * Sets the name of the block. Will check if the name is valid.\r\n     */\r\n    public set name(newName: string) {\r\n        if (!this.validateBlockName(newName)) {\r\n            return;\r\n        }\r\n\r\n        this._name = newName;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the unique id of the node\r\n     */\r\n    public uniqueId: number;\r\n\r\n    /**\r\n     * Gets or sets the comments associated with this block\r\n     */\r\n    public comments: string = \"\";\r\n\r\n    /**\r\n     * Gets a boolean indicating that this block can only be used once per NodeMaterial\r\n     */\r\n    public get isUnique() {\r\n        return this._isUnique;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating that this block is an end block (e.g. it is generating a system value)\r\n     */\r\n    public get isFinalMerger(): boolean {\r\n        return this._isFinalMerger;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating that this block is an input (e.g. it sends data to the shader)\r\n     */\r\n    public get isInput(): boolean {\r\n        return this._isInput;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if this block is a teleport out\r\n     */\r\n    public get isTeleportOut(): boolean {\r\n        return this._isTeleportOut;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if this block is a teleport in\r\n     */\r\n    public get isTeleportIn(): boolean {\r\n        return this._isTeleportIn;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if this block is a loop\r\n     */\r\n    public get isLoop(): boolean {\r\n        return this._isLoop;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the build Id\r\n     */\r\n    public get buildId(): number {\r\n        return this._buildId;\r\n    }\r\n\r\n    public set buildId(value: number) {\r\n        this._buildId = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the target of the block\r\n     */\r\n    public get target() {\r\n        return this._target;\r\n    }\r\n\r\n    public set target(value: NodeMaterialBlockTargets) {\r\n        if ((this._target & value) !== 0) {\r\n            return;\r\n        }\r\n        this._target = value;\r\n    }\r\n\r\n    /**\r\n     * Gets the list of input points\r\n     */\r\n    public get inputs(): NodeMaterialConnectionPoint[] {\r\n        return this._inputs;\r\n    }\r\n\r\n    /** Gets the list of output points */\r\n    public get outputs(): NodeMaterialConnectionPoint[] {\r\n        return this._outputs;\r\n    }\r\n\r\n    /**\r\n     * Find an input by its name\r\n     * @param name defines the name of the input to look for\r\n     * @returns the input or null if not found\r\n     */\r\n    public getInputByName(name: string) {\r\n        const filter = this._inputs.filter((e) => e.name === name);\r\n\r\n        if (filter.length) {\r\n            return filter[0];\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Find an output by its name\r\n     * @param name defines the name of the output to look for\r\n     * @returns the output or null if not found\r\n     */\r\n    public getOutputByName(name: string) {\r\n        const filter = this._outputs.filter((e) => e.name === name);\r\n\r\n        if (filter.length) {\r\n            return filter[0];\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /** Gets or sets a boolean indicating that this input can be edited in the Inspector (false by default) */\r\n    public visibleInInspector = false;\r\n\r\n    /** Gets or sets a boolean indicating that this input can be edited from a collapsed frame */\r\n    public visibleOnFrame = false;\r\n\r\n    /**\r\n     * Creates a new NodeMaterialBlock\r\n     * @param name defines the block name\r\n     * @param target defines the target of that block (Vertex by default)\r\n     * @param isFinalMerger defines a boolean indicating that this block is an end block (e.g. it is generating a system value). Default is false\r\n     * @param isFinalOutput defines a boolean indicating that this block is generating a final output and no other block should be generated after\r\n     */\r\n    public constructor(name: string, target = NodeMaterialBlockTargets.Vertex, isFinalMerger = false, isFinalOutput = false) {\r\n        this._target = target;\r\n        this._originalTargetIsNeutral = target === NodeMaterialBlockTargets.Neutral;\r\n        this._isFinalMerger = isFinalMerger;\r\n        this._isFinalOutput = isFinalOutput;\r\n        switch (this.getClassName()) {\r\n            case \"InputBlock\":\r\n                this._isInput = true;\r\n                break;\r\n            case \"NodeMaterialTeleportOutBlock\":\r\n                this._isTeleportOut = true;\r\n                break;\r\n            case \"NodeMaterialTeleportInBlock\":\r\n                this._isTeleportIn = true;\r\n                break;\r\n            case \"LoopBlock\":\r\n                this._isLoop = true;\r\n                break;\r\n        }\r\n\r\n        this._name = name;\r\n        this.uniqueId = UniqueIdGenerator.UniqueId;\r\n    }\r\n\r\n    /** @internal */\r\n    public _setInitialTarget(target: NodeMaterialBlockTargets): void {\r\n        this._target = target;\r\n        // marked as read only\r\n        // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\r\n        (this._originalTargetIsNeutral as boolean) = target === NodeMaterialBlockTargets.Neutral;\r\n    }\r\n\r\n    /**\r\n     * Initialize the block and prepare the context for build\r\n     * @param state defines the state that will be used for the build\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public initialize(state: NodeMaterialBuildState) {\r\n        // Do nothing\r\n    }\r\n\r\n    /**\r\n     * Bind data to effect. Will only be called for blocks with isBindable === true\r\n     * @param effect defines the effect to bind data to\r\n     * @param nodeMaterial defines the hosting NodeMaterial\r\n     * @param mesh defines the mesh that will be rendered\r\n     * @param subMesh defines the submesh that will be rendered\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public bind(effect: Effect, nodeMaterial: NodeMaterial, mesh?: Mesh, subMesh?: SubMesh) {\r\n        // Do nothing\r\n    }\r\n\r\n    protected _writeVariable(currentPoint: NodeMaterialConnectionPoint): string {\r\n        const connectionPoint = currentPoint.connectedPoint;\r\n\r\n        if (connectionPoint) {\r\n            return `${currentPoint.associatedVariableName}`;\r\n        }\r\n\r\n        return `0.`;\r\n    }\r\n\r\n    protected _writeFloat(value: number) {\r\n        let stringVersion = value.toString();\r\n\r\n        if (stringVersion.indexOf(\".\") === -1) {\r\n            stringVersion += \".0\";\r\n        }\r\n        return `${stringVersion}`;\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name e.g. \"NodeMaterialBlock\"\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"NodeMaterialBlock\";\r\n    }\r\n\r\n    /** Gets a boolean indicating that this connection will be used in the fragment shader\r\n     * @returns true if connected in fragment shader\r\n     */\r\n    public isConnectedInFragmentShader() {\r\n        return this.outputs.some((o) => o.isConnectedInFragmentShader);\r\n    }\r\n\r\n    /**\r\n     * Register a new input. Must be called inside a block constructor\r\n     * @param name defines the connection point name\r\n     * @param type defines the connection point type\r\n     * @param isOptional defines a boolean indicating that this input can be omitted\r\n     * @param target defines the target to use to limit the connection point (will be VertexAndFragment by default)\r\n     * @param point an already created connection point. If not provided, create a new one\r\n     * @returns the current block\r\n     */\r\n    public registerInput(\r\n        name: string,\r\n        type: NodeMaterialBlockConnectionPointTypes,\r\n        isOptional: boolean = false,\r\n        target?: NodeMaterialBlockTargets,\r\n        point?: NodeMaterialConnectionPoint\r\n    ) {\r\n        point = point ?? new NodeMaterialConnectionPoint(name, this, NodeMaterialConnectionPointDirection.Input);\r\n        point.type = type;\r\n        point.isOptional = isOptional;\r\n        if (target) {\r\n            point.target = target;\r\n        }\r\n\r\n        this._inputs.push(point);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Register a new output. Must be called inside a block constructor\r\n     * @param name defines the connection point name\r\n     * @param type defines the connection point type\r\n     * @param target defines the target to use to limit the connection point (will be VertexAndFragment by default)\r\n     * @param point an already created connection point. If not provided, create a new one\r\n     * @returns the current block\r\n     */\r\n    public registerOutput(name: string, type: NodeMaterialBlockConnectionPointTypes, target?: NodeMaterialBlockTargets, point?: NodeMaterialConnectionPoint) {\r\n        point = point ?? new NodeMaterialConnectionPoint(name, this, NodeMaterialConnectionPointDirection.Output);\r\n        point.type = type;\r\n        if (target) {\r\n            point.target = target;\r\n        }\r\n\r\n        this._outputs.push(point);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Will return the first available input e.g. the first one which is not an uniform or an attribute\r\n     * @param forOutput defines an optional connection point to check compatibility with\r\n     * @returns the first available input or null\r\n     */\r\n    public getFirstAvailableInput(forOutput: Nullable<NodeMaterialConnectionPoint> = null) {\r\n        for (const input of this._inputs) {\r\n            if (!input.connectedPoint) {\r\n                if (\r\n                    !forOutput ||\r\n                    forOutput.type === input.type ||\r\n                    input.type === NodeMaterialBlockConnectionPointTypes.AutoDetect ||\r\n                    input.acceptedConnectionPointTypes.indexOf(forOutput.type) !== -1\r\n                ) {\r\n                    return input;\r\n                }\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Will return the first available output e.g. the first one which is not yet connected and not a varying\r\n     * @param forBlock defines an optional block to check compatibility with\r\n     * @returns the first available input or null\r\n     */\r\n    public getFirstAvailableOutput(forBlock: Nullable<NodeMaterialBlock> = null) {\r\n        for (const output of this._outputs) {\r\n            if (!forBlock || !forBlock.target || forBlock.target === NodeMaterialBlockTargets.Neutral || (forBlock.target & output.target) !== 0) {\r\n                return output;\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets the sibling of the given output\r\n     * @param current defines the current output\r\n     * @returns the next output in the list or null\r\n     */\r\n    public getSiblingOutput(current: NodeMaterialConnectionPoint) {\r\n        const index = this._outputs.indexOf(current);\r\n\r\n        if (index === -1 || index >= this._outputs.length) {\r\n            return null;\r\n        }\r\n\r\n        return this._outputs[index + 1];\r\n    }\r\n\r\n    /**\r\n     * Checks if the current block is an ancestor of a given block\r\n     * @param block defines the potential descendant block to check\r\n     * @returns true if block is a descendant\r\n     */\r\n    public isAnAncestorOf(block: NodeMaterialBlock): boolean {\r\n        for (const output of this._outputs) {\r\n            if (!output.hasEndpoints) {\r\n                continue;\r\n            }\r\n\r\n            for (const endpoint of output.endpoints) {\r\n                if (endpoint.ownerBlock === block) {\r\n                    return true;\r\n                }\r\n                if (endpoint.ownerBlock.isAnAncestorOf(block)) {\r\n                    return true;\r\n                }\r\n            }\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Connect current block with another block\r\n     * @param other defines the block to connect with\r\n     * @param options define the various options to help pick the right connections\r\n     * @param options.input\r\n     * @param options.output\r\n     * @param options.outputSwizzle\r\n     * @returns the current block\r\n     */\r\n    public connectTo(\r\n        other: NodeMaterialBlock,\r\n        options?: {\r\n            input?: string;\r\n            output?: string;\r\n            outputSwizzle?: string;\r\n        }\r\n    ) {\r\n        if (this._outputs.length === 0) {\r\n            return;\r\n        }\r\n\r\n        let output = options && options.output ? this.getOutputByName(options.output) : this.getFirstAvailableOutput(other);\r\n\r\n        let notFound = true;\r\n        while (notFound) {\r\n            const input = options && options.input ? other.getInputByName(options.input) : other.getFirstAvailableInput(output);\r\n\r\n            if (output && input && output.canConnectTo(input)) {\r\n                output.connectTo(input);\r\n                notFound = false;\r\n            } else if (!output) {\r\n                // eslint-disable-next-line no-throw-literal\r\n                throw \"Unable to find a compatible match\";\r\n            } else {\r\n                output = this.getSiblingOutput(output);\r\n            }\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    protected _buildBlock(state: NodeMaterialBuildState) {\r\n        // Empty. Must be defined by child nodes\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    protected _postBuildBlock(state: NodeMaterialBuildState) {\r\n        // Empty. Must be defined by child nodes\r\n    }\r\n\r\n    /**\r\n     * Add uniforms, samplers and uniform buffers at compilation time\r\n     * @param state defines the state to update\r\n     * @param nodeMaterial defines the node material requesting the update\r\n     * @param defines defines the material defines to update\r\n     * @param uniformBuffers defines the list of uniform buffer names\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public updateUniformsAndSamples(state: NodeMaterialBuildState, nodeMaterial: NodeMaterial, defines: NodeMaterialDefines, uniformBuffers: string[]) {\r\n        // Do nothing\r\n    }\r\n\r\n    /**\r\n     * Add potential fallbacks if shader compilation fails\r\n     * @param fallbacks defines the current prioritized list of fallbacks\r\n     * @param mesh defines the mesh to be rendered\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public provideFallbacks(fallbacks: EffectFallbacks, mesh?: AbstractMesh) {\r\n        // Do nothing\r\n    }\r\n\r\n    /**\r\n     * Initialize defines for shader compilation\r\n     * @param defines defines the material defines to update\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public initializeDefines(defines: NodeMaterialDefines) {\r\n        // Do nothing\r\n    }\r\n\r\n    /**\r\n     * Update defines for shader compilation\r\n     * @param defines defines the material defines to update\r\n     * @param nodeMaterial defines the node material requesting the update\r\n     * @param mesh defines the mesh to be rendered\r\n     * @param useInstances specifies that instances should be used\r\n     * @param subMesh defines which submesh to render\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public prepareDefines(defines: NodeMaterialDefines, nodeMaterial: NodeMaterial, mesh?: AbstractMesh, useInstances: boolean = false, subMesh?: SubMesh) {\r\n        // Do nothing\r\n    }\r\n\r\n    /**\r\n     * Lets the block try to connect some inputs automatically\r\n     * @param material defines the hosting NodeMaterial\r\n     * @param additionalFilteringInfo optional additional filtering condition when looking for compatible blocks\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public autoConfigure(material: NodeMaterial, additionalFilteringInfo: (node: NodeMaterialBlock) => boolean = () => true) {\r\n        // Do nothing\r\n    }\r\n\r\n    /**\r\n     * Function called when a block is declared as repeatable content generator\r\n     * @param vertexShaderState defines the current compilation state for the vertex shader\r\n     * @param defines defines the material defines to update\r\n     * @param mesh defines the mesh to be rendered\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public replaceRepeatableContent(vertexShaderState: NodeMaterialBuildState, defines: NodeMaterialDefines, mesh?: AbstractMesh) {\r\n        // Do nothing\r\n    }\r\n\r\n    /** Gets a boolean indicating that the code of this block will be promoted to vertex shader even if connected to fragment output */\r\n    public get willBeGeneratedIntoVertexShaderFromFragmentShader(): boolean {\r\n        if (this.isInput || this.isFinalMerger) {\r\n            return false;\r\n        }\r\n\r\n        if (this._outputs.some((o) => o.isDirectlyConnectedToVertexOutput)) {\r\n            return false;\r\n        }\r\n\r\n        if (this.target === NodeMaterialBlockTargets.Vertex) {\r\n            return false;\r\n        }\r\n\r\n        if (this.target === NodeMaterialBlockTargets.VertexAndFragment || this.target === NodeMaterialBlockTargets.Neutral) {\r\n            if (this._outputs.some((o) => o.isConnectedInVertexShader)) {\r\n                return true;\r\n            }\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Checks if the block is ready\r\n     * @param mesh defines the mesh to be rendered\r\n     * @param nodeMaterial defines the node material requesting the update\r\n     * @param defines defines the material defines to update\r\n     * @param useInstances specifies that instances should be used\r\n     * @returns true if the block is ready\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public isReady(mesh: AbstractMesh, nodeMaterial: NodeMaterial, defines: NodeMaterialDefines, useInstances: boolean = false) {\r\n        return true;\r\n    }\r\n\r\n    protected _linkConnectionTypes(inputIndex0: number, inputIndex1: number, looseCoupling = false) {\r\n        if (looseCoupling) {\r\n            this._inputs[inputIndex1]._acceptedConnectionPointType = this._inputs[inputIndex0];\r\n        } else {\r\n            this._inputs[inputIndex0]._linkedConnectionSource = this._inputs[inputIndex1];\r\n            this._inputs[inputIndex0]._isMainLinkSource = true;\r\n        }\r\n        this._inputs[inputIndex1]._linkedConnectionSource = this._inputs[inputIndex0];\r\n    }\r\n\r\n    private _processBuild(block: NodeMaterialBlock, state: NodeMaterialBuildState, input: NodeMaterialConnectionPoint, activeBlocks: NodeMaterialBlock[]) {\r\n        block.build(state, activeBlocks);\r\n\r\n        const localBlockIsFragment = state._vertexState != null;\r\n        const otherBlockWasGeneratedInVertexShader = block._buildTarget === NodeMaterialBlockTargets.Vertex && block.target !== NodeMaterialBlockTargets.VertexAndFragment;\r\n\r\n        if (block.isTeleportOut && (block as NodeMaterialTeleportOutBlock).entryPoint?.isConnectedToUniform) {\r\n            // In that case, we skip the context switch as the teleport out block is connected to a uniform\r\n            return;\r\n        }\r\n\r\n        if (\r\n            localBlockIsFragment &&\r\n            ((block.target & block._buildTarget) === 0 ||\r\n                (block.target & input.target) === 0 ||\r\n                (this.target !== NodeMaterialBlockTargets.VertexAndFragment && otherBlockWasGeneratedInVertexShader))\r\n        ) {\r\n            // context switch! We need a varying\r\n            if (\r\n                (!block.isInput && state.target !== block._buildTarget) || // block was already emitted by vertex shader\r\n                (block.isInput && (block as InputBlock).isAttribute && !(block as InputBlock)._noContextSwitch) // block is an attribute\r\n            ) {\r\n                const connectedPoint = input.connectedPoint!;\r\n                if (state._vertexState._emitVaryingFromString(\"v_\" + connectedPoint.declarationVariableName, connectedPoint.type)) {\r\n                    const prefix = state.shaderLanguage === ShaderLanguage.WGSL ? \"vertexOutputs.\" : \"\";\r\n                    if (state.shaderLanguage === ShaderLanguage.WGSL && connectedPoint.type === NodeMaterialBlockConnectionPointTypes.Matrix) {\r\n                        // We can't pass a matrix as a varying in WGSL, so we need to split it into 4 vectors\r\n                        state._vertexState.compilationString += `${prefix}${\"v_\" + connectedPoint.declarationVariableName}_r0 = ${connectedPoint.associatedVariableName}[0];\\n`;\r\n                        state._vertexState.compilationString += `${prefix}${\"v_\" + connectedPoint.declarationVariableName}_r1 = ${connectedPoint.associatedVariableName}[1];\\n`;\r\n                        state._vertexState.compilationString += `${prefix}${\"v_\" + connectedPoint.declarationVariableName}_r2 = ${connectedPoint.associatedVariableName}[2];\\n`;\r\n                        state._vertexState.compilationString += `${prefix}${\"v_\" + connectedPoint.declarationVariableName}_r3 = ${connectedPoint.associatedVariableName}[3];\\n`;\r\n                    } else {\r\n                        state._vertexState.compilationString += `${prefix}${\"v_\" + connectedPoint.declarationVariableName} = ${connectedPoint.associatedVariableName};\\n`;\r\n                    }\r\n                }\r\n                const prefix = state.shaderLanguage === ShaderLanguage.WGSL && connectedPoint.type !== NodeMaterialBlockConnectionPointTypes.Matrix ? \"fragmentInputs.\" : \"\";\r\n                input.associatedVariableName = prefix + \"v_\" + connectedPoint.declarationVariableName;\r\n                input._enforceAssociatedVariableName = true;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Validates the new name for the block node.\r\n     * @param newName the new name to be given to the node.\r\n     * @returns false if the name is a reserve word, else true.\r\n     */\r\n    public validateBlockName(newName: string) {\r\n        const reservedNames: Array<string> = [\r\n            \"position\",\r\n            \"normal\",\r\n            \"tangent\",\r\n            \"particle_positionw\",\r\n            \"uv\",\r\n            \"uv2\",\r\n            \"uv3\",\r\n            \"uv4\",\r\n            \"uv5\",\r\n            \"uv6\",\r\n            \"position2d\",\r\n            \"particle_uv\",\r\n            \"postprocess_uv\",\r\n            \"matricesIndices\",\r\n            \"matricesWeights\",\r\n            \"world0\",\r\n            \"world1\",\r\n            \"world2\",\r\n            \"world3\",\r\n            \"particle_color\",\r\n            \"particle_texturemask\",\r\n        ];\r\n        for (const reservedName of reservedNames) {\r\n            if (newName === reservedName) {\r\n                return false;\r\n            }\r\n        }\r\n        return true;\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    protected _customBuildStep(state: NodeMaterialBuildState, activeBlocks: NodeMaterialBlock[]): void {\r\n        // Must be implemented by children\r\n    }\r\n\r\n    /**\r\n     * Compile the current node and generate the shader code\r\n     * @param state defines the current compilation state (uniforms, samplers, current string)\r\n     * @param activeBlocks defines the list of active blocks (i.e. blocks to compile)\r\n     * @returns true if already built\r\n     */\r\n    public build(state: NodeMaterialBuildState, activeBlocks: NodeMaterialBlock[]): boolean {\r\n        if (this._buildId === state.sharedData.buildId) {\r\n            return true;\r\n        }\r\n\r\n        if (!this.isInput) {\r\n            /** Prepare outputs */\r\n            for (const output of this._outputs) {\r\n                if (!output.associatedVariableName) {\r\n                    output.associatedVariableName = state._getFreeVariableName(output.name);\r\n                }\r\n            }\r\n        }\r\n\r\n        // Check if \"parent\" blocks are compiled\r\n        for (const input of this._inputs) {\r\n            if (!input.connectedPoint) {\r\n                if (!input.isOptional) {\r\n                    // Emit a warning\r\n                    state.sharedData.checks.notConnectedNonOptionalInputs.push(input);\r\n                }\r\n                continue;\r\n            }\r\n\r\n            if (this.target !== NodeMaterialBlockTargets.Neutral) {\r\n                if ((input.target & this.target) === 0) {\r\n                    continue;\r\n                }\r\n\r\n                if ((input.target & state.target) === 0) {\r\n                    continue;\r\n                }\r\n            }\r\n\r\n            const block = input.connectedPoint.ownerBlock;\r\n            if (block && block !== this) {\r\n                this._processBuild(block, state, input, activeBlocks);\r\n            }\r\n        }\r\n\r\n        this._customBuildStep(state, activeBlocks);\r\n\r\n        if (this._buildId === state.sharedData.buildId) {\r\n            return true; // Need to check again as inputs can be connected multiple time to this endpoint\r\n        }\r\n\r\n        // Logs\r\n        if (state.sharedData.verbose) {\r\n            Logger.Log(`${state.target === NodeMaterialBlockTargets.Vertex ? \"Vertex shader\" : \"Fragment shader\"}: Building ${this.name} [${this.getClassName()}]`);\r\n        }\r\n\r\n        // Checks final outputs\r\n        if (this.isFinalMerger) {\r\n            switch (state.target) {\r\n                case NodeMaterialBlockTargets.Vertex:\r\n                    state.sharedData.checks.emitVertex = true;\r\n                    break;\r\n                case NodeMaterialBlockTargets.Fragment:\r\n                    state.sharedData.checks.emitFragment = true;\r\n                    break;\r\n            }\r\n        }\r\n\r\n        if (!this.isInput && state.sharedData.emitComments) {\r\n            state.compilationString += `\\n//${this.name}\\n`;\r\n        }\r\n\r\n        this._buildBlock(state);\r\n\r\n        this._buildId = state.sharedData.buildId;\r\n        this._buildTarget = state.target;\r\n\r\n        // Compile connected blocks\r\n        for (const output of this._outputs) {\r\n            if (output._forPostBuild) {\r\n                continue;\r\n            }\r\n            if ((output.target & state.target) === 0) {\r\n                continue;\r\n            }\r\n\r\n            for (const endpoint of output.endpoints) {\r\n                const block = endpoint.ownerBlock;\r\n\r\n                if (block) {\r\n                    if (((block.target & state.target) !== 0 && activeBlocks.indexOf(block) !== -1) || state._terminalBlocks.has(block)) {\r\n                        this._processBuild(block, state, endpoint, activeBlocks);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        this._postBuildBlock(state);\r\n\r\n        // Compile post build connected blocks\r\n        for (const output of this._outputs) {\r\n            if (!output._forPostBuild) {\r\n                continue;\r\n            }\r\n            if ((output.target & state.target) === 0) {\r\n                continue;\r\n            }\r\n\r\n            for (const endpoint of output.endpoints) {\r\n                const block = endpoint.ownerBlock;\r\n\r\n                if (block && (block.target & state.target) !== 0 && activeBlocks.indexOf(block) !== -1) {\r\n                    this._processBuild(block, state, endpoint, activeBlocks);\r\n                }\r\n            }\r\n        }\r\n        return false;\r\n    }\r\n\r\n    protected _inputRename(name: string) {\r\n        return name;\r\n    }\r\n\r\n    protected _outputRename(name: string) {\r\n        return name;\r\n    }\r\n\r\n    protected _dumpPropertiesCode() {\r\n        const variableName = this._codeVariableName;\r\n        return `${variableName}.visibleInInspector = ${this.visibleInInspector};\\n${variableName}.visibleOnFrame = ${this.visibleOnFrame};\\n${variableName}.target = ${this.target};\\n`;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _dumpCode(uniqueNames: string[], alreadyDumped: NodeMaterialBlock[]) {\r\n        alreadyDumped.push(this);\r\n\r\n        // Get unique name\r\n        const nameAsVariableName = this.name.replace(/[^A-Za-z_]+/g, \"\");\r\n        this._codeVariableName = nameAsVariableName || `${this.getClassName()}_${this.uniqueId}`;\r\n\r\n        if (uniqueNames.indexOf(this._codeVariableName) !== -1) {\r\n            let index = 0;\r\n            do {\r\n                index++;\r\n                this._codeVariableName = nameAsVariableName + index;\r\n            } while (uniqueNames.indexOf(this._codeVariableName) !== -1);\r\n        }\r\n\r\n        uniqueNames.push(this._codeVariableName);\r\n\r\n        // Declaration\r\n        let codeString = `\\n// ${this.getClassName()}\\n`;\r\n        if (this.comments) {\r\n            codeString += `// ${this.comments}\\n`;\r\n        }\r\n        codeString += `var ${this._codeVariableName} = new BABYLON.${this.getClassName()}(\"${this.name}\");\\n`;\r\n\r\n        // Properties\r\n        codeString += this._dumpPropertiesCode();\r\n\r\n        // Inputs\r\n        for (const input of this.inputs) {\r\n            if (!input.isConnected) {\r\n                continue;\r\n            }\r\n\r\n            const connectedOutput = input.connectedPoint!;\r\n            const connectedBlock = connectedOutput.ownerBlock;\r\n\r\n            if (alreadyDumped.indexOf(connectedBlock) === -1) {\r\n                codeString += connectedBlock._dumpCode(uniqueNames, alreadyDumped);\r\n            }\r\n        }\r\n\r\n        // Outputs\r\n        for (const output of this.outputs) {\r\n            if (!output.hasEndpoints) {\r\n                continue;\r\n            }\r\n\r\n            for (const endpoint of output.endpoints) {\r\n                const connectedBlock = endpoint.ownerBlock;\r\n                if (connectedBlock && alreadyDumped.indexOf(connectedBlock) === -1) {\r\n                    codeString += connectedBlock._dumpCode(uniqueNames, alreadyDumped);\r\n                }\r\n            }\r\n        }\r\n\r\n        return codeString;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _dumpCodeForOutputConnections(alreadyDumped: NodeMaterialBlock[]) {\r\n        let codeString = \"\";\r\n\r\n        if (alreadyDumped.indexOf(this) !== -1) {\r\n            return codeString;\r\n        }\r\n\r\n        alreadyDumped.push(this);\r\n\r\n        for (const input of this.inputs) {\r\n            if (!input.isConnected) {\r\n                continue;\r\n            }\r\n\r\n            const connectedOutput = input.connectedPoint!;\r\n            const connectedBlock = connectedOutput.ownerBlock;\r\n\r\n            codeString += connectedBlock._dumpCodeForOutputConnections(alreadyDumped);\r\n            codeString += `${connectedBlock._codeVariableName}.${connectedBlock._outputRename(connectedOutput.name)}.connectTo(${this._codeVariableName}.${this._inputRename(\r\n                input.name\r\n            )});\\n`;\r\n        }\r\n\r\n        return codeString;\r\n    }\r\n\r\n    /**\r\n     * Clone the current block to a new identical block\r\n     * @param scene defines the hosting scene\r\n     * @param rootUrl defines the root URL to use to load textures and relative dependencies\r\n     * @returns a copy of the current block\r\n     */\r\n    public clone(scene: Scene, rootUrl: string = \"\") {\r\n        const serializationObject = this.serialize();\r\n\r\n        const blockType = GetClass(serializationObject.customType);\r\n        if (blockType) {\r\n            const block: NodeMaterialBlock = new blockType();\r\n            block._deserialize(serializationObject, scene, rootUrl);\r\n\r\n            return block;\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Serializes this block in a JSON representation\r\n     * @returns the serialized block object\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject: any = {};\r\n        serializationObject.customType = \"BABYLON.\" + this.getClassName();\r\n        serializationObject.id = this.uniqueId;\r\n        serializationObject.name = this.name;\r\n        serializationObject.comments = this.comments;\r\n        serializationObject.visibleInInspector = this.visibleInInspector;\r\n        serializationObject.visibleOnFrame = this.visibleOnFrame;\r\n        serializationObject.target = this.target;\r\n\r\n        serializationObject.inputs = [];\r\n        serializationObject.outputs = [];\r\n\r\n        for (const input of this.inputs) {\r\n            serializationObject.inputs.push(input.serialize());\r\n        }\r\n\r\n        for (const output of this.outputs) {\r\n            serializationObject.outputs.push(output.serialize(false));\r\n        }\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public _deserialize(serializationObject: any, scene: Scene, rootUrl: string, urlRewriter?: (url: string) => string) {\r\n        this.name = serializationObject.name;\r\n        this.comments = serializationObject.comments;\r\n        this.visibleInInspector = !!serializationObject.visibleInInspector;\r\n        this.visibleOnFrame = !!serializationObject.visibleOnFrame;\r\n        this._target = serializationObject.target ?? this.target;\r\n        this._deserializePortDisplayNamesAndExposedOnFrame(serializationObject);\r\n    }\r\n\r\n    private _deserializePortDisplayNamesAndExposedOnFrame(serializationObject: any) {\r\n        const serializedInputs = serializationObject.inputs;\r\n        const serializedOutputs = serializationObject.outputs;\r\n        if (serializedInputs) {\r\n            for (let i = 0; i < serializedInputs.length; i++) {\r\n                const port = serializedInputs[i];\r\n\r\n                if (port.displayName) {\r\n                    this.inputs[i].displayName = port.displayName;\r\n                }\r\n                if (port.isExposedOnFrame) {\r\n                    this.inputs[i].isExposedOnFrame = port.isExposedOnFrame;\r\n                    this.inputs[i].exposedPortPosition = port.exposedPortPosition;\r\n                }\r\n            }\r\n        }\r\n        if (serializedOutputs) {\r\n            for (let i = 0; i < serializedOutputs.length; i++) {\r\n                const port = serializedOutputs[i];\r\n                if (port.displayName) {\r\n                    this.outputs[i].displayName = port.displayName;\r\n                }\r\n                if (port.isExposedOnFrame) {\r\n                    this.outputs[i].isExposedOnFrame = port.isExposedOnFrame;\r\n                    this.outputs[i].exposedPortPosition = port.exposedPortPosition;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Release resources\r\n     */\r\n    public dispose() {\r\n        this.onCodeIsReadyObservable.clear();\r\n\r\n        for (const input of this.inputs) {\r\n            input.dispose();\r\n        }\r\n\r\n        for (const output of this.outputs) {\r\n            output.dispose();\r\n        }\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,qCAAqC,EAAE,MAAM,+CAA+C,CAAC;AAGtG,OAAO,EAAE,2BAA2B,EAAwC,MAAM,oCAAoC,CAAC;AACvH,OAAO,EAAE,wBAAwB,EAAE,MAAM,kCAAkC,CAAC;AAO5E,OAAO,EAAE,iBAAiB,EAAE,MAAM,8BAA8B,CAAC;AAEjE,OAAO,EAAE,QAAQ,EAAE,MAAM,sBAAsB,CAAC;AAEhD,OAAO,EAAE,MAAM,EAAE,6BAAyB;AAE1C,OAAO,EAAE,UAAU,EAAE,iCAA6B;;;;;;;;AAM5C,MAAO,iBAAiB;IAe1B,cAAA,EAAgB,CAChB,IAAW,uBAAuB,GAAA;QAC9B,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,cAAc,GAAA;QACrB,OAAO,KAAK,CAAC;IACjB,CAAC;IAwBD;;OAEG,CACH,IAAW,IAAI,GAAA;QACX,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED;;OAEG,CACH,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;OAEG,CACH,IAAW,IAAI,CAAC,OAAe,EAAA;QAC3B,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,CAAC;YACnC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC;IACzB,CAAC;IAYD;;OAEG,CACH,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;OAEG,CACH,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED;;OAEG,CACH,IAAW,OAAO,GAAA;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED;;OAEG,CACH,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED;;OAEG,CACH,IAAW,YAAY,GAAA;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;OAEG,CACH,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED;;OAEG,CACH,IAAW,OAAO,GAAA;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,IAAW,OAAO,CAAC,KAAa,EAAA;QAC5B,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;IAC1B,CAAC;IAED;;OAEG,CACH,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,IAAW,MAAM,CAAC,KAA+B,EAAA;QAC7C,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;YAC/B,OAAO;QACX,CAAC;QACD,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;IACzB,CAAC;IAED;;OAEG,CACH,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,mCAAA,EAAqC,CACrC,IAAW,OAAO,GAAA;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED;;;;OAIG,CACI,cAAc,CAAC,IAAY,EAAA;QAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;QAE3D,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAChB,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,eAAe,CAAC,IAAY,EAAA;QAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;QAE5D,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAChB,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAuCD,cAAA,EAAgB,CACT,iBAAiB,CAAC,MAAgC,EAAA;QACrD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,sBAAsB;QACtB,4EAA4E;QAC3E,IAAI,CAAC,wBAAoC,GAAG,MAAM,sMAAK,2BAAwB,CAAC,OAAO,CAAC;IAC7F,CAAC;IAED;;;OAGG,CACH,6DAA6D;IACtD,UAAU,CAAC,KAA6B,EAAA;IAC3C,aAAa;IACjB,CAAC;IAED;;;;;;OAMG,CACH,6DAA6D;IACtD,IAAI,CAAC,MAAc,EAAE,YAA0B,EAAE,IAAW,EAAE,OAAiB,EAAA;IAClF,aAAa;IACjB,CAAC;IAES,cAAc,CAAC,YAAyC,EAAA;QAC9D,MAAM,eAAe,GAAG,YAAY,CAAC,cAAc,CAAC;QAEpD,IAAI,eAAe,EAAE,CAAC;YAClB,OAAO,GAAsC,CAAE,CAAC,KAAtC,YAAY,CAAC,sBAAsB;QACjD,CAAC;QAED,OAAO,GAAI,CAAC;IAChB,CAAC;IAES,WAAW,CAAC,KAAa,EAAA;QAC/B,IAAI,aAAa,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;QAErC,IAAI,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YACpC,aAAa,IAAI,IAAI,CAAC;QAC1B,CAAC;QACD,OAAO,GAAgB,CAAE,CAAC,KAAhB,aAAa;IAC3B,CAAC;IAED;;;OAGG,CACI,YAAY,GAAA;QACf,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;OAEG,CACI,2BAA2B,GAAA;QAC9B,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAG,CAAC,AAAF,CAAG,2BAA2B,CAAC,CAAC;IACnE,CAAC;IAED;;;;;;;;OAQG,CACI,aAAa,CAChB,IAAY,EACZ,IAA2C,EAGR;yBAFnC,iEAAsB,KAAK,EAC3B,MAAiC,iDACjC,KAAmC;QAEnC,KAAK,wCAAG,KAAK,GAAI,oMAAI,8BAA2B,CAAC,IAAI,EAAE,IAAI,EAAA,EAAA,8CAAA,GAA6C,CAAC;QACzG,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;QAClB,KAAK,CAAC,UAAU,GAAG,UAAU,CAAC;QAC9B,IAAI,MAAM,EAAE,CAAC;YACT,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;QAC1B,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEzB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;OAOG,CACI,cAAc,CAAC,IAAY,EAAE,IAA2C,EAAE,MAAiC,EAAE,KAAmC,EAAA;QACnJ,KAAK,wCAAG,KAAK,GAAI,oMAAI,8BAA2B,CAAC,IAAI,EAAE,IAAI,EAAA,EAAA,+CAAA,GAA8C,CAAC;QAC1G,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;QAClB,IAAI,MAAM,EAAE,CAAC;YACT,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;QAC1B,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAE1B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,sBAAsB,GAAwD;wBAAvD,iEAAmD,IAAI;QACjF,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,OAAO,CAAE,CAAC;YAC/B,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;gBACxB,IACI,CAAC,SAAS,IACV,SAAS,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,IAC7B,KAAK,CAAC,IAAI,mNAAK,wCAAqC,CAAC,UAAU,IAC/D,KAAK,CAAC,4BAA4B,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EACnE,CAAC;oBACC,OAAO,KAAK,CAAC;gBACjB,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,uBAAuB,GAA6C;uBAA5C,iEAAwC,IAAI;QACvE,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAE,CAAC;YACjC,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,MAAM,sMAAK,2BAAwB,CAAC,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;gBACnI,OAAO,MAAM,CAAC;YAClB,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,gBAAgB,CAAC,OAAoC,EAAA;QACxD,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAE7C,IAAI,KAAK,KAAK,CAAC,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;YAChD,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;IACpC,CAAC;IAED;;;;OAIG,CACI,cAAc,CAAC,KAAwB,EAAA;QAC1C,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAE,CAAC;YACjC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;gBACvB,SAAS;YACb,CAAC;YAED,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,SAAS,CAAE,CAAC;gBACtC,IAAI,QAAQ,CAAC,UAAU,KAAK,KAAK,EAAE,CAAC;oBAChC,OAAO,IAAI,CAAC;gBAChB,CAAC;gBACD,IAAI,QAAQ,CAAC,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC5C,OAAO,IAAI,CAAC;gBAChB,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;;;;OAQG,CACI,SAAS,CACZ,KAAwB,EACxB,OAIC,EAAA;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC7B,OAAO;QACX,CAAC;QAED,IAAI,MAAM,GAAG,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC;QAEpH,IAAI,QAAQ,GAAG,IAAI,CAAC;QACpB,MAAO,QAAQ,CAAE,CAAC;YACd,MAAM,KAAK,GAAG,OAAO,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;YAEpH,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;gBAChD,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACxB,QAAQ,GAAG,KAAK,CAAC;YACrB,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;gBACjB,4CAA4C;gBAC5C,MAAM,mCAAmC,CAAC;YAC9C,CAAC,MAAM,CAAC;gBACJ,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;YAC3C,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,6DAA6D;IACnD,WAAW,CAAC,KAA6B,EAAA;IAC/C,wCAAwC;IAC5C,CAAC;IAED,6DAA6D;IACnD,eAAe,CAAC,KAA6B,EAAA;IACnD,wCAAwC;IAC5C,CAAC;IAED;;;;;;OAMG,CACH,6DAA6D;IACtD,wBAAwB,CAAC,KAA6B,EAAE,YAA0B,EAAE,OAA4B,EAAE,cAAwB,EAAA;IAC7I,aAAa;IACjB,CAAC;IAED;;;;OAIG,CACH,6DAA6D;IACtD,gBAAgB,CAAC,SAA0B,EAAE,IAAmB,EAAA;IACnE,aAAa;IACjB,CAAC;IAED;;;OAGG,CACH,6DAA6D;IACtD,iBAAiB,CAAC,OAA4B,EAAA;IACjD,aAAa;IACjB,CAAC;IAED;;;;;;;OAOG,CACH,6DAA6D;IACtD,cAAc,CAAC,OAA4B,EAAE,YAA0B,EAAE,IAAmB,EAAkD;2BAAhD,iEAAwB,KAAK,EAAE,OAAiB;IACjJ,aAAa;IACjB,CAAC;IAED;;;;OAIG,CACH,6DAA6D;IACtD,aAAa,CAAC,QAAsB,EAA4E;sCAA1E,iEAAgE,GAAG,CAAG,CAAD,GAAK;IACnH,aAAa;IACjB,CAAC;IAED;;;;;OAKG,CACH,6DAA6D;IACtD,wBAAwB,CAAC,iBAAyC,EAAE,OAA4B,EAAE,IAAmB,EAAA;IACxH,aAAa;IACjB,CAAC;IAED,iIAAA,EAAmI,CACnI,IAAW,iDAAiD,GAAA;QACxD,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrC,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,iCAAiC,CAAC,EAAE,CAAC;YACjE,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,sMAAK,2BAAwB,CAAC,MAAM,EAAE,CAAC;YAClD,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,sMAAK,2BAAwB,CAAC,iBAAiB,IAAI,IAAI,CAAC,MAAM,sMAAK,2BAAwB,CAAC,OAAO,EAAE,CAAC;YACjH,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,yBAAyB,CAAC,EAAE,CAAC;gBACzD,OAAO,IAAI,CAAC;YAChB,CAAC;QACL,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;;;OAOG,CACH,6DAA6D;IACtD,OAAO,CAAC,IAAkB,EAAE,YAA0B,EAAE,OAA4B,EAA+B;2BAA7B,iEAAwB,KAAK;QACtH,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,oBAAoB,CAAC,WAAmB,EAAE,WAAmB,EAAuB;4BAArB,aAAa,oDAAG,KAAK;QAC1F,IAAI,aAAa,EAAE,CAAC;YAChB,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,4BAA4B,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QACvF,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,uBAAuB,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YAC9E,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,iBAAiB,GAAG,IAAI,CAAC;QACvD,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,uBAAuB,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;IAClF,CAAC;IAEO,aAAa,CAAC,KAAwB,EAAE,KAA6B,EAAE,KAAkC,EAAE,YAAiC,EAAA;;QAChJ,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;QAEjC,MAAM,oBAAoB,GAAG,KAAK,CAAC,YAAY,IAAI,IAAI,CAAC;QACxD,MAAM,oCAAoC,GAAG,KAAK,CAAC,YAAY,sMAAK,2BAAwB,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,sMAAK,2BAAwB,CAAC,iBAAiB,CAAC;QAEnK,IAAI,KAAK,CAAC,aAAa,gCAA4C,UAAU,sDAAjD,KAAsC,aAAa,oBAAoB,GAAE,CAAC;YAClG,+FAA+F;YAC/F,OAAO;QACX,CAAC;QAED,IACI,oBAAoB,IACpB,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,IACtC,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,IAClC,IAAI,CAAC,MAAM,sMAAK,2BAAwB,CAAC,iBAAiB,IAAI,oCAAoC,AAAC,CAAC,EAC3G,CAAC;YACC,oCAAoC;YACpC,IACI,AAAC,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,MAAM,KAAK,KAAK,CAAC,YAAY,CAAC,GACtD,CAD0D,IACrD,CAAC,OAAO,IAAK,KAAoB,CAAC,WAAW,IAAI,CAAE,KAAoB,CAAC,CAD0B,eACV,CAAC,AAAC,wBAAwB;cAC1H,CAAC;gBACC,MAAM,cAAc,GAAG,KAAK,CAAC,cAAe,CAAC;gBAC7C,IAAI,KAAK,CAAC,YAAY,CAAC,sBAAsB,CAAC,IAAI,GAAG,cAAc,CAAC,uBAAuB,EAAE,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC;oBAChH,MAAM,MAAM,GAAG,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,CAAC,CAAC,EAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC;oBACpF,IAAI,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,KAAI,cAAc,CAAC,IAAI,mNAAK,wCAAqC,CAAC,MAAM,EAAE,CAAC;wBACvH,qFAAqF;wBACrF,KAAK,CAAC,YAAY,CAAC,iBAAiB,IAAI,GAAY,IAAI,GAAb,MAAM,gBAAU,cAAc,CAAC,uBAAuB,EAAA,UAA8C,OAArC,cAAc,CAAC,sBAAsB,EAAA,OAAQ,CAAC;wBACxJ,KAAK,CAAC,YAAY,CAAC,iBAAiB,IAAI,UAAG,MAAM,EAAyD,OAAtD,IAAI,GAAG,AAA6D,cAA/C,CAAC,uBAAuB,EAAA,UAA8C,sBAAtB,sBAAsB,EAAA,OAAQ,CAAC;wBACxJ,KAAK,CAAC,YAAY,CAAC,iBAAiB,IAAI,UAAG,MAAM,EAAyD,OAAtD,IAAI,GAAgE,AAA7D,cAAc,CAAC,uBAAuB,EAAA,UAA8C,sBAAtB,sBAAsB,EAAA,OAAQ,CAAC;wBACxJ,KAAK,CAAC,YAAY,CAAC,iBAAiB,IAAI,UAAG,MAAM,EAAyD,OAAtD,IAAI,GAAG,AAA6D,cAA/C,CAAC,uBAAuB,EAAA,UAA8C,sBAAtB,sBAAsB,EAAA,OAAQ,CAAC;oBAC5J,CAAC,MAAM,CAAC;wBACJ,KAAK,CAAC,YAAY,CAAC,iBAAiB,IAAI,UAAG,MAAM,SAAG,IAAI,GAAG,cAAc,CAAC,uBAAuB,EAAA,OAA2C,IAAK,CAAC,EAA3C,cAAc,CAAC,sBAAsB,EAAA;oBAChJ,CAAC;gBACL,CAAC;gBACD,MAAM,MAAM,GAAG,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,KAAI,cAAc,CAAC,IAAI,mNAAK,wCAAqC,CAAC,MAAM,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC7J,KAAK,CAAC,sBAAsB,GAAG,MAAM,GAAG,IAAI,GAAG,cAAc,CAAC,uBAAuB,CAAC;gBACtF,KAAK,CAAC,8BAA8B,GAAG,IAAI,CAAC;YAChD,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;OAIG,CACI,iBAAiB,CAAC,OAAe,EAAA;QACpC,MAAM,aAAa,GAAkB;YACjC,UAAU;YACV,QAAQ;YACR,SAAS;YACT,oBAAoB;YACpB,IAAI;YACJ,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,YAAY;YACZ,aAAa;YACb,gBAAgB;YAChB,iBAAiB;YACjB,iBAAiB;YACjB,QAAQ;YACR,QAAQ;YACR,QAAQ;YACR,QAAQ;YACR,gBAAgB;YAChB,sBAAsB;SACzB,CAAC;QACF,KAAK,MAAM,YAAY,IAAI,aAAa,CAAE,CAAC;YACvC,IAAI,OAAO,KAAK,YAAY,EAAE,CAAC;gBAC3B,OAAO,KAAK,CAAC;YACjB,CAAC;QACL,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,6DAA6D;IACnD,gBAAgB,CAAC,KAA6B,EAAE,YAAiC,EAAA;IACvF,kCAAkC;IACtC,CAAC;IAED;;;;;OAKG,CACI,KAAK,CAAC,KAA6B,EAAE,YAAiC,EAAA;QACzE,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;YAC7C,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAChB,oBAAA,EAAsB,CACtB,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAE,CAAC;gBACjC,IAAI,CAAC,MAAM,CAAC,sBAAsB,EAAE,CAAC;oBACjC,MAAM,CAAC,sBAAsB,GAAG,KAAK,CAAC,oBAAoB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBAC5E,CAAC;YACL,CAAC;QACL,CAAC;QAED,wCAAwC;QACxC,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,OAAO,CAAE,CAAC;YAC/B,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;gBACxB,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;oBACpB,iBAAiB;oBACjB,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,6BAA6B,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACtE,CAAC;gBACD,SAAS;YACb,CAAC;YAED,IAAI,IAAI,CAAC,MAAM,sMAAK,2BAAwB,CAAC,OAAO,EAAE,CAAC;gBACnD,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;oBACrC,SAAS;gBACb,CAAC;gBAED,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;oBACtC,SAAS;gBACb,CAAC;YACL,CAAC;YAED,MAAM,KAAK,GAAG,KAAK,CAAC,cAAc,CAAC,UAAU,CAAC;YAC9C,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;gBAC1B,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC;YAC1D,CAAC;QACL,CAAC;QAED,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;QAE3C,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;YAC7C,OAAO,IAAI,CAAC,CAAC,gFAAgF;QACjG,CAAC;QAED,OAAO;QACP,IAAI,KAAK,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;qKAC3B,SAAM,CAAC,GAAG,CAAC,GAAuG,OAApG,KAAK,CAAC,MAAM,sMAAK,2BAAwB,CAAC,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,iBAAiB,EAAA,eAA4B,WAAV,CAAC,IAAI,EAAA,MAAwB,WAAf,CAAC,YAAY,EAAE,EAAA,EAAG,CAAC,CAAC;QAC5J,CAAC;QAED,uBAAuB;QACvB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,OAAQ,KAAK,CAAC,MAAM,EAAE,CAAC;gBACnB,sMAAK,2BAAwB,CAAC,MAAM;oBAChC,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC;oBAC1C,MAAM;gBACV,sMAAK,2BAAwB,CAAC,QAAQ;oBAClC,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC;oBAC5C,MAAM;YACd,CAAC;QACL,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;YACjD,KAAK,CAAC,iBAAiB,IAAI,OAAgB,OAAT,IAAI,CAAC,IAAI,EAAA,GAAI,CAAC;QACpD,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAExB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC;QACzC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC;QAEjC,2BAA2B;QAC3B,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAE,CAAC;YACjC,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;gBACvB,SAAS;YACb,CAAC;YACD,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;gBACvC,SAAS;YACb,CAAC;YAED,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,SAAS,CAAE,CAAC;gBACtC,MAAM,KAAK,GAAG,QAAQ,CAAC,UAAU,CAAC;gBAElC,IAAI,KAAK,EAAE,CAAC;oBACR,IAAI,AAAC,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,GAAI,KAAK,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;wBAClH,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;oBAC7D,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;QAED,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QAE5B,sCAAsC;QACtC,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAE,CAAC;YACjC,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;gBACxB,SAAS;YACb,CAAC;YACD,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;gBACvC,SAAS;YACb,CAAC;YAED,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,SAAS,CAAE,CAAC;gBACtC,MAAM,KAAK,GAAG,QAAQ,CAAC,UAAU,CAAC;gBAElC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;oBACrF,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;gBAC7D,CAAC;YACL,CAAC;QACL,CAAC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAES,YAAY,CAAC,IAAY,EAAA;QAC/B,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,aAAa,CAAC,IAAY,EAAA;QAChC,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,mBAAmB,GAAA;QACzB,MAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAC5C,OAAO,UAAG,YAAY,EAAA,0BAAsD,OAA7B,IAAI,CAAC,AAAoC,kBAAlB,EAAA,4BAAkB,6BAAqB,IAAI,CAAC,cAAc,EAAA,cAAM,YAAY,EAAA,cAAwB,OAAX,IAAI,CAAC,MAAM,EAAA,IAAK,CAAC;IACpL,CAAC;IAED;;OAEG,CACI,SAAS,CAAC,WAAqB,EAAE,aAAkC,EAAA;QACtE,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEzB,kBAAkB;QAClB,MAAM,kBAAkB,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;QACjE,IAAI,CAAC,iBAAiB,GAAG,kBAAkB,IAAI,UAAG,IAAI,CAAC,YAAY,EAAE,EAAA,KAAiB,CAAE,CAAC,KAAhB,IAAI,CAAC,QAAQ;QAEtF,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YACrD,IAAI,KAAK,GAAG,CAAC,CAAC;YACd,GAAG,CAAC;gBACA,KAAK,EAAE,CAAC;gBACR,IAAI,CAAC,iBAAiB,GAAG,kBAAkB,GAAG,KAAK,CAAC;YACxD,CAAC,OAAQ,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAE;QACjE,CAAC;QAED,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAEzC,cAAc;QACd,IAAI,UAAU,GAAG,QAA2B,OAAnB,IAAI,CAAC,YAAY,EAAE,EAAA,GAAI,CAAC;QACjD,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,UAAU,IAAI,MAAmB,OAAb,IAAI,CAAC,QAAQ,EAAA,GAAI,CAAC;QAC1C,CAAC;QACD,UAAU,IAAI,cAAO,IAAI,CAAC,iBAAiB,EAAA,mBAA0C,OAAxB,IAAI,CAAC,YAAY,EAAE,EAAA,MAAc,WAAL,CAAC,IAAI,EAAA,MAAO,CAAC;QAEtG,aAAa;QACb,UAAU,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAEzC,SAAS;QACT,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,CAAE,CAAC;YAC9B,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;gBACrB,SAAS;YACb,CAAC;YAED,MAAM,eAAe,GAAG,KAAK,CAAC,cAAe,CAAC;YAC9C,MAAM,cAAc,GAAG,eAAe,CAAC,UAAU,CAAC;YAElD,IAAI,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;gBAC/C,UAAU,IAAI,cAAc,CAAC,SAAS,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;YACvE,CAAC;QACL,CAAC;QAED,UAAU;QACV,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,CAAE,CAAC;YAChC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;gBACvB,SAAS;YACb,CAAC;YAED,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,SAAS,CAAE,CAAC;gBACtC,MAAM,cAAc,GAAG,QAAQ,CAAC,UAAU,CAAC;gBAC3C,IAAI,cAAc,IAAI,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;oBACjE,UAAU,IAAI,cAAc,CAAC,SAAS,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;gBACvE,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;OAEG,CACI,6BAA6B,CAAC,aAAkC,EAAA;QACnE,IAAI,UAAU,GAAG,EAAE,CAAC;QAEpB,IAAI,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YACrC,OAAO,UAAU,CAAC;QACtB,CAAC;QAED,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEzB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,CAAE,CAAC;YAC9B,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;gBACrB,SAAS;YACb,CAAC;YAED,MAAM,eAAe,GAAG,KAAK,CAAC,cAAe,CAAC;YAC9C,MAAM,cAAc,GAAG,eAAe,CAAC,UAAU,CAAC;YAElD,UAAU,IAAI,cAAc,CAAC,6BAA6B,CAAC,aAAa,CAAC,CAAC;YAC1E,UAAU,IAAI,UAAG,cAAc,CAAC,iBAAiB,EAAA,YAAI,cAAc,CAAC,aAAa,CAAC,eAAe,CAAC,IAAI,CAAC,EAAA,sBAAc,IAAI,CAAC,iBAAiB,EAAA,KAE1I,OAF8I,IAAI,CAAC,YAAY,CAC5J,KAAK,CAAC,IAAI,CACb,EAAA,KAAM,CAAC;QACZ,CAAC;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;;;OAKG,CACI,KAAK,CAAC,KAAY,EAAsB;sBAApB,iEAAkB,EAAE;QAC3C,MAAM,mBAAmB,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAE7C,MAAM,SAAS,mKAAG,WAAA,AAAQ,EAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;QAC3D,IAAI,SAAS,EAAE,CAAC;YACZ,MAAM,KAAK,GAAsB,IAAI,SAAS,EAAE,CAAC;YACjD,KAAK,CAAC,YAAY,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;YAExD,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG,CACI,SAAS,GAAA;QACZ,MAAM,mBAAmB,GAAQ,CAAA,CAAE,CAAC;QACpC,mBAAmB,CAAC,UAAU,GAAG,UAAU,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAClE,mBAAmB,CAAC,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC;QACvC,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACrC,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC7C,mBAAmB,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QACjE,mBAAmB,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;QACzD,mBAAmB,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAEzC,mBAAmB,CAAC,MAAM,GAAG,EAAE,CAAC;QAChC,mBAAmB,CAAC,OAAO,GAAG,EAAE,CAAC;QAEjC,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,CAAE,CAAC;YAC9B,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;QACvD,CAAC;QAED,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,CAAE,CAAC;YAChC,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;QAC9D,CAAC;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;OAEG,CACH,6DAA6D;IACtD,YAAY,CAAC,mBAAwB,EAAE,KAAY,EAAE,OAAe,EAAE,WAAqC,EAAA;QAC9G,IAAI,CAAC,IAAI,GAAG,mBAAmB,CAAC,IAAI,CAAC;QACrC,IAAI,CAAC,QAAQ,GAAG,mBAAmB,CAAC,QAAQ,CAAC;QAC7C,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC,mBAAmB,CAAC,kBAAkB,CAAC;QACnE,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,mBAAmB,CAAC,cAAc,CAAC;YAC5C,mBAAmB;QAAlC,IAAI,CAAC,OAAO,sDAAuB,MAAM,qFAAI,IAAI,CAAC,MAAM,CAAC;QACzD,IAAI,CAAC,6CAA6C,CAAC,mBAAmB,CAAC,CAAC;IAC5E,CAAC;IAEO,6CAA6C,CAAC,mBAAwB,EAAA;QAC1E,MAAM,gBAAgB,GAAG,mBAAmB,CAAC,MAAM,CAAC;QACpD,MAAM,iBAAiB,GAAG,mBAAmB,CAAC,OAAO,CAAC;QACtD,IAAI,gBAAgB,EAAE,CAAC;YACnB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;gBAC/C,MAAM,IAAI,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;gBAEjC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;oBACnB,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;gBAClD,CAAC;gBACD,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACxB,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;oBACxD,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;gBAClE,CAAC;YACL,CAAC;QACL,CAAC;QACD,IAAI,iBAAiB,EAAE,CAAC;YACpB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;gBAChD,MAAM,IAAI,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC;gBAClC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;oBACnB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;gBACnD,CAAC;gBACD,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACxB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;oBACzD,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;gBACnE,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG,CACI,OAAO,GAAA;QACV,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAC;QAErC,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,CAAE,CAAC;YAC9B,KAAK,CAAC,OAAO,EAAE,CAAC;QACpB,CAAC;QAED,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,CAAE,CAAC;YAChC,MAAM,CAAC,OAAO,EAAE,CAAC;QACrB,CAAC;IACL,CAAC;IAvxBD;;;;;;OAMG,CACH,YAAmB,IAAY,EAAE,MAAM,GAAG,4NAAwB,CAAC,MAAM,EAAE,aAAa,GAAG,KAAK,EAAE,aAAa,GAAG,KAAK,CAAA;QAxM/G,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QACvB,IAAA,CAAA,QAAQ,GAAG,KAAK,CAAC;QACjB,IAAA,CAAA,OAAO,GAAG,KAAK,CAAC;QAChB,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QACvB,IAAA,CAAA,aAAa,GAAG,KAAK,CAAC;QACtB,IAAA,CAAA,KAAK,GAAG,EAAE,CAAC;QACT,IAAA,CAAA,SAAS,GAAG,KAAK,CAAC;QAClB,IAAA,CAAA,YAAY,GAAG,IAAI,CAAC;QAC9B,cAAA,EAAgB,CACT,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QAY9B;;WAEG,CACI,IAAA,CAAA,uBAAuB,GAAG,iKAAI,aAAU,EAAqB,CAAC;QAErE,qFAAA,EAAuF,CAChF,IAAA,CAAA,kBAAkB,GAAG,KAAK,CAAC;QAElC,cAAA,EAAgB,CACT,IAAA,CAAA,iBAAiB,GAAG,EAAE,CAAC;QAE9B,cAAA,EAAgB,CACT,IAAA,CAAA,OAAO,GAAG,IAAI,KAAK,EAA+B,CAAC;QAC1D,cAAA,EAAgB,CACT,IAAA,CAAA,QAAQ,GAAG,IAAI,KAAK,EAA+B,CAAC;QAsC3D;;WAEG,CACI,IAAA,CAAA,QAAQ,GAAW,EAAE,CAAC;QA+G7B,wGAAA,EAA0G,CACnG,IAAA,CAAA,kBAAkB,GAAG,KAAK,CAAC;QAElC,2FAAA,EAA6F,CACtF,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QAU1B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,wBAAwB,GAAG,MAAM,qMAAK,4BAAwB,CAAC,OAAO,CAAC;QAC5E,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,OAAQ,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;YAC1B,KAAK,YAAY;gBACb,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;gBACrB,MAAM;YACV,KAAK,8BAA8B;gBAC/B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;gBAC3B,MAAM;YACV,KAAK,6BAA6B;gBAC9B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;gBAC1B,MAAM;YACV,KAAK,WAAW;gBACZ,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;gBACpB,MAAM;QACd,CAAC;QAED,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,QAAQ,uKAAG,oBAAiB,CAAC,QAAQ,CAAC;IAC/C,CAAC;CA2vBJ", "debugId": null}}, {"offset": {"line": 2250, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterial.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/nodeMaterial.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport type { NodeMaterialBlock } from \"./nodeMaterialBlock\";\r\nimport { PushMaterial } from \"../pushMaterial\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport { Matrix, Vector2 } from \"../../Maths/math.vector\";\r\nimport { Color3, Color4 } from \"../../Maths/math.color\";\r\nimport type { Mesh } from \"../../Meshes/mesh\";\r\nimport { NodeMaterialBuildState } from \"./nodeMaterialBuildState\";\r\nimport type { IEffectCreationOptions } from \"../effect\";\r\nimport { Effect } from \"../effect\";\r\nimport type { BaseTexture } from \"../../Materials/Textures/baseTexture\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport { NodeMaterialBlockTargets } from \"./Enums/nodeMaterialBlockTargets\";\r\nimport { NodeMaterialBuildStateSharedData } from \"./nodeMaterialBuildStateSharedData\";\r\nimport type { SubMesh } from \"../../Meshes/subMesh\";\r\nimport { MaterialDefines } from \"../../Materials/materialDefines\";\r\nimport type { NodeMaterialOptimizer } from \"./Optimizers/nodeMaterialOptimizer\";\r\nimport type { ImageProcessingConfiguration } from \"../imageProcessingConfiguration\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { VertexBuffer } from \"../../Buffers/buffer\";\r\nimport { Tools } from \"../../Misc/tools\";\r\nimport { SfeModeDefine } from \"./Blocks/Fragment/smartFilterFragmentOutputBlock\";\r\nimport { TransformBlock } from \"./Blocks/transformBlock\";\r\nimport { VertexOutputBlock } from \"./Blocks/Vertex/vertexOutputBlock\";\r\nimport { FragmentOutputBlock } from \"./Blocks/Fragment/fragmentOutputBlock\";\r\nimport { InputBlock } from \"./Blocks/Input/inputBlock\";\r\nimport { GetClass, RegisterClass } from \"../../Misc/typeStore\";\r\nimport { serialize } from \"../../Misc/decorators\";\r\nimport { SerializationHelper } from \"../../Misc/decorators.serialization\";\r\nimport type { TextureBlock } from \"./Blocks/Dual/textureBlock\";\r\nimport type { ReflectionTextureBaseBlock } from \"./Blocks/Dual/reflectionTextureBaseBlock\";\r\nimport type { RefractionBlock } from \"./Blocks/PBR/refractionBlock\";\r\nimport { CurrentScreenBlock } from \"./Blocks/Dual/currentScreenBlock\";\r\nimport { ParticleTextureBlock } from \"./Blocks/Particle/particleTextureBlock\";\r\nimport { ParticleRampGradientBlock } from \"./Blocks/Particle/particleRampGradientBlock\";\r\nimport { ParticleBlendMultiplyBlock } from \"./Blocks/Particle/particleBlendMultiplyBlock\";\r\nimport { EffectFallbacks } from \"../effectFallbacks\";\r\nimport { WebRequest } from \"../../Misc/webRequest\";\r\nimport type { PostProcessOptions } from \"../../PostProcesses/postProcess\";\r\nimport { PostProcess } from \"../../PostProcesses/postProcess\";\r\nimport { Constants } from \"../../Engines/constants\";\r\nimport type { Camera } from \"../../Cameras/camera\";\r\nimport { VectorMergerBlock } from \"./Blocks/vectorMergerBlock\";\r\nimport { RemapBlock } from \"./Blocks/remapBlock\";\r\nimport { MultiplyBlock } from \"./Blocks/multiplyBlock\";\r\nimport { NodeMaterialModes } from \"./Enums/nodeMaterialModes\";\r\nimport { Texture } from \"../Textures/texture\";\r\nimport type { IParticleSystem } from \"../../Particles/IParticleSystem\";\r\nimport { BaseParticleSystem } from \"../../Particles/baseParticleSystem\";\r\nimport { ColorSplitterBlock } from \"./Blocks/colorSplitterBlock\";\r\nimport { TimingTools } from \"../../Misc/timingTools\";\r\nimport { ProceduralTexture } from \"../Textures/Procedurals/proceduralTexture\";\r\nimport { AnimatedInputBlockTypes } from \"./Blocks/Input/animatedInputBlockTypes\";\r\nimport { TrigonometryBlock, TrigonometryBlockOperations } from \"./Blocks/trigonometryBlock\";\r\nimport { NodeMaterialSystemValues } from \"./Enums/nodeMaterialSystemValues\";\r\nimport type { ImageSourceBlock } from \"./Blocks/Dual/imageSourceBlock\";\r\nimport { EngineStore } from \"../../Engines/engineStore\";\r\nimport type { Material } from \"../material\";\r\nimport type { TriPlanarBlock } from \"./Blocks/triPlanarBlock\";\r\nimport type { BiPlanarBlock } from \"./Blocks/biPlanarBlock\";\r\nimport type { PrePassRenderer } from \"../../Rendering/prePassRenderer\";\r\nimport type { PrePassTextureBlock } from \"./Blocks/Input/prePassTextureBlock\";\r\nimport type { PrePassOutputBlock } from \"./Blocks/Fragment/prePassOutputBlock\";\r\nimport type { NodeMaterialTeleportOutBlock } from \"./Blocks/Teleport/teleportOutBlock\";\r\nimport type { NodeMaterialTeleportInBlock } from \"./Blocks/Teleport/teleportInBlock\";\r\nimport { Logger } from \"core/Misc/logger\";\r\nimport { PrepareDefinesForCamera, PrepareDefinesForPrePass } from \"../materialHelper.functions\";\r\nimport type { IImageProcessingConfigurationDefines } from \"../imageProcessingConfiguration.defines\";\r\nimport { ShaderLanguage } from \"../shaderLanguage\";\r\nimport { AbstractEngine } from \"../../Engines/abstractEngine\";\r\nimport type { LoopBlock } from \"./Blocks/loopBlock\";\r\nimport { MaterialHelperGeometryRendering } from \"../materialHelper.geometryrendering\";\r\n\r\nconst onCreatedEffectParameters = { effect: null as unknown as Effect, subMesh: null as unknown as Nullable<SubMesh> };\r\n\r\n// declare NODEEDITOR namespace for compilation issue\r\ndeclare let NODEEDITOR: any;\r\ndeclare let BABYLON: any;\r\n\r\n/**\r\n * Interface used to configure the node material editor\r\n */\r\nexport interface INodeMaterialEditorOptions {\r\n    /** Define the URL to load node editor script from */\r\n    editorURL?: string;\r\n    /** Additional configuration for the NME */\r\n    nodeEditorConfig?: {\r\n        backgroundColor?: Color4;\r\n    };\r\n}\r\n\r\n/** @internal */\r\nexport class NodeMaterialDefines extends MaterialDefines implements IImageProcessingConfigurationDefines {\r\n    /** Normal */\r\n    public NORMAL = false;\r\n    /** Tangent */\r\n    public TANGENT = false;\r\n    /** Vertex color */\r\n    public VERTEXCOLOR_NME = false;\r\n    /**  Uv1 **/\r\n    public UV1 = false;\r\n    /** Uv2 **/\r\n    public UV2 = false;\r\n    /** Uv3 **/\r\n    public UV3 = false;\r\n    /** Uv4 **/\r\n    public UV4 = false;\r\n    /** Uv5 **/\r\n    public UV5 = false;\r\n    /** Uv6 **/\r\n    public UV6 = false;\r\n\r\n    /** Prepass **/\r\n    public PREPASS = false;\r\n    /** Prepass normal */\r\n    public PREPASS_NORMAL = false;\r\n    /** Prepass normal index */\r\n    public PREPASS_NORMAL_INDEX = -1;\r\n    /** Prepass world normal */\r\n    public PREPASS_WORLD_NORMAL = false;\r\n    /** Prepass world normal index */\r\n    public PREPASS_WORLD_NORMAL_INDEX = -1;\r\n    /** Prepass position */\r\n    public PREPASS_POSITION = false;\r\n    /** Prepass position index */\r\n    public PREPASS_POSITION_INDEX = -1;\r\n    /** Prepass local position */\r\n    public PREPASS_LOCAL_POSITION = false;\r\n    /** Prepass local position index */\r\n    public PREPASS_LOCAL_POSITION_INDEX = -1;\r\n    /** Prepass depth */\r\n    public PREPASS_DEPTH = false;\r\n    /** Prepass depth index */\r\n    public PREPASS_DEPTH_INDEX = -1;\r\n    /** Clip-space depth */\r\n    public PREPASS_SCREENSPACE_DEPTH = false;\r\n    /** Clip-space depth index */\r\n    public PREPASS_SCREENSPACE_DEPTH_INDEX = -1;\r\n    /** Scene MRT count */\r\n    public SCENE_MRT_COUNT = 0;\r\n\r\n    /** BONES */\r\n    public NUM_BONE_INFLUENCERS = 0;\r\n    /** Bones per mesh */\r\n    public BonesPerMesh = 0;\r\n    /** Using texture for bone storage */\r\n    public BONETEXTURE = false;\r\n\r\n    /** MORPH TARGETS */\r\n    public MORPHTARGETS = false;\r\n    /** Morph target position */\r\n    public MORPHTARGETS_POSITION = false;\r\n    /** Morph target normal */\r\n    public MORPHTARGETS_NORMAL = false;\r\n    /** Morph target tangent */\r\n    public MORPHTARGETS_TANGENT = false;\r\n    /** Morph target uv */\r\n    public MORPHTARGETS_UV = false;\r\n    /** Morph target uv2 */\r\n    public MORPHTARGETS_UV2 = false;\r\n    public MORPHTARGETS_COLOR = false;\r\n    /** Morph target support positions */\r\n    public MORPHTARGETTEXTURE_HASPOSITIONS = false;\r\n    /** Morph target support normals */\r\n    public MORPHTARGETTEXTURE_HASNORMALS = false;\r\n    /** Morph target support tangents */\r\n    public MORPHTARGETTEXTURE_HASTANGENTS = false;\r\n    /** Morph target support uvs */\r\n    public MORPHTARGETTEXTURE_HASUVS = false;\r\n    /** Morph target support uv2s */\r\n    public MORPHTARGETTEXTURE_HASUV2S = false;\r\n    public MORPHTARGETTEXTURE_HASCOLORS = false;\r\n    /** Number of morph influencers */\r\n    public NUM_MORPH_INFLUENCERS = 0;\r\n    /** Using a texture to store morph target data */\r\n    public MORPHTARGETS_TEXTURE = false;\r\n\r\n    /** IMAGE PROCESSING */\r\n    public IMAGEPROCESSING = false;\r\n    /** Vignette */\r\n    public VIGNETTE = false;\r\n    /** Multiply blend mode for vignette */\r\n    public VIGNETTEBLENDMODEMULTIPLY = false;\r\n    /** Opaque blend mode for vignette */\r\n    public VIGNETTEBLENDMODEOPAQUE = false;\r\n    /** Tone mapping */\r\n    public TONEMAPPING = 0;\r\n    /** Contrast */\r\n    public CONTRAST = false;\r\n    /** Exposure */\r\n    public EXPOSURE = false;\r\n    /** Color curves */\r\n    public COLORCURVES = false;\r\n    /** Color grading */\r\n    public COLORGRADING = false;\r\n    /** 3D color grading */\r\n    public COLORGRADING3D = false;\r\n    /** Sampler green depth */\r\n    public SAMPLER3DGREENDEPTH = false;\r\n    /** Sampler for BGR map */\r\n    public SAMPLER3DBGRMAP = false;\r\n    /** Dithering */\r\n    public DITHER = false;\r\n    /** Using post process for image processing */\r\n    public IMAGEPROCESSINGPOSTPROCESS = false;\r\n    /** Skip color clamp */\r\n    public SKIPFINALCOLORCLAMP = false;\r\n\r\n    /** MISC. */\r\n    public BUMPDIRECTUV = 0;\r\n    /** Camera is orthographic */\r\n    public CAMERA_ORTHOGRAPHIC = false;\r\n    /** Camera is perspective */\r\n    public CAMERA_PERSPECTIVE = false;\r\n\r\n    public AREALIGHTSUPPORTED = true;\r\n\r\n    public AREALIGHTNOROUGHTNESS = true;\r\n\r\n    /**\r\n     * Creates a new NodeMaterialDefines\r\n     */\r\n    constructor() {\r\n        super();\r\n        this.rebuild();\r\n    }\r\n\r\n    /**\r\n     * Set the value of a specific key\r\n     * @param name defines the name of the key to set\r\n     * @param value defines the value to set\r\n     * @param markAsUnprocessedIfDirty Flag to indicate to the cache that this value needs processing\r\n     */\r\n    public setValue(name: string, value: any, markAsUnprocessedIfDirty = false) {\r\n        if (this[name] === undefined) {\r\n            this._keys.push(name);\r\n        }\r\n\r\n        if (markAsUnprocessedIfDirty && this[name] !== value) {\r\n            this.markAsUnprocessed();\r\n        }\r\n\r\n        this[name] = value;\r\n    }\r\n}\r\n\r\n/**\r\n * Class used to configure NodeMaterial\r\n */\r\nexport interface INodeMaterialOptions {\r\n    /**\r\n     * Defines if blocks should emit comments\r\n     */\r\n    emitComments: boolean;\r\n    /** Defines shader language to use (default to GLSL) */\r\n    shaderLanguage: ShaderLanguage;\r\n}\r\n\r\n/**\r\n * Blocks that manage a texture\r\n */\r\nexport type NodeMaterialTextureBlocks =\r\n    | TextureBlock\r\n    | ReflectionTextureBaseBlock\r\n    | RefractionBlock\r\n    | CurrentScreenBlock\r\n    | ParticleTextureBlock\r\n    | ImageSourceBlock\r\n    | TriPlanarBlock\r\n    | BiPlanarBlock\r\n    | PrePassTextureBlock;\r\n\r\n/**\r\n * Class used to create a node based material built by assembling shader blocks\r\n */\r\nexport class NodeMaterial extends PushMaterial {\r\n    private static _BuildIdGenerator: number = 0;\r\n    private _options: INodeMaterialOptions;\r\n    private _vertexCompilationState: NodeMaterialBuildState;\r\n    private _fragmentCompilationState: NodeMaterialBuildState;\r\n    private _sharedData: NodeMaterialBuildStateSharedData;\r\n    private _buildId: number = NodeMaterial._BuildIdGenerator++;\r\n    private _buildWasSuccessful = false;\r\n    private _cachedWorldViewMatrix = new Matrix();\r\n    private _cachedWorldViewProjectionMatrix = new Matrix();\r\n    private _optimizers = new Array<NodeMaterialOptimizer>();\r\n    private _animationFrame = -1;\r\n    private _buildIsInProgress = false;\r\n\r\n    /** Define the Url to load node editor script */\r\n    public static EditorURL = `${Tools._DefaultCdnUrl}/v${AbstractEngine.Version}/nodeEditor/babylon.nodeEditor.js`;\r\n\r\n    /** Define the Url to load snippets */\r\n    public static SnippetUrl = Constants.SnippetUrl;\r\n\r\n    /** Gets or sets a boolean indicating that node materials should not deserialize textures from json / snippet content */\r\n    public static IgnoreTexturesAtLoadTime = false;\r\n\r\n    /** Defines default shader language when no option is defined */\r\n    public static DefaultShaderLanguage = ShaderLanguage.GLSL;\r\n\r\n    /** If true, the node material will use GLSL if the engine is WebGL and WGSL if it's WebGPU. It takes priority over DefaultShaderLanguage if it's true */\r\n    public static UseNativeShaderLanguageOfEngine = false;\r\n\r\n    /**\r\n     * Checks if a block is a texture block\r\n     * @param block The block to check\r\n     * @returns True if the block is a texture block\r\n     */\r\n    public static _BlockIsTextureBlock(block: NodeMaterialBlock): block is NodeMaterialTextureBlocks {\r\n        return (\r\n            block.getClassName() === \"TextureBlock\" ||\r\n            block.getClassName() === \"ReflectionTextureBaseBlock\" ||\r\n            block.getClassName() === \"ReflectionTextureBlock\" ||\r\n            block.getClassName() === \"ReflectionBlock\" ||\r\n            block.getClassName() === \"RefractionBlock\" ||\r\n            block.getClassName() === \"CurrentScreenBlock\" ||\r\n            block.getClassName() === \"SmartFilterTextureBlock\" ||\r\n            block.getClassName() === \"ParticleTextureBlock\" ||\r\n            block.getClassName() === \"ImageSourceBlock\" ||\r\n            block.getClassName() === \"TriPlanarBlock\" ||\r\n            block.getClassName() === \"BiPlanarBlock\" ||\r\n            block.getClassName() === \"PrePassTextureBlock\"\r\n        );\r\n    }\r\n\r\n    private BJSNODEMATERIALEDITOR = this._getGlobalNodeMaterialEditor();\r\n\r\n    /** @internal */\r\n    public _useAdditionalColor = false;\r\n\r\n    public override set _glowModeEnabled(value: boolean) {\r\n        this._useAdditionalColor = value;\r\n    }\r\n\r\n    /** Get the inspector from bundle or global\r\n     * @returns the global NME\r\n     */\r\n    private _getGlobalNodeMaterialEditor(): any {\r\n        // UMD Global name detection from Webpack Bundle UMD Name.\r\n        if (typeof NODEEDITOR !== \"undefined\") {\r\n            return NODEEDITOR;\r\n        }\r\n\r\n        // In case of module let's check the global emitted from the editor entry point.\r\n        if (typeof BABYLON !== \"undefined\" && typeof BABYLON.NodeEditor !== \"undefined\") {\r\n            return BABYLON;\r\n        }\r\n\r\n        return undefined;\r\n    }\r\n\r\n    /** Gets or sets the active shader language */\r\n    public override get shaderLanguage(): ShaderLanguage {\r\n        return this._options?.shaderLanguage || NodeMaterial.DefaultShaderLanguage;\r\n    }\r\n\r\n    public override set shaderLanguage(value: ShaderLanguage) {\r\n        this._options.shaderLanguage = value;\r\n    }\r\n\r\n    /**\r\n     * Snippet ID if the material was created from the snippet server\r\n     */\r\n    public snippetId: string;\r\n\r\n    /**\r\n     * Gets or sets data used by visual editor\r\n     * @see https://nme.babylonjs.com\r\n     */\r\n    public editorData: any = null;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating that alpha value must be ignored (This will turn alpha blending off even if an alpha value is produced by the material)\r\n     */\r\n    @serialize()\r\n    public ignoreAlpha = false;\r\n\r\n    /**\r\n     * Defines the maximum number of lights that can be used in the material\r\n     */\r\n    @serialize()\r\n    public maxSimultaneousLights = 4;\r\n\r\n    /**\r\n     * Observable raised when the material is built\r\n     */\r\n    public onBuildObservable = new Observable<NodeMaterial>();\r\n\r\n    /**\r\n     * Observable raised when an error is detected\r\n     */\r\n    public onBuildErrorObservable = new Observable<string>();\r\n\r\n    /**\r\n     * Gets or sets the root nodes of the material vertex shader\r\n     */\r\n    public _vertexOutputNodes = new Array<NodeMaterialBlock>();\r\n\r\n    /**\r\n     * Gets or sets the root nodes of the material fragment (pixel) shader\r\n     */\r\n    public _fragmentOutputNodes = new Array<NodeMaterialBlock>();\r\n\r\n    /** Gets or sets options to control the node material overall behavior */\r\n    public get options() {\r\n        return this._options;\r\n    }\r\n\r\n    public set options(options: INodeMaterialOptions) {\r\n        this._options = options;\r\n    }\r\n\r\n    /**\r\n     * Default configuration related to image processing available in the standard Material.\r\n     */\r\n    protected _imageProcessingConfiguration: ImageProcessingConfiguration;\r\n\r\n    /**\r\n     * Gets the image processing configuration used either in this material.\r\n     */\r\n    public get imageProcessingConfiguration(): ImageProcessingConfiguration {\r\n        return this._imageProcessingConfiguration;\r\n    }\r\n\r\n    /**\r\n     * Sets the Default image processing configuration used either in the this material.\r\n     *\r\n     * If sets to null, the scene one is in use.\r\n     */\r\n    public set imageProcessingConfiguration(value: ImageProcessingConfiguration) {\r\n        this._attachImageProcessingConfiguration(value);\r\n\r\n        // Ensure the effect will be rebuilt.\r\n        this._markAllSubMeshesAsTexturesDirty();\r\n    }\r\n\r\n    /**\r\n     * Gets an array of blocks that needs to be serialized even if they are not yet connected\r\n     */\r\n    public attachedBlocks: NodeMaterialBlock[] = [];\r\n\r\n    /**\r\n     * Specifies the mode of the node material\r\n     * @internal\r\n     */\r\n    @serialize(\"mode\")\r\n    public _mode: NodeMaterialModes = NodeMaterialModes.Material;\r\n\r\n    /**\r\n     * Gets or sets the mode property\r\n     */\r\n    public get mode(): NodeMaterialModes {\r\n        return this._mode;\r\n    }\r\n\r\n    public set mode(value: NodeMaterialModes) {\r\n        this._mode = value;\r\n    }\r\n\r\n    /** Gets or sets the unique identifier used to identified the effect associated with the material */\r\n    public get buildId() {\r\n        return this._buildId;\r\n    }\r\n\r\n    public set buildId(value: number) {\r\n        this._buildId = value;\r\n    }\r\n\r\n    /**\r\n     * A free comment about the material\r\n     */\r\n    @serialize(\"comment\")\r\n    public comment: string;\r\n\r\n    /**\r\n     * Create a new node based material\r\n     * @param name defines the material name\r\n     * @param scene defines the hosting scene\r\n     * @param options defines creation option\r\n     */\r\n    constructor(name: string, scene?: Scene, options: Partial<INodeMaterialOptions> = {}) {\r\n        super(name, scene || EngineStore.LastCreatedScene!);\r\n\r\n        if (!NodeMaterial.UseNativeShaderLanguageOfEngine && options && options.shaderLanguage === ShaderLanguage.WGSL && !this.getScene().getEngine().isWebGPU) {\r\n            throw new Error(\"WebGPU shader language is only supported with WebGPU engine\");\r\n        }\r\n\r\n        this._options = {\r\n            emitComments: false,\r\n            shaderLanguage: NodeMaterial.DefaultShaderLanguage,\r\n            ...options,\r\n        };\r\n\r\n        if (NodeMaterial.UseNativeShaderLanguageOfEngine) {\r\n            this._options.shaderLanguage = this.getScene().getEngine().isWebGPU ? ShaderLanguage.WGSL : ShaderLanguage.GLSL;\r\n        }\r\n\r\n        // Setup the default processing configuration to the scene.\r\n        this._attachImageProcessingConfiguration(null);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name of the material e.g. \"NodeMaterial\"\r\n     * @returns the class name\r\n     */\r\n    public override getClassName(): string {\r\n        return \"NodeMaterial\";\r\n    }\r\n\r\n    /**\r\n     * Keep track of the image processing observer to allow dispose and replace.\r\n     */\r\n    private _imageProcessingObserver: Nullable<Observer<ImageProcessingConfiguration>>;\r\n\r\n    /**\r\n     * Attaches a new image processing configuration to the Standard Material.\r\n     * @param configuration\r\n     */\r\n    protected _attachImageProcessingConfiguration(configuration: Nullable<ImageProcessingConfiguration>): void {\r\n        if (configuration === this._imageProcessingConfiguration) {\r\n            return;\r\n        }\r\n\r\n        // Detaches observer.\r\n        if (this._imageProcessingConfiguration && this._imageProcessingObserver) {\r\n            this._imageProcessingConfiguration.onUpdateParameters.remove(this._imageProcessingObserver);\r\n        }\r\n\r\n        // Pick the scene configuration if needed.\r\n        if (!configuration) {\r\n            this._imageProcessingConfiguration = this.getScene().imageProcessingConfiguration;\r\n        } else {\r\n            this._imageProcessingConfiguration = configuration;\r\n        }\r\n\r\n        // Attaches observer.\r\n        if (this._imageProcessingConfiguration) {\r\n            this._imageProcessingObserver = this._imageProcessingConfiguration.onUpdateParameters.add(() => {\r\n                this._markAllSubMeshesAsImageProcessingDirty();\r\n            });\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get a block by its name\r\n     * @param name defines the name of the block to retrieve\r\n     * @returns the required block or null if not found\r\n     */\r\n    public getBlockByName(name: string) {\r\n        let result = null;\r\n        for (const block of this.attachedBlocks) {\r\n            if (block.name === name) {\r\n                if (!result) {\r\n                    result = block;\r\n                } else {\r\n                    Tools.Warn(\"More than one block was found with the name `\" + name + \"`\");\r\n                    return result;\r\n                }\r\n            }\r\n        }\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Get a block using a predicate\r\n     * @param predicate defines the predicate used to find the good candidate\r\n     * @returns the required block or null if not found\r\n     */\r\n    public getBlockByPredicate(predicate: (block: NodeMaterialBlock) => boolean) {\r\n        for (const block of this.attachedBlocks) {\r\n            if (predicate(block)) {\r\n                return block;\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Get an input block using a predicate\r\n     * @param predicate defines the predicate used to find the good candidate\r\n     * @returns the required input block or null if not found\r\n     */\r\n    public getInputBlockByPredicate(predicate: (block: InputBlock) => boolean): Nullable<InputBlock> {\r\n        for (const block of this.attachedBlocks) {\r\n            if (block.isInput && predicate(block as InputBlock)) {\r\n                return block as InputBlock;\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets the list of input blocks attached to this material\r\n     * @returns an array of InputBlocks\r\n     */\r\n    public getInputBlocks() {\r\n        const blocks: InputBlock[] = [];\r\n        for (const block of this.attachedBlocks) {\r\n            if (block.isInput) {\r\n                blocks.push(block as InputBlock);\r\n            }\r\n        }\r\n\r\n        return blocks;\r\n    }\r\n\r\n    /**\r\n     * Adds a new optimizer to the list of optimizers\r\n     * @param optimizer defines the optimizers to add\r\n     * @returns the current material\r\n     */\r\n    public registerOptimizer(optimizer: NodeMaterialOptimizer) {\r\n        const index = this._optimizers.indexOf(optimizer);\r\n\r\n        if (index > -1) {\r\n            return;\r\n        }\r\n\r\n        this._optimizers.push(optimizer);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Remove an optimizer from the list of optimizers\r\n     * @param optimizer defines the optimizers to remove\r\n     * @returns the current material\r\n     */\r\n    public unregisterOptimizer(optimizer: NodeMaterialOptimizer) {\r\n        const index = this._optimizers.indexOf(optimizer);\r\n\r\n        if (index === -1) {\r\n            return;\r\n        }\r\n\r\n        this._optimizers.splice(index, 1);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Add a new block to the list of output nodes\r\n     * @param node defines the node to add\r\n     * @returns the current material\r\n     */\r\n    public addOutputNode(node: NodeMaterialBlock) {\r\n        if (node.target === null) {\r\n            // eslint-disable-next-line no-throw-literal\r\n            throw \"This node is not meant to be an output node. You may want to explicitly set its target value.\";\r\n        }\r\n\r\n        if ((node.target & NodeMaterialBlockTargets.Vertex) !== 0) {\r\n            this._addVertexOutputNode(node);\r\n        }\r\n\r\n        if ((node.target & NodeMaterialBlockTargets.Fragment) !== 0) {\r\n            this._addFragmentOutputNode(node);\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Remove a block from the list of root nodes\r\n     * @param node defines the node to remove\r\n     * @returns the current material\r\n     */\r\n    public removeOutputNode(node: NodeMaterialBlock) {\r\n        if (node.target === null) {\r\n            return this;\r\n        }\r\n\r\n        if ((node.target & NodeMaterialBlockTargets.Vertex) !== 0) {\r\n            this._removeVertexOutputNode(node);\r\n        }\r\n\r\n        if ((node.target & NodeMaterialBlockTargets.Fragment) !== 0) {\r\n            this._removeFragmentOutputNode(node);\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    private _addVertexOutputNode(node: NodeMaterialBlock) {\r\n        if (this._vertexOutputNodes.indexOf(node) !== -1) {\r\n            return;\r\n        }\r\n\r\n        node.target = NodeMaterialBlockTargets.Vertex;\r\n        this._vertexOutputNodes.push(node);\r\n\r\n        return this;\r\n    }\r\n\r\n    private _removeVertexOutputNode(node: NodeMaterialBlock) {\r\n        const index = this._vertexOutputNodes.indexOf(node);\r\n        if (index === -1) {\r\n            return;\r\n        }\r\n\r\n        this._vertexOutputNodes.splice(index, 1);\r\n\r\n        return this;\r\n    }\r\n\r\n    private _addFragmentOutputNode(node: NodeMaterialBlock) {\r\n        if (this._fragmentOutputNodes.indexOf(node) !== -1) {\r\n            return;\r\n        }\r\n\r\n        node.target = NodeMaterialBlockTargets.Fragment;\r\n        this._fragmentOutputNodes.push(node);\r\n\r\n        return this;\r\n    }\r\n\r\n    private _removeFragmentOutputNode(node: NodeMaterialBlock) {\r\n        const index = this._fragmentOutputNodes.indexOf(node);\r\n        if (index === -1) {\r\n            return;\r\n        }\r\n\r\n        this._fragmentOutputNodes.splice(index, 1);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating that alpha blending must be enabled no matter what alpha value or alpha channel of the FragmentBlock are\r\n     */\r\n    @serialize()\r\n    public forceAlphaBlending = false;\r\n\r\n    public override get _supportGlowLayer() {\r\n        if (this._fragmentOutputNodes.length === 0) {\r\n            return false;\r\n        }\r\n\r\n        if (this._fragmentOutputNodes.some((f) => (f as FragmentOutputBlock).additionalColor && (f as FragmentOutputBlock).additionalColor.isConnected)) {\r\n            return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Specifies if the material will require alpha blending\r\n     * @returns a boolean specifying if alpha blending is needed\r\n     */\r\n    public override needAlphaBlending(): boolean {\r\n        if (this.ignoreAlpha) {\r\n            return false;\r\n        }\r\n        return this.forceAlphaBlending || this.alpha < 1.0 || (this._sharedData && this._sharedData.hints.needAlphaBlending);\r\n    }\r\n\r\n    /**\r\n     * Specifies if this material should be rendered in alpha test mode\r\n     * @returns a boolean specifying if an alpha test is needed.\r\n     */\r\n    public override needAlphaTesting(): boolean {\r\n        return this._sharedData && this._sharedData.hints.needAlphaTesting;\r\n    }\r\n\r\n    private _processInitializeOnLink(block: NodeMaterialBlock, state: NodeMaterialBuildState, nodesToProcessForOtherBuildState: NodeMaterialBlock[], autoConfigure = true) {\r\n        if (block.target === NodeMaterialBlockTargets.VertexAndFragment) {\r\n            nodesToProcessForOtherBuildState.push(block);\r\n        } else if (state.target === NodeMaterialBlockTargets.Fragment && block.target === NodeMaterialBlockTargets.Vertex && block._preparationId !== this._buildId) {\r\n            nodesToProcessForOtherBuildState.push(block);\r\n        }\r\n        this._initializeBlock(block, state, nodesToProcessForOtherBuildState, autoConfigure);\r\n    }\r\n\r\n    private _attachBlock(node: NodeMaterialBlock) {\r\n        if (this.attachedBlocks.indexOf(node) === -1) {\r\n            if (node.isUnique) {\r\n                const className = node.getClassName();\r\n\r\n                for (const other of this.attachedBlocks) {\r\n                    if (other.getClassName() === className) {\r\n                        this._sharedData.raiseBuildError(`Cannot have multiple blocks of type ${className} in the same NodeMaterial`);\r\n                        return;\r\n                    }\r\n                }\r\n            }\r\n            this.attachedBlocks.push(node);\r\n        }\r\n    }\r\n\r\n    private _initializeBlock(node: NodeMaterialBlock, state: NodeMaterialBuildState, nodesToProcessForOtherBuildState: NodeMaterialBlock[], autoConfigure = true) {\r\n        node.initialize(state);\r\n        if (autoConfigure) {\r\n            node.autoConfigure(this);\r\n        }\r\n        node._preparationId = this._buildId;\r\n\r\n        this._attachBlock(node);\r\n\r\n        for (const input of node.inputs) {\r\n            input.associatedVariableName = \"\";\r\n\r\n            const connectedPoint = input.connectedPoint;\r\n            if (connectedPoint && !connectedPoint._preventBubbleUp) {\r\n                const block = connectedPoint.ownerBlock;\r\n                if (block !== node) {\r\n                    this._processInitializeOnLink(block, state, nodesToProcessForOtherBuildState, autoConfigure);\r\n                }\r\n            }\r\n        }\r\n\r\n        // Loop\r\n        if (node.isLoop) {\r\n            // We need to keep the storage write block in the active blocks\r\n            const loopBlock = node as LoopBlock;\r\n            if (loopBlock.loopID.hasEndpoints) {\r\n                for (const endpoint of loopBlock.loopID.endpoints) {\r\n                    const block = endpoint.ownerBlock;\r\n                    if (block.outputs.length !== 0) {\r\n                        continue;\r\n                    }\r\n                    state._terminalBlocks.add(block); // Attach the storage write only\r\n                    this._processInitializeOnLink(block, state, nodesToProcessForOtherBuildState, autoConfigure);\r\n                }\r\n            }\r\n        } else if (node.isTeleportOut) {\r\n            // Teleportation\r\n            const teleport = node as NodeMaterialTeleportOutBlock;\r\n            if (teleport.entryPoint) {\r\n                this._processInitializeOnLink(teleport.entryPoint, state, nodesToProcessForOtherBuildState, autoConfigure);\r\n            }\r\n        }\r\n\r\n        for (const output of node.outputs) {\r\n            output.associatedVariableName = \"\";\r\n        }\r\n    }\r\n\r\n    private _resetDualBlocks(node: NodeMaterialBlock, id: number) {\r\n        if (node.target === NodeMaterialBlockTargets.VertexAndFragment) {\r\n            node.buildId = id;\r\n        }\r\n\r\n        for (const input of node.inputs) {\r\n            const connectedPoint = input.connectedPoint;\r\n            if (connectedPoint && !connectedPoint._preventBubbleUp) {\r\n                const block = connectedPoint.ownerBlock;\r\n                if (block !== node) {\r\n                    this._resetDualBlocks(block, id);\r\n                }\r\n            }\r\n        }\r\n\r\n        // If this is a teleport out, we need to reset the connected block\r\n        if (node.isTeleportOut) {\r\n            const teleportOut = node as NodeMaterialTeleportOutBlock;\r\n            if (teleportOut.entryPoint) {\r\n                this._resetDualBlocks(teleportOut.entryPoint, id);\r\n            }\r\n        } else if (node.isLoop) {\r\n            // Loop\r\n            const loopBlock = node as LoopBlock;\r\n            if (loopBlock.loopID.hasEndpoints) {\r\n                for (const endpoint of loopBlock.loopID.endpoints) {\r\n                    const block = endpoint.ownerBlock;\r\n                    if (block.outputs.length !== 0) {\r\n                        continue;\r\n                    }\r\n                    this._resetDualBlocks(block, id);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Remove a block from the current node material\r\n     * @param block defines the block to remove\r\n     */\r\n    public removeBlock(block: NodeMaterialBlock) {\r\n        const attachedBlockIndex = this.attachedBlocks.indexOf(block);\r\n        if (attachedBlockIndex > -1) {\r\n            this.attachedBlocks.splice(attachedBlockIndex, 1);\r\n        }\r\n\r\n        if (block.isFinalMerger) {\r\n            this.removeOutputNode(block);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Build the material and generates the inner effect\r\n     * @param verbose defines if the build should log activity\r\n     * @param updateBuildId defines if the internal build Id should be updated (default is true)\r\n     * @param autoConfigure defines if the autoConfigure method should be called when initializing blocks (default is false)\r\n     */\r\n    public build(verbose: boolean = false, updateBuildId = true, autoConfigure = false) {\r\n        if (this._buildIsInProgress) {\r\n            Logger.Warn(\"Build is already in progress, You can use NodeMaterial.onBuildObservable to determine when the build is completed.\");\r\n            return;\r\n        }\r\n        this._buildIsInProgress = true;\r\n        // First time?\r\n        if (!this._vertexCompilationState && !autoConfigure) {\r\n            autoConfigure = true;\r\n        }\r\n\r\n        this._buildWasSuccessful = false;\r\n        const engine = this.getScene().getEngine();\r\n\r\n        const allowEmptyVertexProgram = this._mode === NodeMaterialModes.Particle || this._mode === NodeMaterialModes.SFE;\r\n\r\n        if (this._vertexOutputNodes.length === 0 && !allowEmptyVertexProgram) {\r\n            this.onBuildErrorObservable.notifyObservers(\"You must define at least one vertexOutputNode\");\r\n            this._buildIsInProgress = false;\r\n            return;\r\n        }\r\n\r\n        if (this._fragmentOutputNodes.length === 0) {\r\n            this.onBuildErrorObservable.notifyObservers(\"You must define at least one fragmentOutputNode\");\r\n            this._buildIsInProgress = false;\r\n            return;\r\n        }\r\n\r\n        // Compilation state\r\n        this._vertexCompilationState = new NodeMaterialBuildState();\r\n        this._vertexCompilationState.supportUniformBuffers = engine.supportsUniformBuffers;\r\n        this._vertexCompilationState.target = NodeMaterialBlockTargets.Vertex;\r\n        this._fragmentCompilationState = new NodeMaterialBuildState();\r\n        this._fragmentCompilationState.supportUniformBuffers = engine.supportsUniformBuffers;\r\n        this._fragmentCompilationState.target = NodeMaterialBlockTargets.Fragment;\r\n\r\n        // Shared data\r\n        const needToPurgeList = this._fragmentOutputNodes.filter((n) => n._isFinalOutputAndActive).length > 1;\r\n        let fragmentOutputNodes = this._fragmentOutputNodes;\r\n\r\n        if (needToPurgeList) {\r\n            // Get all but the final output nodes\r\n            fragmentOutputNodes = this._fragmentOutputNodes.filter((n) => !n._isFinalOutputAndActive);\r\n\r\n            // Get the first with precedence on\r\n            fragmentOutputNodes.push(this._fragmentOutputNodes.filter((n) => n._isFinalOutputAndActive && n._hasPrecedence)[0]);\r\n        }\r\n\r\n        this._sharedData = new NodeMaterialBuildStateSharedData();\r\n        this._sharedData.nodeMaterial = this;\r\n        this._sharedData.fragmentOutputNodes = fragmentOutputNodes;\r\n        this._vertexCompilationState.sharedData = this._sharedData;\r\n        this._fragmentCompilationState.sharedData = this._sharedData;\r\n        this._sharedData.buildId = this._buildId;\r\n        this._sharedData.emitComments = this._options.emitComments;\r\n        this._sharedData.verbose = verbose;\r\n        this._sharedData.scene = this.getScene();\r\n        this._sharedData.allowEmptyVertexProgram = allowEmptyVertexProgram;\r\n\r\n        // Initialize blocks\r\n        const vertexNodes: NodeMaterialBlock[] = [];\r\n        const fragmentNodes: NodeMaterialBlock[] = [];\r\n\r\n        for (const vertexOutputNode of this._vertexOutputNodes) {\r\n            vertexNodes.push(vertexOutputNode);\r\n            this._initializeBlock(vertexOutputNode, this._vertexCompilationState, fragmentNodes, autoConfigure);\r\n        }\r\n\r\n        for (const fragmentOutputNode of fragmentOutputNodes) {\r\n            fragmentNodes.push(fragmentOutputNode);\r\n            this._initializeBlock(fragmentOutputNode, this._fragmentCompilationState, vertexNodes, autoConfigure);\r\n        }\r\n\r\n        // Are blocks code ready?\r\n        let waitingNodeCount = 0;\r\n        for (const node of this.attachedBlocks) {\r\n            if (!node.codeIsReady) {\r\n                waitingNodeCount++;\r\n                node.onCodeIsReadyObservable.addOnce(() => {\r\n                    waitingNodeCount--;\r\n                    if (waitingNodeCount === 0) {\r\n                        this._finishBuildProcess(verbose, updateBuildId, vertexNodes, fragmentNodes);\r\n                    }\r\n                });\r\n            }\r\n        }\r\n\r\n        if (waitingNodeCount !== 0) {\r\n            return;\r\n        }\r\n\r\n        this._finishBuildProcess(verbose, updateBuildId, vertexNodes, fragmentNodes);\r\n    }\r\n\r\n    private _finishBuildProcess(verbose: boolean = false, updateBuildId = true, vertexNodes: NodeMaterialBlock[], fragmentNodes: NodeMaterialBlock[]) {\r\n        // Optimize\r\n        this.optimize();\r\n\r\n        // Vertex\r\n        for (const vertexOutputNode of vertexNodes) {\r\n            vertexOutputNode.build(this._vertexCompilationState, vertexNodes);\r\n        }\r\n\r\n        // Fragment\r\n        this._fragmentCompilationState.uniforms = this._vertexCompilationState.uniforms.slice(0);\r\n        this._fragmentCompilationState._uniformDeclaration = this._vertexCompilationState._uniformDeclaration;\r\n        this._fragmentCompilationState._constantDeclaration = this._vertexCompilationState._constantDeclaration;\r\n        this._fragmentCompilationState._vertexState = this._vertexCompilationState;\r\n\r\n        for (const fragmentOutputNode of fragmentNodes) {\r\n            this._resetDualBlocks(fragmentOutputNode, this._buildId - 1);\r\n        }\r\n\r\n        for (const fragmentOutputNode of fragmentNodes) {\r\n            fragmentOutputNode.build(this._fragmentCompilationState, fragmentNodes);\r\n        }\r\n\r\n        // Finalize\r\n        this._vertexCompilationState.finalize(this._vertexCompilationState);\r\n        this._fragmentCompilationState.finalize(this._fragmentCompilationState);\r\n\r\n        if (updateBuildId) {\r\n            this._buildId = NodeMaterial._BuildIdGenerator++;\r\n        }\r\n\r\n        if (verbose) {\r\n            Logger.Log(\"Vertex shader:\");\r\n            Logger.Log(this._vertexCompilationState.compilationString);\r\n            Logger.Log(\"Fragment shader:\");\r\n            Logger.Log(this._fragmentCompilationState.compilationString);\r\n        }\r\n\r\n        // Errors\r\n        const noError = this._sharedData.emitErrors();\r\n\r\n        this._buildIsInProgress = false;\r\n        if (noError) {\r\n            this.onBuildObservable.notifyObservers(this);\r\n            this._buildWasSuccessful = true;\r\n        }\r\n\r\n        // Wipe defines\r\n        const meshes = this.getScene().meshes;\r\n        for (const mesh of meshes) {\r\n            if (!mesh.subMeshes) {\r\n                continue;\r\n            }\r\n            for (const subMesh of mesh.subMeshes) {\r\n                if (subMesh.getMaterial() !== this) {\r\n                    continue;\r\n                }\r\n\r\n                if (!subMesh.materialDefines) {\r\n                    continue;\r\n                }\r\n\r\n                const defines = subMesh.materialDefines;\r\n                defines.markAllAsDirty();\r\n                defines.reset();\r\n            }\r\n        }\r\n\r\n        if (this.prePassTextureInputs.length) {\r\n            this.getScene().enablePrePassRenderer();\r\n        }\r\n        const prePassRenderer = this.getScene().prePassRenderer;\r\n        if (prePassRenderer) {\r\n            prePassRenderer.markAsDirty();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Runs an optimization phase to try to improve the shader code\r\n     */\r\n    public optimize() {\r\n        for (const optimizer of this._optimizers) {\r\n            optimizer.optimize(this._vertexOutputNodes, this._fragmentOutputNodes);\r\n        }\r\n    }\r\n\r\n    private _prepareDefinesForAttributes(mesh: AbstractMesh, defines: NodeMaterialDefines) {\r\n        const oldNormal = defines[\"NORMAL\"];\r\n        const oldTangent = defines[\"TANGENT\"];\r\n        const oldColor = defines[\"VERTEXCOLOR_NME\"];\r\n\r\n        defines[\"NORMAL\"] = mesh.isVerticesDataPresent(VertexBuffer.NormalKind);\r\n        defines[\"TANGENT\"] = mesh.isVerticesDataPresent(VertexBuffer.TangentKind);\r\n\r\n        const hasVertexColors = mesh.useVertexColors && mesh.isVerticesDataPresent(VertexBuffer.ColorKind);\r\n        defines[\"VERTEXCOLOR_NME\"] = hasVertexColors;\r\n\r\n        let uvChanged = false;\r\n        for (let i = 1; i <= Constants.MAX_SUPPORTED_UV_SETS; ++i) {\r\n            const oldUV = defines[\"UV\" + i];\r\n            defines[\"UV\" + i] = mesh.isVerticesDataPresent(`uv${i === 1 ? \"\" : i}`);\r\n            uvChanged = uvChanged || defines[\"UV\" + i] !== oldUV;\r\n        }\r\n\r\n        // PrePass\r\n        const oit = this.needAlphaBlendingForMesh(mesh) && this.getScene().useOrderIndependentTransparency;\r\n        PrepareDefinesForPrePass(this.getScene(), defines, !oit);\r\n\r\n        MaterialHelperGeometryRendering.PrepareDefines(this.getScene().getEngine().currentRenderPassId, mesh, defines);\r\n\r\n        if (oldNormal !== defines[\"NORMAL\"] || oldTangent !== defines[\"TANGENT\"] || oldColor !== defines[\"VERTEXCOLOR_NME\"] || uvChanged) {\r\n            defines.markAsAttributesDirty();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Can this material render to prepass\r\n     */\r\n    public override get isPrePassCapable(): boolean {\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Outputs written to the prepass\r\n     */\r\n    public get prePassTextureOutputs(): number[] {\r\n        const prePassOutputBlock = this.getBlockByPredicate((block) => block.getClassName() === \"PrePassOutputBlock\") as PrePassOutputBlock;\r\n        const result = [Constants.PREPASS_COLOR_TEXTURE_TYPE];\r\n        if (!prePassOutputBlock) {\r\n            return result;\r\n        }\r\n        // Cannot write to prepass if we alread read from prepass\r\n        if (this.prePassTextureInputs.length) {\r\n            return result;\r\n        }\r\n\r\n        if (prePassOutputBlock.viewDepth.isConnected) {\r\n            result.push(Constants.PREPASS_DEPTH_TEXTURE_TYPE);\r\n        }\r\n\r\n        if (prePassOutputBlock.screenDepth.isConnected) {\r\n            result.push(Constants.PREPASS_SCREENSPACE_DEPTH_TEXTURE_TYPE);\r\n        }\r\n\r\n        if (prePassOutputBlock.viewNormal.isConnected) {\r\n            result.push(Constants.PREPASS_NORMAL_TEXTURE_TYPE);\r\n        }\r\n\r\n        if (prePassOutputBlock.worldNormal.isConnected) {\r\n            result.push(Constants.PREPASS_WORLD_NORMAL_TEXTURE_TYPE);\r\n        }\r\n\r\n        if (prePassOutputBlock.worldPosition.isConnected) {\r\n            result.push(Constants.PREPASS_POSITION_TEXTURE_TYPE);\r\n        }\r\n\r\n        if (prePassOutputBlock.localPosition.isConnected) {\r\n            result.push(Constants.PREPASS_LOCAL_POSITION_TEXTURE_TYPE);\r\n        }\r\n\r\n        if (prePassOutputBlock.reflectivity.isConnected) {\r\n            result.push(Constants.PREPASS_REFLECTIVITY_TEXTURE_TYPE);\r\n        }\r\n\r\n        if (prePassOutputBlock.velocity.isConnected) {\r\n            result.push(Constants.PREPASS_VELOCITY_TEXTURE_TYPE);\r\n        }\r\n\r\n        if (prePassOutputBlock.velocityLinear.isConnected) {\r\n            result.push(Constants.PREPASS_VELOCITY_LINEAR_TEXTURE_TYPE);\r\n        }\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Gets the list of prepass texture required\r\n     */\r\n    public get prePassTextureInputs(): number[] {\r\n        const prePassTextureBlocks = this.getAllTextureBlocks().filter((block) => block.getClassName() === \"PrePassTextureBlock\") as PrePassTextureBlock[];\r\n        const result = [] as number[];\r\n\r\n        for (const block of prePassTextureBlocks) {\r\n            if (block.position.isConnected && !result.includes(Constants.PREPASS_POSITION_TEXTURE_TYPE)) {\r\n                result.push(Constants.PREPASS_POSITION_TEXTURE_TYPE);\r\n            }\r\n            if (block.localPosition.isConnected && !result.includes(Constants.PREPASS_LOCAL_POSITION_TEXTURE_TYPE)) {\r\n                result.push(Constants.PREPASS_LOCAL_POSITION_TEXTURE_TYPE);\r\n            }\r\n            if (block.depth.isConnected && !result.includes(Constants.PREPASS_DEPTH_TEXTURE_TYPE)) {\r\n                result.push(Constants.PREPASS_DEPTH_TEXTURE_TYPE);\r\n            }\r\n            if (block.screenDepth.isConnected && !result.includes(Constants.PREPASS_SCREENSPACE_DEPTH_TEXTURE_TYPE)) {\r\n                result.push(Constants.PREPASS_SCREENSPACE_DEPTH_TEXTURE_TYPE);\r\n            }\r\n            if (block.normal.isConnected && !result.includes(Constants.PREPASS_NORMAL_TEXTURE_TYPE)) {\r\n                result.push(Constants.PREPASS_NORMAL_TEXTURE_TYPE);\r\n            }\r\n            if (block.worldNormal.isConnected && !result.includes(Constants.PREPASS_WORLD_NORMAL_TEXTURE_TYPE)) {\r\n                result.push(Constants.PREPASS_WORLD_NORMAL_TEXTURE_TYPE);\r\n            }\r\n        }\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Sets the required values to the prepass renderer.\r\n     * @param prePassRenderer defines the prepass renderer to set\r\n     * @returns true if the pre pass is needed\r\n     */\r\n    public override setPrePassRenderer(prePassRenderer: PrePassRenderer): boolean {\r\n        const prePassTexturesRequired = this.prePassTextureInputs.concat(this.prePassTextureOutputs);\r\n\r\n        if (prePassRenderer && prePassTexturesRequired.length > 1) {\r\n            let cfg = prePassRenderer.getEffectConfiguration(\"nodeMaterial\");\r\n            if (!cfg) {\r\n                cfg = prePassRenderer.addEffectConfiguration({\r\n                    enabled: true,\r\n                    needsImageProcessing: false,\r\n                    name: \"nodeMaterial\",\r\n                    texturesRequired: [],\r\n                });\r\n            }\r\n            for (const prePassTexture of prePassTexturesRequired) {\r\n                if (!cfg.texturesRequired.includes(prePassTexture)) {\r\n                    cfg.texturesRequired.push(prePassTexture);\r\n                }\r\n            }\r\n            cfg.enabled = true;\r\n        }\r\n\r\n        // COLOR_TEXTURE is always required for prepass, length > 1 means\r\n        // we actually need to write to special prepass textures\r\n        return prePassTexturesRequired.length > 1;\r\n    }\r\n\r\n    /**\r\n     * Create a post process from the material\r\n     * @param camera The camera to apply the render pass to.\r\n     * @param options The required width/height ratio to downsize to before computing the render pass. (Use 1.0 for full size)\r\n     * @param samplingMode The sampling mode to be used when computing the pass. (default: 0)\r\n     * @param engine The engine which the post process will be applied. (default: current engine)\r\n     * @param reusable If the post process can be reused on the same frame. (default: false)\r\n     * @param textureType Type of textures used when performing the post process. (default: 0)\r\n     * @param textureFormat Format of textures used when performing the post process. (default: TEXTUREFORMAT_RGBA)\r\n     * @returns the post process created\r\n     */\r\n    public createPostProcess(\r\n        camera: Nullable<Camera>,\r\n        options: number | PostProcessOptions = 1,\r\n        samplingMode: number = Constants.TEXTURE_NEAREST_SAMPLINGMODE,\r\n        engine?: AbstractEngine,\r\n        reusable?: boolean,\r\n        textureType: number = Constants.TEXTURETYPE_UNSIGNED_BYTE,\r\n        textureFormat = Constants.TEXTUREFORMAT_RGBA\r\n    ): Nullable<PostProcess> {\r\n        if (this.mode !== NodeMaterialModes.PostProcess && this.mode !== NodeMaterialModes.SFE) {\r\n            Logger.Log(\"Incompatible material mode\");\r\n            return null;\r\n        }\r\n        return this._createEffectForPostProcess(null, camera, options, samplingMode, engine, reusable, textureType, textureFormat);\r\n    }\r\n\r\n    /**\r\n     * Create the post process effect from the material\r\n     * @param postProcess The post process to create the effect for\r\n     */\r\n    public createEffectForPostProcess(postProcess: PostProcess) {\r\n        this._createEffectForPostProcess(postProcess);\r\n    }\r\n\r\n    private _createEffectForPostProcess(\r\n        postProcess: Nullable<PostProcess>,\r\n        camera?: Nullable<Camera>,\r\n        options: number | PostProcessOptions = 1,\r\n        samplingMode: number = Constants.TEXTURE_NEAREST_SAMPLINGMODE,\r\n        engine?: AbstractEngine,\r\n        reusable?: boolean,\r\n        textureType: number = Constants.TEXTURETYPE_UNSIGNED_BYTE,\r\n        textureFormat = Constants.TEXTUREFORMAT_RGBA\r\n    ): PostProcess {\r\n        let tempName = this.name + this._buildId;\r\n\r\n        const defines = new NodeMaterialDefines();\r\n\r\n        let buildId = this._buildId;\r\n\r\n        this._processDefines(defines);\r\n\r\n        // If no vertex shader emitted, fallback to default postprocess vertex shader\r\n        const vertexCode = this._sharedData.checks.emitVertex ? this._vertexCompilationState._builtCompilationString : undefined;\r\n\r\n        Effect.RegisterShader(tempName, this._fragmentCompilationState._builtCompilationString, vertexCode, this.shaderLanguage);\r\n\r\n        if (!postProcess) {\r\n            postProcess = new PostProcess(\r\n                this.name + \"PostProcess\",\r\n                tempName,\r\n                this._fragmentCompilationState.uniforms,\r\n                this._fragmentCompilationState.samplers,\r\n                options,\r\n                camera!,\r\n                samplingMode,\r\n                engine,\r\n                reusable,\r\n                defines.toString(),\r\n                textureType,\r\n                vertexCode ? tempName : \"postprocess\",\r\n                { maxSimultaneousLights: this.maxSimultaneousLights },\r\n                false,\r\n                textureFormat,\r\n                this.shaderLanguage\r\n            );\r\n        } else {\r\n            postProcess.updateEffect(\r\n                defines.toString(),\r\n                this._fragmentCompilationState.uniforms,\r\n                this._fragmentCompilationState.samplers,\r\n                { maxSimultaneousLights: this.maxSimultaneousLights },\r\n                undefined,\r\n                undefined,\r\n                tempName,\r\n                tempName\r\n            );\r\n        }\r\n\r\n        postProcess.nodeMaterialSource = this;\r\n\r\n        postProcess.onApplyObservable.add((effect) => {\r\n            if (buildId !== this._buildId) {\r\n                delete Effect.ShadersStore[tempName + \"VertexShader\"];\r\n                delete Effect.ShadersStore[tempName + \"PixelShader\"];\r\n\r\n                tempName = this.name + this._buildId;\r\n\r\n                defines.markAllAsDirty();\r\n\r\n                buildId = this._buildId;\r\n            }\r\n\r\n            const result = this._processDefines(defines);\r\n\r\n            if (result) {\r\n                Effect.RegisterShader(tempName, this._fragmentCompilationState._builtCompilationString, this._vertexCompilationState._builtCompilationString);\r\n\r\n                TimingTools.SetImmediate(() =>\r\n                    postProcess.updateEffect(\r\n                        defines.toString(),\r\n                        this._fragmentCompilationState.uniforms,\r\n                        this._fragmentCompilationState.samplers,\r\n                        { maxSimultaneousLights: this.maxSimultaneousLights },\r\n                        undefined,\r\n                        undefined,\r\n                        tempName,\r\n                        tempName\r\n                    )\r\n                );\r\n            }\r\n\r\n            this._checkInternals(effect);\r\n        });\r\n\r\n        return postProcess;\r\n    }\r\n\r\n    /**\r\n     * Create a new procedural texture based on this node material\r\n     * @param size defines the size of the texture\r\n     * @param scene defines the hosting scene\r\n     * @returns the new procedural texture attached to this node material\r\n     */\r\n    public createProceduralTexture(size: number | { width: number; height: number; layers?: number }, scene: Scene): Nullable<ProceduralTexture> {\r\n        if (this.mode !== NodeMaterialModes.ProceduralTexture) {\r\n            Logger.Log(\"Incompatible material mode\");\r\n            return null;\r\n        }\r\n\r\n        let tempName = this.name + this._buildId;\r\n\r\n        const proceduralTexture = new ProceduralTexture(tempName, size, null, scene);\r\n\r\n        const defines = new NodeMaterialDefines();\r\n        const result = this._processDefines(defines);\r\n        Effect.RegisterShader(tempName, this._fragmentCompilationState._builtCompilationString, this._vertexCompilationState._builtCompilationString, this.shaderLanguage);\r\n\r\n        let effect = this.getScene().getEngine().createEffect(\r\n            {\r\n                vertexElement: tempName,\r\n                fragmentElement: tempName,\r\n            },\r\n            [VertexBuffer.PositionKind],\r\n            this._fragmentCompilationState.uniforms,\r\n            this._fragmentCompilationState.samplers,\r\n            defines.toString(),\r\n            result?.fallbacks,\r\n            undefined,\r\n            undefined,\r\n            undefined,\r\n            this.shaderLanguage\r\n        );\r\n\r\n        proceduralTexture.nodeMaterialSource = this;\r\n        proceduralTexture._setEffect(effect);\r\n\r\n        let buildId = this._buildId;\r\n        const refreshEffect = () => {\r\n            if (buildId !== this._buildId) {\r\n                delete Effect.ShadersStore[tempName + \"VertexShader\"];\r\n                delete Effect.ShadersStore[tempName + \"PixelShader\"];\r\n\r\n                tempName = this.name + this._buildId;\r\n\r\n                defines.markAllAsDirty();\r\n\r\n                buildId = this._buildId;\r\n            }\r\n\r\n            const result = this._processDefines(defines);\r\n\r\n            if (result) {\r\n                Effect.RegisterShader(tempName, this._fragmentCompilationState._builtCompilationString, this._vertexCompilationState._builtCompilationString, this.shaderLanguage);\r\n\r\n                TimingTools.SetImmediate(() => {\r\n                    effect = this.getScene().getEngine().createEffect(\r\n                        {\r\n                            vertexElement: tempName,\r\n                            fragmentElement: tempName,\r\n                        },\r\n                        [VertexBuffer.PositionKind],\r\n                        this._fragmentCompilationState.uniforms,\r\n                        this._fragmentCompilationState.samplers,\r\n                        defines.toString(),\r\n                        result?.fallbacks,\r\n                        undefined\r\n                    );\r\n\r\n                    proceduralTexture._setEffect(effect);\r\n                });\r\n            }\r\n\r\n            this._checkInternals(effect);\r\n        };\r\n\r\n        proceduralTexture.onBeforeGenerationObservable.add(() => {\r\n            refreshEffect();\r\n        });\r\n\r\n        // This is needed if the procedural texture is not set to refresh automatically\r\n        this.onBuildObservable.add(() => {\r\n            refreshEffect();\r\n        });\r\n\r\n        return proceduralTexture;\r\n    }\r\n\r\n    private _createEffectForParticles(\r\n        particleSystem: IParticleSystem,\r\n        blendMode: number,\r\n        onCompiled?: (effect: Effect) => void,\r\n        onError?: (effect: Effect, errors: string) => void,\r\n        effect?: Effect,\r\n        defines?: NodeMaterialDefines,\r\n        particleSystemDefinesJoined = \"\"\r\n    ) {\r\n        let tempName = this.name + this._buildId + \"_\" + blendMode;\r\n\r\n        if (!defines) {\r\n            defines = new NodeMaterialDefines();\r\n        }\r\n\r\n        let buildId = this._buildId;\r\n\r\n        const particleSystemDefines: Array<string> = [];\r\n        let join = particleSystemDefinesJoined;\r\n\r\n        if (!effect) {\r\n            const result = this._processDefines(defines);\r\n\r\n            Effect.RegisterShader(tempName, this._fragmentCompilationState._builtCompilationString, undefined, this.shaderLanguage);\r\n\r\n            particleSystem.fillDefines(particleSystemDefines, blendMode, false);\r\n\r\n            join = particleSystemDefines.join(\"\\n\");\r\n\r\n            effect = this.getScene()\r\n                .getEngine()\r\n                .createEffectForParticles(\r\n                    tempName,\r\n                    this._fragmentCompilationState.uniforms,\r\n                    this._fragmentCompilationState.samplers,\r\n                    defines.toString() + \"\\n\" + join,\r\n                    result?.fallbacks,\r\n                    onCompiled,\r\n                    onError,\r\n                    particleSystem,\r\n                    this.shaderLanguage\r\n                );\r\n\r\n            particleSystem.setCustomEffect(effect, blendMode);\r\n        }\r\n\r\n        effect.onBindObservable.add((effect) => {\r\n            if (buildId !== this._buildId) {\r\n                delete Effect.ShadersStore[tempName + \"PixelShader\"];\r\n\r\n                tempName = this.name + this._buildId + \"_\" + blendMode;\r\n\r\n                defines.markAllAsDirty();\r\n\r\n                buildId = this._buildId;\r\n            }\r\n\r\n            particleSystemDefines.length = 0;\r\n\r\n            particleSystem.fillDefines(particleSystemDefines, blendMode, false);\r\n\r\n            const particleSystemDefinesJoinedCurrent = particleSystemDefines.join(\"\\n\");\r\n\r\n            if (particleSystemDefinesJoinedCurrent !== join) {\r\n                defines.markAllAsDirty();\r\n                join = particleSystemDefinesJoinedCurrent;\r\n            }\r\n\r\n            const result = this._processDefines(defines);\r\n\r\n            if (result) {\r\n                Effect.RegisterShader(tempName, this._fragmentCompilationState._builtCompilationString, undefined, this.shaderLanguage);\r\n\r\n                effect = this.getScene()\r\n                    .getEngine()\r\n                    .createEffectForParticles(\r\n                        tempName,\r\n                        this._fragmentCompilationState.uniforms,\r\n                        this._fragmentCompilationState.samplers,\r\n                        defines.toString() + \"\\n\" + join,\r\n                        result?.fallbacks,\r\n                        onCompiled,\r\n                        onError,\r\n                        particleSystem\r\n                    );\r\n                particleSystem.setCustomEffect(effect, blendMode);\r\n                this._createEffectForParticles(particleSystem, blendMode, onCompiled, onError, effect, defines, particleSystemDefinesJoined); // add the effect.onBindObservable observer\r\n                return;\r\n            }\r\n\r\n            this._checkInternals(effect);\r\n        });\r\n    }\r\n\r\n    private _checkInternals(effect: Effect) {\r\n        // Animated blocks\r\n        if (this._sharedData.animatedInputs) {\r\n            const scene = this.getScene();\r\n\r\n            const frameId = scene.getFrameId();\r\n\r\n            if (this._animationFrame !== frameId) {\r\n                for (const input of this._sharedData.animatedInputs) {\r\n                    input.animate(scene);\r\n                }\r\n\r\n                this._animationFrame = frameId;\r\n            }\r\n        }\r\n\r\n        // Bindable blocks\r\n        for (const block of this._sharedData.bindableBlocks) {\r\n            block.bind(effect, this);\r\n        }\r\n\r\n        // Connection points\r\n        for (const inputBlock of this._sharedData.inputBlocks) {\r\n            inputBlock._transmit(effect, this.getScene(), this);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Create the effect to be used as the custom effect for a particle system\r\n     * @param particleSystem Particle system to create the effect for\r\n     * @param onCompiled defines a function to call when the effect creation is successful\r\n     * @param onError defines a function to call when the effect creation has failed\r\n     */\r\n    public createEffectForParticles(particleSystem: IParticleSystem, onCompiled?: (effect: Effect) => void, onError?: (effect: Effect, errors: string) => void) {\r\n        if (this.mode !== NodeMaterialModes.Particle) {\r\n            Logger.Log(\"Incompatible material mode\");\r\n            return;\r\n        }\r\n\r\n        this._createEffectForParticles(particleSystem, BaseParticleSystem.BLENDMODE_ONEONE, onCompiled, onError);\r\n        this._createEffectForParticles(particleSystem, BaseParticleSystem.BLENDMODE_MULTIPLY, onCompiled, onError);\r\n    }\r\n\r\n    /**\r\n     * Use this material as the shadow depth wrapper of a target material\r\n     * @param targetMaterial defines the target material\r\n     */\r\n    public createAsShadowDepthWrapper(targetMaterial: Material) {\r\n        if (this.mode !== NodeMaterialModes.Material) {\r\n            Logger.Log(\"Incompatible material mode\");\r\n            return;\r\n        }\r\n\r\n        targetMaterial.shadowDepthWrapper = new BABYLON.ShadowDepthWrapper(this, this.getScene());\r\n    }\r\n\r\n    private _processDefines(\r\n        defines: NodeMaterialDefines,\r\n        mesh?: AbstractMesh,\r\n        useInstances = false,\r\n        subMesh?: SubMesh\r\n    ): Nullable<{\r\n        lightDisposed: boolean;\r\n        uniformBuffers: string[];\r\n        mergedUniforms: string[];\r\n        mergedSamplers: string[];\r\n        fallbacks: EffectFallbacks;\r\n    }> {\r\n        let result = null;\r\n\r\n        // Global defines\r\n        const scene = this.getScene();\r\n        if (PrepareDefinesForCamera(scene, defines)) {\r\n            defines.markAsMiscDirty();\r\n        }\r\n\r\n        // Shared defines\r\n        for (const b of this._sharedData.blocksWithDefines) {\r\n            b.initializeDefines(defines);\r\n        }\r\n\r\n        for (const b of this._sharedData.blocksWithDefines) {\r\n            b.prepareDefines(defines, this, mesh, useInstances, subMesh);\r\n        }\r\n\r\n        // Need to recompile?\r\n        if (defines.isDirty) {\r\n            const lightDisposed = defines._areLightsDisposed;\r\n            defines.markAsProcessed();\r\n\r\n            // Repeatable content generators\r\n            this._vertexCompilationState.compilationString = this._vertexCompilationState._builtCompilationString;\r\n            this._fragmentCompilationState.compilationString = this._fragmentCompilationState._builtCompilationString;\r\n\r\n            for (const b of this._sharedData.repeatableContentBlocks) {\r\n                b.replaceRepeatableContent(this._vertexCompilationState, defines, mesh);\r\n            }\r\n\r\n            // Uniforms\r\n            const uniformBuffers: string[] = [];\r\n            for (const b of this._sharedData.dynamicUniformBlocks) {\r\n                b.updateUniformsAndSamples(this._vertexCompilationState, this, defines, uniformBuffers);\r\n            }\r\n\r\n            const mergedUniforms = this._vertexCompilationState.uniforms;\r\n\r\n            for (const u of this._fragmentCompilationState.uniforms) {\r\n                const index = mergedUniforms.indexOf(u);\r\n\r\n                if (index === -1) {\r\n                    mergedUniforms.push(u);\r\n                }\r\n            }\r\n\r\n            // Samplers\r\n            const mergedSamplers = this._vertexCompilationState.samplers;\r\n\r\n            for (const s of this._fragmentCompilationState.samplers) {\r\n                const index = mergedSamplers.indexOf(s);\r\n\r\n                if (index === -1) {\r\n                    mergedSamplers.push(s);\r\n                }\r\n            }\r\n\r\n            const fallbacks = new EffectFallbacks();\r\n\r\n            for (const b of this._sharedData.blocksWithFallbacks) {\r\n                b.provideFallbacks(fallbacks, mesh);\r\n            }\r\n\r\n            result = {\r\n                lightDisposed,\r\n                uniformBuffers,\r\n                mergedUniforms,\r\n                mergedSamplers,\r\n                fallbacks,\r\n            };\r\n        }\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Get if the submesh is ready to be used and all its information available.\r\n     * Child classes can use it to update shaders\r\n     * @param mesh defines the mesh to check\r\n     * @param subMesh defines which submesh to check\r\n     * @param useInstances specifies that instances should be used\r\n     * @returns a boolean indicating that the submesh is ready or not\r\n     */\r\n    public override isReadyForSubMesh(mesh: AbstractMesh, subMesh: SubMesh, useInstances: boolean = false): boolean {\r\n        if (!this._buildWasSuccessful) {\r\n            return false;\r\n        }\r\n\r\n        const scene = this.getScene();\r\n        if (this._sharedData.animatedInputs) {\r\n            const frameId = scene.getFrameId();\r\n\r\n            if (this._animationFrame !== frameId) {\r\n                for (const input of this._sharedData.animatedInputs) {\r\n                    input.animate(scene);\r\n                }\r\n\r\n                this._animationFrame = frameId;\r\n            }\r\n        }\r\n\r\n        const drawWrapper = subMesh._drawWrapper;\r\n\r\n        if (drawWrapper.effect && this.isFrozen) {\r\n            if (drawWrapper._wasPreviouslyReady && drawWrapper._wasPreviouslyUsingInstances === useInstances) {\r\n                return true;\r\n            }\r\n        }\r\n\r\n        if (!subMesh.materialDefines || typeof subMesh.materialDefines === \"string\") {\r\n            subMesh.materialDefines = new NodeMaterialDefines();\r\n        }\r\n\r\n        const defines = <NodeMaterialDefines>subMesh.materialDefines;\r\n        if (this._isReadyForSubMesh(subMesh)) {\r\n            return true;\r\n        }\r\n\r\n        const engine = scene.getEngine();\r\n\r\n        this._prepareDefinesForAttributes(mesh, defines);\r\n\r\n        // Check if blocks are ready\r\n        if (this._sharedData.blockingBlocks.some((b) => !b.isReady(mesh, this, defines, useInstances))) {\r\n            return false;\r\n        }\r\n\r\n        const result = this._processDefines(defines, mesh, useInstances, subMesh);\r\n\r\n        if (result) {\r\n            const previousEffect = subMesh.effect;\r\n            // Compilation\r\n            const join = defines.toString();\r\n            let effect = engine.createEffect(\r\n                {\r\n                    vertex: \"nodeMaterial\" + this._buildId,\r\n                    fragment: \"nodeMaterial\" + this._buildId,\r\n                    vertexSource: this._vertexCompilationState.compilationString,\r\n                    fragmentSource: this._fragmentCompilationState.compilationString,\r\n                },\r\n                <IEffectCreationOptions>{\r\n                    attributes: this._vertexCompilationState.attributes,\r\n                    uniformsNames: result.mergedUniforms,\r\n                    uniformBuffersNames: result.uniformBuffers,\r\n                    samplers: result.mergedSamplers,\r\n                    defines: join,\r\n                    fallbacks: result.fallbacks,\r\n                    onCompiled: this.onCompiled,\r\n                    onError: this.onError,\r\n                    multiTarget: defines.PREPASS,\r\n                    indexParameters: { maxSimultaneousLights: this.maxSimultaneousLights, maxSimultaneousMorphTargets: defines.NUM_MORPH_INFLUENCERS },\r\n                    shaderLanguage: this.shaderLanguage,\r\n                },\r\n                engine\r\n            );\r\n\r\n            if (effect) {\r\n                if (this._onEffectCreatedObservable) {\r\n                    onCreatedEffectParameters.effect = effect;\r\n                    onCreatedEffectParameters.subMesh = subMesh;\r\n                    this._onEffectCreatedObservable.notifyObservers(onCreatedEffectParameters);\r\n                }\r\n\r\n                // Use previous effect while new one is compiling\r\n                if (this.allowShaderHotSwapping && previousEffect && !effect.isReady()) {\r\n                    effect = previousEffect;\r\n                    defines.markAsUnprocessed();\r\n\r\n                    if (result.lightDisposed) {\r\n                        // re register in case it takes more than one frame.\r\n                        defines._areLightsDisposed = true;\r\n                        return false;\r\n                    }\r\n                } else {\r\n                    scene.resetCachedMaterial();\r\n                    subMesh.setEffect(effect, defines, this._materialContext);\r\n                }\r\n            }\r\n        }\r\n\r\n        // Check if Area Lights have LTC texture.\r\n        if (defines[\"AREALIGHTUSED\"]) {\r\n            for (let index = 0; index < mesh.lightSources.length; index++) {\r\n                if (!mesh.lightSources[index]._isReady()) {\r\n                    return false;\r\n                }\r\n            }\r\n        }\r\n\r\n        if (!subMesh.effect || !subMesh.effect.isReady()) {\r\n            return false;\r\n        }\r\n\r\n        defines._renderId = scene.getRenderId();\r\n        drawWrapper._wasPreviouslyReady = true;\r\n        drawWrapper._wasPreviouslyUsingInstances = useInstances;\r\n\r\n        this._checkScenePerformancePriority();\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Get a string representing the shaders built by the current node graph\r\n     */\r\n    public get compiledShaders() {\r\n        if (!this._buildWasSuccessful) {\r\n            this.build();\r\n        }\r\n        return `// Vertex shader\\n${this._vertexCompilationState.compilationString}\\n\\n// Fragment shader\\n${this._fragmentCompilationState.compilationString}`;\r\n    }\r\n\r\n    /**\r\n     * Get a string representing the fragment shader used by the engine for the current node graph\r\n     * @internal\r\n     */\r\n    public async _getProcessedFragmentAsync(): Promise<string> {\r\n        if (!this._buildWasSuccessful) {\r\n            this.build();\r\n        }\r\n\r\n        const defines = new NodeMaterialDefines();\r\n        this._processDefines(defines);\r\n\r\n        let processingDefines = defines.toString();\r\n        if (this.mode === NodeMaterialModes.SFE) {\r\n            processingDefines += `#define ${SfeModeDefine}\\n`;\r\n        }\r\n\r\n        return await this._fragmentCompilationState.getProcessedShaderAsync(processingDefines);\r\n    }\r\n\r\n    /**\r\n     * Binds the world matrix to the material\r\n     * @param world defines the world transformation matrix\r\n     */\r\n    public override bindOnlyWorldMatrix(world: Matrix): void {\r\n        const scene = this.getScene();\r\n\r\n        if (!this._activeEffect) {\r\n            return;\r\n        }\r\n\r\n        const hints = this._sharedData.hints;\r\n\r\n        if (hints.needWorldViewMatrix) {\r\n            world.multiplyToRef(scene.getViewMatrix(), this._cachedWorldViewMatrix);\r\n        }\r\n\r\n        if (hints.needWorldViewProjectionMatrix) {\r\n            world.multiplyToRef(scene.getTransformMatrix(), this._cachedWorldViewProjectionMatrix);\r\n        }\r\n\r\n        // Connection points\r\n        for (const inputBlock of this._sharedData.inputBlocks) {\r\n            inputBlock._transmitWorld(this._activeEffect, world, this._cachedWorldViewMatrix, this._cachedWorldViewProjectionMatrix);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Binds the submesh to this material by preparing the effect and shader to draw\r\n     * @param world defines the world transformation matrix\r\n     * @param mesh defines the mesh containing the submesh\r\n     * @param subMesh defines the submesh to bind the material to\r\n     */\r\n    public override bindForSubMesh(world: Matrix, mesh: Mesh, subMesh: SubMesh): void {\r\n        const scene = this.getScene();\r\n        const effect = subMesh.effect;\r\n        if (!effect) {\r\n            return;\r\n        }\r\n        this._activeEffect = effect;\r\n\r\n        // Matrices\r\n        this.bindOnlyWorldMatrix(world);\r\n\r\n        const mustRebind = this._mustRebind(scene, effect, subMesh, mesh.visibility);\r\n        const sharedData = this._sharedData;\r\n\r\n        if (mustRebind) {\r\n            // Bindable blocks\r\n            for (const block of sharedData.bindableBlocks) {\r\n                block.bind(effect, this, mesh, subMesh);\r\n            }\r\n\r\n            for (const block of sharedData.forcedBindableBlocks) {\r\n                block.bind(effect, this, mesh, subMesh);\r\n            }\r\n\r\n            // Connection points\r\n            for (const inputBlock of sharedData.inputBlocks) {\r\n                inputBlock._transmit(effect, scene, this);\r\n            }\r\n        } else if (!this.isFrozen) {\r\n            for (const block of sharedData.forcedBindableBlocks) {\r\n                block.bind(effect, this, mesh, subMesh);\r\n            }\r\n        }\r\n\r\n        this._afterBind(mesh, this._activeEffect, subMesh);\r\n    }\r\n\r\n    /**\r\n     * Gets the active textures from the material\r\n     * @returns an array of textures\r\n     */\r\n    public override getActiveTextures(): BaseTexture[] {\r\n        const activeTextures = super.getActiveTextures();\r\n\r\n        if (this._sharedData) {\r\n            activeTextures.push(...this._sharedData.textureBlocks.filter((tb) => tb.texture).map((tb) => tb.texture!));\r\n        }\r\n\r\n        return activeTextures;\r\n    }\r\n\r\n    /**\r\n     * Gets the list of texture blocks\r\n     * Note that this method will only return blocks that are reachable from the final block(s) and only after the material has been built!\r\n     * @returns an array of texture blocks\r\n     */\r\n    public getTextureBlocks(): NodeMaterialTextureBlocks[] {\r\n        if (!this._sharedData) {\r\n            return [];\r\n        }\r\n\r\n        return this._sharedData.textureBlocks;\r\n    }\r\n\r\n    /**\r\n     * Gets the list of all texture blocks\r\n     * Note that this method will scan all attachedBlocks and return blocks that are texture blocks\r\n     * @returns\r\n     */\r\n    public getAllTextureBlocks(): NodeMaterialTextureBlocks[] {\r\n        const textureBlocks: NodeMaterialTextureBlocks[] = [];\r\n\r\n        for (const block of this.attachedBlocks) {\r\n            if (NodeMaterial._BlockIsTextureBlock(block)) {\r\n                textureBlocks.push(block);\r\n            }\r\n        }\r\n\r\n        return textureBlocks;\r\n    }\r\n\r\n    /**\r\n     * Specifies if the material uses a texture\r\n     * @param texture defines the texture to check against the material\r\n     * @returns a boolean specifying if the material uses the texture\r\n     */\r\n    public override hasTexture(texture: BaseTexture): boolean {\r\n        if (super.hasTexture(texture)) {\r\n            return true;\r\n        }\r\n\r\n        if (!this._sharedData) {\r\n            return false;\r\n        }\r\n\r\n        for (const t of this._sharedData.textureBlocks) {\r\n            if (t.texture === texture) {\r\n                return true;\r\n            }\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Disposes the material\r\n     * @param forceDisposeEffect specifies if effects should be forcefully disposed\r\n     * @param forceDisposeTextures specifies if textures should be forcefully disposed\r\n     * @param notBoundToMesh specifies if the material that is being disposed is known to be not bound to any mesh\r\n     */\r\n    public override dispose(forceDisposeEffect?: boolean, forceDisposeTextures?: boolean, notBoundToMesh?: boolean): void {\r\n        if (forceDisposeTextures) {\r\n            for (const texture of this.getTextureBlocks()\r\n                .filter((tb) => tb.texture)\r\n                .map((tb) => tb.texture!)) {\r\n                texture.dispose();\r\n            }\r\n        }\r\n\r\n        for (const block of this.attachedBlocks) {\r\n            block.dispose();\r\n        }\r\n\r\n        this.attachedBlocks.length = 0;\r\n        (this._sharedData as any) = null;\r\n        (this._vertexCompilationState as any) = null;\r\n        (this._fragmentCompilationState as any) = null;\r\n\r\n        this.onBuildObservable.clear();\r\n        this.onBuildErrorObservable.clear();\r\n\r\n        if (this._imageProcessingObserver) {\r\n            this._imageProcessingConfiguration.onUpdateParameters.remove(this._imageProcessingObserver);\r\n            this._imageProcessingObserver = null;\r\n        }\r\n\r\n        super.dispose(forceDisposeEffect, forceDisposeTextures, notBoundToMesh);\r\n    }\r\n\r\n    /** Creates the node editor window.\r\n     * @param additionalConfig Define the configuration of the editor\r\n     */\r\n    private _createNodeEditor(additionalConfig?: any) {\r\n        const nodeEditorConfig: any = {\r\n            nodeMaterial: this,\r\n            ...additionalConfig,\r\n        };\r\n        this.BJSNODEMATERIALEDITOR.NodeEditor.Show(nodeEditorConfig);\r\n    }\r\n\r\n    /**\r\n     * Launch the node material editor\r\n     * @param config Define the configuration of the editor\r\n     * @returns a promise fulfilled when the node editor is visible\r\n     */\r\n    public async edit(config?: INodeMaterialEditorOptions): Promise<void> {\r\n        return await new Promise((resolve) => {\r\n            this.BJSNODEMATERIALEDITOR = this.BJSNODEMATERIALEDITOR || this._getGlobalNodeMaterialEditor();\r\n            if (typeof this.BJSNODEMATERIALEDITOR == \"undefined\") {\r\n                const editorUrl = config && config.editorURL ? config.editorURL : NodeMaterial.EditorURL;\r\n\r\n                // Load editor and add it to the DOM\r\n                Tools.LoadBabylonScript(editorUrl, () => {\r\n                    this.BJSNODEMATERIALEDITOR = this.BJSNODEMATERIALEDITOR || this._getGlobalNodeMaterialEditor();\r\n                    this._createNodeEditor(config?.nodeEditorConfig);\r\n                    resolve();\r\n                });\r\n            } else {\r\n                // Otherwise creates the editor\r\n                this._createNodeEditor(config?.nodeEditorConfig);\r\n                resolve();\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Clear the current material\r\n     */\r\n    public clear() {\r\n        this._vertexOutputNodes.length = 0;\r\n        this._fragmentOutputNodes.length = 0;\r\n        this.attachedBlocks.length = 0;\r\n        this._buildIsInProgress = false;\r\n    }\r\n\r\n    /**\r\n     * Clear the current material and set it to a default state\r\n     */\r\n    public setToDefault() {\r\n        this.clear();\r\n\r\n        this.editorData = null;\r\n\r\n        const positionInput = new InputBlock(\"Position\");\r\n        positionInput.setAsAttribute(\"position\");\r\n\r\n        const worldInput = new InputBlock(\"World\");\r\n        worldInput.setAsSystemValue(NodeMaterialSystemValues.World);\r\n\r\n        const worldPos = new TransformBlock(\"WorldPos\");\r\n        positionInput.connectTo(worldPos);\r\n        worldInput.connectTo(worldPos);\r\n\r\n        const viewProjectionInput = new InputBlock(\"ViewProjection\");\r\n        viewProjectionInput.setAsSystemValue(NodeMaterialSystemValues.ViewProjection);\r\n\r\n        const worldPosdMultipliedByViewProjection = new TransformBlock(\"WorldPos * ViewProjectionTransform\");\r\n        worldPos.connectTo(worldPosdMultipliedByViewProjection);\r\n        viewProjectionInput.connectTo(worldPosdMultipliedByViewProjection);\r\n\r\n        const vertexOutput = new VertexOutputBlock(\"VertexOutput\");\r\n        worldPosdMultipliedByViewProjection.connectTo(vertexOutput);\r\n\r\n        // Pixel\r\n        const pixelColor = new InputBlock(\"color\");\r\n        pixelColor.value = new Color4(0.8, 0.8, 0.8, 1);\r\n\r\n        const fragmentOutput = new FragmentOutputBlock(\"FragmentOutput\");\r\n        pixelColor.connectTo(fragmentOutput);\r\n\r\n        // Add to nodes\r\n        this.addOutputNode(vertexOutput);\r\n        this.addOutputNode(fragmentOutput);\r\n\r\n        this._mode = NodeMaterialModes.Material;\r\n    }\r\n\r\n    /**\r\n     * Clear the current material and set it to a default state for post process\r\n     */\r\n    public setToDefaultPostProcess() {\r\n        this.clear();\r\n\r\n        this.editorData = null;\r\n\r\n        const position = new InputBlock(\"Position\");\r\n        position.setAsAttribute(\"position2d\");\r\n\r\n        const const1 = new InputBlock(\"Constant1\");\r\n        const1.isConstant = true;\r\n        const1.value = 1;\r\n\r\n        const vmerger = new VectorMergerBlock(\"Position3D\");\r\n\r\n        position.connectTo(vmerger);\r\n        const1.connectTo(vmerger, { input: \"w\" });\r\n\r\n        const vertexOutput = new VertexOutputBlock(\"VertexOutput\");\r\n        vmerger.connectTo(vertexOutput);\r\n\r\n        // Pixel\r\n        const scale = new InputBlock(\"Scale\");\r\n        scale.visibleInInspector = true;\r\n        scale.value = new Vector2(1, 1);\r\n\r\n        const uv0 = new RemapBlock(\"uv0\");\r\n        position.connectTo(uv0);\r\n\r\n        const uv = new MultiplyBlock(\"UV scale\");\r\n        uv0.connectTo(uv);\r\n        scale.connectTo(uv);\r\n\r\n        const currentScreen = new CurrentScreenBlock(\"CurrentScreen\");\r\n        uv.connectTo(currentScreen);\r\n        const textureUrl = Tools.GetAssetUrl(\"https://assets.babylonjs.com/core/nme/currentScreenPostProcess.png\");\r\n        currentScreen.texture = new Texture(textureUrl, this.getScene());\r\n\r\n        const fragmentOutput = new FragmentOutputBlock(\"FragmentOutput\");\r\n        currentScreen.connectTo(fragmentOutput, { output: \"rgba\" });\r\n\r\n        // Add to nodes\r\n        this.addOutputNode(vertexOutput);\r\n        this.addOutputNode(fragmentOutput);\r\n\r\n        this._mode = NodeMaterialModes.PostProcess;\r\n    }\r\n\r\n    /**\r\n     * Clear the current material and set it to a default state for procedural texture\r\n     */\r\n    public setToDefaultProceduralTexture() {\r\n        this.clear();\r\n\r\n        this.editorData = null;\r\n\r\n        const position = new InputBlock(\"Position\");\r\n        position.setAsAttribute(\"position2d\");\r\n\r\n        const const1 = new InputBlock(\"Constant1\");\r\n        const1.isConstant = true;\r\n        const1.value = 1;\r\n\r\n        const vmerger = new VectorMergerBlock(\"Position3D\");\r\n\r\n        position.connectTo(vmerger);\r\n        const1.connectTo(vmerger, { input: \"w\" });\r\n\r\n        const vertexOutput = new VertexOutputBlock(\"VertexOutput\");\r\n        vmerger.connectTo(vertexOutput);\r\n\r\n        // Pixel\r\n        const time = new InputBlock(\"Time\");\r\n        time.value = 0;\r\n        time.min = 0;\r\n        time.max = 0;\r\n        time.isBoolean = false;\r\n        time.matrixMode = 0;\r\n        time.animationType = AnimatedInputBlockTypes.Time;\r\n        time.isConstant = false;\r\n\r\n        const color = new InputBlock(\"Color3\");\r\n        color.value = new Color3(1, 1, 1);\r\n        color.isConstant = false;\r\n        const fragmentOutput = new FragmentOutputBlock(\"FragmentOutput\");\r\n\r\n        const vectorMerger = new VectorMergerBlock(\"VectorMerger\");\r\n        vectorMerger.visibleInInspector = false;\r\n\r\n        const cos = new TrigonometryBlock(\"Cos\");\r\n        cos.operation = TrigonometryBlockOperations.Cos;\r\n\r\n        position.connectTo(vectorMerger);\r\n        time.output.connectTo(cos.input);\r\n        cos.output.connectTo(vectorMerger.z);\r\n        vectorMerger.xyzOut.connectTo(fragmentOutput.rgb);\r\n\r\n        // Add to nodes\r\n        this.addOutputNode(vertexOutput);\r\n        this.addOutputNode(fragmentOutput);\r\n\r\n        this._mode = NodeMaterialModes.ProceduralTexture;\r\n    }\r\n\r\n    /**\r\n     * Clear the current material and set it to a default state for particle\r\n     */\r\n    public setToDefaultParticle() {\r\n        this.clear();\r\n\r\n        this.editorData = null;\r\n\r\n        // Pixel\r\n        const uv = new InputBlock(\"uv\");\r\n        uv.setAsAttribute(\"particle_uv\");\r\n\r\n        const texture = new ParticleTextureBlock(\"ParticleTexture\");\r\n        uv.connectTo(texture);\r\n\r\n        const color = new InputBlock(\"Color\");\r\n        color.setAsAttribute(\"particle_color\");\r\n\r\n        const multiply = new MultiplyBlock(\"Texture * Color\");\r\n        texture.connectTo(multiply);\r\n        color.connectTo(multiply);\r\n\r\n        const rampGradient = new ParticleRampGradientBlock(\"ParticleRampGradient\");\r\n        multiply.connectTo(rampGradient);\r\n\r\n        const cSplitter = new ColorSplitterBlock(\"ColorSplitter\");\r\n        color.connectTo(cSplitter);\r\n\r\n        const blendMultiply = new ParticleBlendMultiplyBlock(\"ParticleBlendMultiply\");\r\n        rampGradient.connectTo(blendMultiply);\r\n        texture.connectTo(blendMultiply, { output: \"a\" });\r\n        cSplitter.connectTo(blendMultiply, { output: \"a\" });\r\n\r\n        const fragmentOutput = new FragmentOutputBlock(\"FragmentOutput\");\r\n        blendMultiply.connectTo(fragmentOutput);\r\n\r\n        // Add to nodes\r\n        this.addOutputNode(fragmentOutput);\r\n\r\n        this._mode = NodeMaterialModes.Particle;\r\n    }\r\n\r\n    /**\r\n     * Loads the current Node Material from a url pointing to a file save by the Node Material Editor\r\n     * @deprecated Please use NodeMaterial.ParseFromFileAsync instead\r\n     * @param url defines the url to load from\r\n     * @param rootUrl defines the root URL for nested url in the node material\r\n     * @returns a promise that will fulfil when the material is fully loaded\r\n     */\r\n    public async loadAsync(url: string, rootUrl: string = \"\") {\r\n        return await NodeMaterial.ParseFromFileAsync(\"\", url, this.getScene(), rootUrl, true, this);\r\n    }\r\n\r\n    private _gatherBlocks(rootNode: NodeMaterialBlock, list: NodeMaterialBlock[]) {\r\n        if (list.indexOf(rootNode) !== -1) {\r\n            return;\r\n        }\r\n        list.push(rootNode);\r\n\r\n        for (const input of rootNode.inputs) {\r\n            const connectedPoint = input.connectedPoint;\r\n            if (connectedPoint) {\r\n                const block = connectedPoint.ownerBlock;\r\n                if (block !== rootNode) {\r\n                    this._gatherBlocks(block, list);\r\n                }\r\n            }\r\n        }\r\n\r\n        // Teleportation\r\n        if (rootNode.isTeleportOut) {\r\n            const block = rootNode as NodeMaterialTeleportOutBlock;\r\n            if (block.entryPoint) {\r\n                this._gatherBlocks(block.entryPoint, list);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Generate a string containing the code declaration required to create an equivalent of this material\r\n     * @returns a string\r\n     */\r\n    public generateCode() {\r\n        let alreadyDumped: NodeMaterialBlock[] = [];\r\n        const vertexBlocks: NodeMaterialBlock[] = [];\r\n        const uniqueNames: string[] = [\"const\", \"var\", \"let\"];\r\n        // Gets active blocks\r\n        for (const outputNode of this._vertexOutputNodes) {\r\n            this._gatherBlocks(outputNode, vertexBlocks);\r\n        }\r\n\r\n        const fragmentBlocks: NodeMaterialBlock[] = [];\r\n        for (const outputNode of this._fragmentOutputNodes) {\r\n            this._gatherBlocks(outputNode, fragmentBlocks);\r\n        }\r\n\r\n        // Generate vertex shader\r\n        let codeString = `var nodeMaterial = new BABYLON.NodeMaterial(\"${this.name || \"node material\"}\");\\n`;\r\n        codeString += `nodeMaterial.mode = BABYLON.NodeMaterialModes.${NodeMaterialModes[this.mode]};\\n`;\r\n        for (const node of vertexBlocks) {\r\n            if (node.isInput && alreadyDumped.indexOf(node) === -1) {\r\n                codeString += node._dumpCode(uniqueNames, alreadyDumped);\r\n            }\r\n        }\r\n\r\n        // Generate fragment shader\r\n        for (const node of fragmentBlocks) {\r\n            if (node.isInput && alreadyDumped.indexOf(node) === -1) {\r\n                codeString += node._dumpCode(uniqueNames, alreadyDumped);\r\n            }\r\n        }\r\n\r\n        // Connections\r\n        alreadyDumped = [];\r\n        codeString += \"\\n// Connections\\n\";\r\n        for (const node of this._vertexOutputNodes) {\r\n            codeString += node._dumpCodeForOutputConnections(alreadyDumped);\r\n        }\r\n        for (const node of this._fragmentOutputNodes) {\r\n            codeString += node._dumpCodeForOutputConnections(alreadyDumped);\r\n        }\r\n\r\n        // Output nodes\r\n        codeString += \"\\n// Output nodes\\n\";\r\n        for (const node of this._vertexOutputNodes) {\r\n            codeString += `nodeMaterial.addOutputNode(${node._codeVariableName});\\n`;\r\n        }\r\n\r\n        for (const node of this._fragmentOutputNodes) {\r\n            codeString += `nodeMaterial.addOutputNode(${node._codeVariableName});\\n`;\r\n        }\r\n\r\n        codeString += `nodeMaterial.build();\\n`;\r\n\r\n        return codeString;\r\n    }\r\n\r\n    /**\r\n     * Serializes this material in a JSON representation\r\n     * @param selectedBlocks defines an optional list of blocks to serialize\r\n     * @returns the serialized material object\r\n     */\r\n    public override serialize(selectedBlocks?: NodeMaterialBlock[]): any {\r\n        const serializationObject = selectedBlocks ? {} : SerializationHelper.Serialize(this);\r\n        serializationObject.editorData = JSON.parse(JSON.stringify(this.editorData)); // Copy\r\n\r\n        let blocks: NodeMaterialBlock[] = [];\r\n\r\n        if (selectedBlocks) {\r\n            blocks = selectedBlocks;\r\n        } else {\r\n            serializationObject.customType = \"BABYLON.NodeMaterial\";\r\n            serializationObject.outputNodes = [];\r\n\r\n            // Outputs\r\n            for (const outputNode of this._vertexOutputNodes) {\r\n                this._gatherBlocks(outputNode, blocks);\r\n                serializationObject.outputNodes.push(outputNode.uniqueId);\r\n            }\r\n\r\n            for (const outputNode of this._fragmentOutputNodes) {\r\n                this._gatherBlocks(outputNode, blocks);\r\n\r\n                if (serializationObject.outputNodes.indexOf(outputNode.uniqueId) === -1) {\r\n                    serializationObject.outputNodes.push(outputNode.uniqueId);\r\n                }\r\n            }\r\n        }\r\n\r\n        // Blocks\r\n        serializationObject.blocks = [];\r\n\r\n        for (const block of blocks) {\r\n            serializationObject.blocks.push(block.serialize());\r\n        }\r\n\r\n        if (!selectedBlocks) {\r\n            for (const block of this.attachedBlocks) {\r\n                if (blocks.indexOf(block) !== -1) {\r\n                    continue;\r\n                }\r\n                serializationObject.blocks.push(block.serialize());\r\n            }\r\n        }\r\n\r\n        serializationObject.uniqueId = this.uniqueId;\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    private _restoreConnections(block: NodeMaterialBlock, source: any, map: { [key: number]: NodeMaterialBlock }) {\r\n        for (const outputPoint of block.outputs) {\r\n            for (const candidate of source.blocks) {\r\n                const target = map[candidate.id];\r\n\r\n                if (!target) {\r\n                    continue;\r\n                }\r\n\r\n                for (const input of candidate.inputs) {\r\n                    if (map[input.targetBlockId] === block && input.targetConnectionName === outputPoint.name) {\r\n                        const inputPoint = target.getInputByName(input.inputName);\r\n                        if (!inputPoint || inputPoint.isConnected) {\r\n                            continue;\r\n                        }\r\n\r\n                        outputPoint.connectTo(inputPoint, true);\r\n                        this._restoreConnections(target, source, map);\r\n                        continue;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Clear the current graph and load a new one from a serialization object\r\n     * @param source defines the JSON representation of the material\r\n     * @param rootUrl defines the root URL to use to load textures and relative dependencies\r\n     * @param merge defines whether or not the source must be merged or replace the current content\r\n     * @param urlRewriter defines a function used to rewrite urls\r\n     */\r\n    public parseSerializedObject(source: any, rootUrl: string = \"\", merge = false, urlRewriter?: (url: string) => string) {\r\n        if (!merge) {\r\n            this.clear();\r\n        }\r\n\r\n        const map: { [key: number]: NodeMaterialBlock } = {};\r\n\r\n        // Create blocks\r\n        for (const parsedBlock of source.blocks) {\r\n            const blockType = GetClass(parsedBlock.customType);\r\n            if (blockType) {\r\n                const block: NodeMaterialBlock = new blockType();\r\n                block._deserialize(parsedBlock, this.getScene(), rootUrl, urlRewriter);\r\n                map[parsedBlock.id] = block;\r\n\r\n                this.attachedBlocks.push(block);\r\n            }\r\n        }\r\n\r\n        // Reconnect teleportation\r\n        for (const block of this.attachedBlocks) {\r\n            if (block.isTeleportOut) {\r\n                const teleportOut = block as NodeMaterialTeleportOutBlock;\r\n                const id = teleportOut._tempEntryPointUniqueId;\r\n                if (id) {\r\n                    const source = map[id] as NodeMaterialTeleportInBlock;\r\n                    source.attachToEndpoint(teleportOut);\r\n                }\r\n            }\r\n        }\r\n\r\n        // Connections - Starts with input blocks only (except if in \"merge\" mode where we scan all blocks)\r\n        for (let blockIndex = 0; blockIndex < source.blocks.length; blockIndex++) {\r\n            const parsedBlock = source.blocks[blockIndex];\r\n            const block = map[parsedBlock.id];\r\n\r\n            if (!block) {\r\n                continue;\r\n            }\r\n\r\n            if (block.inputs.length && !merge) {\r\n                continue;\r\n            }\r\n            this._restoreConnections(block, source, map);\r\n        }\r\n\r\n        // Outputs\r\n        if (source.outputNodes) {\r\n            for (const outputNodeId of source.outputNodes) {\r\n                this.addOutputNode(map[outputNodeId]);\r\n            }\r\n        }\r\n\r\n        // UI related info\r\n        if (source.locations || (source.editorData && source.editorData.locations)) {\r\n            const locations: {\r\n                blockId: number;\r\n                x: number;\r\n                y: number;\r\n                isCollapsed: boolean;\r\n            }[] = source.locations || source.editorData.locations;\r\n\r\n            for (const location of locations) {\r\n                if (map[location.blockId]) {\r\n                    location.blockId = map[location.blockId].uniqueId;\r\n                }\r\n            }\r\n\r\n            if (merge && this.editorData && this.editorData.locations) {\r\n                locations.concat(this.editorData.locations);\r\n            }\r\n\r\n            if (source.locations) {\r\n                this.editorData = {\r\n                    locations: locations,\r\n                };\r\n            } else {\r\n                this.editorData = source.editorData;\r\n                this.editorData.locations = locations;\r\n            }\r\n\r\n            const blockMap: number[] = [];\r\n\r\n            for (const key in map) {\r\n                blockMap[key] = map[key].uniqueId;\r\n            }\r\n\r\n            this.editorData.map = blockMap;\r\n        }\r\n\r\n        this.comment = source.comment;\r\n\r\n        if (source.forceAlphaBlending !== undefined) {\r\n            this.forceAlphaBlending = source.forceAlphaBlending;\r\n        }\r\n\r\n        if (source.alphaMode !== undefined) {\r\n            this.alphaMode = source.alphaMode;\r\n        }\r\n\r\n        if (!merge) {\r\n            this._mode = source.mode ?? NodeMaterialModes.Material;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Clear the current graph and load a new one from a serialization object\r\n     * @param source defines the JSON representation of the material\r\n     * @param rootUrl defines the root URL to use to load textures and relative dependencies\r\n     * @param merge defines whether or not the source must be merged or replace the current content\r\n     * @deprecated Please use the parseSerializedObject method instead\r\n     */\r\n    public loadFromSerialization(source: any, rootUrl: string = \"\", merge = false) {\r\n        this.parseSerializedObject(source, rootUrl, merge);\r\n    }\r\n\r\n    /**\r\n     * Makes a duplicate of the current material.\r\n     * @param name defines the name to use for the new material\r\n     * @param shareEffect defines if the clone material should share the same effect (default is false)\r\n     * @returns the cloned material\r\n     */\r\n    public override clone(name: string, shareEffect: boolean = false): NodeMaterial {\r\n        const serializationObject = this.serialize();\r\n\r\n        const clone = SerializationHelper.Clone(() => new NodeMaterial(name, this.getScene(), this.options), this);\r\n        clone.id = name;\r\n        clone.name = name;\r\n\r\n        clone.parseSerializedObject(serializationObject);\r\n        clone._buildId = this._buildId;\r\n        clone.build(false, !shareEffect);\r\n\r\n        return clone;\r\n    }\r\n\r\n    /**\r\n     * Awaits for all the material textures to be ready before resolving the returned promise.\r\n     * @returns A promise that resolves when the textures are ready.\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/promise-function-async, no-restricted-syntax\r\n    public whenTexturesReadyAsync(): Promise<void[]> {\r\n        // Ensures all textures are ready to render.\r\n        const textureReadyPromises: Promise<void>[] = [];\r\n        const activeTextures = this.getActiveTextures();\r\n        for (const texture of activeTextures) {\r\n            const internalTexture = texture.getInternalTexture();\r\n            if (internalTexture && !internalTexture.isReady) {\r\n                textureReadyPromises.push(\r\n                    new Promise((textureResolve, textureReject) => {\r\n                        internalTexture.onLoadedObservable.addOnce(() => {\r\n                            textureResolve();\r\n                        });\r\n                        internalTexture.onErrorObservable.addOnce((e) => {\r\n                            // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors\r\n                            textureReject(e);\r\n                        });\r\n                    })\r\n                );\r\n            }\r\n        }\r\n\r\n        return Promise.all(textureReadyPromises);\r\n    }\r\n\r\n    /**\r\n     * Creates a node material from parsed material data\r\n     * @param source defines the JSON representation of the material\r\n     * @param scene defines the hosting scene\r\n     * @param rootUrl defines the root URL to use to load textures and relative dependencies\r\n     * @param shaderLanguage defines the language to use (GLSL by default)\r\n     * @returns a new node material\r\n     */\r\n    public static override Parse(source: any, scene: Scene, rootUrl: string = \"\", shaderLanguage = ShaderLanguage.GLSL): NodeMaterial {\r\n        const nodeMaterial = SerializationHelper.Parse(() => new NodeMaterial(source.name, scene, { shaderLanguage: shaderLanguage }), source, scene, rootUrl);\r\n\r\n        nodeMaterial.parseSerializedObject(source, rootUrl);\r\n        nodeMaterial.build();\r\n\r\n        return nodeMaterial;\r\n    }\r\n\r\n    /**\r\n     * Creates a node material from a snippet saved in a remote file\r\n     * @param name defines the name of the material to create\r\n     * @param url defines the url to load from\r\n     * @param scene defines the hosting scene\r\n     * @param rootUrl defines the root URL for nested url in the node material\r\n     * @param skipBuild defines whether to build the node material\r\n     * @param targetMaterial defines a material to use instead of creating a new one\r\n     * @param urlRewriter defines a function used to rewrite urls\r\n     * @param options defines options to be used with the node material\r\n     * @returns a promise that will resolve to the new node material\r\n     */\r\n    public static async ParseFromFileAsync(\r\n        name: string,\r\n        url: string,\r\n        scene: Scene,\r\n        rootUrl: string = \"\",\r\n        skipBuild: boolean = false,\r\n        targetMaterial?: NodeMaterial,\r\n        urlRewriter?: (url: string) => string,\r\n        options?: Partial<INodeMaterialOptions>\r\n    ): Promise<NodeMaterial> {\r\n        const material = targetMaterial ?? new NodeMaterial(name, scene, options);\r\n\r\n        const data = await scene._loadFileAsync(url);\r\n        const serializationObject = JSON.parse(data);\r\n        material.parseSerializedObject(serializationObject, rootUrl, undefined, urlRewriter);\r\n        if (!skipBuild) {\r\n            material.build();\r\n        }\r\n        return material;\r\n    }\r\n\r\n    /**\r\n     * Creates a node material from a snippet saved by the node material editor\r\n     * @param snippetId defines the snippet to load\r\n     * @param scene defines the hosting scene\r\n     * @param rootUrl defines the root URL to use to load textures and relative dependencies\r\n     * @param nodeMaterial defines a node material to update (instead of creating a new one)\r\n     * @param skipBuild defines whether to build the node material\r\n     * @param waitForTextureReadyness defines whether to wait for texture readiness resolving the promise (default: false)\r\n     * @param urlRewriter defines a function used to rewrite urls\r\n     * @param options defines options to be used with the node material\r\n     * @returns a promise that will resolve to the new node material\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/promise-function-async, no-restricted-syntax\r\n    public static ParseFromSnippetAsync(\r\n        snippetId: string,\r\n        scene: Scene = EngineStore.LastCreatedScene!,\r\n        rootUrl: string = \"\",\r\n        nodeMaterial?: NodeMaterial,\r\n        skipBuild: boolean = false,\r\n        waitForTextureReadyness: boolean = false,\r\n        urlRewriter?: (url: string) => string,\r\n        options?: Partial<INodeMaterialOptions>\r\n    ): Promise<NodeMaterial> {\r\n        if (snippetId === \"_BLANK\") {\r\n            return Promise.resolve(NodeMaterial.CreateDefault(\"blank\", scene));\r\n        }\r\n\r\n        return new Promise((resolve, reject) => {\r\n            const request = new WebRequest();\r\n            request.addEventListener(\"readystatechange\", () => {\r\n                if (request.readyState == 4) {\r\n                    if (request.status == 200) {\r\n                        const snippet = JSON.parse(JSON.parse(request.responseText).jsonPayload);\r\n                        const serializationObject = JSON.parse(snippet.nodeMaterial);\r\n\r\n                        if (!nodeMaterial) {\r\n                            nodeMaterial = SerializationHelper.Parse(() => new NodeMaterial(snippetId, scene, options), serializationObject, scene, rootUrl);\r\n                            nodeMaterial.uniqueId = scene.getUniqueId();\r\n                        }\r\n\r\n                        nodeMaterial.parseSerializedObject(serializationObject, undefined, undefined, urlRewriter);\r\n                        nodeMaterial.snippetId = snippetId;\r\n\r\n                        // We reset sideOrientation to default value\r\n                        nodeMaterial.sideOrientation = null;\r\n\r\n                        try {\r\n                            if (!skipBuild) {\r\n                                nodeMaterial.build();\r\n                            }\r\n                        } catch (err) {\r\n                            // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors\r\n                            reject(err);\r\n                        }\r\n\r\n                        if (waitForTextureReadyness) {\r\n                            nodeMaterial\r\n                                .whenTexturesReadyAsync()\r\n                                // eslint-disable-next-line github/no-then\r\n                                .then(() => {\r\n                                    resolve(nodeMaterial!);\r\n                                })\r\n                                // eslint-disable-next-line github/no-then\r\n                                .catch((err) => {\r\n                                    // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors\r\n                                    reject(err);\r\n                                });\r\n                        } else {\r\n                            resolve(nodeMaterial);\r\n                        }\r\n                    } else {\r\n                        // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors\r\n                        reject(\"Unable to load the snippet \" + snippetId);\r\n                    }\r\n                }\r\n            });\r\n\r\n            request.open(\"GET\", this.SnippetUrl + \"/\" + snippetId.replace(/#/g, \"/\"));\r\n            request.send();\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Creates a new node material set to default basic configuration\r\n     * @param name defines the name of the material\r\n     * @param scene defines the hosting scene\r\n     * @returns a new NodeMaterial\r\n     */\r\n    public static CreateDefault(name: string, scene?: Scene) {\r\n        const newMaterial = new NodeMaterial(name, scene);\r\n\r\n        newMaterial.setToDefault();\r\n        newMaterial.build();\r\n\r\n        return newMaterial;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.NodeMaterial\", NodeMaterial);\r\n"], "names": [], "mappings": ";;;;;AAEA,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAG/C,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAC1D,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,wBAAwB,CAAC;AAExD,OAAO,EAAE,sBAAsB,EAAE,MAAM,0BAA0B,CAAC;AAElE,OAAO,EAAE,MAAM,EAAE,MAAM,WAAW,CAAC;AAGnC,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACnD,OAAO,EAAE,wBAAwB,EAAE,MAAM,kCAAkC,CAAC;AAC5E,OAAO,EAAE,gCAAgC,EAAE,MAAM,oCAAoC,CAAC;AAEtF,OAAO,EAAE,eAAe,EAAE,MAAM,iCAAiC,CAAC;AAIlE,OAAO,EAAE,YAAY,EAAE,MAAM,sBAAsB,CAAC;AACpD,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AACzC,OAAO,EAAE,aAAa,EAAE,MAAM,kDAAkD,CAAC;AACjF,OAAO,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AACzD,OAAO,EAAE,iBAAiB,EAAE,MAAM,mCAAmC,CAAC;AACtE,OAAO,EAAE,mBAAmB,EAAE,MAAM,uCAAuC,CAAC;AAC5E,OAAO,EAAE,UAAU,EAAE,MAAM,2BAA2B,CAAC;AACvD,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAE,MAAM,sBAAsB,CAAC;AAC/D,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAC;AAClD,OAAO,EAAE,mBAAmB,EAAE,MAAM,qCAAqC,CAAC;AAI1E,OAAO,EAAE,kBAAkB,EAAE,MAAM,kCAAkC,CAAC;AACtE,OAAO,EAAE,oBAAoB,EAAE,MAAM,wCAAwC,CAAC;AAC9E,OAAO,EAAE,yBAAyB,EAAE,MAAM,6CAA6C,CAAC;AACxF,OAAO,EAAE,0BAA0B,EAAE,MAAM,8CAA8C,CAAC;AAC1F,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AACrD,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AAEnD,OAAO,EAAE,WAAW,EAAE,MAAM,iCAAiC,CAAC;AAG9D,OAAO,EAAE,iBAAiB,EAAE,MAAM,4BAA4B,CAAC;AAC/D,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AACjD,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AACvD,OAAO,EAAE,iBAAiB,EAAE,MAAM,2BAA2B,CAAC;AAC9D,OAAO,EAAE,OAAO,EAAE,MAAM,qBAAqB,CAAC;AAE9C,OAAO,EAAE,kBAAkB,EAAE,MAAM,oCAAoC,CAAC;AACxE,OAAO,EAAE,kBAAkB,EAAE,MAAM,6BAA6B,CAAC;AACjE,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AACrD,OAAO,EAAE,iBAAiB,EAAE,MAAM,2CAA2C,CAAC;AAC9E,OAAO,EAAE,uBAAuB,EAAE,MAAM,wCAAwC,CAAC;AACjF,OAAO,EAAE,iBAAiB,EAAE,2BAA2B,EAAE,MAAM,4BAA4B,CAAC;AAC5F,OAAO,EAAE,wBAAwB,EAAE,MAAM,kCAAkC,CAAC;AAE5E,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AASxD,OAAO,EAAE,MAAM,EAAE,6BAAyB;AAC1C,OAAO,EAAE,uBAAuB,EAAE,wBAAwB,EAAE,MAAM,6BAA6B,CAAC;AAGhG,OAAO,EAAE,cAAc,EAAE,MAAM,8BAA8B,CAAC;AAE9D,OAAO,EAAE,+BAA+B,EAAE,MAAM,qCAAqC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEtF,MAAM,yBAAyB,GAAG;IAAE,MAAM,EAAE,IAAyB;IAAE,OAAO,EAAE,IAAoC;AAAA,CAAE,CAAC;AAmBjH,MAAO,mBAAoB,gLAAQ,kBAAe;IAuIpD;;;;;OAKG,CACI,QAAQ,CAAC,IAAY,EAAE,KAAU,EAAkC;uCAAhC,wBAAwB,yCAAG,KAAK;QACtE,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,SAAS,EAAE,CAAC;YAC3B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1B,CAAC;QAED,IAAI,wBAAwB,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,EAAE,CAAC;YACnD,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC7B,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;IACvB,CAAC;IAxBD;;OAEG,CACH,aAAA;QACI,KAAK,EAAE,CAAC;QAlIZ,WAAA,EAAa,CACN,IAAA,CAAA,MAAM,GAAG,KAAK,CAAC;QACtB,YAAA,EAAc,CACP,IAAA,CAAA,OAAO,GAAG,KAAK,CAAC;QACvB,iBAAA,EAAmB,CACZ,IAAA,CAAA,eAAe,GAAG,KAAK,CAAC;QAC/B,UAAA,EAAY,CACL,IAAA,CAAA,GAAG,GAAG,KAAK,CAAC;QACnB,SAAA,EAAW,CACJ,IAAA,CAAA,GAAG,GAAG,KAAK,CAAC;QACnB,SAAA,EAAW,CACJ,IAAA,CAAA,GAAG,GAAG,KAAK,CAAC;QACnB,SAAA,EAAW,CACJ,IAAA,CAAA,GAAG,GAAG,KAAK,CAAC;QACnB,SAAA,EAAW,CACJ,IAAA,CAAA,GAAG,GAAG,KAAK,CAAC;QACnB,SAAA,EAAW,CACJ,IAAA,CAAA,GAAG,GAAG,KAAK,CAAC;QAEnB,aAAA,EAAe,CACR,IAAA,CAAA,OAAO,GAAG,KAAK,CAAC;QACvB,mBAAA,EAAqB,CACd,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QAC9B,yBAAA,EAA2B,CACpB,IAAA,CAAA,oBAAoB,GAAG,CAAC,CAAC,CAAC;QACjC,yBAAA,EAA2B,CACpB,IAAA,CAAA,oBAAoB,GAAG,KAAK,CAAC;QACpC,+BAAA,EAAiC,CAC1B,IAAA,CAAA,0BAA0B,GAAG,CAAC,CAAC,CAAC;QACvC,qBAAA,EAAuB,CAChB,IAAA,CAAA,gBAAgB,GAAG,KAAK,CAAC;QAChC,2BAAA,EAA6B,CACtB,IAAA,CAAA,sBAAsB,GAAG,CAAC,CAAC,CAAC;QACnC,2BAAA,EAA6B,CACtB,IAAA,CAAA,sBAAsB,GAAG,KAAK,CAAC;QACtC,iCAAA,EAAmC,CAC5B,IAAA,CAAA,4BAA4B,GAAG,CAAC,CAAC,CAAC;QACzC,kBAAA,EAAoB,CACb,IAAA,CAAA,aAAa,GAAG,KAAK,CAAC;QAC7B,wBAAA,EAA0B,CACnB,IAAA,CAAA,mBAAmB,GAAG,CAAC,CAAC,CAAC;QAChC,qBAAA,EAAuB,CAChB,IAAA,CAAA,yBAAyB,GAAG,KAAK,CAAC;QACzC,2BAAA,EAA6B,CACtB,IAAA,CAAA,+BAA+B,GAAG,CAAC,CAAC,CAAC;QAC5C,oBAAA,EAAsB,CACf,IAAA,CAAA,eAAe,GAAG,CAAC,CAAC;QAE3B,UAAA,EAAY,CACL,IAAA,CAAA,oBAAoB,GAAG,CAAC,CAAC;QAChC,mBAAA,EAAqB,CACd,IAAA,CAAA,YAAY,GAAG,CAAC,CAAC;QACxB,mCAAA,EAAqC,CAC9B,IAAA,CAAA,WAAW,GAAG,KAAK,CAAC;QAE3B,kBAAA,EAAoB,CACb,IAAA,CAAA,YAAY,GAAG,KAAK,CAAC;QAC5B,0BAAA,EAA4B,CACrB,IAAA,CAAA,qBAAqB,GAAG,KAAK,CAAC;QACrC,wBAAA,EAA0B,CACnB,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QACnC,yBAAA,EAA2B,CACpB,IAAA,CAAA,oBAAoB,GAAG,KAAK,CAAC;QACpC,oBAAA,EAAsB,CACf,IAAA,CAAA,eAAe,GAAG,KAAK,CAAC;QAC/B,qBAAA,EAAuB,CAChB,IAAA,CAAA,gBAAgB,GAAG,KAAK,CAAC;QACzB,IAAA,CAAA,kBAAkB,GAAG,KAAK,CAAC;QAClC,mCAAA,EAAqC,CAC9B,IAAA,CAAA,+BAA+B,GAAG,KAAK,CAAC;QAC/C,iCAAA,EAAmC,CAC5B,IAAA,CAAA,6BAA6B,GAAG,KAAK,CAAC;QAC7C,kCAAA,EAAoC,CAC7B,IAAA,CAAA,8BAA8B,GAAG,KAAK,CAAC;QAC9C,6BAAA,EAA+B,CACxB,IAAA,CAAA,yBAAyB,GAAG,KAAK,CAAC;QACzC,8BAAA,EAAgC,CACzB,IAAA,CAAA,0BAA0B,GAAG,KAAK,CAAC;QACnC,IAAA,CAAA,4BAA4B,GAAG,KAAK,CAAC;QAC5C,gCAAA,EAAkC,CAC3B,IAAA,CAAA,qBAAqB,GAAG,CAAC,CAAC;QACjC,+CAAA,EAAiD,CAC1C,IAAA,CAAA,oBAAoB,GAAG,KAAK,CAAC;QAEpC,qBAAA,EAAuB,CAChB,IAAA,CAAA,eAAe,GAAG,KAAK,CAAC;QAC/B,aAAA,EAAe,CACR,IAAA,CAAA,QAAQ,GAAG,KAAK,CAAC;QACxB,qCAAA,EAAuC,CAChC,IAAA,CAAA,yBAAyB,GAAG,KAAK,CAAC;QACzC,mCAAA,EAAqC,CAC9B,IAAA,CAAA,uBAAuB,GAAG,KAAK,CAAC;QACvC,iBAAA,EAAmB,CACZ,IAAA,CAAA,WAAW,GAAG,CAAC,CAAC;QACvB,aAAA,EAAe,CACR,IAAA,CAAA,QAAQ,GAAG,KAAK,CAAC;QACxB,aAAA,EAAe,CACR,IAAA,CAAA,QAAQ,GAAG,KAAK,CAAC;QACxB,iBAAA,EAAmB,CACZ,IAAA,CAAA,WAAW,GAAG,KAAK,CAAC;QAC3B,kBAAA,EAAoB,CACb,IAAA,CAAA,YAAY,GAAG,KAAK,CAAC;QAC5B,qBAAA,EAAuB,CAChB,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QAC9B,wBAAA,EAA0B,CACnB,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QACnC,wBAAA,EAA0B,CACnB,IAAA,CAAA,eAAe,GAAG,KAAK,CAAC;QAC/B,cAAA,EAAgB,CACT,IAAA,CAAA,MAAM,GAAG,KAAK,CAAC;QACtB,4CAAA,EAA8C,CACvC,IAAA,CAAA,0BAA0B,GAAG,KAAK,CAAC;QAC1C,qBAAA,EAAuB,CAChB,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QAEnC,UAAA,EAAY,CACL,IAAA,CAAA,YAAY,GAAG,CAAC,CAAC;QACxB,2BAAA,EAA6B,CACtB,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QACnC,0BAAA,EAA4B,CACrB,IAAA,CAAA,kBAAkB,GAAG,KAAK,CAAC;QAE3B,IAAA,CAAA,kBAAkB,GAAG,IAAI,CAAC;QAE1B,IAAA,CAAA,qBAAqB,GAAG,IAAI,CAAC;QAOhC,IAAI,CAAC,OAAO,EAAE,CAAC;IACnB,CAAC;CAmBJ;AA+BK,MAAO,YAAa,6KAAQ,eAAY;IA6B1C;;;;OAIG,CACI,MAAM,CAAC,oBAAoB,CAAC,KAAwB,EAAA;QACvD,OAAO,AACH,KAAK,CAAC,YAAY,EAAE,KAAK,cAAc,IACvC,KAAK,CAAC,YAAY,EAAE,KAAK,4BAA4B,IACrD,KAAK,CAAC,YAAY,EAAE,KAAK,wBAAwB,IACjD,KAAK,CAAC,YAAY,EAAE,KAAK,iBAAiB,IAC1C,KAAK,CAAC,YAAY,EAAE,KAAK,iBAAiB,IAC1C,KAAK,CAAC,YAAY,EAAE,KAAK,oBAAoB,IAC7C,KAAK,CAAC,YAAY,EAAE,KAAK,yBAAyB,IAClD,KAAK,CAAC,YAAY,EAAE,KAAK,sBAAsB,IAC/C,KAAK,CAAC,YAAY,EAAE,KAAK,kBAAkB,IAC3C,KAAK,CAAC,YAAY,EAAE,KAAK,gBAAgB,IACzC,KAAK,CAAC,YAAY,EAAE,KAAK,eAAe,IACxC,KAAK,CAAC,YAAY,EAAE,KAAK,qBAAqB,CACjD,CAAC;IACN,CAAC;IAOD,IAAoB,gBAAgB,CAAC,KAAc,EAAA;QAC/C,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;IACrC,CAAC;IAED;;OAEG,CACK,4BAA4B,GAAA;QAChC,0DAA0D;QAC1D,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE,CAAC;YACpC,OAAO,UAAU,CAAC;QACtB,CAAC;QAED,gFAAgF;QAChF,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,OAAO,CAAC,UAAU,KAAK,WAAW,EAAE,CAAC;YAC9E,OAAO,OAAO,CAAC;QACnB,CAAC;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAED,4CAAA,EAA8C,CAC9C,IAAoB,cAAc,GAAA;;QAC9B,8BAAW,CAAC,QAAQ,mDAAb,eAAe,cAAc,KAAI,YAAY,CAAC,qBAAqB,CAAC;IAC/E,CAAC;IAED,IAAoB,cAAc,CAAC,KAAqB,EAAA;QACpD,IAAI,CAAC,QAAQ,CAAC,cAAc,GAAG,KAAK,CAAC;IACzC,CAAC;IA6CD,uEAAA,EAAyE,CACzE,IAAW,OAAO,GAAA;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,IAAW,OAAO,CAAC,OAA6B,EAAA;QAC5C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;IAC5B,CAAC;IAOD;;OAEG,CACH,IAAW,4BAA4B,GAAA;QACnC,OAAO,IAAI,CAAC,6BAA6B,CAAC;IAC9C,CAAC;IAED;;;;OAIG,CACH,IAAW,4BAA4B,CAAC,KAAmC,EAAA;QACvE,IAAI,CAAC,mCAAmC,CAAC,KAAK,CAAC,CAAC;QAEhD,qCAAqC;QACrC,IAAI,CAAC,gCAAgC,EAAE,CAAC;IAC5C,CAAC;IAcD;;OAEG,CACH,IAAW,IAAI,GAAA;QACX,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,IAAW,IAAI,CAAC,KAAwB,EAAA;QACpC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACvB,CAAC;IAED,kGAAA,EAAoG,CACpG,IAAW,OAAO,GAAA;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,IAAW,OAAO,CAAC,KAAa,EAAA;QAC5B,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;IAC1B,CAAC;IAmCD;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,cAAc,CAAC;IAC1B,CAAC;IAOD;;;OAGG,CACO,mCAAmC,CAAC,aAAqD,EAAA;QAC/F,IAAI,aAAa,KAAK,IAAI,CAAC,6BAA6B,EAAE,CAAC;YACvD,OAAO;QACX,CAAC;QAED,qBAAqB;QACrB,IAAI,IAAI,CAAC,6BAA6B,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;YACtE,IAAI,CAAC,6BAA6B,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAChG,CAAC;QAED,0CAA0C;QAC1C,IAAI,CAAC,aAAa,EAAE,CAAC;YACjB,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,4BAA4B,CAAC;QACtF,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,6BAA6B,GAAG,aAAa,CAAC;QACvD,CAAC;QAED,qBAAqB;QACrB,IAAI,IAAI,CAAC,6BAA6B,EAAE,CAAC;YACrC,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,6BAA6B,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC3F,IAAI,CAAC,uCAAuC,EAAE,CAAC;YACnD,CAAC,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAED;;;;OAIG,CACI,cAAc,CAAC,IAAY,EAAA;QAC9B,IAAI,MAAM,GAAG,IAAI,CAAC;QAClB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,cAAc,CAAE,CAAC;YACtC,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;gBACtB,IAAI,CAAC,MAAM,EAAE,CAAC;oBACV,MAAM,GAAG,KAAK,CAAC;gBACnB,CAAC,MAAM,CAAC;4KACJ,QAAK,CAAC,IAAI,CAAC,+CAA+C,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC;oBACzE,OAAO,MAAM,CAAC;gBAClB,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;OAIG,CACI,mBAAmB,CAAC,SAAgD,EAAA;QACvE,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,cAAc,CAAE,CAAC;YACtC,IAAI,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;gBACnB,OAAO,KAAK,CAAC;YACjB,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,wBAAwB,CAAC,SAAyC,EAAA;QACrE,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,cAAc,CAAE,CAAC;YACtC,IAAI,KAAK,CAAC,OAAO,IAAI,SAAS,CAAC,KAAmB,CAAC,EAAE,CAAC;gBAClD,OAAO,KAAmB,CAAC;YAC/B,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG,CACI,cAAc,GAAA;QACjB,MAAM,MAAM,GAAiB,EAAE,CAAC;QAChC,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,cAAc,CAAE,CAAC;YACtC,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;gBAChB,MAAM,CAAC,IAAI,CAAC,KAAmB,CAAC,CAAC;YACrC,CAAC;QACL,CAAC;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;OAIG,CACI,iBAAiB,CAAC,SAAgC,EAAA;QACrD,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAElD,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;YACb,OAAO;QACX,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAEjC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,mBAAmB,CAAC,SAAgC,EAAA;QACvD,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAElD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACf,OAAO;QACX,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAElC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,aAAa,CAAC,IAAuB,EAAA;QACxC,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,EAAE,CAAC;YACvB,4CAA4C;YAC5C,MAAM,+FAA+F,CAAC;QAC1G,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,oMAAG,2BAAwB,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;YACxD,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,mMAAG,4BAAwB,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YAC1D,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;QACtC,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,gBAAgB,CAAC,IAAuB,EAAA;QAC3C,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,EAAE,CAAC;YACvB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,oMAAG,2BAAwB,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;YACxD,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;QACvC,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,oMAAG,2BAAwB,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YAC1D,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,oBAAoB,CAAC,IAAuB,EAAA;QAChD,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YAC/C,OAAO;QACX,CAAC;QAED,IAAI,CAAC,MAAM,oMAAG,2BAAwB,CAAC,MAAM,CAAC;QAC9C,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEnC,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,uBAAuB,CAAC,IAAuB,EAAA;QACnD,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACpD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACf,OAAO;QACX,CAAC;QAED,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAEzC,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,sBAAsB,CAAC,IAAuB,EAAA;QAClD,IAAI,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YACjD,OAAO;QACX,CAAC;QAED,IAAI,CAAC,MAAM,oMAAG,2BAAwB,CAAC,QAAQ,CAAC;QAChD,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAErC,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,yBAAyB,CAAC,IAAuB,EAAA;QACrD,MAAM,KAAK,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACtD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACf,OAAO;QACX,CAAC;QAED,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAE3C,OAAO,IAAI,CAAC;IAChB,CAAC;IAQD,IAAoB,iBAAiB,GAAA;QACjC,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzC,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAI,CAAF,AAA2B,CAAC,eAAe,IAAK,CAAyB,CAAC,eAAe,CAAC,WAAW,CAAC,EAAE,CAAC;YAC9I,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;OAGG,CACa,iBAAiB,GAAA;QAC7B,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,OAAO,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,AAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACzH,CAAC;IAED;;;OAGG,CACa,gBAAgB,GAAA;QAC5B,OAAO,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,gBAAgB,CAAC;IACvE,CAAC;IAEO,wBAAwB,CAAC,KAAwB,EAAE,KAA6B,EAAE,gCAAqD,EAAsB;4BAApB,aAAa,oDAAG,IAAI;QACjK,IAAI,KAAK,CAAC,MAAM,sMAAK,2BAAwB,CAAC,iBAAiB,EAAE,CAAC;YAC9D,gCAAgC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjD,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,sMAAK,2BAAwB,CAAC,QAAQ,IAAI,KAAK,CAAC,MAAM,sMAAK,2BAAwB,CAAC,MAAM,IAAI,KAAK,CAAC,cAAc,KAAK,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC1J,gCAAgC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjD,CAAC;QACD,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,KAAK,EAAE,gCAAgC,EAAE,aAAa,CAAC,CAAC;IACzF,CAAC;IAEO,YAAY,CAAC,IAAuB,EAAA;QACxC,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YAC3C,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAChB,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;gBAEtC,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,cAAc,CAAE,CAAC;oBACtC,IAAI,KAAK,CAAC,YAAY,EAAE,KAAK,SAAS,EAAE,CAAC;wBACrC,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,uCAAgD,OAAT,SAAS,EAAA,QAA2B,CAAC,CAAC;wBAC9G,OAAO;oBACX,CAAC;gBACL,CAAC;YACL,CAAC;YACD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnC,CAAC;IACL,CAAC;IAEO,gBAAgB,CAAC,IAAuB,EAAE,KAA6B,EAAE,gCAAqD,EAAsB;4BAApB,aAAa,oDAAG,IAAI;QACxJ,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACvB,IAAI,aAAa,EAAE,CAAC;YAChB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAC7B,CAAC;QACD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;QAEpC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QAExB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,CAAE,CAAC;YAC9B,KAAK,CAAC,sBAAsB,GAAG,EAAE,CAAC;YAElC,MAAM,cAAc,GAAG,KAAK,CAAC,cAAc,CAAC;YAC5C,IAAI,cAAc,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC;gBACrD,MAAM,KAAK,GAAG,cAAc,CAAC,UAAU,CAAC;gBACxC,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;oBACjB,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,KAAK,EAAE,gCAAgC,EAAE,aAAa,CAAC,CAAC;gBACjG,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO;QACP,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,+DAA+D;YAC/D,MAAM,SAAS,GAAG,IAAiB,CAAC;YACpC,IAAI,SAAS,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;gBAChC,KAAK,MAAM,QAAQ,IAAI,SAAS,CAAC,MAAM,CAAC,SAAS,CAAE,CAAC;oBAChD,MAAM,KAAK,GAAG,QAAQ,CAAC,UAAU,CAAC;oBAClC,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;wBAC7B,SAAS;oBACb,CAAC;oBACD,KAAK,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,gCAAgC;oBAClE,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,KAAK,EAAE,gCAAgC,EAAE,aAAa,CAAC,CAAC;gBACjG,CAAC;YACL,CAAC;QACL,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YAC5B,gBAAgB;YAChB,MAAM,QAAQ,GAAG,IAAoC,CAAC;YACtD,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC;gBACtB,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,UAAU,EAAE,KAAK,EAAE,gCAAgC,EAAE,aAAa,CAAC,CAAC;YAC/G,CAAC;QACL,CAAC;QAED,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,CAAE,CAAC;YAChC,MAAM,CAAC,sBAAsB,GAAG,EAAE,CAAC;QACvC,CAAC;IACL,CAAC;IAEO,gBAAgB,CAAC,IAAuB,EAAE,EAAU,EAAA;QACxD,IAAI,IAAI,CAAC,MAAM,qMAAK,4BAAwB,CAAC,iBAAiB,EAAE,CAAC;YAC7D,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QACtB,CAAC;QAED,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,CAAE,CAAC;YAC9B,MAAM,cAAc,GAAG,KAAK,CAAC,cAAc,CAAC;YAC5C,IAAI,cAAc,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC;gBACrD,MAAM,KAAK,GAAG,cAAc,CAAC,UAAU,CAAC;gBACxC,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;oBACjB,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;gBACrC,CAAC;YACL,CAAC;QACL,CAAC;QAED,kEAAkE;QAClE,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,MAAM,WAAW,GAAG,IAAoC,CAAC;YACzD,IAAI,WAAW,CAAC,UAAU,EAAE,CAAC;gBACzB,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;YACtD,CAAC;QACL,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACrB,OAAO;YACP,MAAM,SAAS,GAAG,IAAiB,CAAC;YACpC,IAAI,SAAS,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;gBAChC,KAAK,MAAM,QAAQ,IAAI,SAAS,CAAC,MAAM,CAAC,SAAS,CAAE,CAAC;oBAChD,MAAM,KAAK,GAAG,QAAQ,CAAC,UAAU,CAAC;oBAClC,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;wBAC7B,SAAS;oBACb,CAAC;oBACD,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;gBACrC,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,WAAW,CAAC,KAAwB,EAAA;QACvC,MAAM,kBAAkB,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAC9D,IAAI,kBAAkB,GAAG,CAAC,CAAC,EAAE,CAAC;YAC1B,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,KAAK,CAAC,aAAa,EAAE,CAAC;YACtB,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QACjC,CAAC;IACL,CAAC;IAED;;;;;OAKG,CACI,KAAK,GAAsE;YAArE,2EAAmB,KAAK,kBAAE,aAAa,oDAAG,IAAI,kBAAE,aAAa,oDAAG,KAAK;QAC9E,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;qKAC1B,SAAM,CAAC,IAAI,CAAC,oHAAoH,CAAC,CAAC;YAClI,OAAO;QACX,CAAC;QACD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC/B,cAAc;QACd,IAAI,CAAC,IAAI,CAAC,uBAAuB,IAAI,CAAC,aAAa,EAAE,CAAC;YAClD,aAAa,GAAG,IAAI,CAAC;QACzB,CAAC;QAED,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;QACjC,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC;QAE3C,MAAM,uBAAuB,GAAG,IAAI,CAAC,KAAK,+LAAK,oBAAiB,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,+LAAK,oBAAiB,CAAC,GAAG,CAAC;QAElH,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC;YACnE,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,+CAA+C,CAAC,CAAC;YAC7F,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;YAChC,OAAO;QACX,CAAC;QAED,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzC,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,iDAAiD,CAAC,CAAC;YAC/F,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;YAChC,OAAO;QACX,CAAC;QAED,oBAAoB;QACpB,IAAI,CAAC,uBAAuB,GAAG,0LAAI,yBAAsB,EAAE,CAAC;QAC5D,IAAI,CAAC,uBAAuB,CAAC,qBAAqB,GAAG,MAAM,CAAC,sBAAsB,CAAC;QACnF,IAAI,CAAC,uBAAuB,CAAC,MAAM,mMAAG,4BAAwB,CAAC,MAAM,CAAC;QACtE,IAAI,CAAC,yBAAyB,GAAG,yLAAI,0BAAsB,EAAE,CAAC;QAC9D,IAAI,CAAC,yBAAyB,CAAC,qBAAqB,GAAG,MAAM,CAAC,sBAAsB,CAAC;QACrF,IAAI,CAAC,yBAAyB,CAAC,MAAM,mMAAG,4BAAwB,CAAC,QAAQ,CAAC;QAE1E,cAAc;QACd,MAAM,eAAe,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,uBAAuB,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;QACtG,IAAI,mBAAmB,GAAG,IAAI,CAAC,oBAAoB,CAAC;QAEpD,IAAI,eAAe,EAAE,CAAC;YAClB,qCAAqC;YACrC,mBAAmB,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,CAAC,uBAAuB,CAAC,CAAC;YAE1F,mCAAmC;YACnC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,uBAAuB,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxH,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,IAAI,mOAAgC,EAAE,CAAC;QAC1D,IAAI,CAAC,WAAW,CAAC,YAAY,GAAG,IAAI,CAAC;QACrC,IAAI,CAAC,WAAW,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;QAC3D,IAAI,CAAC,uBAAuB,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;QAC3D,IAAI,CAAC,yBAAyB,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;QAC7D,IAAI,CAAC,WAAW,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;QACzC,IAAI,CAAC,WAAW,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC;QAC3D,IAAI,CAAC,WAAW,CAAC,OAAO,GAAG,OAAO,CAAC;QACnC,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QACzC,IAAI,CAAC,WAAW,CAAC,uBAAuB,GAAG,uBAAuB,CAAC;QAEnE,oBAAoB;QACpB,MAAM,WAAW,GAAwB,EAAE,CAAC;QAC5C,MAAM,aAAa,GAAwB,EAAE,CAAC;QAE9C,KAAK,MAAM,gBAAgB,IAAI,IAAI,CAAC,kBAAkB,CAAE,CAAC;YACrD,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACnC,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,IAAI,CAAC,uBAAuB,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;QACxG,CAAC;QAED,KAAK,MAAM,kBAAkB,IAAI,mBAAmB,CAAE,CAAC;YACnD,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACvC,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,IAAI,CAAC,yBAAyB,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC;QAC1G,CAAC;QAED,yBAAyB;QACzB,IAAI,gBAAgB,GAAG,CAAC,CAAC;QACzB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,cAAc,CAAE,CAAC;YACrC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;gBACpB,gBAAgB,EAAE,CAAC;gBACnB,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,GAAG,EAAE;oBACtC,gBAAgB,EAAE,CAAC;oBACnB,IAAI,gBAAgB,KAAK,CAAC,EAAE,CAAC;wBACzB,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,aAAa,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC;oBACjF,CAAC;gBACL,CAAC,CAAC,CAAC;YACP,CAAC;QACL,CAAC;QAED,IAAI,gBAAgB,KAAK,CAAC,EAAE,CAAC;YACzB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,aAAa,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC;IACjF,CAAC;IAEO,mBAAmB,GAAqH;sBAApH,iEAAmB,KAAK,kBAAE,aAAa,oDAAG,IAAI,EAAE,WAAgC,iDAAE,aAAkC;QAC5I,WAAW;QACX,IAAI,CAAC,QAAQ,EAAE,CAAC;QAEhB,SAAS;QACT,KAAK,MAAM,gBAAgB,IAAI,WAAW,CAAE,CAAC;YACzC,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAuB,EAAE,WAAW,CAAC,CAAC;QACtE,CAAC;QAED,WAAW;QACX,IAAI,CAAC,yBAAyB,CAAC,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACzF,IAAI,CAAC,yBAAyB,CAAC,mBAAmB,GAAG,IAAI,CAAC,uBAAuB,CAAC,mBAAmB,CAAC;QACtG,IAAI,CAAC,yBAAyB,CAAC,oBAAoB,GAAG,IAAI,CAAC,uBAAuB,CAAC,oBAAoB,CAAC;QACxG,IAAI,CAAC,yBAAyB,CAAC,YAAY,GAAG,IAAI,CAAC,uBAAuB,CAAC;QAE3E,KAAK,MAAM,kBAAkB,IAAI,aAAa,CAAE,CAAC;YAC7C,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;QACjE,CAAC;QAED,KAAK,MAAM,kBAAkB,IAAI,aAAa,CAAE,CAAC;YAC7C,kBAAkB,CAAC,KAAK,CAAC,IAAI,CAAC,yBAAyB,EAAE,aAAa,CAAC,CAAC;QAC5E,CAAC;QAED,WAAW;QACX,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACpE,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;QAExE,IAAI,aAAa,EAAE,CAAC;YAChB,IAAI,CAAC,QAAQ,GAAG,YAAY,CAAC,iBAAiB,EAAE,CAAC;QACrD,CAAC;QAED,IAAI,OAAO,EAAE,CAAC;qKACV,SAAM,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;qKAC7B,SAAM,CAAC,GAAG,CAAC,IAAI,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,CAAC;qKAC3D,SAAM,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;qKAC/B,SAAM,CAAC,GAAG,CAAC,IAAI,CAAC,yBAAyB,CAAC,iBAAiB,CAAC,CAAC;QACjE,CAAC;QAED,SAAS;QACT,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC;QAE9C,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;QAChC,IAAI,OAAO,EAAE,CAAC;YACV,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAC7C,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QACpC,CAAC;QAED,eAAe;QACf,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,MAAM,CAAC;QACtC,KAAK,MAAM,IAAI,IAAI,MAAM,CAAE,CAAC;YACxB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;gBAClB,SAAS;YACb,CAAC;YACD,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,SAAS,CAAE,CAAC;gBACnC,IAAI,OAAO,CAAC,WAAW,EAAE,KAAK,IAAI,EAAE,CAAC;oBACjC,SAAS;gBACb,CAAC;gBAED,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;oBAC3B,SAAS;gBACb,CAAC;gBAED,MAAM,OAAO,GAAG,OAAO,CAAC,eAAe,CAAC;gBACxC,OAAO,CAAC,cAAc,EAAE,CAAC;gBACzB,OAAO,CAAC,KAAK,EAAE,CAAC;YACpB,CAAC;QACL,CAAC;QAED,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,CAAC;YACnC,IAAI,CAAC,QAAQ,EAAE,CAAC,qBAAqB,EAAE,CAAC;QAC5C,CAAC;QACD,MAAM,eAAe,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,eAAe,CAAC;QACxD,IAAI,eAAe,EAAE,CAAC;YAClB,eAAe,CAAC,WAAW,EAAE,CAAC;QAClC,CAAC;IACL,CAAC;IAED;;OAEG,CACI,QAAQ,GAAA;QACX,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,CAAE,CAAC;YACvC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAC3E,CAAC;IACL,CAAC;IAEO,4BAA4B,CAAC,IAAkB,EAAE,OAA4B,EAAA;QACjF,MAAM,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;QACpC,MAAM,UAAU,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;QACtC,MAAM,QAAQ,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAC;QAE5C,OAAO,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC,2KAAY,CAAC,UAAU,CAAC,CAAC;QACxE,OAAO,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,qBAAqB,6JAAC,eAAY,CAAC,WAAW,CAAC,CAAC;QAE1E,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,qBAAqB,6JAAC,eAAY,CAAC,SAAS,CAAC,CAAC;QACnG,OAAO,CAAC,iBAAiB,CAAC,GAAG,eAAe,CAAC;QAE7C,IAAI,SAAS,GAAG,KAAK,CAAC;QACtB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,GAAA,EAAA,EAAA,CAAS,CAAC,qBAAqB,EAAE,EAAE,CAAC,EAAE,CAAC;YACxD,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;YAChC,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC,KAAqB,CAAE,CAAC,CAAC,IAApB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACpE,SAAS,GAAG,SAAS,IAAI,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC,KAAK,KAAK,CAAC;QACzD,CAAC;QAED,UAAU;QACV,MAAM,GAAG,GAAG,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,+BAA+B,CAAC;+LACnG,2BAAA,AAAwB,EAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,CAAC,GAAG,CAAC,CAAC;mMAEzD,kCAA+B,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC,mBAAmB,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QAE/G,IAAI,SAAS,KAAK,OAAO,CAAC,QAAQ,CAAC,IAAI,UAAU,KAAK,OAAO,CAAC,SAAS,CAAC,IAAI,QAAQ,KAAK,OAAO,CAAC,iBAAiB,CAAC,IAAI,SAAS,EAAE,CAAC;YAC/H,OAAO,CAAC,qBAAqB,EAAE,CAAC;QACpC,CAAC;IACL,CAAC;IAED;;OAEG,CACH,IAAoB,gBAAgB,GAAA;QAChC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG,CACH,IAAW,qBAAqB,GAAA;QAC5B,MAAM,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,KAAK,EAAE,CAAG,CAAD,IAAM,CAAC,YAAY,EAAE,KAAK,oBAAoB,CAAuB,CAAC;QACpI,MAAM,MAAM,GAAG;YAAC;SAAA,OAAS,CAAC,0BAA0B,CAAC,CAAC;QACtD,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACtB,OAAO,MAAM,CAAC;QAClB,CAAC;QACD,yDAAyD;QACzD,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,CAAC;YACnC,OAAO,MAAM,CAAC;QAClB,CAAC;QAED,IAAI,kBAAkB,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;YAC3C,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,kBAAkB,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;YAC7C,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,sCAAsC,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,kBAAkB,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;YAC5C,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,kBAAkB,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;YAC7C,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,iCAAiC,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,kBAAkB,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC;YAC/C,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,6BAA6B,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,kBAAkB,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC;YAC/C,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,mCAAmC,CAAC,CAAC;QAC/D,CAAC;QAED,IAAI,kBAAkB,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;YAC9C,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,iCAAiC,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,kBAAkB,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,6BAA6B,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,kBAAkB,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC;YAChD,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,oCAAoC,CAAC,CAAC;QAChE,CAAC;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;OAEG,CACH,IAAW,oBAAoB,GAAA;QAC3B,MAAM,oBAAoB,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,CAAG,CAAD,IAAM,CAAC,YAAY,EAAE,KAAK,qBAAqB,CAA0B,CAAC;QACnJ,MAAM,MAAM,GAAG,EAAc,CAAC;QAE9B,KAAK,MAAM,KAAK,IAAI,oBAAoB,CAAE,CAAC;YACvC,IAAI,KAAK,CAAC,QAAQ,CAAC,WAAW,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAA,KAAS,CAAC,6BAA6B,CAAC,EAAE,CAAC;gBAC1F,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,6BAA6B,CAAC,CAAC;YACzD,CAAC;YACD,IAAI,KAAK,CAAC,aAAa,CAAC,WAAW,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAA,KAAS,CAAC,mCAAmC,CAAC,EAAE,CAAC;gBACrG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,mCAAmC,CAAC,CAAC;YAC/D,CAAC;YACD,IAAI,KAAK,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAA,KAAS,CAAC,0BAA0B,CAAC,EAAE,CAAC;gBACpF,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;YACtD,CAAC;YACD,IAAI,KAAK,CAAC,WAAW,CAAC,WAAW,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAA,IAAS,CAAC,sCAAsC,CAAC,EAAE,CAAC;gBACtG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,sCAAsC,CAAC,CAAC;YAClE,CAAC;YACD,IAAI,KAAK,CAAC,MAAM,CAAC,WAAW,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAA,KAAS,CAAC,2BAA2B,CAAC,EAAE,CAAC;gBACtF,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC;YACvD,CAAC;YACD,IAAI,KAAK,CAAC,WAAW,CAAC,WAAW,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAA,KAAS,CAAC,iCAAiC,CAAC,EAAE,CAAC;gBACjG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,iCAAiC,CAAC,CAAC;YAC7D,CAAC;QACL,CAAC;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;OAIG,CACa,kBAAkB,CAAC,eAAgC,EAAA;QAC/D,MAAM,uBAAuB,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAE7F,IAAI,eAAe,IAAI,uBAAuB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxD,IAAI,GAAG,GAAG,eAAe,CAAC,sBAAsB,CAAC,cAAc,CAAC,CAAC;YACjE,IAAI,CAAC,GAAG,EAAE,CAAC;gBACP,GAAG,GAAG,eAAe,CAAC,sBAAsB,CAAC;oBACzC,OAAO,EAAE,IAAI;oBACb,oBAAoB,EAAE,KAAK;oBAC3B,IAAI,EAAE,cAAc;oBACpB,gBAAgB,EAAE,EAAE;iBACvB,CAAC,CAAC;YACP,CAAC;YACD,KAAK,MAAM,cAAc,IAAI,uBAAuB,CAAE,CAAC;gBACnD,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;oBACjD,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBAC9C,CAAC;YACL,CAAC;YACD,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC;QACvB,CAAC;QAED,iEAAiE;QACjE,wDAAwD;QACxD,OAAO,uBAAuB,CAAC,MAAM,GAAG,CAAC,CAAC;IAC9C,CAAC;IAED;;;;;;;;;;OAUG,CACI,iBAAiB,CACpB,MAAwB,EAIN,CAClB,cAAsB,SAAS,CAAC,yBAAyB,EACzD,aAAa,GAAG,SAAS,CAAC,kBAAkB;sBAL5C,iEAAuC,CAAC,iBACxC,iEAAuB,GAAA,MAAS,CAAC,gDAAA,yDAAA,cAAA,iEAAA,mBAA4B,EAC7D,MAAuB,EACvB,uDAAA,EAAkB;QAIlB,IAAI,IAAI,CAAC,IAAI,8LAAK,qBAAiB,CAAC,WAAW,IAAI,IAAI,CAAC,IAAI,+LAAK,oBAAiB,CAAC,GAAG,EAAE,CAAC;qKACrF,SAAM,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;YACzC,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,OAAO,IAAI,CAAC,2BAA2B,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC;IAC/H,CAAC;IAED;;;OAGG,CACI,0BAA0B,CAAC,WAAwB,EAAA;QACtD,IAAI,CAAC,2BAA2B,CAAC,WAAW,CAAC,CAAC;IAClD,CAAC;IAEO,2BAA2B,CAC/B,WAAkC,EAClC,MAAyB,EAIP,CAClB,cAAsB,SAAS,CAAC,yBAAyB,EACzD,aAAa,GAAG,SAAS,CAAC,kBAAkB;sBAL5C,iEAAuC,CAAC,iBACxC,iEAAuB,GAAA,MAAS,CAAC,gDAAA,yDAAA,cAAA,iEAAA,mBAA4B,EAC7D,MAAuB,EACvB,uDAAA,EAAkB;QAIlB,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC;QAEzC,MAAM,OAAO,GAAG,IAAI,mBAAmB,EAAE,CAAC;QAE1C,IAAI,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;QAE5B,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QAE9B,6EAA6E;QAC7E,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,uBAAuB,CAAC,CAAC,CAAC,SAAS,CAAC;sKAEzH,SAAM,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,yBAAyB,CAAC,uBAAuB,EAAE,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAEzH,IAAI,CAAC,WAAW,EAAE,CAAC;YACf,WAAW,GAAG,2KAAI,cAAW,CACzB,IAAI,CAAC,IAAI,GAAG,aAAa,EACzB,QAAQ,EACR,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EACvC,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EACvC,OAAO,EACP,MAAO,EACP,YAAY,EACZ,MAAM,EACN,QAAQ,EACR,OAAO,CAAC,QAAQ,EAAE,EAClB,WAAW,EACX,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,aAAa,EACrC;gBAAE,qBAAqB,EAAE,IAAI,CAAC,qBAAqB;YAAA,CAAE,EACrD,KAAK,EACL,aAAa,EACb,IAAI,CAAC,cAAc,CACtB,CAAC;QACN,CAAC,MAAM,CAAC;YACJ,WAAW,CAAC,YAAY,CACpB,OAAO,CAAC,QAAQ,EAAE,EAClB,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EACvC,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EACvC;gBAAE,qBAAqB,EAAE,IAAI,CAAC,qBAAqB;YAAA,CAAE,EACrD,SAAS,EACT,SAAS,EACT,QAAQ,EACR,QAAQ,CACX,CAAC;QACN,CAAC;QAED,WAAW,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAEtC,WAAW,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;YACzC,IAAI,OAAO,KAAK,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC5B,qKAAO,SAAM,CAAC,YAAY,CAAC,QAAQ,GAAG,cAAc,CAAC,CAAC;gBACtD,qKAAO,SAAM,CAAC,YAAY,CAAC,QAAQ,GAAG,aAAa,CAAC,CAAC;gBAErD,QAAQ,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC;gBAErC,OAAO,CAAC,cAAc,EAAE,CAAC;gBAEzB,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC5B,CAAC;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YAE7C,IAAI,MAAM,EAAE,CAAC;8KACT,SAAM,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,yBAAyB,CAAC,uBAAuB,EAAE,IAAI,CAAC,uBAAuB,CAAC,uBAAuB,CAAC,CAAC;8KAE9I,cAAW,CAAC,YAAY,CAAC,GAAG,CACxB,CAD0B,UACf,CAAC,YAAY,CACpB,OAAO,CAAC,QAAQ,EAAE,EAClB,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EACvC,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EACvC;wBAAE,qBAAqB,EAAE,IAAI,CAAC,qBAAqB;oBAAA,CAAE,EACrD,SAAS,EACT,SAAS,EACT,QAAQ,EACR,QAAQ,CACX,CACJ,CAAC;YACN,CAAC;YAED,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,OAAO,WAAW,CAAC;IACvB,CAAC;IAED;;;;;OAKG,CACI,uBAAuB,CAAC,IAAiE,EAAE,KAAY,EAAA;QAC1G,IAAI,IAAI,CAAC,IAAI,+LAAK,oBAAiB,CAAC,iBAAiB,EAAE,CAAC;qKACpD,SAAM,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;YACzC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC;QAEzC,MAAM,iBAAiB,GAAG,wMAAI,oBAAiB,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QAE7E,MAAM,OAAO,GAAG,IAAI,mBAAmB,EAAE,CAAC;QAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;sKAC7C,SAAM,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,yBAAyB,CAAC,uBAAuB,EAAE,IAAI,CAAC,uBAAuB,CAAC,uBAAuB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAEnK,IAAI,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC,YAAY,CACjD;YACI,aAAa,EAAE,QAAQ;YACvB,eAAe,EAAE,QAAQ;SAC5B,EACD;wKAAC,eAAY,CAAC,YAAY;SAAC,EAC3B,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EACvC,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EACvC,OAAO,CAAC,QAAQ,EAAE,qBAClB,MAAM,8BAAE,SAAS,EACjB,SAAS,EACT,SAAS,EACT,SAAS,EACT,IAAI,CAAC,cAAc,CACtB,CAAC;QAEF,iBAAiB,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC5C,iBAAiB,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAErC,IAAI,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC5B,MAAM,aAAa,GAAG,GAAG,EAAE;YACvB,IAAI,OAAO,KAAK,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC5B,qKAAO,SAAM,CAAC,YAAY,CAAC,QAAQ,GAAG,cAAc,CAAC,CAAC;gBACtD,qKAAO,SAAM,CAAC,YAAY,CAAC,QAAQ,GAAG,aAAa,CAAC,CAAC;gBAErD,QAAQ,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC;gBAErC,OAAO,CAAC,cAAc,EAAE,CAAC;gBAEzB,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC5B,CAAC;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YAE7C,IAAI,MAAM,EAAE,CAAC;8KACT,SAAM,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,yBAAyB,CAAC,uBAAuB,EAAE,IAAI,CAAC,uBAAuB,CAAC,uBAAuB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;8KAEnK,cAAW,CAAC,YAAY,CAAC,GAAG,EAAE;oBAC1B,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC,YAAY,CAC7C;wBACI,aAAa,EAAE,QAAQ;wBACvB,eAAe,EAAE,QAAQ;qBAC5B,EACD;oLAAC,eAAY,CAAC,YAAY;qBAAC,EAC3B,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EACvC,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EACvC,OAAO,CAAC,QAAQ,EAAE,kDAClB,MAAM,CAAE,SAAS,EACjB,SAAS,CACZ,CAAC;oBAEF,iBAAiB,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;gBACzC,CAAC,CAAC,CAAC;YACP,CAAC;YAED,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QACjC,CAAC,CAAC;QAEF,iBAAiB,CAAC,4BAA4B,CAAC,GAAG,CAAC,GAAG,EAAE;YACpD,aAAa,EAAE,CAAC;QACpB,CAAC,CAAC,CAAC;QAEH,+EAA+E;QAC/E,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC5B,aAAa,EAAE,CAAC;QACpB,CAAC,CAAC,CAAC;QAEH,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IAEO,yBAAyB,CAC7B,cAA+B,EAC/B,SAAiB,EACjB,UAAqC,EACrC,OAAkD,EAClD,MAAe,EACf,OAA6B,EACG;0CAAhC,2BAA2B,sCAAG,EAAE;QAEhC,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,GAAG,GAAG,GAAG,SAAS,CAAC;QAE3D,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,GAAG,IAAI,mBAAmB,EAAE,CAAC;QACxC,CAAC;QAED,IAAI,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;QAE5B,MAAM,qBAAqB,GAAkB,EAAE,CAAC;QAChD,IAAI,IAAI,GAAG,2BAA2B,CAAC;QAEvC,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;0KAE7C,SAAM,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,yBAAyB,CAAC,uBAAuB,EAAE,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;YAExH,cAAc,CAAC,WAAW,CAAC,qBAAqB,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;YAEpE,IAAI,GAAG,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAExC,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,CACnB,SAAS,EAAE,CACX,wBAAwB,CACrB,QAAQ,EACR,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EACvC,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EACvC,OAAO,CAAC,QAAQ,EAAE,GAAG,IAAI,GAAG,IAAI,kDAChC,MAAM,CAAE,SAAS,EACjB,UAAU,EACV,OAAO,EACP,cAAc,EACd,IAAI,CAAC,cAAc,CACtB,CAAC;YAEN,cAAc,CAAC,eAAe,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QACtD,CAAC;QAED,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;YACnC,IAAI,OAAO,KAAK,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC5B,qKAAO,SAAM,CAAC,YAAY,CAAC,QAAQ,GAAG,aAAa,CAAC,CAAC;gBAErD,QAAQ,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,GAAG,GAAG,GAAG,SAAS,CAAC;gBAEvD,OAAO,CAAC,cAAc,EAAE,CAAC;gBAEzB,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC5B,CAAC;YAED,qBAAqB,CAAC,MAAM,GAAG,CAAC,CAAC;YAEjC,cAAc,CAAC,WAAW,CAAC,qBAAqB,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;YAEpE,MAAM,kCAAkC,GAAG,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE5E,IAAI,kCAAkC,KAAK,IAAI,EAAE,CAAC;gBAC9C,OAAO,CAAC,cAAc,EAAE,CAAC;gBACzB,IAAI,GAAG,kCAAkC,CAAC;YAC9C,CAAC;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YAE7C,IAAI,MAAM,EAAE,CAAC;8KACT,SAAM,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,yBAAyB,CAAC,uBAAuB,EAAE,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;gBAExH,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,CACnB,SAAS,EAAE,CACX,wBAAwB,CACrB,QAAQ,EACR,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EACvC,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EACvC,OAAO,CAAC,QAAQ,EAAE,GAAG,IAAI,GAAG,IAAI,kDAChC,MAAM,CAAE,SAAS,EACjB,UAAU,EACV,OAAO,EACP,cAAc,CACjB,CAAC;gBACN,cAAc,CAAC,eAAe,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;gBAClD,IAAI,CAAC,yBAAyB,CAAC,cAAc,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,2BAA2B,CAAC,CAAC,CAAC,2CAA2C;gBACzK,OAAO;YACX,CAAC;YAED,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,eAAe,CAAC,MAAc,EAAA;QAClC,kBAAkB;QAClB,IAAI,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC;YAClC,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YAE9B,MAAM,OAAO,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC;YAEnC,IAAI,IAAI,CAAC,eAAe,KAAK,OAAO,EAAE,CAAC;gBACnC,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,WAAW,CAAC,cAAc,CAAE,CAAC;oBAClD,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBACzB,CAAC;gBAED,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC;YACnC,CAAC;QACL,CAAC;QAED,kBAAkB;QAClB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,WAAW,CAAC,cAAc,CAAE,CAAC;YAClD,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAC7B,CAAC;QAED,oBAAoB;QACpB,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,WAAW,CAAC,WAAW,CAAE,CAAC;YACpD,UAAU,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,CAAC;QACxD,CAAC;IACL,CAAC;IAED;;;;;OAKG,CACI,wBAAwB,CAAC,cAA+B,EAAE,UAAqC,EAAE,OAAkD,EAAA;QACtJ,IAAI,IAAI,CAAC,IAAI,+LAAK,oBAAiB,CAAC,QAAQ,EAAE,CAAC;qKAC3C,SAAM,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;YACzC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,yBAAyB,CAAC,cAAc,4KAAE,qBAAkB,CAAC,gBAAgB,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;QACzG,IAAI,CAAC,yBAAyB,CAAC,cAAc,EAAE,+LAAkB,CAAC,kBAAkB,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IAC/G,CAAC;IAED;;;OAGG,CACI,0BAA0B,CAAC,cAAwB,EAAA;QACtD,IAAI,IAAI,CAAC,IAAI,+LAAK,oBAAiB,CAAC,QAAQ,EAAE,CAAC;YAC3C,kKAAM,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;YACzC,OAAO;QACX,CAAC;QAED,cAAc,CAAC,kBAAkB,GAAG,IAAI,OAAO,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;IAC9F,CAAC;IAEO,eAAe,CACnB,OAA4B,EAC5B,IAAmB,EAEF;2BADjB,YAAY,qDAAG,KAAK,EACpB,OAAiB;QAQjB,IAAI,MAAM,GAAG,IAAI,CAAC;QAElB,iBAAiB;QACjB,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,2LAAI,0BAAA,AAAuB,EAAC,KAAK,EAAE,OAAO,CAAC,EAAE,CAAC;YAC1C,OAAO,CAAC,eAAe,EAAE,CAAC;QAC9B,CAAC;QAED,iBAAiB;QACjB,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAE,CAAC;YACjD,CAAC,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QACjC,CAAC;QAED,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAE,CAAC;YACjD,CAAC,CAAC,cAAc,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;QACjE,CAAC;QAED,qBAAqB;QACrB,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YAClB,MAAM,aAAa,GAAG,OAAO,CAAC,kBAAkB,CAAC;YACjD,OAAO,CAAC,eAAe,EAAE,CAAC;YAE1B,gCAAgC;YAChC,IAAI,CAAC,uBAAuB,CAAC,iBAAiB,GAAG,IAAI,CAAC,uBAAuB,CAAC,uBAAuB,CAAC;YACtG,IAAI,CAAC,yBAAyB,CAAC,iBAAiB,GAAG,IAAI,CAAC,yBAAyB,CAAC,uBAAuB,CAAC;YAE1G,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,uBAAuB,CAAE,CAAC;gBACvD,CAAC,CAAC,wBAAwB,CAAC,IAAI,CAAC,uBAAuB,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;YAC5E,CAAC;YAED,WAAW;YACX,MAAM,cAAc,GAAa,EAAE,CAAC;YACpC,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,oBAAoB,CAAE,CAAC;gBACpD,CAAC,CAAC,wBAAwB,CAAC,IAAI,CAAC,uBAAuB,EAAE,IAAI,EAAE,OAAO,EAAE,cAAc,CAAC,CAAC;YAC5F,CAAC;YAED,MAAM,cAAc,GAAG,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC;YAE7D,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAE,CAAC;gBACtD,MAAM,KAAK,GAAG,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBAExC,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;oBACf,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC3B,CAAC;YACL,CAAC;YAED,WAAW;YACX,MAAM,cAAc,GAAG,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC;YAE7D,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAE,CAAC;gBACtD,MAAM,KAAK,GAAG,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBAExC,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;oBACf,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC3B,CAAC;YACL,CAAC;YAED,MAAM,SAAS,GAAG,2KAAI,kBAAe,EAAE,CAAC;YAExC,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAAE,CAAC;gBACnD,CAAC,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;YACxC,CAAC;YAED,MAAM,GAAG;gBACL,aAAa;gBACb,cAAc;gBACd,cAAc;gBACd,cAAc;gBACd,SAAS;aACZ,CAAC;QACN,CAAC;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;;;OAOG,CACa,iBAAiB,CAAC,IAAkB,EAAE,OAAgB,EAA+B;2BAA7B,iEAAwB,KAAK;QACjG,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC5B,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,IAAI,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC;YAClC,MAAM,OAAO,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC;YAEnC,IAAI,IAAI,CAAC,eAAe,KAAK,OAAO,EAAE,CAAC;gBACnC,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,WAAW,CAAC,cAAc,CAAE,CAAC;oBAClD,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBACzB,CAAC;gBAED,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC;YACnC,CAAC;QACL,CAAC;QAED,MAAM,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC;QAEzC,IAAI,WAAW,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YACtC,IAAI,WAAW,CAAC,mBAAmB,IAAI,WAAW,CAAC,4BAA4B,KAAK,YAAY,EAAE,CAAC;gBAC/F,OAAO,IAAI,CAAC;YAChB,CAAC;QACL,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,eAAe,IAAI,OAAO,OAAO,CAAC,eAAe,KAAK,QAAQ,EAAE,CAAC;YAC1E,OAAO,CAAC,eAAe,GAAG,IAAI,mBAAmB,EAAE,CAAC;QACxD,CAAC;QAED,MAAM,OAAO,GAAwB,OAAO,CAAC,eAAe,CAAC;QAC7D,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE,CAAC;YACnC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAEjC,IAAI,CAAC,4BAA4B,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAEjD,4BAA4B;QAC5B,IAAI,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC,EAAE,CAAC;YAC7F,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;QAE1E,IAAI,MAAM,EAAE,CAAC;YACT,MAAM,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC;YACtC,cAAc;YACd,MAAM,IAAI,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;YAChC,IAAI,MAAM,GAAG,MAAM,CAAC,YAAY,CAC5B;gBACI,MAAM,EAAE,cAAc,GAAG,IAAI,CAAC,QAAQ;gBACtC,QAAQ,EAAE,cAAc,GAAG,IAAI,CAAC,QAAQ;gBACxC,YAAY,EAAE,IAAI,CAAC,uBAAuB,CAAC,iBAAiB;gBAC5D,cAAc,EAAE,IAAI,CAAC,yBAAyB,CAAC,iBAAiB;aACnE,EACuB;gBACpB,UAAU,EAAE,IAAI,CAAC,uBAAuB,CAAC,UAAU;gBACnD,aAAa,EAAE,MAAM,CAAC,cAAc;gBACpC,mBAAmB,EAAE,MAAM,CAAC,cAAc;gBAC1C,QAAQ,EAAE,MAAM,CAAC,cAAc;gBAC/B,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,MAAM,CAAC,SAAS;gBAC3B,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,WAAW,EAAE,OAAO,CAAC,OAAO;gBAC5B,eAAe,EAAE;oBAAE,qBAAqB,EAAE,IAAI,CAAC,qBAAqB;oBAAE,2BAA2B,EAAE,OAAO,CAAC,qBAAqB;gBAAA,CAAE;gBAClI,cAAc,EAAE,IAAI,CAAC,cAAc;aACtC,EACD,MAAM,CACT,CAAC;YAEF,IAAI,MAAM,EAAE,CAAC;gBACT,IAAI,IAAI,CAAC,0BAA0B,EAAE,CAAC;oBAClC,yBAAyB,CAAC,MAAM,GAAG,MAAM,CAAC;oBAC1C,yBAAyB,CAAC,OAAO,GAAG,OAAO,CAAC;oBAC5C,IAAI,CAAC,0BAA0B,CAAC,eAAe,CAAC,yBAAyB,CAAC,CAAC;gBAC/E,CAAC;gBAED,iDAAiD;gBACjD,IAAI,IAAI,CAAC,sBAAsB,IAAI,cAAc,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;oBACrE,MAAM,GAAG,cAAc,CAAC;oBACxB,OAAO,CAAC,iBAAiB,EAAE,CAAC;oBAE5B,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;wBACvB,oDAAoD;wBACpD,OAAO,CAAC,kBAAkB,GAAG,IAAI,CAAC;wBAClC,OAAO,KAAK,CAAC;oBACjB,CAAC;gBACL,CAAC,MAAM,CAAC;oBACJ,KAAK,CAAC,mBAAmB,EAAE,CAAC;oBAC5B,OAAO,CAAC,SAAS,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAC9D,CAAC;YACL,CAAC;QACL,CAAC;QAED,yCAAyC;QACzC,IAAI,OAAO,CAAC,eAAe,CAAC,EAAE,CAAC;YAC3B,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBAC5D,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC;oBACvC,OAAO,KAAK,CAAC;gBACjB,CAAC;YACL,CAAC;QACL,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;YAC/C,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QACxC,WAAW,CAAC,mBAAmB,GAAG,IAAI,CAAC;QACvC,WAAW,CAAC,4BAA4B,GAAG,YAAY,CAAC;QAExD,IAAI,CAAC,8BAA8B,EAAE,CAAC;QAEtC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG,CACH,IAAW,eAAe,GAAA;QACtB,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC5B,IAAI,CAAC,KAAK,EAAE,CAAC;QACjB,CAAC;QACD,OAAO,4BAAqB,IAAI,CAAC,uBAAuB,CAAC,iBAAiB,EAAA,4BAA2E,CAAE,CAAC,KAAnD,IAAI,CAAC,yBAAyB,CAAC,iBAAiB;IACzJ,CAAC;IAED;;;OAGG,CACI,KAAK,CAAC,0BAA0B,GAAA;QACnC,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC5B,IAAI,CAAC,KAAK,EAAE,CAAC;QACjB,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,mBAAmB,EAAE,CAAC;QAC1C,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QAE9B,IAAI,iBAAiB,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;QAC3C,IAAI,IAAI,CAAC,IAAI,8LAAK,qBAAiB,CAAC,GAAG,EAAE,CAAC;YACtC,iBAAiB,IAAI,WAAwB,2NAAb,gBAAa,EAAA,GAAI,CAAC;QACtD,CAAC;QAED,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,CAAC;IAC3F,CAAC;IAED;;;OAGG,CACa,mBAAmB,CAAC,KAAa,EAAA;QAC7C,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtB,OAAO;QACX,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;QAErC,IAAI,KAAK,CAAC,mBAAmB,EAAE,CAAC;YAC5B,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,aAAa,EAAE,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;QAC5E,CAAC;QAED,IAAI,KAAK,CAAC,6BAA6B,EAAE,CAAC;YACtC,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,kBAAkB,EAAE,EAAE,IAAI,CAAC,gCAAgC,CAAC,CAAC;QAC3F,CAAC;QAED,oBAAoB;QACpB,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,WAAW,CAAC,WAAW,CAAE,CAAC;YACpD,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,gCAAgC,CAAC,CAAC;QAC7H,CAAC;IACL,CAAC;IAED;;;;;OAKG,CACa,cAAc,CAAC,KAAa,EAAE,IAAU,EAAE,OAAgB,EAAA;QACtE,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAC9B,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,OAAO;QACX,CAAC;QACD,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;QAE5B,WAAW;QACX,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;QAEhC,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAC7E,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;QAEpC,IAAI,UAAU,EAAE,CAAC;YACb,kBAAkB;YAClB,KAAK,MAAM,KAAK,IAAI,UAAU,CAAC,cAAc,CAAE,CAAC;gBAC5C,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;YAC5C,CAAC;YAED,KAAK,MAAM,KAAK,IAAI,UAAU,CAAC,oBAAoB,CAAE,CAAC;gBAClD,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;YAC5C,CAAC;YAED,oBAAoB;YACpB,KAAK,MAAM,UAAU,IAAI,UAAU,CAAC,WAAW,CAAE,CAAC;gBAC9C,UAAU,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;YAC9C,CAAC;QACL,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACxB,KAAK,MAAM,KAAK,IAAI,UAAU,CAAC,oBAAoB,CAAE,CAAC;gBAClD,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;YAC5C,CAAC;QACL,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;IACvD,CAAC;IAED;;;OAGG,CACa,iBAAiB,GAAA;QAC7B,MAAM,cAAc,GAAG,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAEjD,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAG,CAAD,CAAG,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAG,CAAD,CAAG,CAAC,OAAQ,CAAC,CAAC,CAAC;QAC/G,CAAC;QAED,OAAO,cAAc,CAAC;IAC1B,CAAC;IAED;;;;OAIG,CACI,gBAAgB,GAAA;QACnB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACpB,OAAO,EAAE,CAAC;QACd,CAAC;QAED,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;IAC1C,CAAC;IAED;;;;OAIG,CACI,mBAAmB,GAAA;QACtB,MAAM,aAAa,GAAgC,EAAE,CAAC;QAEtD,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,cAAc,CAAE,CAAC;YACtC,IAAI,YAAY,CAAC,oBAAoB,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC3C,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9B,CAAC;QACL,CAAC;QAED,OAAO,aAAa,CAAC;IACzB,CAAC;IAED;;;;OAIG,CACa,UAAU,CAAC,OAAoB,EAAA;QAC3C,IAAI,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;YAC5B,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACpB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,aAAa,CAAE,CAAC;YAC7C,IAAI,CAAC,CAAC,OAAO,KAAK,OAAO,EAAE,CAAC;gBACxB,OAAO,IAAI,CAAC;YAChB,CAAC;QACL,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;OAKG,CACa,OAAO,CAAC,kBAA4B,EAAE,oBAA8B,EAAE,cAAwB,EAAA;QAC1G,IAAI,oBAAoB,EAAE,CAAC;YACvB,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,gBAAgB,EAAE,CACxC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAG,CAAD,CAAG,CAAC,OAAO,CAAC,CAC1B,GAAG,CAAC,CAAC,EAAE,EAAE,CAAG,CAAD,CAAG,CAAC,OAAQ,CAAC,CAAE,CAAC;gBAC5B,OAAO,CAAC,OAAO,EAAE,CAAC;YACtB,CAAC;QACL,CAAC;QAED,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,cAAc,CAAE,CAAC;YACtC,KAAK,CAAC,OAAO,EAAE,CAAC;QACpB,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,WAAmB,GAAG,IAAI,CAAC;QAChC,IAAI,CAAC,uBAA+B,GAAG,IAAI,CAAC;QAC5C,IAAI,CAAC,yBAAiC,GAAG,IAAI,CAAC;QAE/C,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAC/B,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;QAEpC,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAChC,IAAI,CAAC,6BAA6B,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YAC5F,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;QACzC,CAAC;QAED,KAAK,CAAC,OAAO,CAAC,kBAAkB,EAAE,oBAAoB,EAAE,cAAc,CAAC,CAAC;IAC5E,CAAC;IAED;;OAEG,CACK,iBAAiB,CAAC,gBAAsB,EAAA;QAC5C,MAAM,gBAAgB,GAAQ;YAC1B,YAAY,EAAE,IAAI;YAClB,GAAG,gBAAgB;SACtB,CAAC;QACF,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;IACjE,CAAC;IAED;;;;OAIG,CACI,KAAK,CAAC,IAAI,CAAC,MAAmC,EAAA;QACjD,OAAO,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YACjC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;YAC/F,IAAI,OAAO,IAAI,CAAC,qBAAqB,IAAI,WAAW,EAAE,CAAC;gBACnD,MAAM,SAAS,GAAG,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC;gBAEzF,oCAAoC;wKACpC,QAAK,CAAC,iBAAiB,CAAC,SAAS,EAAE,GAAG,EAAE;oBACpC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;oBAC/F,IAAI,CAAC,iBAAiB,iDAAC,MAAM,CAAE,gBAAgB,CAAC,CAAC;oBACjD,OAAO,EAAE,CAAC;gBACd,CAAC,CAAC,CAAC;YACP,CAAC,MAAM,CAAC;gBACJ,+BAA+B;gBAC/B,IAAI,CAAC,iBAAiB,iDAAC,MAAM,CAAE,gBAAgB,CAAC,CAAC;gBACjD,OAAO,EAAE,CAAC;YACd,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG,CACI,KAAK,GAAA;QACR,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC;QACnC,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,CAAC;QACrC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;QAC/B,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;IACpC,CAAC;IAED;;OAEG,CACI,YAAY,GAAA;QACf,IAAI,CAAC,KAAK,EAAE,CAAC;QAEb,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAEvB,MAAM,aAAa,GAAG,iMAAI,aAAU,CAAC,UAAU,CAAC,CAAC;QACjD,aAAa,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QAEzC,MAAM,UAAU,GAAG,iMAAI,aAAU,CAAC,OAAO,CAAC,CAAC;QAC3C,UAAU,CAAC,gBAAgB,kMAAC,2BAAwB,CAAC,KAAK,CAAC,CAAC;QAE5D,MAAM,QAAQ,GAAG,4LAAI,iBAAc,CAAC,UAAU,CAAC,CAAC;QAChD,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAClC,UAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAE/B,MAAM,mBAAmB,GAAG,iMAAI,aAAU,CAAC,gBAAgB,CAAC,CAAC;QAC7D,mBAAmB,CAAC,gBAAgB,iMAAC,4BAAwB,CAAC,cAAc,CAAC,CAAC;QAE9E,MAAM,mCAAmC,GAAG,2LAAI,kBAAc,CAAC,oCAAoC,CAAC,CAAC;QACrG,QAAQ,CAAC,SAAS,CAAC,mCAAmC,CAAC,CAAC;QACxD,mBAAmB,CAAC,SAAS,CAAC,mCAAmC,CAAC,CAAC;QAEnE,MAAM,YAAY,GAAG,yMAAI,oBAAiB,CAAC,cAAc,CAAC,CAAC;QAC3D,mCAAmC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QAE5D,QAAQ;QACR,MAAM,UAAU,GAAG,iMAAI,aAAU,CAAC,OAAO,CAAC,CAAC;QAC3C,UAAU,CAAC,KAAK,GAAG,qKAAI,SAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QAEhD,MAAM,cAAc,GAAG,4MAAI,uBAAmB,CAAC,gBAAgB,CAAC,CAAC;QACjE,UAAU,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;QAErC,eAAe;QACf,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;QACjC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;QAEnC,IAAI,CAAC,KAAK,6LAAG,oBAAiB,CAAC,QAAQ,CAAC;IAC5C,CAAC;IAED;;OAEG,CACI,uBAAuB,GAAA;QAC1B,IAAI,CAAC,KAAK,EAAE,CAAC;QAEb,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAEvB,MAAM,QAAQ,GAAG,IAAI,0MAAU,CAAC,UAAU,CAAC,CAAC;QAC5C,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;QAEtC,MAAM,MAAM,GAAG,gMAAI,cAAU,CAAC,WAAW,CAAC,CAAC;QAC3C,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC;QACzB,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC;QAEjB,MAAM,OAAO,GAAG,+LAAI,oBAAiB,CAAC,YAAY,CAAC,CAAC;QAEpD,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAC5B,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE;YAAE,KAAK,EAAE,GAAG;QAAA,CAAE,CAAC,CAAC;QAE1C,MAAM,YAAY,GAAG,yMAAI,oBAAiB,CAAC,cAAc,CAAC,CAAC;QAC3D,OAAO,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QAEhC,QAAQ;QACR,MAAM,KAAK,GAAG,iMAAI,aAAU,CAAC,OAAO,CAAC,CAAC;QACtC,KAAK,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAChC,KAAK,CAAC,KAAK,GAAG,sKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEhC,MAAM,GAAG,GAAG,wLAAI,aAAU,CAAC,KAAK,CAAC,CAAC;QAClC,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAExB,MAAM,EAAE,GAAG,2LAAI,gBAAa,CAAC,UAAU,CAAC,CAAC;QACzC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;QAClB,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;QAEpB,MAAM,aAAa,GAAG,wMAAI,qBAAkB,CAAC,eAAe,CAAC,CAAC;QAC9D,EAAE,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QAC5B,MAAM,UAAU,2JAAG,QAAK,CAAC,WAAW,CAAC,oEAAoE,CAAC,CAAC;QAC3G,aAAa,CAAC,OAAO,GAAG,+KAAI,UAAO,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QAEjE,MAAM,cAAc,GAAG,6MAAI,sBAAmB,CAAC,gBAAgB,CAAC,CAAC;QACjE,aAAa,CAAC,SAAS,CAAC,cAAc,EAAE;YAAE,MAAM,EAAE,MAAM;QAAA,CAAE,CAAC,CAAC;QAE5D,eAAe;QACf,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;QACjC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;QAEnC,IAAI,CAAC,KAAK,6LAAG,oBAAiB,CAAC,WAAW,CAAC;IAC/C,CAAC;IAED;;OAEG,CACI,6BAA6B,GAAA;QAChC,IAAI,CAAC,KAAK,EAAE,CAAC;QAEb,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAEvB,MAAM,QAAQ,GAAG,iMAAI,aAAU,CAAC,UAAU,CAAC,CAAC;QAC5C,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;QAEtC,MAAM,MAAM,GAAG,iMAAI,aAAU,CAAC,WAAW,CAAC,CAAC;QAC3C,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC;QACzB,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC;QAEjB,MAAM,OAAO,GAAG,+LAAI,oBAAiB,CAAC,YAAY,CAAC,CAAC;QAEpD,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAC5B,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE;YAAE,KAAK,EAAE,GAAG;QAAA,CAAE,CAAC,CAAC;QAE1C,MAAM,YAAY,GAAG,yMAAI,oBAAiB,CAAC,cAAc,CAAC,CAAC;QAC3D,OAAO,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QAEhC,QAAQ;QACR,MAAM,IAAI,GAAG,gMAAI,cAAU,CAAC,MAAM,CAAC,CAAC;QACpC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;QACf,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;QACb,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;QACb,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,aAAa,6MAAG,0BAAuB,CAAC,IAAI,CAAC;QAClD,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QAExB,MAAM,KAAK,GAAG,gMAAI,cAAU,CAAC,QAAQ,CAAC,CAAC;QACvC,KAAK,CAAC,KAAK,GAAG,qKAAI,SAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAClC,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC;QACzB,MAAM,cAAc,GAAG,6MAAI,sBAAmB,CAAC,gBAAgB,CAAC,CAAC;QAEjE,MAAM,YAAY,GAAG,+LAAI,oBAAiB,CAAC,cAAc,CAAC,CAAC;QAC3D,YAAY,CAAC,kBAAkB,GAAG,KAAK,CAAC;QAExC,MAAM,GAAG,GAAG,+LAAI,oBAAiB,CAAC,KAAK,CAAC,CAAC;QACzC,GAAG,CAAC,SAAS,8LAAG,8BAA2B,CAAC,GAAG,CAAC;QAEhD,QAAQ,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QACjC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACjC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACrC,YAAY,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;QAElD,eAAe;QACf,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;QACjC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;QAEnC,IAAI,CAAC,KAAK,GAAG,8MAAiB,CAAC,iBAAiB,CAAC;IACrD,CAAC;IAED;;OAEG,CACI,oBAAoB,GAAA;QACvB,IAAI,CAAC,KAAK,EAAE,CAAC;QAEb,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAEvB,QAAQ;QACR,MAAM,EAAE,GAAG,iMAAI,aAAU,CAAC,IAAI,CAAC,CAAC;QAChC,EAAE,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;QAEjC,MAAM,OAAO,GAAG,8MAAI,uBAAoB,CAAC,iBAAiB,CAAC,CAAC;QAC5D,EAAE,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAEtB,MAAM,KAAK,GAAG,iMAAI,aAAU,CAAC,OAAO,CAAC,CAAC;QACtC,KAAK,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;QAEvC,MAAM,QAAQ,GAAG,2LAAI,gBAAa,CAAC,iBAAiB,CAAC,CAAC;QACtD,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAC5B,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAE1B,MAAM,YAAY,GAAG,mNAAI,4BAAyB,CAAC,sBAAsB,CAAC,CAAC;QAC3E,QAAQ,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QAEjC,MAAM,SAAS,GAAG,gMAAI,qBAAkB,CAAC,eAAe,CAAC,CAAC;QAC1D,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAE3B,MAAM,aAAa,GAAG,oNAAI,6BAA0B,CAAC,uBAAuB,CAAC,CAAC;QAC9E,YAAY,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QACtC,OAAO,CAAC,SAAS,CAAC,aAAa,EAAE;YAAE,MAAM,EAAE,GAAG;QAAA,CAAE,CAAC,CAAC;QAClD,SAAS,CAAC,SAAS,CAAC,aAAa,EAAE;YAAE,MAAM,EAAE,GAAG;QAAA,CAAE,CAAC,CAAC;QAEpD,MAAM,cAAc,GAAG,6MAAI,sBAAmB,CAAC,gBAAgB,CAAC,CAAC;QACjE,aAAa,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;QAExC,eAAe;QACf,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;QAEnC,IAAI,CAAC,KAAK,6LAAG,oBAAiB,CAAC,QAAQ,CAAC;IAC5C,CAAC;IAED;;;;;;OAMG,CACI,KAAK,CAAC,SAAS,CAAC,GAAW,EAAsB;sBAApB,iEAAkB,EAAE;QACpD,OAAO,MAAM,YAAY,CAAC,kBAAkB,CAAC,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAChG,CAAC;IAEO,aAAa,CAAC,QAA2B,EAAE,IAAyB,EAAA;QACxE,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YAChC,OAAO;QACX,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEpB,KAAK,MAAM,KAAK,IAAI,QAAQ,CAAC,MAAM,CAAE,CAAC;YAClC,MAAM,cAAc,GAAG,KAAK,CAAC,cAAc,CAAC;YAC5C,IAAI,cAAc,EAAE,CAAC;gBACjB,MAAM,KAAK,GAAG,cAAc,CAAC,UAAU,CAAC;gBACxC,IAAI,KAAK,KAAK,QAAQ,EAAE,CAAC;oBACrB,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;gBACpC,CAAC;YACL,CAAC;QACL,CAAC;QAED,gBAAgB;QAChB,IAAI,QAAQ,CAAC,aAAa,EAAE,CAAC;YACzB,MAAM,KAAK,GAAG,QAAwC,CAAC;YACvD,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;gBACnB,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;YAC/C,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,YAAY,GAAA;QACf,IAAI,aAAa,GAAwB,EAAE,CAAC;QAC5C,MAAM,YAAY,GAAwB,EAAE,CAAC;QAC7C,MAAM,WAAW,GAAa;YAAC,OAAO;YAAE,KAAK;YAAE,KAAK;SAAC,CAAC;QACtD,qBAAqB;QACrB,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,kBAAkB,CAAE,CAAC;YAC/C,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;QACjD,CAAC;QAED,MAAM,cAAc,GAAwB,EAAE,CAAC;QAC/C,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,oBAAoB,CAAE,CAAC;YACjD,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;QACnD,CAAC;QAED,yBAAyB;QACzB,IAAI,UAAU,GAAG,gDAA4E,MAAO,CAAC,AAApC,IAAI,CAAC,IAAI,IAAI,eAAe,EAAA;QAC7F,UAAU,IAAI,iDAA6E,iMAA5B,oBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAA,IAAK,CAAC;QACjG,KAAK,MAAM,IAAI,IAAI,YAAY,CAAE,CAAC;YAC9B,IAAI,IAAI,CAAC,OAAO,IAAI,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;gBACrD,UAAU,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;YAC7D,CAAC;QACL,CAAC;QAED,2BAA2B;QAC3B,KAAK,MAAM,IAAI,IAAI,cAAc,CAAE,CAAC;YAChC,IAAI,IAAI,CAAC,OAAO,IAAI,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;gBACrD,UAAU,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;YAC7D,CAAC;QACL,CAAC;QAED,cAAc;QACd,aAAa,GAAG,EAAE,CAAC;QACnB,UAAU,IAAI,oBAAoB,CAAC;QACnC,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,kBAAkB,CAAE,CAAC;YACzC,UAAU,IAAI,IAAI,CAAC,6BAA6B,CAAC,aAAa,CAAC,CAAC;QACpE,CAAC;QACD,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,oBAAoB,CAAE,CAAC;YAC3C,UAAU,IAAI,IAAI,CAAC,6BAA6B,CAAC,aAAa,CAAC,CAAC;QACpE,CAAC;QAED,eAAe;QACf,UAAU,IAAI,qBAAqB,CAAC;QACpC,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,kBAAkB,CAAE,CAAC;YACzC,UAAU,IAAI,8BAAoD,OAAtB,IAAI,CAAC,iBAAiB,EAAA,KAAM,CAAC;QAC7E,CAAC;QAED,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,oBAAoB,CAAE,CAAC;YAC3C,UAAU,IAAI,8BAAoD,OAAtB,IAAI,CAAC,iBAAiB,EAAA,KAAM,CAAC;QAC7E,CAAC;QAED,UAAU,IAAI,wBAAyB,CAAC;QAExC,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;;OAIG,CACa,SAAS,CAAC,cAAoC,EAAA;QAC1D,MAAM,mBAAmB,GAAG,cAAc,CAAC,CAAC,CAAC,CAAA,CAAE,CAAC,CAAC,+KAAC,sBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACtF,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO;QAErF,IAAI,MAAM,GAAwB,EAAE,CAAC;QAErC,IAAI,cAAc,EAAE,CAAC;YACjB,MAAM,GAAG,cAAc,CAAC;QAC5B,CAAC,MAAM,CAAC;YACJ,mBAAmB,CAAC,UAAU,GAAG,sBAAsB,CAAC;YACxD,mBAAmB,CAAC,WAAW,GAAG,EAAE,CAAC;YAErC,UAAU;YACV,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,kBAAkB,CAAE,CAAC;gBAC/C,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;gBACvC,mBAAmB,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YAC9D,CAAC;YAED,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,oBAAoB,CAAE,CAAC;gBACjD,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;gBAEvC,IAAI,mBAAmB,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;oBACtE,mBAAmB,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;gBAC9D,CAAC;YACL,CAAC;QACL,CAAC;QAED,SAAS;QACT,mBAAmB,CAAC,MAAM,GAAG,EAAE,CAAC;QAEhC,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;YACzB,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,CAAC,cAAc,EAAE,CAAC;YAClB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,cAAc,CAAE,CAAC;gBACtC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;oBAC/B,SAAS;gBACb,CAAC;gBACD,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;YACvD,CAAC;QACL,CAAC;QAED,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAE7C,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEO,mBAAmB,CAAC,KAAwB,EAAE,MAAW,EAAE,GAAyC,EAAA;QACxG,KAAK,MAAM,WAAW,IAAI,KAAK,CAAC,OAAO,CAAE,CAAC;YACtC,KAAK,MAAM,SAAS,IAAI,MAAM,CAAC,MAAM,CAAE,CAAC;gBACpC,MAAM,MAAM,GAAG,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;gBAEjC,IAAI,CAAC,MAAM,EAAE,CAAC;oBACV,SAAS;gBACb,CAAC;gBAED,KAAK,MAAM,KAAK,IAAI,SAAS,CAAC,MAAM,CAAE,CAAC;oBACnC,IAAI,GAAG,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,KAAK,IAAI,KAAK,CAAC,oBAAoB,KAAK,WAAW,CAAC,IAAI,EAAE,CAAC;wBACxF,MAAM,UAAU,GAAG,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;wBAC1D,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,WAAW,EAAE,CAAC;4BACxC,SAAS;wBACb,CAAC;wBAED,WAAW,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;wBACxC,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;wBAC9C,SAAS;oBACb,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;;;OAMG,CACI,qBAAqB,CAAC,MAAW,EAA4E;sBAA1E,iEAAkB,EAAE,UAAE,KAAK,4DAAG,KAAK,EAAE,WAAqC;QAChH,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,IAAI,CAAC,KAAK,EAAE,CAAC;QACjB,CAAC;QAED,MAAM,GAAG,GAAyC,CAAA,CAAE,CAAC;QAErD,gBAAgB;QAChB,KAAK,MAAM,WAAW,IAAI,MAAM,CAAC,MAAM,CAAE,CAAC;YACtC,MAAM,SAAS,OAAG,uKAAQ,AAAR,EAAS,WAAW,CAAC,UAAU,CAAC,CAAC;YACnD,IAAI,SAAS,EAAE,CAAC;gBACZ,MAAM,KAAK,GAAsB,IAAI,SAAS,EAAE,CAAC;gBACjD,KAAK,CAAC,YAAY,CAAC,WAAW,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;gBACvE,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC;gBAE5B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpC,CAAC;QACL,CAAC;QAED,0BAA0B;QAC1B,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,cAAc,CAAE,CAAC;YACtC,IAAI,KAAK,CAAC,aAAa,EAAE,CAAC;gBACtB,MAAM,WAAW,GAAG,KAAqC,CAAC;gBAC1D,MAAM,EAAE,GAAG,WAAW,CAAC,uBAAuB,CAAC;gBAC/C,IAAI,EAAE,EAAE,CAAC;oBACL,MAAM,MAAM,GAAG,GAAG,CAAC,EAAE,CAAgC,CAAC;oBACtD,MAAM,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;gBACzC,CAAC;YACL,CAAC;QACL,CAAC;QAED,mGAAmG;QACnG,IAAK,IAAI,UAAU,GAAG,CAAC,EAAE,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,UAAU,EAAE,CAAE,CAAC;YACvE,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAC9C,MAAM,KAAK,GAAG,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YAElC,IAAI,CAAC,KAAK,EAAE,CAAC;gBACT,SAAS;YACb,CAAC;YAED,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;gBAChC,SAAS;YACb,CAAC;YACD,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;QACjD,CAAC;QAED,UAAU;QACV,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;YACrB,KAAK,MAAM,YAAY,IAAI,MAAM,CAAC,WAAW,CAAE,CAAC;gBAC5C,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC;YAC1C,CAAC;QACL,CAAC;QAED,kBAAkB;QAClB,IAAI,MAAM,CAAC,SAAS,IAAI,AAAC,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAE,CAAC;YACzE,MAAM,SAAS,GAKT,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC;YAEtD,KAAK,MAAM,QAAQ,IAAI,SAAS,CAAE,CAAC;gBAC/B,IAAI,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;oBACxB,QAAQ,CAAC,OAAO,GAAG,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC;gBACtD,CAAC;YACL,CAAC;YAED,IAAI,KAAK,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;gBACxD,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YAChD,CAAC;YAED,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;gBACnB,IAAI,CAAC,UAAU,GAAG;oBACd,SAAS,EAAE,SAAS;iBACvB,CAAC;YACN,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;gBACpC,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,SAAS,CAAC;YAC1C,CAAC;YAED,MAAM,QAAQ,GAAa,EAAE,CAAC;YAE9B,IAAK,MAAM,GAAG,IAAI,GAAG,CAAE,CAAC;gBACpB,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC;YACtC,CAAC;YAED,IAAI,CAAC,UAAU,CAAC,GAAG,GAAG,QAAQ,CAAC;QACnC,CAAC;QAED,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;QAE9B,IAAI,MAAM,CAAC,kBAAkB,KAAK,SAAS,EAAE,CAAC;YAC1C,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC,kBAAkB,CAAC;QACxD,CAAC;QAED,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;YACjC,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;QACtC,CAAC;QAED,IAAI,CAAC,KAAK,EAAE,CAAC;;YACT,IAAI,CAAC,KAAK,0BAAU,IAAI,wCAAX,MAAM,mMAAS,oBAAiB,CAAC,QAAQ,CAAC;QAC3D,CAAC;IACL,CAAC;IAED;;;;;;OAMG,CACI,qBAAqB,CAAC,MAAW,EAAqC;sBAAnC,iEAAkB,EAAE,UAAE,KAAK,4DAAG,KAAK;QACzE,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;IACvD,CAAC;IAED;;;;;OAKG,CACa,KAAK,CAAC,IAAY,EAA8B;0BAA5B,iEAAuB,KAAK;QAC5D,MAAM,mBAAmB,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAE7C,MAAM,KAAK,iLAAG,sBAAmB,CAAC,KAAK,CAAC,GAAG,CAAG,CAAD,GAAK,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,CAAC;QAC3G,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC;QAChB,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;QAElB,KAAK,CAAC,qBAAqB,CAAC,mBAAmB,CAAC,CAAC;QACjD,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC/B,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,WAAW,CAAC,CAAC;QAEjC,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;OAGG,CACH,2FAA2F;IACpF,sBAAsB,GAAA;QACzB,4CAA4C;QAC5C,MAAM,oBAAoB,GAAoB,EAAE,CAAC;QACjD,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAChD,KAAK,MAAM,OAAO,IAAI,cAAc,CAAE,CAAC;YACnC,MAAM,eAAe,GAAG,OAAO,CAAC,kBAAkB,EAAE,CAAC;YACrD,IAAI,eAAe,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;gBAC9C,oBAAoB,CAAC,IAAI,CACrB,IAAI,OAAO,CAAC,CAAC,cAAc,EAAE,aAAa,EAAE,EAAE;oBAC1C,eAAe,CAAC,kBAAkB,CAAC,OAAO,CAAC,GAAG,EAAE;wBAC5C,cAAc,EAAE,CAAC;oBACrB,CAAC,CAAC,CAAC;oBACH,eAAe,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;wBAC5C,2EAA2E;wBAC3E,aAAa,CAAC,CAAC,CAAC,CAAC;oBACrB,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CACL,CAAC;YACN,CAAC;QACL,CAAC;QAED,OAAO,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;IAC7C,CAAC;IAED;;;;;;;OAOG,CACI,MAAM,CAAU,KAAK,CAAC,MAAW,EAAE,KAAY,EAA4D;sBAA1D,iEAAkB,EAAE,mBAAE,cAAc,kCAAA,uBAAA,EAAsB,mBAAtB;QACxF,MAAM,YAAY,iLAAG,sBAAmB,CAAC,KAAK,CAAC,GAAG,CAAG,CAAD,GAAK,YAAY,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE;gBAAE,cAAc,EAAE,cAAc;YAAA,CAAE,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAEvJ,YAAY,CAAC,qBAAqB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QACpD,YAAY,CAAC,KAAK,EAAE,CAAC;QAErB,OAAO,YAAY,CAAC;IACxB,CAAC;IAED;;;;;;;;;;;OAWG,CACI,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAClC,IAAY,EACZ,GAAW,EACX,KAAY,EAK2B;sBAJvC,iEAAkB,EAAE,cACpB,iEAAqB,KAAK,EAC1B,cAA6B,iDAC7B,WAAqC,iDACrC,OAAuC;QAEvC,MAAM,QAAQ,0DAAG,cAAc,GAAI,IAAI,YAAY,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAE1E,MAAM,IAAI,GAAG,MAAM,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;QAC7C,MAAM,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC7C,QAAQ,CAAC,qBAAqB,CAAC,mBAAmB,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;QACrF,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,QAAQ,CAAC,KAAK,EAAE,CAAC;QACrB,CAAC;QACD,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED;;;;;;;;;;;OAWG,CACH,2FAA2F;IACpF,MAAM,CAAC,qBAAqB,CAC/B,SAAiB,EAOsB;oBANvC,kOAAe,cAAW,CAAC,gBAAiB,YAC5C,iEAAkB,EAAE,EACpB,YAA2B,6DAC3B,iEAAqB,KAAK,4BAC1B,iEAAmC,KAAK,EACxC,WAAqC,iDACrC,OAAuC;QAEvC,IAAI,SAAS,KAAK,QAAQ,EAAE,CAAC;YACzB,OAAO,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,aAAa,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;QACvE,CAAC;QAED,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,MAAM,OAAO,GAAG,iKAAI,aAAU,EAAE,CAAC;YACjC,OAAO,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,GAAG,EAAE;gBAC9C,IAAI,OAAO,CAAC,UAAU,IAAI,CAAC,EAAE,CAAC;oBAC1B,IAAI,OAAO,CAAC,MAAM,IAAI,GAAG,EAAE,CAAC;wBACxB,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC;wBACzE,MAAM,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;wBAE7D,IAAI,CAAC,YAAY,EAAE,CAAC;4BAChB,YAAY,iLAAG,sBAAmB,CAAC,KAAK,CAAC,GAAG,CAAG,CAAD,GAAK,YAAY,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,CAAC,EAAE,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;4BACjI,YAAY,CAAC,QAAQ,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;wBAChD,CAAC;wBAED,YAAY,CAAC,qBAAqB,CAAC,mBAAmB,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;wBAC3F,YAAY,CAAC,SAAS,GAAG,SAAS,CAAC;wBAEnC,4CAA4C;wBAC5C,YAAY,CAAC,eAAe,GAAG,IAAI,CAAC;wBAEpC,IAAI,CAAC;4BACD,IAAI,CAAC,SAAS,EAAE,CAAC;gCACb,YAAY,CAAC,KAAK,EAAE,CAAC;4BACzB,CAAC;wBACL,CAAC,CAAC,OAAO,GAAG,EAAE,CAAC;4BACX,2EAA2E;4BAC3E,MAAM,CAAC,GAAG,CAAC,CAAC;wBAChB,CAAC;wBAED,IAAI,uBAAuB,EAAE,CAAC;4BAC1B,YAAY,CACP,sBAAsB,EAAE,AACzB,0CAA0C;6BACzC,IAAI,CAAC,GAAG,EAAE;gCACP,OAAO,CAAC,YAAa,CAAC,CAAC;4BAC3B,CAAC,CAAC,AACF,0CAA0C;6BACzC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;gCACX,2EAA2E;gCAC3E,MAAM,CAAC,GAAG,CAAC,CAAC;4BAChB,CAAC,CAAC,CAAC;wBACX,CAAC,MAAM,CAAC;4BACJ,OAAO,CAAC,YAAY,CAAC,CAAC;wBAC1B,CAAC;oBACL,CAAC,MAAM,CAAC;wBACJ,2EAA2E;wBAC3E,MAAM,CAAC,6BAA6B,GAAG,SAAS,CAAC,CAAC;oBACtD,CAAC;gBACL,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,GAAG,GAAG,GAAG,SAAS,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;YAC1E,OAAO,CAAC,IAAI,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;OAKG,CACI,MAAM,CAAC,aAAa,CAAC,IAAY,EAAE,KAAa,EAAA;QACnD,MAAM,WAAW,GAAG,IAAI,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAElD,WAAW,CAAC,YAAY,EAAE,CAAC;QAC3B,WAAW,CAAC,KAAK,EAAE,CAAC;QAEpB,OAAO,WAAW,CAAC;IACvB,CAAC;IAztED;;;;;OAKG,CACH,YAAY,IAAY,EAAE,KAAa,EAAE,UAAyC,CAAA,CAAE,CAAA;QAChF,KAAK,CAAC,IAAI,EAAE,KAAK,qKAAI,cAAW,CAAC,gBAAiB,CAAC,CAAC;QAzMhD,IAAA,CAAA,QAAQ,GAAW,YAAY,CAAC,iBAAiB,EAAE,CAAC;QACpD,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QAC5B,IAAA,CAAA,sBAAsB,GAAG,sKAAI,SAAM,EAAE,CAAC;QACtC,IAAA,CAAA,gCAAgC,GAAG,sKAAI,SAAM,EAAE,CAAC;QAChD,IAAA,CAAA,WAAW,GAAG,IAAI,KAAK,EAAyB,CAAC;QACjD,IAAA,CAAA,eAAe,GAAG,CAAC,CAAC,CAAC;QACrB,IAAA,CAAA,kBAAkB,GAAG,KAAK,CAAC;QAuC3B,IAAA,CAAA,qBAAqB,GAAG,IAAI,CAAC,4BAA4B,EAAE,CAAC;QAEpE,cAAA,EAAgB,CACT,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QAqCnC;;;WAGG,CACI,IAAA,CAAA,UAAU,GAAQ,IAAI,CAAC;QAE9B;;WAEG,CAEI,IAAA,CAAA,WAAW,GAAG,KAAK,CAAC;QAE3B;;WAEG,CAEI,IAAA,CAAA,qBAAqB,GAAG,CAAC,CAAC;QAEjC;;WAEG,CACI,IAAA,CAAA,iBAAiB,GAAG,iKAAI,aAAU,EAAgB,CAAC;QAE1D;;WAEG,CACI,IAAA,CAAA,sBAAsB,GAAG,gKAAI,cAAU,EAAU,CAAC;QAEzD;;WAEG,CACI,IAAA,CAAA,kBAAkB,GAAG,IAAI,KAAK,EAAqB,CAAC;QAE3D;;WAEG,CACI,IAAA,CAAA,oBAAoB,GAAG,IAAI,KAAK,EAAqB,CAAC;QAmC7D;;WAEG,CACI,IAAA,CAAA,cAAc,GAAwB,EAAE,CAAC;QAEhD;;;WAGG,CAEI,IAAA,CAAA,KAAK,6LAAsB,oBAAiB,CAAC,QAAQ,CAAC;QA4R7D;;WAEG,CAEI,IAAA,CAAA,kBAAkB,GAAG,KAAK,CAAC;QA3P9B,IAAI,CAAC,YAAY,CAAC,+BAA+B,IAAI,OAAO,IAAI,OAAO,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,KAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,CAAC;YACtJ,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAC;QACnF,CAAC;QAED,IAAI,CAAC,QAAQ,GAAG;YACZ,YAAY,EAAE,KAAK;YACnB,cAAc,EAAE,YAAY,CAAC,qBAAqB;YAClD,GAAG,OAAO;SACb,CAAC;QAEF,IAAI,YAAY,CAAC,+BAA+B,EAAE,CAAC;YAC/C,IAAI,CAAC,QAAQ,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAA,EAAA,uBAAA,GAAqB,CAAC,EAAA,EAAA,uBAAA,EAAoB,CAAC;QACpH,CAAC;QAED,2DAA2D;QAC3D,IAAI,CAAC,mCAAmC,CAAC,IAAI,CAAC,CAAC;IACnD,CAAC;;AAhOc,aAAA,iBAAiB,GAAW,CAAX,AAAY,CAAC;AAa7C,8CAAA,EAAgD,CAClC,aAAA,SAAS,GAAG,kKAAG,QAAK,CAAC,cAAc,EAAA,MAA2B,kCAAmC,AAAxF,CAAyF,wIAA1D,iBAAc,CAAC,OAAO,EAAA;AAE5E,oCAAA,EAAsC,CACxB,aAAA,UAAU,GAAG,QAAS,CAAC,UAAU,AAAvB,CAAwB;AAEhD,sHAAA,EAAwH,CAC1G,aAAA,wBAAwB,GAAG,KAAK,AAAR,CAAS;AAE/C,8DAAA,EAAgE,CAClD,aAAA,qBAAqB,GAAA,EAAA,uBAAA,EAAA,CAAuB;AAE1D,uJAAA,EAAyJ,CAC3I,aAAA,+BAA+B,GAAG,KAAK,AAAR,CAAS;CA0E/C,uKAAA,EAAA;qKADN,YAAA,AAAS,EAAE;iDACe;2JAMpB,aAAA,EAAA;qKADN,YAAA,AAAS,EAAE;2DACqB;2JAiE1B,aAAA,EAAA;qKADN,YAAA,AAAS,EAAC,MAAM,CAAC;2CAC2C;2JA0BtD,aAAA,EAAA;qKADN,YAAA,AAAS,EAAC,SAAS,CAAC;6CACE;AAsQhB,wKAAA,EAAA;qKADN,YAAA,AAAS,EAAE;wDACsB;gKAw9DtC,gBAAA,AAAa,EAAC,sBAAsB,EAAE,YAAY,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 4273, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterialConnectionPointCustomObject.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/nodeMaterialConnectionPointCustomObject.ts"], "sourcesContent": ["import type { NodeMaterialBlock } from \"./nodeMaterialBlock\";\r\nimport type { NodeMaterialConnectionPointDirection } from \"./nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialConnectionPoint, NodeMaterialConnectionPointCompatibilityStates } from \"./nodeMaterialBlockConnectionPoint\";\r\nimport type { Nullable } from \"../../types\";\r\n\r\n/**\r\n * Defines a connection point to be used for points with a custom object type\r\n */\r\nexport class NodeMaterialConnectionPointCustomObject<T extends NodeMaterialBlock> extends NodeMaterialConnectionPoint {\r\n    /**\r\n     * Creates a new connection point\r\n     * @param name defines the connection point name\r\n     * @param ownerBlock defines the block hosting this connection point\r\n     * @param direction defines the direction of the connection point\r\n     * @param _blockType\r\n     * @param _blockName\r\n     */\r\n    public constructor(\r\n        name: string,\r\n        ownerBlock: NodeMaterialBlock,\r\n        direction: NodeMaterialConnectionPointDirection,\r\n        // @internal\r\n        public _blockType: new (...args: any[]) => T,\r\n        private _blockName: string\r\n    ) {\r\n        super(name, ownerBlock, direction);\r\n\r\n        this.needDualDirectionValidation = true;\r\n    }\r\n\r\n    /**\r\n     * Gets a number indicating if the current point can be connected to another point\r\n     * @param connectionPoint defines the other connection point\r\n     * @returns a number defining the compatibility state\r\n     */\r\n    public override checkCompatibilityState(connectionPoint: NodeMaterialConnectionPoint): NodeMaterialConnectionPointCompatibilityStates {\r\n        return connectionPoint instanceof NodeMaterialConnectionPointCustomObject && connectionPoint._blockName === this._blockName\r\n            ? NodeMaterialConnectionPointCompatibilityStates.Compatible\r\n            : NodeMaterialConnectionPointCompatibilityStates.TypeIncompatible;\r\n    }\r\n\r\n    /**\r\n     * Creates a block suitable to be used as an input for this input point.\r\n     * If null is returned, a block based on the point type will be created.\r\n     * @returns The returned string parameter is the name of the output point of NodeMaterialBlock (first parameter of the returned array) that can be connected to the input\r\n     */\r\n    public override createCustomInputBlock(): Nullable<[NodeMaterialBlock, string]> {\r\n        return [new this._blockType(this._blockName), this.name];\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AAEA,OAAO,EAAE,2BAA2B,EAAkD,MAAM,oCAAoC,CAAC;;AAM3H,MAAO,uCAAqE,yMAAQ,8BAA2B;IAsBjH;;;;OAIG,CACa,uBAAuB,CAAC,eAA4C,EAAA;QAChF,OAAO,eAAe,YAAY,uCAAuC,IAAI,eAAe,CAAC,UAAU,KAAK,IAAI,CAAC,UAAU,GACtH,EAAA,6DAAA,MACA,EAAA,mEAAA,EAAgE,CAAC;IAC1E,CAAC;IAED;;;;OAIG,CACa,sBAAsB,GAAA;QAClC,OAAO;YAAC,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC;YAAE,IAAI,CAAC,IAAI;SAAC,CAAC;IAC7D,CAAC;IAvCD;;;;;;;OAOG,CACH,YACI,IAAY,EACZ,UAA6B,EAC7B,SAA+C,EAC/C,YAAY;IACL,UAAqC,EACpC,UAAkB,CAAA;QAE1B,KAAK,CAAC,IAAI,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;QAH5B,IAAA,CAAA,UAAU,GAAV,UAAU,CAA2B;QACpC,IAAA,CAAA,UAAU,GAAV,UAAU,CAAQ;QAI1B,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC;IAC5C,CAAC;CAqBJ", "debugId": null}}, {"offset": {"line": 4315, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterialDefault.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/nodeMaterialDefault.ts"], "sourcesContent": ["import { TransformBlock } from \"./Blocks/transformBlock\";\r\nimport { VertexOutputBlock } from \"./Blocks/Vertex/vertexOutputBlock\";\r\nimport { FragmentOutputBlock } from \"./Blocks/Fragment/fragmentOutputBlock\";\r\nimport { InputBlock } from \"./Blocks/Input/inputBlock\";\r\nimport { GaussianSplattingBlock } from \"./Blocks/GaussianSplatting/gaussianSplattingBlock\";\r\nimport { GaussianBlock } from \"./Blocks/GaussianSplatting/gaussianBlock\";\r\nimport { SplatReaderBlock } from \"./Blocks/GaussianSplatting/splatReaderBlock\";\r\nimport { NodeMaterialModes } from \"./Enums/nodeMaterialModes\";\r\nimport { NodeMaterialSystemValues } from \"./Enums/nodeMaterialSystemValues\";\r\nimport type { NodeMaterial } from \"./nodeMaterial\";\r\nimport { MultiplyBlock } from \"./Blocks/multiplyBlock\";\r\nimport { Texture } from \"../Textures/texture\";\r\nimport { Tools } from \"core/Misc/tools\";\r\nimport { SmartFilterTextureBlock } from \"./Blocks/Dual/smartFilterTextureBlock\";\r\nimport { Color4 } from \"core/Maths/math.color\";\r\nimport { AddBlock } from \"./Blocks/addBlock\";\r\nimport { SmartFilterFragmentOutputBlock } from \"./Blocks/Fragment/smartFilterFragmentOutputBlock\";\r\n\r\n/**\r\n * Clear the material and set it to a default state for gaussian splatting\r\n * @param nodeMaterial node material to use\r\n */\r\nexport function SetToDefaultGaussianSplatting(nodeMaterial: NodeMaterial): void {\r\n    nodeMaterial.clear();\r\n\r\n    nodeMaterial.editorData = null;\r\n\r\n    // reading splat datas\r\n    const splatIndex = new InputBlock(\"SplatIndex\");\r\n    splatIndex.setAsAttribute(\"splatIndex\");\r\n\r\n    const splatReader = new SplatReaderBlock(\"SplatReader\");\r\n    splatIndex.connectTo(splatReader);\r\n\r\n    // transforming datas into renderable positions\r\n    const gs = new GaussianSplattingBlock(\"GaussianSplatting\");\r\n    splatReader.connectTo(gs);\r\n\r\n    // world transformation\r\n    const worldInput = new InputBlock(\"World\");\r\n    worldInput.setAsSystemValue(NodeMaterialSystemValues.World);\r\n\r\n    const worldPos = new TransformBlock(\"WorldPos\");\r\n\r\n    splatReader.connectTo(worldPos);\r\n    worldInput.connectTo(worldPos);\r\n    worldPos.connectTo(gs, { output: \"xyz\", input: \"splatPosition\" });\r\n\r\n    // view and projections\r\n\r\n    const view = new InputBlock(\"view\");\r\n    view.setAsSystemValue(NodeMaterialSystemValues.View);\r\n\r\n    const projection = new InputBlock(\"Projection\");\r\n    projection.setAsSystemValue(NodeMaterialSystemValues.Projection);\r\n\r\n    worldInput.connectTo(gs, { input: \"world\" });\r\n    view.connectTo(gs, { input: \"view\" });\r\n    projection.connectTo(gs, { input: \"projection\" });\r\n\r\n    const addBlock = new AddBlock(\"Add SH\");\r\n\r\n    // from color to gaussian color\r\n    const gaussian = new GaussianBlock(\"Gaussian\");\r\n    splatReader.connectTo(gaussian, { input: \"splatColor\", output: \"splatColor\" });\r\n\r\n    // fragment and vertex outputs\r\n    const fragmentOutput = new FragmentOutputBlock(\"FragmentOutput\");\r\n\r\n    gs.SH.connectTo(addBlock.left);\r\n    gaussian.rgb.connectTo(addBlock.right);\r\n    addBlock.output.connectTo(fragmentOutput.rgb);\r\n    gaussian.alpha.connectTo(fragmentOutput.a);\r\n\r\n    const vertexOutput = new VertexOutputBlock(\"VertexOutput\");\r\n    gs.connectTo(vertexOutput);\r\n\r\n    // Add to nodes\r\n    nodeMaterial.addOutputNode(vertexOutput);\r\n    nodeMaterial.addOutputNode(fragmentOutput);\r\n\r\n    nodeMaterial._mode = NodeMaterialModes.GaussianSplatting;\r\n}\r\n\r\n/**\r\n * Clear the material and set it to a default state for Smart Filter effects\r\n * @param nodeMaterial node material to use\r\n */\r\nexport function SetToDefaultSFE(nodeMaterial: NodeMaterial): void {\r\n    nodeMaterial.clear();\r\n\r\n    nodeMaterial.editorData = null;\r\n\r\n    const uv = new InputBlock(\"uv\");\r\n    uv.setAsAttribute(\"postprocess_uv\");\r\n    uv.comments = \"Normalized screen position to sample our texture with.\";\r\n\r\n    const currentScreen = new SmartFilterTextureBlock(\"Input Texture\");\r\n    currentScreen.comments = \"A placeholder that represents the input texture to compose.\";\r\n    uv.connectTo(currentScreen);\r\n    const textureUrl = Tools.GetAssetUrl(\"https://assets.babylonjs.com/core/nme/currentScreenPostProcess.png\");\r\n    currentScreen.texture = new Texture(textureUrl, nodeMaterial.getScene());\r\n\r\n    const color = new InputBlock(\"Color4\");\r\n    color.value = new Color4(1, 0, 0, 1);\r\n\r\n    const multiply = new MultiplyBlock(\"Multiply\");\r\n    color.connectTo(multiply);\r\n    currentScreen.connectTo(multiply);\r\n\r\n    const fragmentOutput = new SmartFilterFragmentOutputBlock(\"FragmentOutput\");\r\n    multiply.connectTo(fragmentOutput);\r\n\r\n    nodeMaterial.addOutputNode(fragmentOutput);\r\n\r\n    nodeMaterial._mode = NodeMaterialModes.SFE;\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AACzD,OAAO,EAAE,iBAAiB,EAAE,MAAM,mCAAmC,CAAC;AACtE,OAAO,EAAE,mBAAmB,EAAE,MAAM,uCAAuC,CAAC;AAC5E,OAAO,EAAE,UAAU,EAAE,MAAM,2BAA2B,CAAC;AACvD,OAAO,EAAE,sBAAsB,EAAE,MAAM,mDAAmD,CAAC;AAC3F,OAAO,EAAE,aAAa,EAAE,MAAM,0CAA0C,CAAC;AACzE,OAAO,EAAE,gBAAgB,EAAE,MAAM,6CAA6C,CAAC;AAC/E,OAAO,EAAE,iBAAiB,EAAE,MAAM,2BAA2B,CAAC;AAC9D,OAAO,EAAE,wBAAwB,EAAE,MAAM,kCAAkC,CAAC;AAE5E,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AACvD,OAAO,EAAE,OAAO,EAAE,MAAM,qBAAqB,CAAC;AAC9C,OAAO,EAAE,KAAK,EAAE,4BAAwB;AACxC,OAAO,EAAE,uBAAuB,EAAE,MAAM,uCAAuC,CAAC;AAChF,OAAO,EAAE,MAAM,EAAE,kCAA8B;AAC/C,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAC7C,OAAO,EAAE,8BAA8B,EAAE,MAAM,kDAAkD,CAAC;;;;;;;;;;;;;;;;;AAM5F,SAAU,6BAA6B,CAAC,YAA0B;IACpE,YAAY,CAAC,KAAK,EAAE,CAAC;IAErB,YAAY,CAAC,UAAU,GAAG,IAAI,CAAC;IAE/B,sBAAsB;IACtB,MAAM,UAAU,GAAG,iMAAI,aAAU,CAAC,YAAY,CAAC,CAAC;IAChD,UAAU,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;IAExC,MAAM,WAAW,GAAG,mNAAI,mBAAgB,CAAC,aAAa,CAAC,CAAC;IACxD,UAAU,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;IAElC,+CAA+C;IAC/C,MAAM,EAAE,GAAG,yNAAI,yBAAsB,CAAC,mBAAmB,CAAC,CAAC;IAC3D,WAAW,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;IAE1B,uBAAuB;IACvB,MAAM,UAAU,GAAG,iMAAI,aAAU,CAAC,OAAO,CAAC,CAAC;IAC3C,UAAU,CAAC,gBAAgB,kMAAC,2BAAwB,CAAC,KAAK,CAAC,CAAC;IAE5D,MAAM,QAAQ,GAAG,4LAAI,iBAAc,CAAC,UAAU,CAAC,CAAC;IAEhD,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;IAChC,UAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;IAC/B,QAAQ,CAAC,SAAS,CAAC,EAAE,EAAE;QAAE,MAAM,EAAE,KAAK;QAAE,KAAK,EAAE,eAAe;IAAA,CAAE,CAAC,CAAC;IAElE,uBAAuB;IAEvB,MAAM,IAAI,GAAG,iMAAI,aAAU,CAAC,MAAM,CAAC,CAAC;IACpC,IAAI,CAAC,gBAAgB,kMAAC,2BAAwB,CAAC,IAAI,CAAC,CAAC;IAErD,MAAM,UAAU,GAAG,iMAAI,aAAU,CAAC,YAAY,CAAC,CAAC;IAChD,UAAU,CAAC,gBAAgB,kMAAC,2BAAwB,CAAC,UAAU,CAAC,CAAC;IAEjE,UAAU,CAAC,SAAS,CAAC,EAAE,EAAE;QAAE,KAAK,EAAE,OAAO;IAAA,CAAE,CAAC,CAAC;IAC7C,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE;QAAE,KAAK,EAAE,MAAM;IAAA,CAAE,CAAC,CAAC;IACtC,UAAU,CAAC,SAAS,CAAC,EAAE,EAAE;QAAE,KAAK,EAAE,YAAY;IAAA,CAAE,CAAC,CAAC;IAElD,MAAM,QAAQ,GAAG,sLAAI,WAAQ,CAAC,QAAQ,CAAC,CAAC;IAExC,+BAA+B;IAC/B,MAAM,QAAQ,GAAG,gNAAI,gBAAa,CAAC,UAAU,CAAC,CAAC;IAC/C,WAAW,CAAC,SAAS,CAAC,QAAQ,EAAE;QAAE,KAAK,EAAE,YAAY;QAAE,MAAM,EAAE,YAAY;IAAA,CAAE,CAAC,CAAC;IAE/E,8BAA8B;IAC9B,MAAM,cAAc,GAAG,6MAAI,sBAAmB,CAAC,gBAAgB,CAAC,CAAC;IAEjE,EAAE,CAAC,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC/B,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACvC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;IAC9C,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;IAE3C,MAAM,YAAY,GAAG,yMAAI,oBAAiB,CAAC,cAAc,CAAC,CAAC;IAC3D,EAAE,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;IAE3B,eAAe;IACf,YAAY,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;IACzC,YAAY,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;IAE3C,YAAY,CAAC,KAAK,6LAAG,oBAAiB,CAAC,iBAAiB,CAAC;AAC7D,CAAC;AAMK,SAAU,eAAe,CAAC,YAA0B;IACtD,YAAY,CAAC,KAAK,EAAE,CAAC;IAErB,YAAY,CAAC,UAAU,GAAG,IAAI,CAAC;IAE/B,MAAM,EAAE,GAAG,iMAAI,aAAU,CAAC,IAAI,CAAC,CAAC;IAChC,EAAE,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;IACpC,EAAE,CAAC,QAAQ,GAAG,wDAAwD,CAAC;IAEvE,MAAM,aAAa,GAAG,6MAAI,0BAAuB,CAAC,eAAe,CAAC,CAAC;IACnE,aAAa,CAAC,QAAQ,GAAG,6DAA6D,CAAC;IACvF,EAAE,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;IAC5B,MAAM,UAAU,2JAAG,QAAK,CAAC,WAAW,CAAC,oEAAoE,CAAC,CAAC;IAC3G,aAAa,CAAC,OAAO,GAAG,+KAAI,UAAO,CAAC,UAAU,EAAE,YAAY,CAAC,QAAQ,EAAE,CAAC,CAAC;IAEzE,MAAM,KAAK,GAAG,iMAAI,aAAU,CAAC,QAAQ,CAAC,CAAC;IACvC,KAAK,CAAC,KAAK,GAAG,qKAAI,SAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAErC,MAAM,QAAQ,GAAG,2LAAI,gBAAa,CAAC,UAAU,CAAC,CAAC;IAC/C,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;IAC1B,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;IAElC,MAAM,cAAc,GAAG,wNAAI,iCAA8B,CAAC,gBAAgB,CAAC,CAAC;IAC5E,QAAQ,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;IAEnC,YAAY,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;IAE3C,YAAY,CAAC,KAAK,6LAAG,oBAAiB,CAAC,GAAG,CAAC;AAC/C,CAAC", "debugId": null}}, {"offset": {"line": 4431, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Optimizers/nodeMaterialOptimizer.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Optimizers/nodeMaterialOptimizer.ts"], "sourcesContent": ["import type { NodeMaterialBlock } from \"../nodeMaterialBlock\";\r\n\r\n/**\r\n * Root class for all node material optimizers\r\n */\r\nexport class NodeMaterialOptimizer {\r\n    /**\r\n     * Function used to optimize a NodeMaterial graph\r\n     * @param _vertexOutputNodes defines the list of output nodes for the vertex shader\r\n     * @param _fragmentOutputNodes defines the list of output nodes for the fragment shader\r\n     */\r\n    public optimize(_vertexOutputNodes: NodeMaterialBlock[], _fragmentOutputNodes: NodeMaterialBlock[]) {\r\n        // Do nothing by default\r\n    }\r\n}\r\n"], "names": [], "mappings": "AAEA;;GAEG;;;AACG,MAAO,qBAAqB;IAC9B;;;;OAIG,CACI,QAAQ,CAAC,kBAAuC,EAAE,oBAAyC,EAAA;IAC9F,wBAAwB;IAC5B,CAAC;CACJ", "debugId": null}}, {"offset": {"line": 4449, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Optimizers/index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Optimizers/index.ts"], "sourcesContent": ["export * from \"./nodeMaterialOptimizer\";\r\n"], "names": [], "mappings": ";AAAA,cAAc,yBAAyB,CAAC", "debugId": null}}, {"offset": {"line": 4463, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/index.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-restricted-imports */\r\nexport * from \"./Enums/index\";\r\nexport * from \"./nodeMaterialConnectionPointCustomObject\";\r\nexport * from \"./nodeMaterialBlockConnectionPoint\";\r\nexport * from \"./nodeMaterialBlock\";\r\nexport * from \"./nodeMaterialDefault\";\r\nexport * from \"./nodeMaterial\";\r\nexport * from \"./Blocks/index\";\r\nexport * from \"./Optimizers/index\";\r\nexport * from \"../../Decorators/nodeDecorator\";\r\n"], "names": [], "mappings": "AAAA,2DAAA,EAA6D;AAC7D,cAAc,eAAe,CAAC;AAC9B,cAAc,2CAA2C,CAAC;AAC1D,cAAc,oCAAoC,CAAC;AACnD,cAAc,qBAAqB,CAAC;AACpC,cAAc,uBAAuB,CAAC;AACtC,cAAc,gBAAgB,CAAC;AAC/B,cAAc,gBAAgB,CAAC;AAC/B,cAAc,oBAAoB,CAAC;AACnC,cAAc,gCAAgC,CAAC", "debugId": null}}]}