{"version": 3, "file": "shaderDefineArithmeticOperator.js", "sourceRoot": "", "sources": ["../../../../../../../dev/core/src/Engines/Processors/Expressions/Operators/shaderDefineArithmeticOperator.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,sBAAsB,EAAE,MAAM,2BAA2B,CAAC;AAEnE,gBAAgB;AAChB,MAAM,OAAO,8BAA+B,SAAQ,sBAAsB;IACtE,YACW,MAAc,EACd,OAAe,EACf,SAAiB;QAExB,KAAK,EAAE,CAAC;QAJD,WAAM,GAAN,MAAM,CAAQ;QACd,YAAO,GAAP,OAAO,CAAQ;QACf,cAAS,GAAT,SAAS,CAAQ;IAG5B,CAAC;IAEe,QAAQ;QACpB,OAAO,GAAG,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;IAC9D,CAAC;IAEe,MAAM,CAAC,aAAwC;QAC3D,IAAI,SAAS,GAAG,KAAK,CAAC;QAEtB,MAAM,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC1G,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAEpH,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;YAC9B,uFAAuF;YACvF,yHAAyH;YACzH,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,QAAQ,IAAI,CAAC,OAAO,EAAE,CAAC;YACnB,KAAK,GAAG;gBACJ,SAAS,GAAG,IAAI,GAAG,KAAK,CAAC;gBACzB,MAAM;YACV,KAAK,GAAG;gBACJ,SAAS,GAAG,IAAI,GAAG,KAAK,CAAC;gBACzB,MAAM;YACV,KAAK,IAAI;gBACL,SAAS,GAAG,IAAI,IAAI,KAAK,CAAC;gBAC1B,MAAM;YACV,KAAK,IAAI;gBACL,SAAS,GAAG,IAAI,IAAI,KAAK,CAAC;gBAC1B,MAAM;YACV,KAAK,IAAI;gBACL,SAAS,GAAG,IAAI,KAAK,KAAK,CAAC;gBAC3B,MAAM;YACV,KAAK,IAAI;gBACL,SAAS,GAAG,IAAI,KAAK,KAAK,CAAC;gBAC3B,MAAM;QACd,CAAC;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;CACJ", "sourcesContent": ["import { ShaderDefineExpression } from \"../shaderDefineExpression\";\r\n\r\n/** @internal */\r\nexport class ShaderDefineArithmeticOperator extends ShaderDefineExpression {\r\n    public constructor(\r\n        public define: string,\r\n        public operand: string,\r\n        public testValue: string\r\n    ) {\r\n        super();\r\n    }\r\n\r\n    public override toString() {\r\n        return `${this.define} ${this.operand} ${this.testValue}`;\r\n    }\r\n\r\n    public override isTrue(preprocessors: { [key: string]: string }) {\r\n        let condition = false;\r\n\r\n        const left = parseInt(preprocessors[this.define] != undefined ? preprocessors[this.define] : this.define);\r\n        const right = parseInt(preprocessors[this.testValue] != undefined ? preprocessors[this.testValue] : this.testValue);\r\n\r\n        if (isNaN(left) || isNaN(right)) {\r\n            // We can't evaluate the expression because we can't resolve the left and/or right side\r\n            // We should not throw an error here because the code might be using a define that is not defined in the material/shader!\r\n            return false;\r\n        }\r\n\r\n        switch (this.operand) {\r\n            case \">\":\r\n                condition = left > right;\r\n                break;\r\n            case \"<\":\r\n                condition = left < right;\r\n                break;\r\n            case \"<=\":\r\n                condition = left <= right;\r\n                break;\r\n            case \">=\":\r\n                condition = left >= right;\r\n                break;\r\n            case \"==\":\r\n                condition = left === right;\r\n                break;\r\n            case \"!=\":\r\n                condition = left !== right;\r\n                break;\r\n        }\r\n\r\n        return condition;\r\n    }\r\n}\r\n"]}