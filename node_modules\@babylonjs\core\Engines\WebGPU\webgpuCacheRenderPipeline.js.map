{"version": 3, "file": "webgpuCacheRenderPipeline.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGPU/webgpuCacheRenderPipeline.ts"], "names": [], "mappings": "AAAA,yDAAyD;AACzD,wCAAwC;AACxC,wCAAwC;AACxC,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AAIzC,OAAO,EAAE,YAAY,EAAE,MAAM,sBAAsB,CAAC;AAKpD,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAC5D,OAAO,EAAE,8BAA8B,EAAE,MAAM,wBAAwB,CAAC;AACxE,OAAO,EAAE,0BAA0B,EAAE,sDAAkD;AACvF,OAAO,EAAE,MAAM,EAAE,6BAAyB;AAE1C,IAAK,aAkBJ;AAlBD,WAAK,aAAa;IACd,uEAAmB,CAAA;IACnB,yEAAoB,CAAA;IACpB,+DAA+D;IAC/D,2DAAa,CAAA;IACb,+EAAuB,CAAA;IACvB,2EAAqB,CAAA;IACrB,qEAAkB,CAAA;IAClB,6EAAsB,CAAA;IACtB,iEAAgB,CAAA;IAChB,iEAAgB,CAAA;IAChB,iEAAgB,CAAA;IAChB,kEAAiB,CAAA;IACjB,gEAAgB,CAAA;IAChB,kEAAiB,CAAA;IACjB,gEAAgB,CAAA;IAEhB,4DAAc,CAAA;AAClB,CAAC,EAlBI,aAAa,KAAb,aAAa,QAkBjB;AAED,MAAM,uBAAuB,GAA+B;IACxD,CAAC,EAAE,CAAC,EAAE,OAAO;IACb,CAAC,EAAE,CAAC,EAAE,MAAM;IACZ,MAAM,EAAE,CAAC,EAAE,WAAW;IACtB,MAAM,EAAE,CAAC,EAAE,mBAAmB;IAC9B,MAAM,EAAE,CAAC,EAAE,WAAW;IACtB,MAAM,EAAE,CAAC,EAAE,mBAAmB;IAC9B,MAAM,EAAE,CAAC,EAAE,WAAW;IACtB,MAAM,EAAE,CAAC,EAAE,mBAAmB;IAC9B,MAAM,EAAE,CAAC,EAAE,WAAW;IACtB,MAAM,EAAE,EAAE,EAAE,mBAAmB;IAC/B,MAAM,EAAE,EAAE,EAAE,oBAAoB;IAChC,MAAM,EAAE,EAAE,EAAE,aAAa;IACzB,MAAM,EAAE,EAAE,EAAE,qBAAqB;IACjC,MAAM,EAAE,EAAE,EAAE,qBAAqB;IACjC,MAAM,EAAE,EAAE,EAAE,6BAA6B;IACzC,MAAM,EAAE,EAAE,EAAE,YAAY;IACxB,MAAM,EAAE,EAAE,EAAE,oBAAoB;IAChC,MAAM,EAAE,EAAE,EAAE,YAAY;IACxB,MAAM,EAAE,EAAE,EAAE,oBAAoB;CACnC,CAAC;AAEF,MAAM,yBAAyB,GAA+B;IAC1D,MAAM,EAAE,CAAC,EAAE,MAAM;IACjB,MAAM,EAAE,CAAC,EAAE,MAAM;IACjB,MAAM,EAAE,CAAC,EAAE,MAAM;IACjB,MAAM,EAAE,CAAC,EAAE,WAAW;IACtB,MAAM,EAAE,CAAC,EAAE,kBAAkB;CAChC,CAAC;AAEF,MAAM,gBAAgB,GAA+B;IACjD,MAAM,EAAE,CAAC,EAAE,OAAO;IAClB,MAAM,EAAE,CAAC,EAAE,OAAO;IAClB,MAAM,EAAE,CAAC,EAAE,UAAU;IACrB,MAAM,EAAE,CAAC,EAAE,OAAO;IAClB,MAAM,EAAE,CAAC,EAAE,OAAO;IAClB,MAAM,EAAE,CAAC,EAAE,SAAS;IACpB,MAAM,EAAE,CAAC,EAAE,YAAY;IACvB,MAAM,EAAE,CAAC,EAAE,YAAY;CAC1B,CAAC;AAEF,MAAM,WAAW,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAEjC,gBAAgB;AAChB,MAAM,OAAgB,yBAAyB;IAkE3C,YAAY,MAAiB,EAAE,iBAA+B;QA+C9C,oBAAe,GAAW,CAAC,CAAC;QA9CxC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,CAAC,0KAA0K;QACxM,IAAI,CAAC,OAAO,GAAG,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,gFAAgF;QAC9G,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,sBAAsB,GAAG,CAAC,CAAC;QAChC,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;QAC5C,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,IAAI,CAAC,UAAU,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;QACvD,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,uBAAuB,GAAG,MAAM,CAAC,MAAM,CAAC,0BAA0B,IAAI,IAAI,CAAC;QAChF,IAAI,CAAC,KAAK,EAAE,CAAC;IACjB,CAAC;IAEM,KAAK;QACR,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;QAC/B,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC9B,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC1B,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACrB,4BAA4B;QAC5B,IAAI,CAAC,kBAAkB,GAAG,6DAA0C,CAAC;QACrE,IAAI,CAAC,cAAc,6DAA0C,CAAC;QAC9D,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QAChB,IAAI,CAAC,oBAAoB,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;QACtC,IAAI,CAAC,oBAAoB,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;QAClE,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,qBAAqB,gFAAmD,CAAC;QAC9E,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAC9B,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAClC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC;IAOD,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC;IACjF,CAAC;IAMM,iBAAiB,CAAC,QAAgB,EAAE,MAAc,EAAE,WAAmB,EAAE,YAAY,GAAG,CAAC;QAC5F,WAAW,GAAG,mBAAmB,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAEzD,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,MAAM,QAAQ,GAAG,yBAAyB,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAElE,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,+CAA+C;YAC7E,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;YAEpC,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;YAErF,yBAAyB,CAAC,YAAY,EAAE,CAAC;YACzC,yBAAyB,CAAC,gCAAgC,EAAE,CAAC;YAE7D,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;QACpC,CAAC;QAED,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACtC,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;QACnD,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QAC7B,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;QAEpC,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,sBAAsB,CAAC;QAE7D,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;YAC7C,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,aAAa,CAAC;YACjD,yBAAyB,CAAC,sBAAsB,EAAE,CAAC;YACnD,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;QACpC,CAAC;QAED,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAEzC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,aAAa,CAAC;QAEjD,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;YAC3B,yBAAyB,CAAC,mBAAmB,EAAE,CAAC;YAChD,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;QACpC,CAAC;QAED,MAAM,QAAQ,GAAG,yBAAyB,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAElE,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;QACrF,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAEzC,yBAAyB,CAAC,YAAY,EAAE,CAAC;QACzC,yBAAyB,CAAC,gCAAgC,EAAE,CAAC;QAE7D,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;IACpC,CAAC;IAEM,QAAQ;QACX,yBAAyB,CAAC,4BAA4B,GAAG,yBAAyB,CAAC,gCAAgC,CAAC;QACpH,yBAAyB,CAAC,gCAAgC,GAAG,CAAC,CAAC;IACnE,CAAC;IAEM,kBAAkB,CAAC,OAAgB;QACtC,IAAI,CAAC,uBAAuB,GAAG,OAAO,CAAC;IAC3C,CAAC;IAEM,YAAY,CAAC,SAAiB;QACjC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;IAChC,CAAC;IAEM,cAAc,CAAC,OAAgB;QAClC,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC;IAChC,CAAC;IAEM,WAAW,CAAC,QAAgB;QAC/B,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC9B,CAAC;IAEM,aAAa,CAAC,UAAmB;QACpC,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;IAClC,CAAC;IAEM,sBAAsB;QACzB,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;IAC/E,CAAC;IAEM,oBAAoB,CACvB,WAAoB,EACpB,SAAiB,EACjB,QAAgB,EAChB,OAAe,EACf,YAAoB,EACpB,gBAAyB,EACzB,iBAA0B,EAC1B,YAA8B;QAE9B,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;QAC5C,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;QAC1C,IAAI,CAAC,aAAa,GAAG,CAAC,YAAY,IAAI,SAAS,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;QACjE,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;QACrC,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;IACpC,CAAC;IAEM,YAAY,CAAC,SAAiB;QACjC,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;YAChC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;YAC5B,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC;YAClD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,sBAAsB,EAAE,aAAa,CAAC,SAAS,CAAC,CAAC;QACjG,CAAC;IACL,CAAC;IAED;;;;;;OAMG;IAEI,sBAAsB,CAAC,mBAA2B;QACrD,IAAI,IAAI,CAAC,oBAAoB,KAAK,mBAAmB,EAAE,CAAC;YACpD,IAAI,CAAC,oBAAoB,GAAG,mBAAmB,CAAC;YAChD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,mBAAmB,CAAC,GAAG,mBAAmB,CAAC;YACtE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,sBAAsB,EAAE,aAAa,CAAC,mBAAmB,CAAC,CAAC;QAC3G,CAAC;IACL,CAAC;IAEM,cAAc,CAAC,MAA+B;QACjD,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;QACpC,IAAI,CAAC,YAAY,GAAG,8BAA8B,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC;IACrE,CAAC;IAED,+BAA+B;IACxB,iBAAiB,CAAC,WAAqB;QACzC,IAAI,CAAC,cAAsB,GAAG,WAAW,CAAC;QAC3C,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;YAC1C,IAAI,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;gBACvB,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;YACnB,CAAC;QACL,CAAC;QACD,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI,EAAE,CAAC;YAChC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;YAC5B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,sBAAsB,EAAE,aAAa,CAAC,cAAc,CAAC,CAAC;QACtG,CAAC;IACL,CAAC;IAEM,MAAM,CAAC,YAA+B,EAAE,YAAqB;QAChE,YAAY,GAAG,YAAY,IAAI,YAAY,CAAC,MAAM,CAAC;QACnD,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;YACnB,0FAA0F;YAC1F,MAAM,IAAI,KAAK,CAAC,0EAA0E,CAAC,CAAC;QAChG,CAAC;QACA,IAAI,CAAC,eAAuB,GAAG,YAAY,CAAC;QAC5C,IAAI,CAAC,eAAuB,GAAG,YAAY,CAAC;QAE7C,0KAA0K;QAC1K,oIAAoI;QAEpI,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC,CAAC,gHAAgH;QAE/I,IAAI,cAAc,GAAG,CAAC,CAAC;QACvB,IAAI,IAAI,GAAG,CAAC,CAAC;QAEb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,EAAE,CAAC,EAAE,CAAC;YACpC,MAAM,OAAO,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,UAAU,GAAG,OAAO,EAAE,gBAAmD,CAAC;YAEhF,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,UAAU,EAAE,MAAM,IAAI,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;YAEvE,cAAc,IAAI,8BAA8B,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC;YACxF,IAAI,IAAI,CAAC,CAAC;QACd,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,YAAY,CAAC;QACvC,IAAI,IAAI,CAAC,eAAe,KAAK,cAAc,EAAE,CAAC;YAC1C,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;YACtC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,cAAc,CAAC,GAAG,cAAc,CAAC;YAC5D,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,sBAAsB,EAAE,aAAa,CAAC,cAAc,CAAC,CAAC;QACtG,CAAC;IACL,CAAC;IAEM,oBAAoB,CAAC,OAAkB,EAAE,2BAAmC;QAC/E,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC;QAClC,IAAI,CAAC,4BAA4B,GAAG,2BAA2B,CAAC;IACpE,CAAC;IAEM,oBAAoB,CAAC,OAAgC,EAAE,UAAmC;QAC7F,IAAI,CAAC,qBAAqB,GAAG,OAAO,CAAC;QACrC,IAAI,CAAC,mBAAmB,GAAG,UAAU,CAAC;IAC1C,CAAC;IAEM,YAAY,CAAC,IAAY;QAC5B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IAC3B,CAAC;IAEM,qBAAqB,CAAC,MAAoC;QAC7D,IAAI,CAAC,yBAAyB,GAAG,MAAM,CAAC;QACxC,IAAI,CAAC,mBAAmB,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,8BAA8B,CAAC,MAAM,CAAC,CAAC;IACjG,CAAC;IAEM,mBAAmB,CAAC,OAAgB;QACvC,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC;IACrC,CAAC;IAEM,oBAAoB,CAAC,OAAgB;QACxC,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC;IACtC,CAAC;IAEM,eAAe,CAAC,IAAsB;QACzC,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,IAAI,SAAS,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;IAC7D,CAAC;IAEM,iBAAiB,CAAC,OAAgB;QACrC,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC;IACnC,CAAC;IAEM,iBAAiB,CAAC,IAAsB;QAC3C,IAAI,CAAC,oBAAoB,GAAG,CAAC,IAAI,IAAI,SAAS,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;IACpE,CAAC;IAEM,qBAAqB,CAAC,EAAoB;QAC7C,IAAI,CAAC,wBAAwB,GAAG,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;IACtF,CAAC;IAEM,gBAAgB,CAAC,EAAoB;QACxC,IAAI,CAAC,mBAAmB,GAAG,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;IACpF,CAAC;IAEM,gBAAgB,CAAC,EAAoB;QACxC,IAAI,CAAC,mBAAmB,GAAG,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;IACjF,CAAC;IAEM,qBAAqB,CAAC,IAAsB;QAC/C,IAAI,CAAC,mBAAmB,GAAG,CAAC,IAAI,IAAI,SAAS,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;IACnE,CAAC;IAEM,yBAAyB,CAAC,EAAoB;QACjD,IAAI,CAAC,uBAAuB,GAAG,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;IACrF,CAAC;IAEM,oBAAoB,CAAC,EAAoB;QAC5C,IAAI,CAAC,kBAAkB,GAAG,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;IACnF,CAAC;IAEM,oBAAoB,CAAC,EAAoB;QAC5C,IAAI,CAAC,kBAAkB,GAAG,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;IAChF,CAAC;IAEM,kBAAkB,CAAC,IAAY;QAClC,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,EAAE,CAAC;YACjC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAC7B,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC;YACnD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,sBAAsB,EAAE,aAAa,CAAC,eAAe,CAAC,CAAC;QACvG,CAAC;IACL,CAAC;IAEM,mBAAmB,CAAC,IAAY;QACnC,IAAI,IAAI,CAAC,iBAAiB,KAAK,IAAI,EAAE,CAAC;YAClC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;YAC9B,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC;YACpD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,sBAAsB,EAAE,aAAa,CAAC,gBAAgB,CAAC,CAAC;QACxG,CAAC;IACL,CAAC;IAEM,iBAAiB;QACpB,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,SAAS,CAAC,MAAM,EAAE,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,OAAO,EAAE,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IACjH,CAAC;IAEM,eAAe,CAClB,cAAuB,EACvB,OAAyB,EACzB,WAA6B,EAC7B,MAAwB,EACxB,MAAwB,EACxB,QAAgB,EAChB,SAAiB,EACjB,cAAgC,IAAI,EACpC,kBAAoC,IAAI,EACxC,aAA+B,IAAI,EACnC,aAA+B,IAAI;QAEnC,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QACtC,IAAI,CAAC,oBAAoB,GAAG,CAAC,OAAO,IAAI,SAAS,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;QACnE,IAAI,CAAC,wBAAwB,GAAG,WAAW,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;QACpG,IAAI,CAAC,mBAAmB,GAAG,MAAM,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QACxF,IAAI,CAAC,mBAAmB,GAAG,MAAM,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QACrF,IAAI,CAAC,mBAAmB,GAAG,CAAC,WAAW,IAAI,SAAS,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;QACtE,IAAI,CAAC,uBAAuB,GAAG,eAAe,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC;QAC3G,IAAI,CAAC,kBAAkB,GAAG,UAAU,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;QAC/F,IAAI,CAAC,kBAAkB,GAAG,UAAU,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;QAC5F,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QAClC,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;IACxC,CAAC;IAEM,UAAU,CACb,aAAkE,EAClE,WAAiC,EACjC,qBAA0E;QAE1E,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,IAAI,CAAC,sBAAsB,GAAG,qBAAqB,CAAC;QACpD,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;IACpC,CAAC;IAEO,MAAM,CAAC,YAAY,CAAC,QAAgB;QACxC,QAAQ,QAAQ,EAAE,CAAC;YACf,iBAAiB;YACjB,KAAK,SAAS,CAAC,yBAAyB;gBACpC,4EAAsD;YAC1D,KAAK,SAAS,CAAC,sBAAsB;gBACjC,sEAAmD;YACvD,KAAK,SAAS,CAAC,0BAA0B;gBACrC,oEAAkD;YACtD,aAAa;YACb,KAAK,SAAS,CAAC,0BAA0B;gBACrC,sEAAmD;YACvD,KAAK,SAAS,CAAC,yBAAyB;gBACpC,oEAAkD;YACtD,KAAK,SAAS,CAAC,yBAAyB;gBACpC,6BAA6B;gBAC7B,4DAA4D;gBAC5D,4CAA4C;gBAC5C,MAAM,+CAA+C,CAAC;YAC1D,KAAK,SAAS,CAAC,0BAA0B;gBACrC,sEAAmD;YACvD,KAAK,SAAS,CAAC,8BAA8B;gBACzC,8EAAuD;YAC3D,KAAK,SAAS,CAAC,4BAA4B;gBACvC,gCAAgC;gBAChC,+DAA+D;gBAC/D,4CAA4C;gBAC5C,MAAM,kDAAkD,CAAC;YAC7D;gBACI,4EAAsD;QAC9D,CAAC;IACL,CAAC;IAEO,MAAM,CAAC,sBAAsB,CAAC,SAA2B;QAC7D,QAAQ,SAAS,EAAE,CAAC;YAChB,KAAK,SAAS,CAAC,qBAAqB;gBAChC,sDAA0C;YAC9C,KAAK,SAAS,CAAC,0BAA0B;gBACrC,gEAA+C;YACnD,KAAK,SAAS,CAAC,kCAAkC;gBAC7C,+EAAsD;YAC1D,KAAK,SAAS,CAAC,qBAAqB;gBAChC,sDAA0C;YAC9C,KAAK,SAAS,CAAC,qBAAqB;gBAChC,sDAA0C;YAC9C;gBACI,sDAA0C;QAClD,CAAC;IACL,CAAC;IAEO,MAAM,CAAC,mBAAmB,CAAC,MAAwB;QACvD,QAAQ,MAAM,EAAE,CAAC;YACb,KAAK,CAAC;gBACF,qDAAwC;YAC5C,KAAK,CAAC;gBACF,mDAAuC;YAC3C,KAAK,SAAS,CAAC,qBAAqB;gBAChC,mDAAuC;YAC3C,KAAK,SAAS,CAAC,qCAAqC;gBAChD,qEAA+C;YACnD,KAAK,SAAS,CAAC,2BAA2B;gBACtC,8DAA4C;YAChD,KAAK,SAAS,CAAC,qCAAqC;gBAChD,gFAAoD;YACxD,KAAK,SAAS,CAAC,2BAA2B;gBACtC,8DAA4C;YAChD,KAAK,SAAS,CAAC,qCAAqC;gBAChD,gFAAoD;YACxD,KAAK,SAAS,CAAC,2BAA2B;gBACtC,mDAAuC;YAC3C,KAAK,SAAS,CAAC,qCAAqC;gBAChD,qEAA+C;YACnD,KAAK,SAAS,CAAC,qCAAqC;gBAChD,iFAAqD;YACzD,KAAK,SAAS,CAAC,gCAAgC,CAAC;YAChD,KAAK,SAAS,CAAC,gCAAgC;gBAC3C,6DAA4C;YAChD,KAAK,SAAS,CAAC,0CAA0C,CAAC;YAC1D,KAAK,SAAS,CAAC,0CAA0C;gBACrD,+EAAoD;YACxD,KAAK,SAAS,CAAC,4BAA4B;gBACvC,qDAAwC;YAC5C,KAAK,SAAS,CAAC,sCAAsC;gBACjD,uEAAgD;YACpD,KAAK,SAAS,CAAC,4BAA4B;gBACvC,gEAA6C;YACjD,KAAK,SAAS,CAAC,sCAAsC;gBACjD,kFAAqD;YACzD;gBACI,mDAAuC;QAC/C,CAAC;IACL,CAAC;IAEO,MAAM,CAAC,mBAAmB,CAAC,eAAuB;QACtD,QAAQ,eAAe,EAAE,CAAC;YACtB,KAAK,CAAC,EAAE,QAAQ;gBACZ,2DAA6C;YACjD,KAAK,CAAC,EAAE,OAAO;gBACX,yDAA4C;YAChD,KAAK,CAAC,EAAE,QAAQ;gBACZ,2DAA6C;YACjD,KAAK,CAAC,EAAE,SAAS;gBACb,oEAAiD;YACrD,KAAK,CAAC,EAAE,UAAU;gBACd,+DAA+C;YACnD,KAAK,CAAC,EAAE,WAAW;gBACf,kEAAgD;YACpD,KAAK,CAAC,EAAE,SAAS;gBACb,0EAAoD;YACxD,KAAK,CAAC,EAAE,SAAS;gBACb,6DAA8C;QACtD,CAAC;QACD,2DAA6C;IACjD,CAAC;IAEO,MAAM,CAAC,qBAAqB,CAAC,SAAiB;QAClD,QAAQ,SAAS,EAAE,CAAC;YAChB,KAAK,CAAC;gBACF,0DAA6C;YACjD,KAAK,CAAC;gBACF,0DAA6C;YACjD,KAAK,CAAC;gBACF,gEAAgD;YACpD,KAAK,CAAC;gBACF,+EAAuD;YAC3D,KAAK,CAAC;gBACF,+EAAuD;YAC3D,KAAK,CAAC;gBACF,8DAA+C;YACnD,KAAK,CAAC;gBACF,6EAAsD;YAC1D,KAAK,CAAC;gBACF,6EAAsD;QAC9D,CAAC;QACD,0DAA6C;IACjD,CAAC;IAEO,MAAM,CAAC,+BAA+B,CAAC,YAA0B;QACrE,MAAM,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC;QAC/B,MAAM,UAAU,GAAG,YAAY,CAAC,UAAU,CAAC;QAC3C,MAAM,IAAI,GAAG,YAAY,CAAC,OAAO,EAAE,CAAC;QAEpC,QAAQ,IAAI,EAAE,CAAC;YACX,KAAK,YAAY,CAAC,IAAI;gBAClB,QAAQ,IAAI,EAAE,CAAC;oBACX,KAAK,CAAC,CAAC;oBACP,KAAK,CAAC;wBACF,OAAO,UAAU,CAAC,CAAC,wDAAuC,CAAC,qDAAqC,CAAC;oBACrG,KAAK,CAAC,CAAC;oBACP,KAAK,CAAC;wBACF,OAAO,UAAU,CAAC,CAAC,wDAAuC,CAAC,qDAAqC,CAAC;gBACzG,CAAC;gBACD,MAAM;YACV,KAAK,YAAY,CAAC,aAAa;gBAC3B,QAAQ,IAAI,EAAE,CAAC;oBACX,KAAK,CAAC,CAAC;oBACP,KAAK,CAAC;wBACF,OAAO,UAAU,CAAC,CAAC,wDAAuC,CAAC,qDAAqC,CAAC;oBACrG,KAAK,CAAC,CAAC;oBACP,KAAK,CAAC;wBACF,OAAO,UAAU,CAAC,CAAC,wDAAuC,CAAC,qDAAqC,CAAC;gBACzG,CAAC;gBACD,MAAM;YACV,KAAK,YAAY,CAAC,KAAK;gBACnB,QAAQ,IAAI,EAAE,CAAC;oBACX,KAAK,CAAC,CAAC;oBACP,KAAK,CAAC;wBACF,OAAO,UAAU,CAAC,CAAC,0DAAwC,CAAC,uDAAsC,CAAC;oBACvG,KAAK,CAAC,CAAC;oBACP,KAAK,CAAC;wBACF,OAAO,UAAU,CAAC,CAAC,0DAAwC,CAAC,uDAAsC,CAAC;gBAC3G,CAAC;gBACD,MAAM;YACV,KAAK,YAAY,CAAC,cAAc;gBAC5B,QAAQ,IAAI,EAAE,CAAC;oBACX,KAAK,CAAC,CAAC;oBACP,KAAK,CAAC;wBACF,OAAO,UAAU,CAAC,CAAC,0DAAwC,CAAC,uDAAsC,CAAC;oBACvG,KAAK,CAAC,CAAC;oBACP,KAAK,CAAC;wBACF,OAAO,UAAU,CAAC,CAAC,0DAAwC,CAAC,uDAAsC,CAAC;gBAC3G,CAAC;gBACD,MAAM;YACV,KAAK,YAAY,CAAC,GAAG;gBACjB,QAAQ,IAAI,EAAE,CAAC;oBACX,KAAK,CAAC;wBACF,0DAA2C;oBAC/C,KAAK,CAAC;wBACF,8DAA6C;oBACjD,KAAK,CAAC;wBACF,8DAA6C;oBACjD,KAAK,CAAC;wBACF,8DAA6C;gBACrD,CAAC;gBACD,MAAM;YACV,KAAK,YAAY,CAAC,YAAY;gBAC1B,QAAQ,IAAI,EAAE,CAAC;oBACX,KAAK,CAAC;wBACF,0DAA2C;oBAC/C,KAAK,CAAC;wBACF,8DAA6C;oBACjD,KAAK,CAAC;wBACF,8DAA6C;oBACjD,KAAK,CAAC;wBACF,8DAA6C;gBACrD,CAAC;gBACD,MAAM;YACV,KAAK,YAAY,CAAC,KAAK;gBACnB,QAAQ,IAAI,EAAE,CAAC;oBACX,KAAK,CAAC;wBACF,4DAA4C;oBAChD,KAAK,CAAC;wBACF,gEAA8C;oBAClD,KAAK,CAAC;wBACF,gEAA8C;oBAClD,KAAK,CAAC;wBACF,gEAA8C;gBACtD,CAAC;gBACD,MAAM;QACd,CAAC;QAED,MAAM,IAAI,KAAK,CAAC,mBAAmB,YAAY,CAAC,OAAO,EAAE,YAAY,IAAI,gBAAgB,UAAU,UAAU,IAAI,EAAE,CAAC,CAAC;IACzH,CAAC;IAEO,kBAAkB,CAAC,WAAmB;QAC1C,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,EAAE,CAAC;YACxC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO;YACH,SAAS,EAAE,yBAAyB,CAAC,mBAAmB,CAAC,IAAI,CAAC,qBAAqB,CAAC,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;YACzG,SAAS,EAAE,yBAAyB,CAAC,mBAAmB,CAAC,IAAI,CAAC,qBAAqB,CAAC,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;YACzG,SAAS,EAAE,yBAAyB,CAAC,sBAAsB,CAAC,IAAI,CAAC,mBAAmB,CAAC,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;SAC7G,CAAC;IACN,CAAC;IAEO,mBAAmB,CAAC,WAAmB;QAC3C,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC3B,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO;YACH,SAAS,EAAE,yBAAyB,CAAC,mBAAmB,CAAC,IAAI,CAAC,qBAAqB,CAAC,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;YACzG,SAAS,EAAE,yBAAyB,CAAC,mBAAmB,CAAC,IAAI,CAAC,qBAAqB,CAAC,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;YACzG,SAAS,EAAE,yBAAyB,CAAC,sBAAsB,CAAC,IAAI,CAAC,mBAAmB,CAAC,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;SAC7G,CAAC;IACN,CAAC;IAEO,eAAe,CAAC,EAAU;QAC9B,IAAI,IAAI,CAAC,SAAS,KAAK,EAAE,EAAE,CAAC;YACxB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;YACpB,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;YAC7C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,sBAAsB,EAAE,aAAa,CAAC,WAAW,CAAC,CAAC;QACnG,CAAC;IACL,CAAC;IAEO,sBAAsB,CAAC,QAAgB,EAAE,WAAmB;QAChE,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;QAClC,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QACxD,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,MAAM,eAAe,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7D,MAAM,kBAAkB,GAAG,SAAS,GAAG,CAAC,GAAG,CAAC,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC,UAAU,IAAI,CAAC,CAAC,GAAG,CAAC,eAAe,IAAI,CAAC,CAAC,GAAG,CAAC,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC,WAAW,IAAI,CAAC,CAAC,CAAC;QAE/I,IAAI,IAAI,CAAC,mBAAmB,KAAK,kBAAkB,EAAE,CAAC;YAClD,IAAI,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;YAC9C,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC;YAC1E,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,sBAAsB,EAAE,aAAa,CAAC,kBAAkB,CAAC,CAAC;QAC1G,CAAC;IACL,CAAC;IAEO,eAAe;QACnB,8LAA8L;QAE9L,0HAA0H;QAC1H,gHAAgH;QAChH,oJAAoJ;QACpJ,8JAA8J;QAC9J,qGAAqG;QAErG,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACrD,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QAC7D,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACtD,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QAEtD,IAAI,eAAe,GAAG,CAAC,CAAC;QACxB,IAAI,OAAO,GAAG,KAAK,CAAC;QAEpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YACzB,IAAI,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC7B,MAAM,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBACzB,MAAM,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBACzB,MAAM,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBACzB,MAAM,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBACzB,MAAM,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBAC3B,MAAM,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBAC3B,MAAM,GAAG,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,yBAAyB,CAAC,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC,CAAC;gBAC5H,MAAM,GAAG,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,yBAAyB,CAAC,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC,CAAC;gBAE5H,WAAW,CAAC,eAAe,CAAC;oBACxB,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,uBAAuB,CAAC,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;wBACtH,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,uBAAuB,CAAC,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;wBACtH,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,uBAAuB,CAAC,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;wBACvH,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,uBAAuB,CAAC,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;wBACvH,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;YACpC,CAAC;YAED,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;gBACR,OAAO,GAAG,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,YAAY,GAAG,eAAe,CAAC,KAAK,WAAW,CAAC,eAAe,CAAC,CAAC;gBACjH,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,YAAY,GAAG,eAAe,CAAC,GAAG,WAAW,CAAC,eAAe,CAAC,CAAC;gBAC1F,eAAe,EAAE,CAAC;YACtB,CAAC;QACL,CAAC;QAED,IAAI,OAAO,EAAE,CAAC;YACV,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,sBAAsB,EAAE,aAAa,CAAC,YAAY,CAAC,CAAC;QACpG,CAAC;IACL,CAAC;IAEO,qBAAqB;QACzB,MAAM,YAAY,GAAG,CAAC,IAAI,CAAC,eAAe;YACtC,CAAC,CAAC,CAAC,CAAC,YAAY;gBACd,CAAC,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC;gBACnB,CAAC,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC;gBACnB,CAAC,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC,GAAG,QAAQ;gBAC9B,CAAC,CAAC,CAAC,YAAY,IAAI,EAAE,CAAC;gBACtB,CAAC,CAAC,CAAC,UAAU,IAAI,EAAE,CAAC;gBACpB,CAAC,CAAC,CAAC,UAAU,IAAI,EAAE,CAAC;gBACpB,CAAC,CAAC,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC,OAAO;YAC9B,CAAC,CAAC,IAAI,CAAC,oBAAoB;gBACzB,CAAC,IAAI,CAAC,wBAAwB,IAAI,CAAC,CAAC;gBACpC,CAAC,IAAI,CAAC,mBAAmB,IAAI,CAAC,CAAC;gBAC/B,CAAC,IAAI,CAAC,mBAAmB,IAAI,CAAC,CAAC,GAAG,QAAQ;gBAC1C,CAAC,IAAI,CAAC,mBAAmB,IAAI,EAAE,CAAC;gBAChC,CAAC,IAAI,CAAC,uBAAuB,IAAI,EAAE,CAAC;gBACpC,CAAC,IAAI,CAAC,kBAAkB,IAAI,EAAE,CAAC;gBAC/B,CAAC,IAAI,CAAC,kBAAkB,IAAI,EAAE,CAAC,CAAC,CAAC,OAAO;QAE9C,MAAM,iBAAiB,GAAG,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,IAAI,CAAC,CAAC,GAAG,YAAY,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,uBAAuB;QAE9K,IAAI,IAAI,CAAC,kBAAkB,KAAK,iBAAiB,EAAE,CAAC;YAChD,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;YAC5C,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC;YACxE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,sBAAsB,EAAE,aAAa,CAAC,iBAAiB,CAAC,CAAC;QACzG,CAAC;IACL,CAAC;IAEO,eAAe,CAAC,MAAc;QAClC,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC;QACxC,IAAI,YAAY,GAAG,aAAa,CAAC,WAAW,CAAC;QAE7C,MAAM,qBAAqB,GAAG,MAAM,CAAC,gBAAyC,CAAC;QAC/E,MAAM,UAAU,GAAG,qBAAqB,CAAC,uBAAuB,CAAC,wBAAwB,CAAC;QAC1F,MAAM,SAAS,GAAG,qBAAqB,CAAC,uBAAuB,CAAC,4BAA4B,CAAC;QAE7F,IAAI,gBAAgB,CAAC;QACrB,IAAI,gBAAgB,GAAG,CAAC,CAAC;QACzB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;YACrD,MAAM,QAAQ,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;YAClC,IAAI,YAAY,GAAG,CAAC,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,cAAe,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;YAC9I,IAAI,CAAC,YAAY,EAAE,CAAC;gBAChB,gGAAgG;gBAChG,oFAAoF;gBACpF,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC;gBACvC,IAAI,yBAAyB,CAAC,wBAAwB,EAAE,CAAC;oBACrD,MAAM,CAAC,KAAK,CACR,yCAAyC,UAAU,CAAC,KAAK,CAAC,yGAAyG,CACtK,CAAC;gBACN,CAAC;YACL,CAAC;YAED,MAAM,MAAM,GAAG,YAAY,CAAC,eAAe,EAAE,kBAAkB,CAAC;YAEhE,6KAA6K;YAC7K,+JAA+J;YAC/J,wCAAwC;YACxC,IAAI,YAAY,CAAC,iBAAiB,KAAK,SAAS,EAAE,CAAC;gBAC/C,MAAM,MAAM,GAAG,YAAY,CAAC,mBAAmB,CAAC;gBAChD,MAAM,UAAU,GAAG,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBAC9C,MAAM,UAAU,GAAG,YAAY,CAAC,mBAAmB,CAAC;gBAEpD,YAAY,CAAC,iBAAiB;oBAC1B,CAAC,MAAM,GAAG,UAAU,IAAI,IAAI,CAAC,uBAAuB,IAAI,UAAU,KAAK,CAAC,CAAC,IAAI,CAAC,UAAU,KAAK,CAAC,IAAI,MAAM,GAAG,UAAU,IAAI,UAAU,CAAC,CAAC;YAC7I,CAAC;YAED,IAAI,CAAC,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,MAAM,IAAI,YAAY,CAAC,iBAAiB,CAAC,EAAE,CAAC;gBACvF,kEAAkE;gBAClE,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,CAAC,GAAG,YAAY,CAAC;gBACtD,gBAAgB,GAAG,YAAY,CAAC,iBAAiB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC;YACtE,CAAC;YAED,MAAM,GAAG,GAAG,YAAY,CAAC,QAAQ,GAAG,CAAC,QAAQ,IAAI,CAAC,CAAC,CAAC;YAEpD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,GAAG,CAAC;YACpE,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,GAAG,GAAG,CAAC;QACvC,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,gBAAgB,CAAC;QAE7C,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,YAAY,KAAK,YAAY,CAAC;QAC/D,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,sBAAsB,EAAE,aAAa,CAAC,WAAW,CAAC,CAAC;QACnG,CAAC;IACL,CAAC;IAEO,gBAAgB,CAAC,YAAoB;QACzC,IAAI,IAAI,CAAC,aAAa,KAAK,YAAY,EAAE,CAAC;YACtC,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;YAClC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;YAC9D,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,sBAAsB,EAAE,aAAa,CAAC,YAAY,CAAC,CAAC;QACpG,CAAC;IACL,CAAC;IAEO,qBAAqB,CAAC,qBAA4C;QACtE,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,OAAO,IAAI,CAAC,qCAAqC,CAAC,qBAAqB,CAAC,CAAC;QAC7E,CAAC;QAED,MAAM,gBAAgB,GAAyB,EAAE,CAAC;QAClD,MAAM,sBAAsB,GAAG,qBAAqB,CAAC,uBAAuB,CAAC,sBAAsB,CAAC;QAEpG,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,sBAAsB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACrD,MAAM,aAAa,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC;YAEhD,gBAAgB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC;gBACrD,OAAO,EAAE,aAAa;aACzB,CAAC,CAAC;QACP,CAAC;QAED,qBAAqB,CAAC,gBAAgB,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC;QAE7D,OAAO,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,EAAE,gBAAgB,EAAE,CAAC,CAAC;IACnE,CAAC;IAEO,qCAAqC,CAAC,qBAA4C;QACtF,MAAM,uBAAuB,GAAG,qBAAqB,CAAC,uBAAuB,CAAC;QAC9E,MAAM,sBAAsB,GAAG,uBAAuB,CAAC,sBAAsB,CAAC;QAE9E,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,sBAAsB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACrD,MAAM,aAAa,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC;YAEhD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC5C,MAAM,KAAK,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAE3C,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;oBAChB,MAAM,IAAI,GAAG,uBAAuB,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC;oBACrF,MAAM,WAAW,GAAG,uBAAuB,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;oBACpE,MAAM,WAAW,GAAG,WAAW,CAAC,eAAe,CAAC,CAAC,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,IAAI,GAAG,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;oBAEvI,IAAI,UAAU,GAAG,WAAW,CAAC,UAAU,CAAC;oBACxC,IAAI,WAAW,GAAG,WAAW,EAAE,IAAI,kEAAgD,CAAC;oBAEpF,IAAI,IAAI,CAAC,aAAa,GAAG,MAAM,IAAI,UAAU,0DAA4C,EAAE,CAAC;wBACxF,6JAA6J;wBAC7J,4FAA4F;wBAC5F,IAAI,WAAW,CAAC,eAAe,EAAE,CAAC;4BAC9B,WAAW,wEAAkD,CAAC;wBAClE,CAAC;wBACD,UAAU,iFAAsD,CAAC;oBACrE,CAAC;oBAED,KAAK,CAAC,OAAO,CAAC,UAAU,GAAG,UAAU,CAAC;oBAEtC,IAAI,WAAW,EAAE,CAAC;wBACd,MAAM,OAAO,GAAG,uBAAuB,CAAC,wBAAwB,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,WAAW,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC;wBACzI,sBAAsB,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,OAAQ,CAAC,IAAI,GAAG,WAAW,CAAC;oBAChG,CAAC;oBAED,MAAM,GAAG,MAAM,IAAI,CAAC,CAAC;gBACzB,CAAC;YACL,CAAC;QACL,CAAC;QAED,MAAM,gBAAgB,GAAyB,EAAE,CAAC;QAElD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,sBAAsB,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;YACrD,gBAAgB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC;gBACrD,OAAO,EAAE,sBAAsB,CAAC,CAAC,CAAC;aACrC,CAAC,CAAC;QACP,CAAC;QAED,qBAAqB,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,gBAAgB,CAAC;QAE9E,OAAO,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,EAAE,gBAAgB,EAAE,CAAC,CAAC;IACnE,CAAC;IAEO,yBAAyB,CAAC,MAAc;QAC5C,MAAM,WAAW,GAA4B,EAAE,CAAC;QAChD,MAAM,qBAAqB,GAAG,MAAM,CAAC,gBAAyC,CAAC;QAC/E,MAAM,UAAU,GAAG,qBAAqB,CAAC,uBAAuB,CAAC,wBAAwB,CAAC;QAC1F,MAAM,SAAS,GAAG,qBAAqB,CAAC,uBAAuB,CAAC,4BAA4B,CAAC;QAE7F,IAAI,gBAAgB,CAAC;QACrB,IAAI,oBAAsD,CAAC;QAC3D,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;YACrD,MAAM,QAAQ,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;YAClC,IAAI,YAAY,GAAG,CAAC,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,cAAe,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;YAC9I,IAAI,CAAC,YAAY,EAAE,CAAC;gBAChB,gGAAgG;gBAChG,oFAAoF;gBACpF,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC;YAC3C,CAAC;YAED,IAAI,MAAM,GAAG,YAAY,CAAC,eAAe,EAAE,kBAAkB,CAAC;YAE9D,yKAAyK;YACzK,IAAI,MAAM,GAAG,YAAY,CAAC,mBAAmB,CAAC;YAC9C,MAAM,kBAAkB,GAAG,CAAC,YAAY,CAAC,iBAAiB,CAAC;YAC3D,IAAI,CAAC,CAAC,gBAAgB,IAAI,oBAAoB,IAAI,gBAAgB,KAAK,MAAM,CAAC,IAAI,kBAAkB,EAAE,CAAC;gBACnG,MAAM,sBAAsB,GAA0B;oBAClD,WAAW,EAAE,YAAY,CAAC,mBAAmB;oBAC7C,QAAQ,EAAE,YAAY,CAAC,cAAc,EAAE,CAAC,CAAC,0DAAyC,CAAC,qDAAsC;oBACzH,UAAU,EAAE,EAAE;iBACjB,CAAC;gBAEF,WAAW,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBACzC,oBAAoB,GAAG,sBAAsB,CAAC,UAAU,CAAC;gBACzD,IAAI,kBAAkB,EAAE,CAAC;oBACrB,MAAM,GAAG,CAAC,CAAC,CAAC,8DAA8D;oBAC1E,MAAM,GAAG,IAAI,CAAC,CAAC,yBAAyB;gBAC5C,CAAC;YACL,CAAC;YAED,oBAAoB,CAAC,IAAI,CAAC;gBACtB,cAAc,EAAE,QAAQ;gBACxB,MAAM;gBACN,MAAM,EAAE,yBAAyB,CAAC,+BAA+B,CAAC,YAAY,CAAC;aAClF,CAAC,CAAC;YAEH,gBAAgB,GAAG,MAAM,CAAC;QAC9B,CAAC;QAED,OAAO,WAAW,CAAC;IACvB,CAAC;IAEO,qBAAqB,CAAC,MAAc,EAAE,QAA8B,EAAE,WAAmB;QAC7F,MAAM,qBAAqB,GAAG,MAAM,CAAC,gBAAyC,CAAC;QAC/E,MAAM,oBAAoB,GAAG,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC;QACpE,MAAM,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,CAAC,CAAC;QAEzE,MAAM,WAAW,GAAsC,EAAE,CAAC;QAE1D,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,0BAA0B,CAAC,IAAI,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,GAAG,CAAC,EAAE,CAAC;YAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;gBAC/C,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;gBACnC,IAAI,MAAM,EAAE,CAAC;oBACT,MAAM,KAAK,GAAwB;wBAC/B,MAAM;wBACN,SAAS,EAAE,CAAC,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;qBAC3E,CAAC;oBACF,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,GAAG,IAAI,CAAC,4BAA4B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC1F,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,GAAG,IAAI,CAAC,4BAA4B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAE3F,IAAI,UAAU,IAAI,UAAU,EAAE,CAAC;wBAC3B,KAAK,CAAC,KAAK,GAAG;4BACV,KAAK,EAAE,UAAU;4BACjB,KAAK,EAAE,UAAU;yBACpB,CAAC;oBACN,CAAC;oBACD,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC5B,CAAC;qBAAM,CAAC;oBACJ,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC3B,CAAC;YACL,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,IAAI,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC7B,MAAM,KAAK,GAAwB;oBAC/B,MAAM,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC;oBAClC,SAAS,EAAE,IAAI,CAAC,UAAU;iBAC7B,CAAC;gBACF,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;gBAC9C,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;gBAE/C,IAAI,UAAU,IAAI,UAAU,EAAE,CAAC;oBAC3B,KAAK,CAAC,KAAK,GAAG;wBACV,KAAK,EAAE,UAAU;wBACjB,KAAK,EAAE,UAAU;qBACpB,CAAC;gBACN,CAAC;gBACD,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5B,CAAC;iBAAM,CAAC;gBACJ,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3B,CAAC;QACL,CAAC;QAED,MAAM,YAAY,GAAwB;YACtC,OAAO,EAAE,yBAAyB,CAAC,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;YACzH,WAAW,EAAE,yBAAyB,CAAC,qBAAqB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;YACjI,MAAM,EAAE,yBAAyB,CAAC,qBAAqB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;YACvH,MAAM,EAAE,yBAAyB,CAAC,qBAAqB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;SAC1H,CAAC;QAEF,MAAM,WAAW,GAAwB;YACrC,OAAO,EAAE,yBAAyB,CAAC,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;YACxH,WAAW,EAAE,yBAAyB,CAAC,qBAAqB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;YAChI,MAAM,EAAE,yBAAyB,CAAC,qBAAqB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;YACtH,MAAM,EAAE,yBAAyB,CAAC,qBAAqB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;SACzH,CAAC;QAEF,MAAM,kBAAkB,GAAG,QAAQ,yEAAmD,IAAI,QAAQ,2EAAoD,CAAC;QAEvJ,IAAI,gBAAgB,GAA+B,SAAS,CAAC;QAC7D,IAAI,QAAQ,mEAAgD,IAAI,QAAQ,2EAAoD,EAAE,CAAC;YAC3H,gBAAgB,GAAG,CAAC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,mDAAoC,CAAC,kDAAmC,CAAC;QAClJ,CAAC;QAED,MAAM,4BAA4B,GAAG,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAEnJ,OAAO,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC;YACrC,KAAK,EAAE,kBAAkB,WAAW,CAAC,CAAC,CAAC,EAAE,MAAM,IAAI,UAAU,IAAI,IAAI,CAAC,yBAAyB,IAAI,SAAS,WAAW,WAAW,gBAAgB,IAAI,CAAC,aAAa,EAAE;YACtK,MAAM,EAAE,cAAc;YACtB,MAAM,EAAE;gBACJ,MAAM,EAAE,qBAAqB,CAAC,MAAO,CAAC,WAAW,CAAC,MAAM;gBACxD,UAAU,EAAE,qBAAqB,CAAC,MAAO,CAAC,WAAW,CAAC,UAAU;gBAChE,OAAO,EAAE,oBAAoB;aAChC;YACD,SAAS,EAAE;gBACP,QAAQ;gBACR,gBAAgB;gBAChB,SAAS,EAAE,IAAI,CAAC,UAAU,KAAK,CAAC,CAAC,CAAC,2CAA+B,CAAC,wCAA6B;gBAC/F,QAAQ,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,4CAA+B,CAAC,CAAC,IAAI,CAAC,SAAS,KAAK,CAAC,CAAC,CAAC,8CAAgC,CAAC,2CAA8B;aACvJ;YACD,QAAQ,EAAE,CAAC,qBAAqB,CAAC,MAAO,CAAC,aAAa;gBAClD,CAAC,CAAC,SAAS;gBACX,CAAC,CAAC;oBACI,MAAM,EAAE,qBAAqB,CAAC,MAAO,CAAC,aAAa,CAAC,MAAM;oBAC1D,UAAU,EAAE,qBAAqB,CAAC,MAAO,CAAC,aAAa,CAAC,UAAU;oBAClE,OAAO,EAAE,WAAW;iBACvB;YAEP,WAAW,EAAE;gBACT,KAAK,EAAE,WAAW;gBAClB;yCACyB;aAC5B;YACD,YAAY,EACR,IAAI,CAAC,yBAAyB,KAAK,SAAS;gBACxC,CAAC,CAAC,SAAS;gBACX,CAAC,CAAC;oBACI,iBAAiB,EAAE,IAAI,CAAC,kBAAkB;oBAC1C,YAAY,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,yBAAyB,CAAC,mBAAmB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,sDAAuC;oBACjJ,MAAM,EAAE,IAAI,CAAC,yBAAyB;oBACtC,YAAY,EAAE,IAAI,CAAC,eAAe,IAAI,4BAA4B,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS;oBAC7F,WAAW,EAAE,IAAI,CAAC,eAAe,IAAI,4BAA4B,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS;oBAC3F,eAAe,EAAE,IAAI,CAAC,eAAe,IAAI,4BAA4B,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,SAAS;oBACzG,gBAAgB,EAAE,IAAI,CAAC,eAAe,IAAI,4BAA4B,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,SAAS;oBAC3G,SAAS,EAAE,IAAI,CAAC,UAAU;oBAC1B,cAAc,EAAE,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;oBAC7D,mBAAmB,EAAE,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;iBAC1E;SACd,CAAC,CAAC;IACP,CAAC;;AA5jCa,kDAAwB,GAAG,KAAK,AAAR,CAAS;AAEjC,gDAAsB,GAAG,CAAC,AAAJ,CAAK;AAC3B,6CAAmB,GAAG,CAAC,AAAJ,CAAK;AACxB,sCAAY,GAAG,CAAC,AAAJ,CAAK;AACjB,sDAA4B,GAAG,CAAC,AAAJ,CAAK;AAIhC,0DAAgC,GAAG,CAAC,AAAJ,CAAK", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\n/* eslint-disable babylonjs/available */\r\n/* eslint-disable jsdoc/require-jsdoc */\r\nimport { Constants } from \"../constants\";\r\nimport * as WebGPUConstants from \"./webgpuConstants\";\r\nimport type { Effect } from \"../../Materials/effect\";\r\nimport type { InternalTexture } from \"../../Materials/Textures/internalTexture\";\r\nimport { VertexBuffer } from \"../../Buffers/buffer\";\r\nimport type { DataBuffer } from \"../../Buffers/dataBuffer\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { WebGPUHardwareTexture } from \"./webgpuHardwareTexture\";\r\nimport type { WebGPUPipelineContext } from \"./webgpuPipelineContext\";\r\nimport { WebGPUTextureHelper } from \"./webgpuTextureHelper\";\r\nimport { renderableTextureFormatToIndex } from \"./webgpuTextureManager\";\r\nimport { checkNonFloatVertexBuffers } from \"core/Buffers/buffer.nonFloatVertexBuffers\";\r\nimport { Logger } from \"core/Misc/logger\";\r\n\r\nenum StatePosition {\r\n    StencilReadMask = 0,\r\n    StencilWriteMask = 1,\r\n    //DepthBiasClamp = 1, // not used, so remove it to improve perf\r\n    DepthBias = 2,\r\n    DepthBiasSlopeScale = 3,\r\n    DepthStencilState = 4,\r\n    MRTAttachments = 5,\r\n    RasterizationState = 6,\r\n    ColorStates1 = 7,\r\n    ColorStates2 = 8,\r\n    ColorStates3 = 9,\r\n    ColorStates4 = 10,\r\n    ShaderStage = 11,\r\n    TextureStage = 12,\r\n    VertexState = 13, // vertex state will consume positions 13, 14, ... depending on the number of vertex inputs\r\n\r\n    NumStates = 14,\r\n}\r\n\r\nconst alphaBlendFactorToIndex: { [name: number]: number } = {\r\n    0: 1, // Zero\r\n    1: 2, // One\r\n    0x0300: 3, // SrcColor\r\n    0x0301: 4, // OneMinusSrcColor\r\n    0x0302: 5, // SrcAlpha\r\n    0x0303: 6, // OneMinusSrcAlpha\r\n    0x0304: 7, // DstAlpha\r\n    0x0305: 8, // OneMinusDstAlpha\r\n    0x0306: 9, // DstColor\r\n    0x0307: 10, // OneMinusDstColor\r\n    0x0308: 11, // SrcAlphaSaturated\r\n    0x8001: 12, // BlendColor\r\n    0x8002: 13, // OneMinusBlendColor\r\n    0x8003: 14, // BlendColor (alpha)\r\n    0x8004: 15, // OneMinusBlendColor (alpha)\r\n    0x88f9: 16, // Src1Color\r\n    0x88fa: 17, // OneMinusSrc1Color\r\n    0x8589: 18, // Src1Alpha\r\n    0x88fb: 19, // OneMinusSrc1Alpha\r\n};\r\n\r\nconst alphaBlendEquationToIndex: { [name: number]: number } = {\r\n    0x8006: 0, // Add\r\n    0x8007: 1, // Min\r\n    0x8008: 2, // Max\r\n    0x800a: 3, // Subtract\r\n    0x800b: 4, // ReverseSubtract\r\n};\r\n\r\nconst stencilOpToIndex: { [name: number]: number } = {\r\n    0x0000: 0, // ZERO\r\n    0x1e00: 1, // KEEP\r\n    0x1e01: 2, // REPLACE\r\n    0x1e02: 3, // INCR\r\n    0x1e03: 4, // DECR\r\n    0x150a: 5, // INVERT\r\n    0x8507: 6, // INCR_WRAP\r\n    0x8508: 7, // DECR_WRAP\r\n};\r\n\r\nconst colorStates = [0, 0, 0, 0];\r\n\r\n/** @internal */\r\nexport abstract class WebGPUCacheRenderPipeline {\r\n    public static LogErrorIfNoVertexBuffer = false;\r\n\r\n    public static NumCacheHitWithoutHash = 0;\r\n    public static NumCacheHitWithHash = 0;\r\n    public static NumCacheMiss = 0;\r\n    public static NumPipelineCreationLastFrame = 0;\r\n\r\n    public disabled: boolean;\r\n\r\n    private static _NumPipelineCreationCurrentFrame = 0;\r\n\r\n    protected _states: number[];\r\n    protected _statesLength: number;\r\n    protected _stateDirtyLowestIndex: number;\r\n    public lastStateDirtyLowestIndex: number; // for stats only\r\n\r\n    private _device: GPUDevice;\r\n    private _isDirty: boolean;\r\n    private _emptyVertexBuffer: VertexBuffer;\r\n    private _parameter: { token: any; pipeline: Nullable<GPURenderPipeline> };\r\n    private _kMaxVertexBufferStride;\r\n\r\n    private _shaderId: number;\r\n    private _alphaToCoverageEnabled: boolean;\r\n    private _frontFace: number;\r\n    private _cullEnabled: boolean;\r\n    private _cullFace: number;\r\n    private _clampDepth: boolean;\r\n    private _rasterizationState: number;\r\n    private _depthBias: number;\r\n    private _depthBiasClamp: number;\r\n    private _depthBiasSlopeScale: number;\r\n    private _colorFormat: number;\r\n    private _webgpuColorFormat: (GPUTextureFormat | null)[];\r\n    private _mrtAttachments: number;\r\n    private _mrtFormats: (GPUTextureFormat | null)[];\r\n    private _mrtEnabledMask: number;\r\n    private _alphaBlendEnabled: boolean[];\r\n    private _numAlphaBlendTargetsEnabled: number;\r\n    private _alphaBlendFuncParams: Array<Nullable<number>>;\r\n    private _alphaBlendEqParams: Array<Nullable<number>>;\r\n    private _writeMask: number;\r\n    private _depthStencilFormat: number;\r\n    private _webgpuDepthStencilFormat: GPUTextureFormat | undefined;\r\n    private _depthTestEnabled: boolean;\r\n    private _depthWriteEnabled: boolean;\r\n    private _depthCompare: number;\r\n    private _stencilEnabled: boolean;\r\n    private _stencilFrontCompare: number;\r\n    private _stencilFrontDepthFailOp: number;\r\n    private _stencilFrontPassOp: number;\r\n    private _stencilFrontFailOp: number;\r\n    private _stencilBackCompare: number;\r\n    private _stencilBackDepthFailOp: number;\r\n    private _stencilBackPassOp: number;\r\n    private _stencilBackFailOp: number;\r\n    private _stencilReadMask: number;\r\n    private _stencilWriteMask: number;\r\n    private _depthStencilState: number;\r\n    private _vertexBuffers: Nullable<{ [key: string]: Nullable<VertexBuffer> }>;\r\n    private _overrideVertexBuffers: Nullable<{ [key: string]: Nullable<VertexBuffer> }>;\r\n    private _indexBuffer: Nullable<DataBuffer>;\r\n    private _textureState: number;\r\n    private _useTextureStage: boolean;\r\n\r\n    constructor(device: GPUDevice, emptyVertexBuffer: VertexBuffer) {\r\n        this._device = device;\r\n        this._useTextureStage = true; // we force usage because we must handle depth textures with \"float\" filtering, which can't be fixed by a caps (like \"textureFloatLinearFiltering\" can for float textures)\r\n        this._states = new Array(30); // pre-allocate enough room so that no new allocation will take place afterwards\r\n        this._statesLength = 0;\r\n        this._stateDirtyLowestIndex = 0;\r\n        this._emptyVertexBuffer = emptyVertexBuffer;\r\n        this._mrtFormats = [];\r\n        this._parameter = { token: undefined, pipeline: null };\r\n        this.disabled = false;\r\n        this.vertexBuffers = [];\r\n        this._kMaxVertexBufferStride = device.limits.maxVertexBufferArrayStride || 2048;\r\n        this.reset();\r\n    }\r\n\r\n    public reset(): void {\r\n        this._isDirty = true;\r\n        this.vertexBuffers.length = 0;\r\n        this.setAlphaToCoverage(false);\r\n        this.resetDepthCullingState();\r\n        this.setClampDepth(false);\r\n        this.setDepthBias(0);\r\n        //this.setDepthBiasClamp(0);\r\n        this._webgpuColorFormat = [WebGPUConstants.TextureFormat.BGRA8Unorm];\r\n        this.setColorFormat(WebGPUConstants.TextureFormat.BGRA8Unorm);\r\n        this.setMRT([]);\r\n        this.setAlphaBlendEnabled([false], 1);\r\n        this.setAlphaBlendFactors([null, null, null, null], [null, null]);\r\n        this.setWriteMask(0xf);\r\n        this.setDepthStencilFormat(WebGPUConstants.TextureFormat.Depth24PlusStencil8);\r\n        this.setStencilEnabled(false);\r\n        this.resetStencilState();\r\n        this.setBuffers(null, null, null);\r\n        this._setTextureState(0);\r\n    }\r\n\r\n    protected abstract _getRenderPipeline(param: { token: any; pipeline: Nullable<GPURenderPipeline> }): void;\r\n    protected abstract _setRenderPipeline(param: { token: any; pipeline: Nullable<GPURenderPipeline> }): void;\r\n\r\n    public readonly vertexBuffers: VertexBuffer[];\r\n\r\n    public get colorFormats(): (GPUTextureFormat | null)[] {\r\n        return this._mrtAttachments > 0 ? this._mrtFormats : this._webgpuColorFormat;\r\n    }\r\n\r\n    public readonly mrtAttachments: number[];\r\n    public readonly mrtTextureArray: InternalTexture[];\r\n    public readonly mrtTextureCount: number = 0;\r\n\r\n    public getRenderPipeline(fillMode: number, effect: Effect, sampleCount: number, textureState = 0): GPURenderPipeline {\r\n        sampleCount = WebGPUTextureHelper.GetSample(sampleCount);\r\n\r\n        if (this.disabled) {\r\n            const topology = WebGPUCacheRenderPipeline._GetTopology(fillMode);\r\n\r\n            this._setVertexState(effect); // to fill this.vertexBuffers with correct data\r\n            this._setTextureState(textureState);\r\n\r\n            this._parameter.pipeline = this._createRenderPipeline(effect, topology, sampleCount);\r\n\r\n            WebGPUCacheRenderPipeline.NumCacheMiss++;\r\n            WebGPUCacheRenderPipeline._NumPipelineCreationCurrentFrame++;\r\n\r\n            return this._parameter.pipeline;\r\n        }\r\n\r\n        this._setShaderStage(effect.uniqueId);\r\n        this._setRasterizationState(fillMode, sampleCount);\r\n        this._setColorStates();\r\n        this._setDepthStencilState();\r\n        this._setVertexState(effect);\r\n        this._setTextureState(textureState);\r\n\r\n        this.lastStateDirtyLowestIndex = this._stateDirtyLowestIndex;\r\n\r\n        if (!this._isDirty && this._parameter.pipeline) {\r\n            this._stateDirtyLowestIndex = this._statesLength;\r\n            WebGPUCacheRenderPipeline.NumCacheHitWithoutHash++;\r\n            return this._parameter.pipeline;\r\n        }\r\n\r\n        this._getRenderPipeline(this._parameter);\r\n\r\n        this._isDirty = false;\r\n        this._stateDirtyLowestIndex = this._statesLength;\r\n\r\n        if (this._parameter.pipeline) {\r\n            WebGPUCacheRenderPipeline.NumCacheHitWithHash++;\r\n            return this._parameter.pipeline;\r\n        }\r\n\r\n        const topology = WebGPUCacheRenderPipeline._GetTopology(fillMode);\r\n\r\n        this._parameter.pipeline = this._createRenderPipeline(effect, topology, sampleCount);\r\n        this._setRenderPipeline(this._parameter);\r\n\r\n        WebGPUCacheRenderPipeline.NumCacheMiss++;\r\n        WebGPUCacheRenderPipeline._NumPipelineCreationCurrentFrame++;\r\n\r\n        return this._parameter.pipeline;\r\n    }\r\n\r\n    public endFrame(): void {\r\n        WebGPUCacheRenderPipeline.NumPipelineCreationLastFrame = WebGPUCacheRenderPipeline._NumPipelineCreationCurrentFrame;\r\n        WebGPUCacheRenderPipeline._NumPipelineCreationCurrentFrame = 0;\r\n    }\r\n\r\n    public setAlphaToCoverage(enabled: boolean): void {\r\n        this._alphaToCoverageEnabled = enabled;\r\n    }\r\n\r\n    public setFrontFace(frontFace: number): void {\r\n        this._frontFace = frontFace;\r\n    }\r\n\r\n    public setCullEnabled(enabled: boolean): void {\r\n        this._cullEnabled = enabled;\r\n    }\r\n\r\n    public setCullFace(cullFace: number): void {\r\n        this._cullFace = cullFace;\r\n    }\r\n\r\n    public setClampDepth(clampDepth: boolean): void {\r\n        this._clampDepth = clampDepth;\r\n    }\r\n\r\n    public resetDepthCullingState(): void {\r\n        this.setDepthCullingState(false, 2, 1, 0, 0, true, true, Constants.ALWAYS);\r\n    }\r\n\r\n    public setDepthCullingState(\r\n        cullEnabled: boolean,\r\n        frontFace: number,\r\n        cullFace: number,\r\n        zOffset: number,\r\n        zOffsetUnits: number,\r\n        depthTestEnabled: boolean,\r\n        depthWriteEnabled: boolean,\r\n        depthCompare: Nullable<number>\r\n    ): void {\r\n        this._depthWriteEnabled = depthWriteEnabled;\r\n        this._depthTestEnabled = depthTestEnabled;\r\n        this._depthCompare = (depthCompare ?? Constants.ALWAYS) - 0x0200;\r\n        this._cullFace = cullFace;\r\n        this._cullEnabled = cullEnabled;\r\n        this._frontFace = frontFace;\r\n        this.setDepthBiasSlopeScale(zOffset);\r\n        this.setDepthBias(zOffsetUnits);\r\n    }\r\n\r\n    public setDepthBias(depthBias: number): void {\r\n        if (this._depthBias !== depthBias) {\r\n            this._depthBias = depthBias;\r\n            this._states[StatePosition.DepthBias] = depthBias;\r\n            this._isDirty = true;\r\n            this._stateDirtyLowestIndex = Math.min(this._stateDirtyLowestIndex, StatePosition.DepthBias);\r\n        }\r\n    }\r\n\r\n    /*public setDepthBiasClamp(depthBiasClamp: number): void {\r\n        if (this._depthBiasClamp !== depthBiasClamp) {\r\n            this._depthBiasClamp = depthBiasClamp;\r\n            this._states[StatePosition.DepthBiasClamp] = depthBiasClamp.toString();\r\n            this._isDirty = true;\r\n        }\r\n    }*/\r\n\r\n    public setDepthBiasSlopeScale(depthBiasSlopeScale: number): void {\r\n        if (this._depthBiasSlopeScale !== depthBiasSlopeScale) {\r\n            this._depthBiasSlopeScale = depthBiasSlopeScale;\r\n            this._states[StatePosition.DepthBiasSlopeScale] = depthBiasSlopeScale;\r\n            this._isDirty = true;\r\n            this._stateDirtyLowestIndex = Math.min(this._stateDirtyLowestIndex, StatePosition.DepthBiasSlopeScale);\r\n        }\r\n    }\r\n\r\n    public setColorFormat(format: GPUTextureFormat | null): void {\r\n        this._webgpuColorFormat[0] = format;\r\n        this._colorFormat = renderableTextureFormatToIndex[format ?? \"\"];\r\n    }\r\n\r\n    // Must be called after setMRT!\r\n    public setMRTAttachments(attachments: number[]): void {\r\n        (this.mrtAttachments as any) = attachments;\r\n        let mask = 0;\r\n        for (let i = 0; i < attachments.length; ++i) {\r\n            if (attachments[i] !== 0) {\r\n                mask += 1 << i;\r\n            }\r\n        }\r\n        if (this._mrtEnabledMask !== mask) {\r\n            this._mrtEnabledMask = mask;\r\n            this._isDirty = true;\r\n            this._stateDirtyLowestIndex = Math.min(this._stateDirtyLowestIndex, StatePosition.MRTAttachments);\r\n        }\r\n    }\r\n\r\n    public setMRT(textureArray: InternalTexture[], textureCount?: number): void {\r\n        textureCount = textureCount ?? textureArray.length;\r\n        if (textureCount > 8) {\r\n            // We only support 8 MRTs in WebGPU, so we throw an error if we try to set more than that.\r\n            throw new Error(\"Can't handle more than 8 attachments for a MRT in cache render pipeline!\");\r\n        }\r\n        (this.mrtTextureArray as any) = textureArray;\r\n        (this.mrtTextureCount as any) = textureCount;\r\n\r\n        // Since we need approximately 45 different values per texture format (see WebGPUTextureManager.renderableTextureFormatToIndex), we use 6 bits to encode a texture format,\r\n        // which means we can encode 8 texture formats in 48 bits (a double can represent integers exactly up until 2^53, so 48 bits is ok).\r\n\r\n        this._mrtEnabledMask = 0xffff; // all textures are enabled at start (meaning we can write to them). Calls to setMRTAttachments may disable some\r\n\r\n        let mrtAttachments = 0;\r\n        let mask = 0;\r\n\r\n        for (let i = 0; i < textureCount; ++i) {\r\n            const texture = textureArray[i];\r\n            const gpuWrapper = texture?._hardwareTexture as Nullable<WebGPUHardwareTexture>;\r\n\r\n            this._mrtFormats[i] = gpuWrapper?.format ?? this._webgpuColorFormat[0];\r\n\r\n            mrtAttachments += renderableTextureFormatToIndex[this._mrtFormats[i] ?? \"\"] * 2 ** mask;\r\n            mask += 6;\r\n        }\r\n        this._mrtFormats.length = textureCount;\r\n        if (this._mrtAttachments !== mrtAttachments) {\r\n            this._mrtAttachments = mrtAttachments;\r\n            this._states[StatePosition.MRTAttachments] = mrtAttachments;\r\n            this._isDirty = true;\r\n            this._stateDirtyLowestIndex = Math.min(this._stateDirtyLowestIndex, StatePosition.MRTAttachments);\r\n        }\r\n    }\r\n\r\n    public setAlphaBlendEnabled(enabled: boolean[], numAlphaBlendTargetsEnabled: number): void {\r\n        this._alphaBlendEnabled = enabled;\r\n        this._numAlphaBlendTargetsEnabled = numAlphaBlendTargetsEnabled;\r\n    }\r\n\r\n    public setAlphaBlendFactors(factors: Array<Nullable<number>>, operations: Array<Nullable<number>>): void {\r\n        this._alphaBlendFuncParams = factors;\r\n        this._alphaBlendEqParams = operations;\r\n    }\r\n\r\n    public setWriteMask(mask: number): void {\r\n        this._writeMask = mask;\r\n    }\r\n\r\n    public setDepthStencilFormat(format: GPUTextureFormat | undefined): void {\r\n        this._webgpuDepthStencilFormat = format;\r\n        this._depthStencilFormat = format === undefined ? 0 : renderableTextureFormatToIndex[format];\r\n    }\r\n\r\n    public setDepthTestEnabled(enabled: boolean): void {\r\n        this._depthTestEnabled = enabled;\r\n    }\r\n\r\n    public setDepthWriteEnabled(enabled: boolean): void {\r\n        this._depthWriteEnabled = enabled;\r\n    }\r\n\r\n    public setDepthCompare(func: Nullable<number>): void {\r\n        this._depthCompare = (func ?? Constants.ALWAYS) - 0x0200;\r\n    }\r\n\r\n    public setStencilEnabled(enabled: boolean): void {\r\n        this._stencilEnabled = enabled;\r\n    }\r\n\r\n    public setStencilCompare(func: Nullable<number>): void {\r\n        this._stencilFrontCompare = (func ?? Constants.ALWAYS) - 0x0200;\r\n    }\r\n\r\n    public setStencilDepthFailOp(op: Nullable<number>): void {\r\n        this._stencilFrontDepthFailOp = op === null ? 1 /* KEEP */ : stencilOpToIndex[op];\r\n    }\r\n\r\n    public setStencilPassOp(op: Nullable<number>): void {\r\n        this._stencilFrontPassOp = op === null ? 2 /* REPLACE */ : stencilOpToIndex[op];\r\n    }\r\n\r\n    public setStencilFailOp(op: Nullable<number>): void {\r\n        this._stencilFrontFailOp = op === null ? 1 /* KEEP */ : stencilOpToIndex[op];\r\n    }\r\n\r\n    public setStencilBackCompare(func: Nullable<number>): void {\r\n        this._stencilBackCompare = (func ?? Constants.ALWAYS) - 0x0200;\r\n    }\r\n\r\n    public setStencilBackDepthFailOp(op: Nullable<number>): void {\r\n        this._stencilBackDepthFailOp = op === null ? 1 /* KEEP */ : stencilOpToIndex[op];\r\n    }\r\n\r\n    public setStencilBackPassOp(op: Nullable<number>): void {\r\n        this._stencilBackPassOp = op === null ? 2 /* REPLACE */ : stencilOpToIndex[op];\r\n    }\r\n\r\n    public setStencilBackFailOp(op: Nullable<number>): void {\r\n        this._stencilBackFailOp = op === null ? 1 /* KEEP */ : stencilOpToIndex[op];\r\n    }\r\n\r\n    public setStencilReadMask(mask: number): void {\r\n        if (this._stencilReadMask !== mask) {\r\n            this._stencilReadMask = mask;\r\n            this._states[StatePosition.StencilReadMask] = mask;\r\n            this._isDirty = true;\r\n            this._stateDirtyLowestIndex = Math.min(this._stateDirtyLowestIndex, StatePosition.StencilReadMask);\r\n        }\r\n    }\r\n\r\n    public setStencilWriteMask(mask: number): void {\r\n        if (this._stencilWriteMask !== mask) {\r\n            this._stencilWriteMask = mask;\r\n            this._states[StatePosition.StencilWriteMask] = mask;\r\n            this._isDirty = true;\r\n            this._stateDirtyLowestIndex = Math.min(this._stateDirtyLowestIndex, StatePosition.StencilWriteMask);\r\n        }\r\n    }\r\n\r\n    public resetStencilState(): void {\r\n        this.setStencilState(false, Constants.ALWAYS, Constants.KEEP, Constants.REPLACE, Constants.KEEP, 0xff, 0xff);\r\n    }\r\n\r\n    public setStencilState(\r\n        stencilEnabled: boolean,\r\n        compare: Nullable<number>,\r\n        depthFailOp: Nullable<number>,\r\n        passOp: Nullable<number>,\r\n        failOp: Nullable<number>,\r\n        readMask: number,\r\n        writeMask: number,\r\n        backCompare: Nullable<number> = null,\r\n        backDepthFailOp: Nullable<number> = null,\r\n        backPassOp: Nullable<number> = null,\r\n        backFailOp: Nullable<number> = null\r\n    ): void {\r\n        this._stencilEnabled = stencilEnabled;\r\n        this._stencilFrontCompare = (compare ?? Constants.ALWAYS) - 0x0200;\r\n        this._stencilFrontDepthFailOp = depthFailOp === null ? 1 /* KEEP */ : stencilOpToIndex[depthFailOp];\r\n        this._stencilFrontPassOp = passOp === null ? 2 /* REPLACE */ : stencilOpToIndex[passOp];\r\n        this._stencilFrontFailOp = failOp === null ? 1 /* KEEP */ : stencilOpToIndex[failOp];\r\n        this._stencilBackCompare = (backCompare ?? Constants.ALWAYS) - 0x0200;\r\n        this._stencilBackDepthFailOp = backDepthFailOp === null ? 1 /* KEEP */ : stencilOpToIndex[backDepthFailOp];\r\n        this._stencilBackPassOp = backPassOp === null ? 2 /* REPLACE */ : stencilOpToIndex[backPassOp];\r\n        this._stencilBackFailOp = backFailOp === null ? 1 /* KEEP */ : stencilOpToIndex[backFailOp];\r\n        this.setStencilReadMask(readMask);\r\n        this.setStencilWriteMask(writeMask);\r\n    }\r\n\r\n    public setBuffers(\r\n        vertexBuffers: Nullable<{ [key: string]: Nullable<VertexBuffer> }>,\r\n        indexBuffer: Nullable<DataBuffer>,\r\n        overrideVertexBuffers: Nullable<{ [key: string]: Nullable<VertexBuffer> }>\r\n    ): void {\r\n        this._vertexBuffers = vertexBuffers;\r\n        this._overrideVertexBuffers = overrideVertexBuffers;\r\n        this._indexBuffer = indexBuffer;\r\n    }\r\n\r\n    private static _GetTopology(fillMode: number): GPUPrimitiveTopology {\r\n        switch (fillMode) {\r\n            // Triangle views\r\n            case Constants.MATERIAL_TriangleFillMode:\r\n                return WebGPUConstants.PrimitiveTopology.TriangleList;\r\n            case Constants.MATERIAL_PointFillMode:\r\n                return WebGPUConstants.PrimitiveTopology.PointList;\r\n            case Constants.MATERIAL_WireFrameFillMode:\r\n                return WebGPUConstants.PrimitiveTopology.LineList;\r\n            // Draw modes\r\n            case Constants.MATERIAL_PointListDrawMode:\r\n                return WebGPUConstants.PrimitiveTopology.PointList;\r\n            case Constants.MATERIAL_LineListDrawMode:\r\n                return WebGPUConstants.PrimitiveTopology.LineList;\r\n            case Constants.MATERIAL_LineLoopDrawMode:\r\n                // return this._gl.LINE_LOOP;\r\n                // TODO WEBGPU. Line Loop Mode Fallback at buffer load time.\r\n                // eslint-disable-next-line no-throw-literal\r\n                throw \"LineLoop is an unsupported fillmode in WebGPU\";\r\n            case Constants.MATERIAL_LineStripDrawMode:\r\n                return WebGPUConstants.PrimitiveTopology.LineStrip;\r\n            case Constants.MATERIAL_TriangleStripDrawMode:\r\n                return WebGPUConstants.PrimitiveTopology.TriangleStrip;\r\n            case Constants.MATERIAL_TriangleFanDrawMode:\r\n                // return this._gl.TRIANGLE_FAN;\r\n                // TODO WEBGPU. Triangle Fan Mode Fallback at buffer load time.\r\n                // eslint-disable-next-line no-throw-literal\r\n                throw \"TriangleFan is an unsupported fillmode in WebGPU\";\r\n            default:\r\n                return WebGPUConstants.PrimitiveTopology.TriangleList;\r\n        }\r\n    }\r\n\r\n    private static _GetAphaBlendOperation(operation: Nullable<number>): GPUBlendOperation {\r\n        switch (operation) {\r\n            case Constants.GL_ALPHA_EQUATION_ADD:\r\n                return WebGPUConstants.BlendOperation.Add;\r\n            case Constants.GL_ALPHA_EQUATION_SUBTRACT:\r\n                return WebGPUConstants.BlendOperation.Subtract;\r\n            case Constants.GL_ALPHA_EQUATION_REVERSE_SUBTRACT:\r\n                return WebGPUConstants.BlendOperation.ReverseSubtract;\r\n            case Constants.GL_ALPHA_EQUATION_MIN:\r\n                return WebGPUConstants.BlendOperation.Min;\r\n            case Constants.GL_ALPHA_EQUATION_MAX:\r\n                return WebGPUConstants.BlendOperation.Max;\r\n            default:\r\n                return WebGPUConstants.BlendOperation.Add;\r\n        }\r\n    }\r\n\r\n    private static _GetAphaBlendFactor(factor: Nullable<number>): GPUBlendFactor {\r\n        switch (factor) {\r\n            case 0:\r\n                return WebGPUConstants.BlendFactor.Zero;\r\n            case 1:\r\n                return WebGPUConstants.BlendFactor.One;\r\n            case Constants.GL_ALPHA_FUNCTION_SRC:\r\n                return WebGPUConstants.BlendFactor.Src;\r\n            case Constants.GL_ALPHA_FUNCTION_ONE_MINUS_SRC_COLOR:\r\n                return WebGPUConstants.BlendFactor.OneMinusSrc;\r\n            case Constants.GL_ALPHA_FUNCTION_SRC_ALPHA:\r\n                return WebGPUConstants.BlendFactor.SrcAlpha;\r\n            case Constants.GL_ALPHA_FUNCTION_ONE_MINUS_SRC_ALPHA:\r\n                return WebGPUConstants.BlendFactor.OneMinusSrcAlpha;\r\n            case Constants.GL_ALPHA_FUNCTION_DST_ALPHA:\r\n                return WebGPUConstants.BlendFactor.DstAlpha;\r\n            case Constants.GL_ALPHA_FUNCTION_ONE_MINUS_DST_ALPHA:\r\n                return WebGPUConstants.BlendFactor.OneMinusDstAlpha;\r\n            case Constants.GL_ALPHA_FUNCTION_DST_COLOR:\r\n                return WebGPUConstants.BlendFactor.Dst;\r\n            case Constants.GL_ALPHA_FUNCTION_ONE_MINUS_DST_COLOR:\r\n                return WebGPUConstants.BlendFactor.OneMinusDst;\r\n            case Constants.GL_ALPHA_FUNCTION_SRC_ALPHA_SATURATED:\r\n                return WebGPUConstants.BlendFactor.SrcAlphaSaturated;\r\n            case Constants.GL_ALPHA_FUNCTION_CONSTANT_COLOR:\r\n            case Constants.GL_ALPHA_FUNCTION_CONSTANT_ALPHA:\r\n                return WebGPUConstants.BlendFactor.Constant;\r\n            case Constants.GL_ALPHA_FUNCTION_ONE_MINUS_CONSTANT_COLOR:\r\n            case Constants.GL_ALPHA_FUNCTION_ONE_MINUS_CONSTANT_ALPHA:\r\n                return WebGPUConstants.BlendFactor.OneMinusConstant;\r\n            case Constants.GL_ALPHA_FUNCTION_SRC1_COLOR:\r\n                return WebGPUConstants.BlendFactor.Src1;\r\n            case Constants.GL_ALPHA_FUNCTION_ONE_MINUS_SRC1_COLOR:\r\n                return WebGPUConstants.BlendFactor.OneMinusSrc1;\r\n            case Constants.GL_ALPHA_FUNCTION_SRC1_ALPHA:\r\n                return WebGPUConstants.BlendFactor.Src1Alpha;\r\n            case Constants.GL_ALPHA_FUNCTION_ONE_MINUS_SRC1_ALPHA:\r\n                return WebGPUConstants.BlendFactor.OneMinusSrc1Alpha;\r\n            default:\r\n                return WebGPUConstants.BlendFactor.One;\r\n        }\r\n    }\r\n\r\n    private static _GetCompareFunction(compareFunction: number): GPUCompareFunction {\r\n        switch (compareFunction) {\r\n            case 0: // NEVER\r\n                return WebGPUConstants.CompareFunction.Never;\r\n            case 1: // LESS\r\n                return WebGPUConstants.CompareFunction.Less;\r\n            case 2: // EQUAL\r\n                return WebGPUConstants.CompareFunction.Equal;\r\n            case 3: // LEQUAL\r\n                return WebGPUConstants.CompareFunction.LessEqual;\r\n            case 4: // GREATER\r\n                return WebGPUConstants.CompareFunction.Greater;\r\n            case 5: // NOTEQUAL\r\n                return WebGPUConstants.CompareFunction.NotEqual;\r\n            case 6: // GEQUAL\r\n                return WebGPUConstants.CompareFunction.GreaterEqual;\r\n            case 7: // ALWAYS\r\n                return WebGPUConstants.CompareFunction.Always;\r\n        }\r\n        return WebGPUConstants.CompareFunction.Never;\r\n    }\r\n\r\n    private static _GetStencilOpFunction(operation: number): GPUStencilOperation {\r\n        switch (operation) {\r\n            case 0:\r\n                return WebGPUConstants.StencilOperation.Zero;\r\n            case 1:\r\n                return WebGPUConstants.StencilOperation.Keep;\r\n            case 2:\r\n                return WebGPUConstants.StencilOperation.Replace;\r\n            case 3:\r\n                return WebGPUConstants.StencilOperation.IncrementClamp;\r\n            case 4:\r\n                return WebGPUConstants.StencilOperation.DecrementClamp;\r\n            case 5:\r\n                return WebGPUConstants.StencilOperation.Invert;\r\n            case 6:\r\n                return WebGPUConstants.StencilOperation.IncrementWrap;\r\n            case 7:\r\n                return WebGPUConstants.StencilOperation.DecrementWrap;\r\n        }\r\n        return WebGPUConstants.StencilOperation.Keep;\r\n    }\r\n\r\n    private static _GetVertexInputDescriptorFormat(vertexBuffer: VertexBuffer): GPUVertexFormat {\r\n        const type = vertexBuffer.type;\r\n        const normalized = vertexBuffer.normalized;\r\n        const size = vertexBuffer.getSize();\r\n\r\n        switch (type) {\r\n            case VertexBuffer.BYTE:\r\n                switch (size) {\r\n                    case 1:\r\n                    case 2:\r\n                        return normalized ? WebGPUConstants.VertexFormat.Snorm8x2 : WebGPUConstants.VertexFormat.Sint8x2;\r\n                    case 3:\r\n                    case 4:\r\n                        return normalized ? WebGPUConstants.VertexFormat.Snorm8x4 : WebGPUConstants.VertexFormat.Sint8x4;\r\n                }\r\n                break;\r\n            case VertexBuffer.UNSIGNED_BYTE:\r\n                switch (size) {\r\n                    case 1:\r\n                    case 2:\r\n                        return normalized ? WebGPUConstants.VertexFormat.Unorm8x2 : WebGPUConstants.VertexFormat.Uint8x2;\r\n                    case 3:\r\n                    case 4:\r\n                        return normalized ? WebGPUConstants.VertexFormat.Unorm8x4 : WebGPUConstants.VertexFormat.Uint8x4;\r\n                }\r\n                break;\r\n            case VertexBuffer.SHORT:\r\n                switch (size) {\r\n                    case 1:\r\n                    case 2:\r\n                        return normalized ? WebGPUConstants.VertexFormat.Snorm16x2 : WebGPUConstants.VertexFormat.Sint16x2;\r\n                    case 3:\r\n                    case 4:\r\n                        return normalized ? WebGPUConstants.VertexFormat.Snorm16x4 : WebGPUConstants.VertexFormat.Sint16x4;\r\n                }\r\n                break;\r\n            case VertexBuffer.UNSIGNED_SHORT:\r\n                switch (size) {\r\n                    case 1:\r\n                    case 2:\r\n                        return normalized ? WebGPUConstants.VertexFormat.Unorm16x2 : WebGPUConstants.VertexFormat.Uint16x2;\r\n                    case 3:\r\n                    case 4:\r\n                        return normalized ? WebGPUConstants.VertexFormat.Unorm16x4 : WebGPUConstants.VertexFormat.Uint16x4;\r\n                }\r\n                break;\r\n            case VertexBuffer.INT:\r\n                switch (size) {\r\n                    case 1:\r\n                        return WebGPUConstants.VertexFormat.Sint32;\r\n                    case 2:\r\n                        return WebGPUConstants.VertexFormat.Sint32x2;\r\n                    case 3:\r\n                        return WebGPUConstants.VertexFormat.Sint32x3;\r\n                    case 4:\r\n                        return WebGPUConstants.VertexFormat.Sint32x4;\r\n                }\r\n                break;\r\n            case VertexBuffer.UNSIGNED_INT:\r\n                switch (size) {\r\n                    case 1:\r\n                        return WebGPUConstants.VertexFormat.Uint32;\r\n                    case 2:\r\n                        return WebGPUConstants.VertexFormat.Uint32x2;\r\n                    case 3:\r\n                        return WebGPUConstants.VertexFormat.Uint32x3;\r\n                    case 4:\r\n                        return WebGPUConstants.VertexFormat.Uint32x4;\r\n                }\r\n                break;\r\n            case VertexBuffer.FLOAT:\r\n                switch (size) {\r\n                    case 1:\r\n                        return WebGPUConstants.VertexFormat.Float32;\r\n                    case 2:\r\n                        return WebGPUConstants.VertexFormat.Float32x2;\r\n                    case 3:\r\n                        return WebGPUConstants.VertexFormat.Float32x3;\r\n                    case 4:\r\n                        return WebGPUConstants.VertexFormat.Float32x4;\r\n                }\r\n                break;\r\n        }\r\n\r\n        throw new Error(`Invalid Format '${vertexBuffer.getKind()}' - type=${type}, normalized=${normalized}, size=${size}`);\r\n    }\r\n\r\n    private _getAphaBlendState(targetIndex: number): Nullable<GPUBlendComponent> {\r\n        if (!this._alphaBlendEnabled[targetIndex]) {\r\n            return null;\r\n        }\r\n\r\n        return {\r\n            srcFactor: WebGPUCacheRenderPipeline._GetAphaBlendFactor(this._alphaBlendFuncParams[targetIndex * 4 + 2]),\r\n            dstFactor: WebGPUCacheRenderPipeline._GetAphaBlendFactor(this._alphaBlendFuncParams[targetIndex * 4 + 3]),\r\n            operation: WebGPUCacheRenderPipeline._GetAphaBlendOperation(this._alphaBlendEqParams[targetIndex * 2 + 1]),\r\n        };\r\n    }\r\n\r\n    private _getColorBlendState(targetIndex: number): Nullable<GPUBlendComponent> {\r\n        if (!this._alphaBlendEnabled) {\r\n            return null;\r\n        }\r\n\r\n        return {\r\n            srcFactor: WebGPUCacheRenderPipeline._GetAphaBlendFactor(this._alphaBlendFuncParams[targetIndex * 4 + 0]),\r\n            dstFactor: WebGPUCacheRenderPipeline._GetAphaBlendFactor(this._alphaBlendFuncParams[targetIndex * 4 + 1]),\r\n            operation: WebGPUCacheRenderPipeline._GetAphaBlendOperation(this._alphaBlendEqParams[targetIndex * 2 + 0]),\r\n        };\r\n    }\r\n\r\n    private _setShaderStage(id: number): void {\r\n        if (this._shaderId !== id) {\r\n            this._shaderId = id;\r\n            this._states[StatePosition.ShaderStage] = id;\r\n            this._isDirty = true;\r\n            this._stateDirtyLowestIndex = Math.min(this._stateDirtyLowestIndex, StatePosition.ShaderStage);\r\n        }\r\n    }\r\n\r\n    private _setRasterizationState(topology: number, sampleCount: number): void {\r\n        const frontFace = this._frontFace;\r\n        const cullMode = this._cullEnabled ? this._cullFace : 0;\r\n        const clampDepth = this._clampDepth ? 1 : 0;\r\n        const alphaToCoverage = this._alphaToCoverageEnabled ? 1 : 0;\r\n        const rasterizationState = frontFace - 1 + (cullMode << 1) + (clampDepth << 3) + (alphaToCoverage << 4) + (topology << 5) + (sampleCount << 8);\r\n\r\n        if (this._rasterizationState !== rasterizationState) {\r\n            this._rasterizationState = rasterizationState;\r\n            this._states[StatePosition.RasterizationState] = this._rasterizationState;\r\n            this._isDirty = true;\r\n            this._stateDirtyLowestIndex = Math.min(this._stateDirtyLowestIndex, StatePosition.RasterizationState);\r\n        }\r\n    }\r\n\r\n    private _setColorStates(): void {\r\n        // Note that _depthWriteEnabled state has been moved from depthStencilState here because alpha and depth are related (generally when alpha is on, depth write is off and the other way around)\r\n\r\n        // We need 4 color states because we will be grouping 2 blend targets in each state (and WebGPU supports up to 8 targets).\r\n        // Integers can only be represented exactly in 53 bits with a double, so we can only use 53 bits for each state.\r\n        // We use 25 bits for each blend target (5 bits for the 2 (color/alpha) equations and 4*5 bits for the 4 factors (src/dst color and src/dst alpha)).\r\n        // This means that we need 25*2=50 bits to pack 2 blend targets, and we can use the remaining 3 bits for other states (write mask, depth write, color format).\r\n        // The color format is encoded on 6 bits, so we dispatch it over 3 bits to the last two color states.\r\n\r\n        colorStates[0] = (this._writeMask ? 1 : 0) * 2 ** 53;\r\n        colorStates[1] = (this._depthWriteEnabled ? 1 : 0) * 2 ** 53;\r\n        colorStates[2] = (this._colorFormat & 0x07) * 2 ** 50;\r\n        colorStates[3] = (this._colorFormat & 0x38) * 2 ** 47;\r\n\r\n        let colorStateIndex = 0;\r\n        let isDirty = false;\r\n\r\n        for (let i = 0; i < 8; i++) {\r\n            if (this._alphaBlendEnabled[i]) {\r\n                const index0 = i * 4 + 0;\r\n                const index1 = i * 4 + 1;\r\n                const index2 = i * 4 + 2;\r\n                const index3 = i * 4 + 3;\r\n                const indexEq0 = i * 2 + 0;\r\n                const indexEq1 = i * 2 + 1;\r\n                const eq0 = this._alphaBlendEqParams[indexEq0] === null ? 0 : alphaBlendEquationToIndex[this._alphaBlendEqParams[indexEq0]];\r\n                const eq1 = this._alphaBlendEqParams[indexEq1] === null ? 0 : alphaBlendEquationToIndex[this._alphaBlendEqParams[indexEq1]];\r\n\r\n                colorStates[colorStateIndex] +=\r\n                    ((this._alphaBlendFuncParams[index0] === null ? 1 : alphaBlendFactorToIndex[this._alphaBlendFuncParams[index0]]) << 0) +\r\n                    ((this._alphaBlendFuncParams[index1] === null ? 1 : alphaBlendFactorToIndex[this._alphaBlendFuncParams[index1]]) << 5) +\r\n                    ((this._alphaBlendFuncParams[index2] === null ? 1 : alphaBlendFactorToIndex[this._alphaBlendFuncParams[index2]]) << 10) +\r\n                    ((this._alphaBlendFuncParams[index3] === null ? 1 : alphaBlendFactorToIndex[this._alphaBlendFuncParams[index3]]) << 15) +\r\n                    (eq0 + eq1 * 5) * (1 << 20);\r\n            }\r\n\r\n            if (i & 1) {\r\n                isDirty = isDirty || this._states[StatePosition.ColorStates1 + colorStateIndex] !== colorStates[colorStateIndex];\r\n                this._states[StatePosition.ColorStates1 + colorStateIndex] = colorStates[colorStateIndex];\r\n                colorStateIndex++;\r\n            }\r\n        }\r\n\r\n        if (isDirty) {\r\n            this._isDirty = true;\r\n            this._stateDirtyLowestIndex = Math.min(this._stateDirtyLowestIndex, StatePosition.ColorStates1);\r\n        }\r\n    }\r\n\r\n    private _setDepthStencilState(): void {\r\n        const stencilState = !this._stencilEnabled\r\n            ? 7 /* ALWAYS */ +\r\n              (1 /* KEEP */ << 3) +\r\n              (1 /* KEEP */ << 6) +\r\n              (1 /* KEEP */ << 9) + // front\r\n              (7 /* ALWAYS */ << 12) +\r\n              (1 /* KEEP */ << 15) +\r\n              (1 /* KEEP */ << 18) +\r\n              (1 /* KEEP */ << 21) // back\r\n            : this._stencilFrontCompare +\r\n              (this._stencilFrontDepthFailOp << 3) +\r\n              (this._stencilFrontPassOp << 6) +\r\n              (this._stencilFrontFailOp << 9) + // front\r\n              (this._stencilBackCompare << 12) +\r\n              (this._stencilBackDepthFailOp << 15) +\r\n              (this._stencilBackPassOp << 18) +\r\n              (this._stencilBackFailOp << 21); // back\r\n\r\n        const depthStencilState = this._depthStencilFormat + ((this._depthTestEnabled ? this._depthCompare : 7) /* ALWAYS */ << 6) + stencilState * (1 << 10); // stencil front + back\r\n\r\n        if (this._depthStencilState !== depthStencilState) {\r\n            this._depthStencilState = depthStencilState;\r\n            this._states[StatePosition.DepthStencilState] = this._depthStencilState;\r\n            this._isDirty = true;\r\n            this._stateDirtyLowestIndex = Math.min(this._stateDirtyLowestIndex, StatePosition.DepthStencilState);\r\n        }\r\n    }\r\n\r\n    private _setVertexState(effect: Effect): void {\r\n        const currStateLen = this._statesLength;\r\n        let newNumStates = StatePosition.VertexState;\r\n\r\n        const webgpuPipelineContext = effect._pipelineContext as WebGPUPipelineContext;\r\n        const attributes = webgpuPipelineContext.shaderProcessingContext.attributeNamesFromEffect;\r\n        const locations = webgpuPipelineContext.shaderProcessingContext.attributeLocationsFromEffect;\r\n\r\n        let currentGPUBuffer;\r\n        let numVertexBuffers = 0;\r\n        for (let index = 0; index < attributes.length; index++) {\r\n            const location = locations[index];\r\n            let vertexBuffer = (this._overrideVertexBuffers && this._overrideVertexBuffers[attributes[index]]) ?? this._vertexBuffers![attributes[index]];\r\n            if (!vertexBuffer) {\r\n                // In WebGL it's valid to not bind a vertex buffer to an attribute, but it's not valid in WebGPU\r\n                // So we must bind a dummy buffer when we are not given one for a specific attribute\r\n                vertexBuffer = this._emptyVertexBuffer;\r\n                if (WebGPUCacheRenderPipeline.LogErrorIfNoVertexBuffer) {\r\n                    Logger.Error(\r\n                        `No vertex buffer is provided for the \"${attributes[index]}\" attribute. A default empty vertex buffer will be used, but this may generate errors in some browsers.`\r\n                    );\r\n                }\r\n            }\r\n\r\n            const buffer = vertexBuffer.effectiveBuffer?.underlyingResource;\r\n\r\n            // We optimize usage of GPUVertexBufferLayout: we will create a single GPUVertexBufferLayout for all the attributes which follow each other and which use the same GPU buffer\r\n            // However, there are some constraints in the attribute.offset value range, so we must check for them before being able to reuse the same GPUVertexBufferLayout\r\n            // See _getVertexInputDescriptor() below\r\n            if (vertexBuffer._validOffsetRange === undefined) {\r\n                const offset = vertexBuffer.effectiveByteOffset;\r\n                const formatSize = vertexBuffer.getSize(true);\r\n                const byteStride = vertexBuffer.effectiveByteStride;\r\n\r\n                vertexBuffer._validOffsetRange =\r\n                    (offset + formatSize <= this._kMaxVertexBufferStride && byteStride === 0) || (byteStride !== 0 && offset + formatSize <= byteStride);\r\n            }\r\n\r\n            if (!(currentGPUBuffer && currentGPUBuffer === buffer && vertexBuffer._validOffsetRange)) {\r\n                // we can't combine the previous vertexBuffer with the current one\r\n                this.vertexBuffers[numVertexBuffers++] = vertexBuffer;\r\n                currentGPUBuffer = vertexBuffer._validOffsetRange ? buffer : null;\r\n            }\r\n\r\n            const vid = vertexBuffer.hashCode + (location << 7);\r\n\r\n            this._isDirty = this._isDirty || this._states[newNumStates] !== vid;\r\n            this._states[newNumStates++] = vid;\r\n        }\r\n\r\n        this.vertexBuffers.length = numVertexBuffers;\r\n\r\n        this._statesLength = newNumStates;\r\n        this._isDirty = this._isDirty || newNumStates !== currStateLen;\r\n        if (this._isDirty) {\r\n            this._stateDirtyLowestIndex = Math.min(this._stateDirtyLowestIndex, StatePosition.VertexState);\r\n        }\r\n    }\r\n\r\n    private _setTextureState(textureState: number): void {\r\n        if (this._textureState !== textureState) {\r\n            this._textureState = textureState;\r\n            this._states[StatePosition.TextureStage] = this._textureState;\r\n            this._isDirty = true;\r\n            this._stateDirtyLowestIndex = Math.min(this._stateDirtyLowestIndex, StatePosition.TextureStage);\r\n        }\r\n    }\r\n\r\n    private _createPipelineLayout(webgpuPipelineContext: WebGPUPipelineContext): GPUPipelineLayout {\r\n        if (this._useTextureStage) {\r\n            return this._createPipelineLayoutWithTextureStage(webgpuPipelineContext);\r\n        }\r\n\r\n        const bindGroupLayouts: GPUBindGroupLayout[] = [];\r\n        const bindGroupLayoutEntries = webgpuPipelineContext.shaderProcessingContext.bindGroupLayoutEntries;\r\n\r\n        for (let i = 0; i < bindGroupLayoutEntries.length; i++) {\r\n            const setDefinition = bindGroupLayoutEntries[i];\r\n\r\n            bindGroupLayouts[i] = this._device.createBindGroupLayout({\r\n                entries: setDefinition,\r\n            });\r\n        }\r\n\r\n        webgpuPipelineContext.bindGroupLayouts[0] = bindGroupLayouts;\r\n\r\n        return this._device.createPipelineLayout({ bindGroupLayouts });\r\n    }\r\n\r\n    private _createPipelineLayoutWithTextureStage(webgpuPipelineContext: WebGPUPipelineContext): GPUPipelineLayout {\r\n        const shaderProcessingContext = webgpuPipelineContext.shaderProcessingContext;\r\n        const bindGroupLayoutEntries = shaderProcessingContext.bindGroupLayoutEntries;\r\n\r\n        let bitVal = 1;\r\n        for (let i = 0; i < bindGroupLayoutEntries.length; i++) {\r\n            const setDefinition = bindGroupLayoutEntries[i];\r\n\r\n            for (let j = 0; j < setDefinition.length; j++) {\r\n                const entry = bindGroupLayoutEntries[i][j];\r\n\r\n                if (entry.texture) {\r\n                    const name = shaderProcessingContext.bindGroupLayoutEntryInfo[i][entry.binding].name;\r\n                    const textureInfo = shaderProcessingContext.availableTextures[name];\r\n                    const samplerInfo = textureInfo.autoBindSampler ? shaderProcessingContext.availableSamplers[name + Constants.AUTOSAMPLERSUFFIX] : null;\r\n\r\n                    let sampleType = textureInfo.sampleType;\r\n                    let samplerType = samplerInfo?.type ?? WebGPUConstants.SamplerBindingType.Filtering;\r\n\r\n                    if (this._textureState & bitVal && sampleType !== WebGPUConstants.TextureSampleType.Depth) {\r\n                        // The texture is a 32 bits float texture but the system does not support linear filtering for them OR the texture is a depth texture with \"float\" filtering:\r\n                        // we set the sampler to \"non-filtering\" and the texture sample type to \"unfilterable-float\"\r\n                        if (textureInfo.autoBindSampler) {\r\n                            samplerType = WebGPUConstants.SamplerBindingType.NonFiltering;\r\n                        }\r\n                        sampleType = WebGPUConstants.TextureSampleType.UnfilterableFloat;\r\n                    }\r\n\r\n                    entry.texture.sampleType = sampleType;\r\n\r\n                    if (samplerInfo) {\r\n                        const binding = shaderProcessingContext.bindGroupLayoutEntryInfo[samplerInfo.binding.groupIndex][samplerInfo.binding.bindingIndex].index;\r\n                        bindGroupLayoutEntries[samplerInfo.binding.groupIndex][binding].sampler!.type = samplerType;\r\n                    }\r\n\r\n                    bitVal = bitVal << 1;\r\n                }\r\n            }\r\n        }\r\n\r\n        const bindGroupLayouts: GPUBindGroupLayout[] = [];\r\n\r\n        for (let i = 0; i < bindGroupLayoutEntries.length; ++i) {\r\n            bindGroupLayouts[i] = this._device.createBindGroupLayout({\r\n                entries: bindGroupLayoutEntries[i],\r\n            });\r\n        }\r\n\r\n        webgpuPipelineContext.bindGroupLayouts[this._textureState] = bindGroupLayouts;\r\n\r\n        return this._device.createPipelineLayout({ bindGroupLayouts });\r\n    }\r\n\r\n    private _getVertexInputDescriptor(effect: Effect): GPUVertexBufferLayout[] {\r\n        const descriptors: GPUVertexBufferLayout[] = [];\r\n        const webgpuPipelineContext = effect._pipelineContext as WebGPUPipelineContext;\r\n        const attributes = webgpuPipelineContext.shaderProcessingContext.attributeNamesFromEffect;\r\n        const locations = webgpuPipelineContext.shaderProcessingContext.attributeLocationsFromEffect;\r\n\r\n        let currentGPUBuffer;\r\n        let currentGPUAttributes: GPUVertexAttribute[] | undefined;\r\n        for (let index = 0; index < attributes.length; index++) {\r\n            const location = locations[index];\r\n            let vertexBuffer = (this._overrideVertexBuffers && this._overrideVertexBuffers[attributes[index]]) ?? this._vertexBuffers![attributes[index]];\r\n            if (!vertexBuffer) {\r\n                // In WebGL it's valid to not bind a vertex buffer to an attribute, but it's not valid in WebGPU\r\n                // So we must bind a dummy buffer when we are not given one for a specific attribute\r\n                vertexBuffer = this._emptyVertexBuffer;\r\n            }\r\n\r\n            let buffer = vertexBuffer.effectiveBuffer?.underlyingResource;\r\n\r\n            // We reuse the same GPUVertexBufferLayout for all attributes that use the same underlying GPU buffer (and for attributes that follow each other in the attributes array)\r\n            let offset = vertexBuffer.effectiveByteOffset;\r\n            const invalidOffsetRange = !vertexBuffer._validOffsetRange;\r\n            if (!(currentGPUBuffer && currentGPUAttributes && currentGPUBuffer === buffer) || invalidOffsetRange) {\r\n                const vertexBufferDescriptor: GPUVertexBufferLayout = {\r\n                    arrayStride: vertexBuffer.effectiveByteStride,\r\n                    stepMode: vertexBuffer.getIsInstanced() ? WebGPUConstants.VertexStepMode.Instance : WebGPUConstants.VertexStepMode.Vertex,\r\n                    attributes: [],\r\n                };\r\n\r\n                descriptors.push(vertexBufferDescriptor);\r\n                currentGPUAttributes = vertexBufferDescriptor.attributes;\r\n                if (invalidOffsetRange) {\r\n                    offset = 0; // the offset will be set directly in the setVertexBuffer call\r\n                    buffer = null; // buffer can't be reused\r\n                }\r\n            }\r\n\r\n            currentGPUAttributes.push({\r\n                shaderLocation: location,\r\n                offset,\r\n                format: WebGPUCacheRenderPipeline._GetVertexInputDescriptorFormat(vertexBuffer),\r\n            });\r\n\r\n            currentGPUBuffer = buffer;\r\n        }\r\n\r\n        return descriptors;\r\n    }\r\n\r\n    private _createRenderPipeline(effect: Effect, topology: GPUPrimitiveTopology, sampleCount: number): GPURenderPipeline {\r\n        const webgpuPipelineContext = effect._pipelineContext as WebGPUPipelineContext;\r\n        const inputStateDescriptor = this._getVertexInputDescriptor(effect);\r\n        const pipelineLayout = this._createPipelineLayout(webgpuPipelineContext);\r\n\r\n        const colorStates: Array<GPUColorTargetState | null> = [];\r\n\r\n        if (this._vertexBuffers) {\r\n            checkNonFloatVertexBuffers(this._vertexBuffers, effect);\r\n        }\r\n\r\n        if (this._mrtAttachments > 0) {\r\n            for (let i = 0; i < this._mrtFormats.length; ++i) {\r\n                const format = this._mrtFormats[i];\r\n                if (format) {\r\n                    const descr: GPUColorTargetState = {\r\n                        format,\r\n                        writeMask: (this._mrtEnabledMask & (1 << i)) !== 0 ? this._writeMask : 0,\r\n                    };\r\n                    const alphaBlend = this._getAphaBlendState(i < this._numAlphaBlendTargetsEnabled ? i : 0);\r\n                    const colorBlend = this._getColorBlendState(i < this._numAlphaBlendTargetsEnabled ? i : 0);\r\n\r\n                    if (alphaBlend && colorBlend) {\r\n                        descr.blend = {\r\n                            alpha: alphaBlend,\r\n                            color: colorBlend,\r\n                        };\r\n                    }\r\n                    colorStates.push(descr);\r\n                } else {\r\n                    colorStates.push(null);\r\n                }\r\n            }\r\n        } else {\r\n            if (this._webgpuColorFormat[0]) {\r\n                const descr: GPUColorTargetState = {\r\n                    format: this._webgpuColorFormat[0],\r\n                    writeMask: this._writeMask,\r\n                };\r\n                const alphaBlend = this._getAphaBlendState(0);\r\n                const colorBlend = this._getColorBlendState(0);\r\n\r\n                if (alphaBlend && colorBlend) {\r\n                    descr.blend = {\r\n                        alpha: alphaBlend,\r\n                        color: colorBlend,\r\n                    };\r\n                }\r\n                colorStates.push(descr);\r\n            } else {\r\n                colorStates.push(null);\r\n            }\r\n        }\r\n\r\n        const stencilFront: GPUStencilFaceState = {\r\n            compare: WebGPUCacheRenderPipeline._GetCompareFunction(this._stencilEnabled ? this._stencilFrontCompare : 7 /* ALWAYS */),\r\n            depthFailOp: WebGPUCacheRenderPipeline._GetStencilOpFunction(this._stencilEnabled ? this._stencilFrontDepthFailOp : 1 /* KEEP */),\r\n            failOp: WebGPUCacheRenderPipeline._GetStencilOpFunction(this._stencilEnabled ? this._stencilFrontFailOp : 1 /* KEEP */),\r\n            passOp: WebGPUCacheRenderPipeline._GetStencilOpFunction(this._stencilEnabled ? this._stencilFrontPassOp : 1 /* KEEP */),\r\n        };\r\n\r\n        const stencilBack: GPUStencilFaceState = {\r\n            compare: WebGPUCacheRenderPipeline._GetCompareFunction(this._stencilEnabled ? this._stencilBackCompare : 7 /* ALWAYS */),\r\n            depthFailOp: WebGPUCacheRenderPipeline._GetStencilOpFunction(this._stencilEnabled ? this._stencilBackDepthFailOp : 1 /* KEEP */),\r\n            failOp: WebGPUCacheRenderPipeline._GetStencilOpFunction(this._stencilEnabled ? this._stencilBackFailOp : 1 /* KEEP */),\r\n            passOp: WebGPUCacheRenderPipeline._GetStencilOpFunction(this._stencilEnabled ? this._stencilBackPassOp : 1 /* KEEP */),\r\n        };\r\n\r\n        const topologyIsTriangle = topology === WebGPUConstants.PrimitiveTopology.TriangleList || topology === WebGPUConstants.PrimitiveTopology.TriangleStrip;\r\n\r\n        let stripIndexFormat: GPUIndexFormat | undefined = undefined;\r\n        if (topology === WebGPUConstants.PrimitiveTopology.LineStrip || topology === WebGPUConstants.PrimitiveTopology.TriangleStrip) {\r\n            stripIndexFormat = !this._indexBuffer || this._indexBuffer.is32Bits ? WebGPUConstants.IndexFormat.Uint32 : WebGPUConstants.IndexFormat.Uint16;\r\n        }\r\n\r\n        const depthStencilFormatHasStencil = this._webgpuDepthStencilFormat ? WebGPUTextureHelper.HasStencilAspect(this._webgpuDepthStencilFormat) : false;\r\n\r\n        return this._device.createRenderPipeline({\r\n            label: `RenderPipeline_${colorStates[0]?.format ?? \"nooutput\"}_${this._webgpuDepthStencilFormat ?? \"nodepth\"}_samples${sampleCount}_textureState${this._textureState}`,\r\n            layout: pipelineLayout,\r\n            vertex: {\r\n                module: webgpuPipelineContext.stages!.vertexStage.module,\r\n                entryPoint: webgpuPipelineContext.stages!.vertexStage.entryPoint,\r\n                buffers: inputStateDescriptor,\r\n            },\r\n            primitive: {\r\n                topology,\r\n                stripIndexFormat,\r\n                frontFace: this._frontFace === 1 ? WebGPUConstants.FrontFace.CCW : WebGPUConstants.FrontFace.CW,\r\n                cullMode: !this._cullEnabled ? WebGPUConstants.CullMode.None : this._cullFace === 2 ? WebGPUConstants.CullMode.Front : WebGPUConstants.CullMode.Back,\r\n            },\r\n            fragment: !webgpuPipelineContext.stages!.fragmentStage\r\n                ? undefined\r\n                : {\r\n                      module: webgpuPipelineContext.stages!.fragmentStage.module,\r\n                      entryPoint: webgpuPipelineContext.stages!.fragmentStage.entryPoint,\r\n                      targets: colorStates,\r\n                  },\r\n\r\n            multisample: {\r\n                count: sampleCount,\r\n                /*mask,\r\n                alphaToCoverageEnabled,*/\r\n            },\r\n            depthStencil:\r\n                this._webgpuDepthStencilFormat === undefined\r\n                    ? undefined\r\n                    : {\r\n                          depthWriteEnabled: this._depthWriteEnabled,\r\n                          depthCompare: this._depthTestEnabled ? WebGPUCacheRenderPipeline._GetCompareFunction(this._depthCompare) : WebGPUConstants.CompareFunction.Always,\r\n                          format: this._webgpuDepthStencilFormat,\r\n                          stencilFront: this._stencilEnabled && depthStencilFormatHasStencil ? stencilFront : undefined,\r\n                          stencilBack: this._stencilEnabled && depthStencilFormatHasStencil ? stencilBack : undefined,\r\n                          stencilReadMask: this._stencilEnabled && depthStencilFormatHasStencil ? this._stencilReadMask : undefined,\r\n                          stencilWriteMask: this._stencilEnabled && depthStencilFormatHasStencil ? this._stencilWriteMask : undefined,\r\n                          depthBias: this._depthBias,\r\n                          depthBiasClamp: topologyIsTriangle ? this._depthBiasClamp : 0,\r\n                          depthBiasSlopeScale: topologyIsTriangle ? this._depthBiasSlopeScale : 0,\r\n                      },\r\n        });\r\n    }\r\n}\r\n"]}