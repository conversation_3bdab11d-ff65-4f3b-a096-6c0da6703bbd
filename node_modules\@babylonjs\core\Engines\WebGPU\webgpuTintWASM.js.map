{"version": 3, "file": "webgpuTintWASM.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGPU/webgpuTintWASM.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,6BAAyB;AAC1C,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AAqBzC,gBAAgB;AAChB,MAAM,OAAO,cAAc;IAavB,gEAAgE;IACzD,KAAK,CAAC,SAAS,CAAC,YAA2B;QAC9C,IAAI,cAAc,CAAC,MAAM,EAAE,CAAC;YACxB,OAAO;QACX,CAAC;QAED,YAAY,GAAG,YAAY,IAAI,EAAE,CAAC;QAClC,YAAY,GAAG;YACX,GAAG,cAAc,CAAC,oBAAoB;YACtC,GAAG,YAAY;SAClB,CAAC;QAEF,IAAI,YAAY,CAAC,KAAK,EAAE,CAAC;YACrB,cAAc,CAAC,MAAM,GAAG,YAAY,CAAC,KAAK,CAAC;YAC3C,OAAO;QACX,CAAC;QAED,IAAI,YAAY,CAAC,MAAM,IAAI,YAAY,CAAC,QAAQ,EAAE,CAAC;YAC/C,MAAM,KAAK,CAAC,sBAAsB,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAC5D,CAAC;QAED,IAAK,IAAY,CAAC,KAAK,EAAE,CAAC;YACtB,kDAAkD;YAClD,cAAc,CAAC,MAAM,GAAG,MAAO,IAAY,CAAC,KAAK,CAAC,KAAK,CAAC,mBAAmB,CAAC,YAAY,CAAC,QAAS,CAAC,CAAC,CAAC;YACrG,OAAO;QACX,CAAC;QAED,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC/C,CAAC;IAEM,iBAAiB,CAAC,IAAiB,EAAE,yBAAyB,GAAG,KAAK;QACzE,MAAM,KAAK,GAAG,cAAc,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,EAAE,cAAc,CAAC,yBAAyB,IAAI,yBAAyB,CAAC,CAAC;QACnI,IAAI,cAAc,CAAC,kBAAkB,EAAE,CAAC;YACpC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAClB,MAAM,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;QAClE,CAAC;QACD,OAAO,cAAc,CAAC,yBAAyB,IAAI,yBAAyB,CAAC,CAAC,CAAC,2CAA2C,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;IAC/I,CAAC;;AAjDD,yBAAyB;AACD,mCAAoB,GAAiB;IACzD,MAAM,EAAE,GAAG,KAAK,CAAC,cAAc,iBAAiB;IAChD,QAAQ,EAAE,GAAG,KAAK,CAAC,cAAc,mBAAmB;CACvD,CAAC;AAEY,iCAAkB,GAAG,KAAK,CAAC;AAE3B,wCAAyB,GAAG,KAAK,CAAC;AAEjC,qBAAM,GAAQ,IAAI,CAAC", "sourcesContent": ["import { Logger } from \"core/Misc/logger\";\r\nimport { Tools } from \"../../Misc/tools\";\r\n\r\n/**\r\n * Options to load the associated Twgsl library\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport interface TwgslOptions {\r\n    /**\r\n     * Defines an existing instance of Twgsl (useful in modules who do not access the global instance).\r\n     */\r\n    twgsl?: any;\r\n    /**\r\n     * Defines the URL of the twgsl JS File.\r\n     */\r\n    jsPath?: string;\r\n    /**\r\n     * Defines the URL of the twgsl WASM File.\r\n     */\r\n    wasmPath?: string;\r\n}\r\n\r\n/** @internal */\r\nexport class WebGPUTintWASM {\r\n    // Default twgsl options.\r\n    private static readonly _TwgslDefaultOptions: TwgslOptions = {\r\n        jsPath: `${Tools._DefaultCdnUrl}/twgsl/twgsl.js`,\r\n        wasmPath: `${Tools._DefaultCdnUrl}/twgsl/twgsl.wasm`,\r\n    };\r\n\r\n    public static ShowWGSLShaderCode = false;\r\n\r\n    public static DisableUniformityAnalysis = false;\r\n\r\n    private static _Twgsl: any = null;\r\n\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public async initTwgsl(twgslOptions?: TwgslOptions): Promise<void> {\r\n        if (WebGPUTintWASM._Twgsl) {\r\n            return;\r\n        }\r\n\r\n        twgslOptions = twgslOptions || {};\r\n        twgslOptions = {\r\n            ...WebGPUTintWASM._TwgslDefaultOptions,\r\n            ...twgslOptions,\r\n        };\r\n\r\n        if (twgslOptions.twgsl) {\r\n            WebGPUTintWASM._Twgsl = twgslOptions.twgsl;\r\n            return;\r\n        }\r\n\r\n        if (twgslOptions.jsPath && twgslOptions.wasmPath) {\r\n            await Tools.LoadBabylonScriptAsync(twgslOptions.jsPath);\r\n        }\r\n\r\n        if ((self as any).twgsl) {\r\n            // eslint-disable-next-line require-atomic-updates\r\n            WebGPUTintWASM._Twgsl = await (self as any).twgsl(Tools.GetBabylonScriptURL(twgslOptions.wasmPath!));\r\n            return;\r\n        }\r\n\r\n        throw new Error(\"twgsl is not available.\");\r\n    }\r\n\r\n    public convertSpirV2WGSL(code: Uint32Array, disableUniformityAnalysis = false): string {\r\n        const ccode = WebGPUTintWASM._Twgsl.convertSpirV2WGSL(code, WebGPUTintWASM.DisableUniformityAnalysis || disableUniformityAnalysis);\r\n        if (WebGPUTintWASM.ShowWGSLShaderCode) {\r\n            Logger.Log(ccode);\r\n            Logger.Log(\"***********************************************\");\r\n        }\r\n        return WebGPUTintWASM.DisableUniformityAnalysis || disableUniformityAnalysis ? \"diagnostic(off, derivative_uniformity);\\n\" + ccode : ccode;\r\n    }\r\n}\r\n"]}