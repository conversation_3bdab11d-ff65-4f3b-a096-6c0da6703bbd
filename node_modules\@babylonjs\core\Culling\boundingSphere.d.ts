import type { DeepImmutable } from "../types.js";
import { Matrix, Vector3 } from "../Maths/math.vector.js";
import type { Plane } from "../Maths/math.plane.js";
/**
 * Class used to store bounding sphere information
 */
export declare class BoundingSphere {
    /**
     * Gets the center of the bounding sphere in local space
     */
    readonly center: Vector3;
    /**
     * Radius of the bounding sphere in local space
     */
    radius: number;
    /**
     * Gets the center of the bounding sphere in world space
     */
    readonly centerWorld: Vector3;
    /**
     * Radius of the bounding sphere in world space
     */
    radiusWorld: number;
    /**
     * Gets the minimum vector in local space
     */
    readonly minimum: Vector3;
    /**
     * Gets the maximum vector in local space
     */
    readonly maximum: Vector3;
    private _worldMatrix;
    private static readonly _TmpVector3;
    /**
     * Creates a new bounding sphere
     * @param min defines the minimum vector (in local space)
     * @param max defines the maximum vector (in local space)
     * @param worldMatrix defines the new world matrix
     */
    constructor(min: DeepImmutable<Vector3>, max: DeepImmutable<Vector3>, worldMatrix?: DeepImmutable<Matrix>);
    /**
     * Recreates the entire bounding sphere from scratch as if we call the constructor in place
     * @param min defines the new minimum vector (in local space)
     * @param max defines the new maximum vector (in local space)
     * @param worldMatrix defines the new world matrix
     */
    reConstruct(min: DeepImmutable<Vector3>, max: DeepImmutable<Vector3>, worldMatrix?: DeepImmutable<Matrix>): void;
    /**
     * Scale the current bounding sphere by applying a scale factor
     * @param factor defines the scale factor to apply
     * @returns the current bounding box
     */
    scale(factor: number): BoundingSphere;
    /**
     * Gets the world matrix of the bounding box
     * @returns a matrix
     */
    getWorldMatrix(): DeepImmutable<Matrix>;
    /**
     * @internal
     */
    _update(worldMatrix: DeepImmutable<Matrix>): void;
    /**
     * Tests if the bounding sphere is intersecting the frustum planes
     * @param frustumPlanes defines the frustum planes to test
     * @returns true if there is an intersection
     */
    isInFrustum(frustumPlanes: Array<DeepImmutable<Plane>>): boolean;
    /**
     * Tests if the bounding sphere center is in between the frustum planes.
     * Used for optimistic fast inclusion.
     * @param frustumPlanes defines the frustum planes to test
     * @returns true if the sphere center is in between the frustum planes
     */
    isCenterInFrustum(frustumPlanes: Array<DeepImmutable<Plane>>): boolean;
    /**
     * Tests if a point is inside the bounding sphere
     * @param point defines the point to test
     * @returns true if the point is inside the bounding sphere
     */
    intersectsPoint(point: DeepImmutable<Vector3>): boolean;
    /**
     * Checks if two sphere intersect
     * @param sphere0 sphere 0
     * @param sphere1 sphere 1
     * @returns true if the spheres intersect
     */
    static Intersects(sphere0: DeepImmutable<BoundingSphere>, sphere1: DeepImmutable<BoundingSphere>): boolean;
    /**
     * Creates a sphere from a center and a radius
     * @param center The center
     * @param radius radius
     * @param matrix Optional worldMatrix
     * @returns The sphere
     */
    static CreateFromCenterAndRadius(center: DeepImmutable<Vector3>, radius: number, matrix?: DeepImmutable<Matrix>): BoundingSphere;
}
