{"version": 3, "file": "engine.readTexture.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Engines/WebGPU/Extensions/engine.readTexture.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,gBAAgB,EAAE,kCAAsC;AAsCjE,qEAAqE;AACrE,gBAAgB,CAAC,SAAS,CAAC,kBAAkB,GAAG,UAC5C,OAAwB,EACxB,KAAa,EACb,MAAc,EACd,SAAS,GAAG,CAAC,CAAC,EACd,KAAK,GAAG,CAAC,EACT,SAAoC,IAAI,EACxC,aAAa,GAAG,IAAI,EACpB,gBAAgB,GAAG,KAAK,EACxB,CAAC,GAAG,CAAC,EACL,CAAC,GAAG,CAAC;IAEL,MAAM,iBAAiB,GAAG,OAAO,CAAC,gBAAyC,CAAC;IAE5E,IAAI,aAAa,EAAE,CAAC;QAChB,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC5B,CAAC;IAED,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,iBAAiB,CAAC,kBAAmB,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,iBAAiB,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,gBAAgB,CAAC,CAAC;AAC5K,CAAC,CAAC;AAEF,gBAAgB,CAAC,SAAS,CAAC,sBAAsB,GAAG;IAChD,4CAA4C;IAC5C,MAAM,kDAAkD,CAAC;AAC7D,CAAC,CAAC", "sourcesContent": ["import { ThinWebGPUEngine } from \"core/Engines/thinWebGPUEngine\";\r\nimport type { InternalTexture } from \"../../../Materials/Textures/internalTexture\";\r\nimport type { Nullable } from \"../../../types\";\r\nimport type { WebGPUHardwareTexture } from \"../webgpuHardwareTexture\";\r\n\r\ndeclare module \"../../abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /** @internal */\r\n        _readTexturePixels(\r\n            texture: InternalTexture,\r\n            width: number,\r\n            height: number,\r\n            faceIndex?: number,\r\n            level?: number,\r\n            buffer?: Nullable<ArrayBufferView>,\r\n            flushRenderer?: boolean,\r\n            noDataConversion?: boolean,\r\n            x?: number,\r\n            y?: number\r\n        ): Promise<ArrayBufferView>;\r\n\r\n        /** @internal */\r\n        _readTexturePixelsSync(\r\n            texture: InternalTexture,\r\n            width: number,\r\n            height: number,\r\n            faceIndex?: number,\r\n            level?: number,\r\n            buffer?: Nullable<ArrayBufferView>,\r\n            flushRenderer?: boolean,\r\n            noDataConversion?: boolean,\r\n            x?: number,\r\n            y?: number\r\n        ): ArrayBufferView;\r\n    }\r\n}\r\n\r\n// eslint-disable-next-line @typescript-eslint/promise-function-async\r\nThinWebGPUEngine.prototype._readTexturePixels = function (\r\n    texture: InternalTexture,\r\n    width: number,\r\n    height: number,\r\n    faceIndex = -1,\r\n    level = 0,\r\n    buffer: Nullable<ArrayBufferView> = null,\r\n    flushRenderer = true,\r\n    noDataConversion = false,\r\n    x = 0,\r\n    y = 0\r\n): Promise<ArrayBufferView> {\r\n    const gpuTextureWrapper = texture._hardwareTexture as WebGPUHardwareTexture;\r\n\r\n    if (flushRenderer) {\r\n        this.flushFramebuffer();\r\n    }\r\n\r\n    return this._textureHelper.readPixels(gpuTextureWrapper.underlyingResource!, x, y, width, height, gpuTextureWrapper.format, faceIndex, level, buffer, noDataConversion);\r\n};\r\n\r\nThinWebGPUEngine.prototype._readTexturePixelsSync = function (): ArrayBufferView {\r\n    // eslint-disable-next-line no-throw-literal\r\n    throw \"_readTexturePixelsSync is unsupported in WebGPU!\";\r\n};\r\n"]}