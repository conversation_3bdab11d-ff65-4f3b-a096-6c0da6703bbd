{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Meshes/mesh.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Meshes/mesh.ts"], "sourcesContent": ["/* eslint-disable jsdoc/require-returns-check */\r\nimport type { Observer } from \"../Misc/observable\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport { Tools, AsyncLoop } from \"../Misc/tools\";\r\nimport type { IAnimatable } from \"../Animations/animatable.interface\";\r\nimport { DeepCopier } from \"../Misc/deepCopier\";\r\nimport { Tags } from \"../Misc/tags\";\r\nimport type { Coroutine } from \"../Misc/coroutine\";\r\nimport { runCoroutineSync, runCoroutineAsync, createYieldingScheduler } from \"../Misc/coroutine\";\r\nimport type { Nullable, FloatArray, IndicesArray, DeepImmutable } from \"../types\";\r\nimport { Camera } from \"../Cameras/camera\";\r\nimport type { Scene } from \"../scene\";\r\nimport { ScenePerformancePriority } from \"../scene\";\r\nimport type { Vector4 } from \"../Maths/math.vector\";\r\nimport { Quaternion, Matrix, Vector3, Vector2 } from \"../Maths/math.vector\";\r\nimport type { Color4 } from \"../Maths/math.color\";\r\nimport { Color3 } from \"../Maths/math.color\";\r\nimport { Node } from \"../node\";\r\nimport { VertexBuffer, Buffer } from \"../Buffers/buffer\";\r\nimport type { IGetSetVerticesData } from \"./mesh.vertexData\";\r\nimport { VertexData } from \"./mesh.vertexData\";\r\n\r\nimport { Geometry } from \"./geometry\";\r\nimport type { IMeshDataOptions } from \"./abstractMesh\";\r\nimport { AbstractMesh } from \"./abstractMesh\";\r\nimport { SubMesh } from \"./subMesh\";\r\nimport type { BoundingSphere } from \"../Culling/boundingSphere\";\r\nimport type { Effect } from \"../Materials/effect\";\r\nimport { Material } from \"../Materials/material\";\r\nimport { MultiMaterial } from \"../Materials/multiMaterial\";\r\nimport { SceneLoaderFlags } from \"../Loading/sceneLoaderFlags\";\r\nimport type { Skeleton } from \"../Bones/skeleton\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport { SerializationHelper } from \"../Misc/decorators.serialization\";\r\nimport { Logger } from \"../Misc/logger\";\r\nimport { GetClass, RegisterClass } from \"../Misc/typeStore\";\r\nimport { _WarnImport } from \"../Misc/devTools\";\r\nimport { SceneComponentConstants } from \"../sceneComponent\";\r\nimport { MeshLODLevel } from \"./meshLODLevel\";\r\nimport type { Path3D } from \"../Maths/math.path\";\r\nimport type { Plane } from \"../Maths/math.plane\";\r\nimport type { TransformNode } from \"./transformNode\";\r\nimport type { DrawWrapper } from \"../Materials/drawWrapper\";\r\nimport type { PhysicsEngine as PhysicsEngineV1 } from \"../Physics/v1/physicsEngine\";\r\n\r\nimport type { GoldbergMesh } from \"./goldbergMesh\";\r\nimport type { InstancedMesh } from \"./instancedMesh\";\r\nimport type { IPhysicsEnabledObject, PhysicsImpostor } from \"../Physics/v1/physicsImpostor\";\r\nimport type { ICreateCapsuleOptions } from \"./Builders/capsuleBuilder\";\r\nimport type { LinesMesh } from \"./linesMesh\";\r\nimport type { GroundMesh } from \"./groundMesh\";\r\nimport type { DataBuffer } from \"core/Buffers/dataBuffer\";\r\nimport type { AbstractEngine } from \"core/Engines/abstractEngine\";\r\n\r\n/**\r\n * @internal\r\n **/\r\nexport class _CreationDataStorage {\r\n    public closePath?: boolean;\r\n    public closeArray?: boolean;\r\n    public idx: number[];\r\n    public dashSize: number;\r\n    public gapSize: number;\r\n    public path3D: Path3D;\r\n    public pathArray: Vector3[][];\r\n    public arc: number;\r\n    public radius: number;\r\n    public cap: number;\r\n    public tessellation: number;\r\n}\r\n\r\n/**\r\n * @internal\r\n **/\r\ninterface IInstanceDataStorageRenderPassVisibleInstances {\r\n    defaultRenderId: number;\r\n    selfDefaultRenderId: number;\r\n    intermediateDefaultRenderId: number;\r\n    [renderId: number]: Nullable<Array<InstancedMesh>>;\r\n}\r\n\r\n/**\r\n * @internal\r\n **/\r\nclass _InstanceDataStorageRenderPass {\r\n    public visibleInstances: Nullable<IInstanceDataStorageRenderPassVisibleInstances>;\r\n    public batchCache = new _InstancesBatch(this);\r\n    public batchCacheReplacementModeInFrozenMode = new _InstancesBatch(this);\r\n    public instancesBufferSize = 32 * 16 * 4; // let's start with a maximum of 32 instances\r\n    public instancesBuffer: Nullable<Buffer>;\r\n    public instancesPreviousBuffer: Nullable<Buffer>;\r\n    public instancesData: Float32Array;\r\n    public instancesPreviousData: Float32Array;\r\n    public previousBatch: Nullable<_InstancesBatch>;\r\n    public previousRenderId: number;\r\n}\r\n\r\n/**\r\n * @internal\r\n **/\r\nclass _InstanceDataStorage {\r\n    public renderPasses: { [id: number]: _InstanceDataStorageRenderPass } = {};\r\n    public overridenInstanceCount: number;\r\n    public isFrozen: boolean;\r\n    public forceMatrixUpdates: boolean;\r\n    public hardwareInstancedRendering: boolean;\r\n    public manualUpdate: boolean;\r\n    public previousManualUpdate: boolean;\r\n    public masterMeshPreviousWorldMatrix: Nullable<Matrix>;\r\n    public engine: AbstractEngine;\r\n}\r\n\r\n/**\r\n * @internal\r\n **/\r\nexport class _InstancesBatch {\r\n    public mustReturn = false;\r\n\r\n    public visibleInstances = new Array<Nullable<Array<InstancedMesh>>>();\r\n\r\n    public renderSelf: boolean[] = [];\r\n\r\n    public hardwareInstancedRendering: boolean[] = [];\r\n\r\n    constructor(public parent: _InstanceDataStorageRenderPass) {}\r\n}\r\n\r\n/**\r\n * @internal\r\n **/\r\nclass _ThinInstanceDataStorage {\r\n    public instancesCount: number = 0;\r\n    public matrixBuffer: Nullable<Buffer> = null;\r\n    public previousMatrixBuffer: Nullable<Buffer> = null;\r\n    public matrixBufferSize = 32 * 16; // let's start with a maximum of 32 thin instances\r\n    public matrixData: Nullable<Float32Array> = null;\r\n    public previousMatrixData: Nullable<Float32Array>;\r\n    public boundingVectors: Array<Vector3> = [];\r\n    public worldMatrices: Nullable<Matrix[]> = null;\r\n    public masterMeshPreviousWorldMatrix: Nullable<Matrix>;\r\n}\r\n\r\n/**\r\n * @internal\r\n **/\r\nclass _InternalMeshDataInfo {\r\n    // Events\r\n    public _onBeforeRenderObservable: Nullable<Observable<Mesh>>;\r\n    public _onBeforeBindObservable: Nullable<Observable<Mesh>>;\r\n    public _onAfterRenderObservable: Nullable<Observable<Mesh>>;\r\n    public _onBeforeDrawObservable: Nullable<Observable<Mesh>>;\r\n    public _onBetweenPassObservable: Nullable<Observable<SubMesh>>;\r\n\r\n    public _areNormalsFrozen: boolean = false; // Will be used by ribbons mainly\r\n    public _sourcePositions: Nullable<Float32Array>; // Will be used to save original positions when using software skinning\r\n    public _sourceNormals: Nullable<Float32Array>; // Will be used to save original normals when using software skinning\r\n\r\n    // Will be used to save a source mesh reference, If any\r\n    public _source: Nullable<Mesh> = null;\r\n    // Will be used to for fast cloned mesh lookup\r\n    public meshMap: Nullable<{ [id: string]: Mesh | undefined }> = null;\r\n\r\n    public _preActivateId: number = -1;\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public _LODLevels = new Array<MeshLODLevel>();\r\n    /** Alternative definition of LOD level, using screen coverage instead of distance */\r\n    public _useLODScreenCoverage: boolean = false;\r\n    public _checkReadinessObserver: Nullable<Observer<Scene>>;\r\n\r\n    public _onMeshReadyObserverAdded: (observer: Observer<Mesh>) => void;\r\n\r\n    public _effectiveMaterial: Nullable<Material> = null;\r\n\r\n    public _forcedInstanceCount: number = 0;\r\n\r\n    public _overrideRenderingFillMode: Nullable<number> = null;\r\n\r\n    public _sideOrientation: number;\r\n\r\n    public _effectiveSideOrientation: number;\r\n}\r\n\r\n/**\r\n * Options used to clone a mesh\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport interface MeshCloneOptions {\r\n    /** The parent of the mesh, if it has one */\r\n    parent?: Nullable<Node>;\r\n\r\n    /** Skips cloning child meshes of source (default: false. When false, achieved by calling a clone(), also passing False. This will make creation of children, recursive. */\r\n    doNotCloneChildren?: boolean;\r\n\r\n    /** Includes cloning mesh physics impostor (default: true) */\r\n    clonePhysicsImpostor?: boolean;\r\n\r\n    /** Includes cloning thin instances (default: false) */\r\n    cloneThinInstances?: boolean;\r\n}\r\n\r\n/**\r\n * Options used to create a mesh\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport interface MeshCreationOptions extends MeshCloneOptions {\r\n    /** An optional Mesh from which the new mesh will be cloned from (geometry will be shared) */\r\n    source?: Nullable<Mesh>;\r\n}\r\n\r\nconst MeshCreationOptions: MeshCreationOptions = {\r\n    source: null,\r\n    parent: null,\r\n    doNotCloneChildren: false,\r\n    clonePhysicsImpostor: true,\r\n    cloneThinInstances: false,\r\n};\r\n\r\n/**\r\n * Class used to represent renderable models\r\n */\r\nexport class Mesh extends AbstractMesh implements IGetSetVerticesData {\r\n    // Consts\r\n\r\n    /**\r\n     * Mesh side orientation : usually the external or front surface\r\n     */\r\n    public static readonly FRONTSIDE = VertexData.FRONTSIDE;\r\n\r\n    /**\r\n     * Mesh side orientation : usually the internal or back surface\r\n     */\r\n    public static readonly BACKSIDE = VertexData.BACKSIDE;\r\n    /**\r\n     * Mesh side orientation : both internal and external or front and back surfaces\r\n     */\r\n    public static readonly DOUBLESIDE = VertexData.DOUBLESIDE;\r\n    /**\r\n     * Mesh side orientation : by default, `FRONTSIDE`\r\n     */\r\n    public static readonly DEFAULTSIDE = VertexData.DEFAULTSIDE;\r\n    /**\r\n     * Mesh cap setting : no cap\r\n     */\r\n    public static readonly NO_CAP = 0;\r\n    /**\r\n     * Mesh cap setting : one cap at the beginning of the mesh\r\n     */\r\n    public static readonly CAP_START = 1;\r\n    /**\r\n     * Mesh cap setting : one cap at the end of the mesh\r\n     */\r\n    public static readonly CAP_END = 2;\r\n    /**\r\n     * Mesh cap setting : two caps, one at the beginning  and one at the end of the mesh\r\n     */\r\n    public static readonly CAP_ALL = 3;\r\n    /**\r\n     * Mesh pattern setting : no flip or rotate\r\n     */\r\n    public static readonly NO_FLIP = 0;\r\n    /**\r\n     * Mesh pattern setting : flip (reflect in y axis) alternate tiles on each row or column\r\n     */\r\n    public static readonly FLIP_TILE = 1;\r\n    /**\r\n     * Mesh pattern setting : rotate (180degs) alternate tiles on each row or column\r\n     */\r\n    public static readonly ROTATE_TILE = 2;\r\n    /**\r\n     * Mesh pattern setting : flip (reflect in y axis) all tiles on alternate rows\r\n     */\r\n    public static readonly FLIP_ROW = 3;\r\n    /**\r\n     * Mesh pattern setting : rotate (180degs) all tiles on alternate rows\r\n     */\r\n    public static readonly ROTATE_ROW = 4;\r\n    /**\r\n     * Mesh pattern setting : flip and rotate alternate tiles on each row or column\r\n     */\r\n    public static readonly FLIP_N_ROTATE_TILE = 5;\r\n    /**\r\n     * Mesh pattern setting : rotate pattern and rotate\r\n     */\r\n    public static readonly FLIP_N_ROTATE_ROW = 6;\r\n    /**\r\n     * Mesh tile positioning : part tiles same on left/right or top/bottom\r\n     */\r\n    public static readonly CENTER = 0;\r\n    /**\r\n     * Mesh tile positioning : part tiles on left\r\n     */\r\n    public static readonly LEFT = 1;\r\n    /**\r\n     * Mesh tile positioning : part tiles on right\r\n     */\r\n    public static readonly RIGHT = 2;\r\n    /**\r\n     * Mesh tile positioning : part tiles on top\r\n     */\r\n    public static readonly TOP = 3;\r\n    /**\r\n     * Mesh tile positioning : part tiles on bottom\r\n     */\r\n    public static readonly BOTTOM = 4;\r\n\r\n    /**\r\n     * Indicates that the instanced meshes should be sorted from back to front before rendering if their material is transparent\r\n     */\r\n    public static INSTANCEDMESH_SORT_TRANSPARENT = false;\r\n\r\n    /**\r\n     * Gets the default side orientation.\r\n     * @param orientation the orientation to value to attempt to get\r\n     * @returns the default orientation\r\n     * @internal\r\n     */\r\n    public static _GetDefaultSideOrientation(orientation?: number): number {\r\n        return orientation || Mesh.FRONTSIDE; // works as Mesh.FRONTSIDE is 0\r\n    }\r\n\r\n    // Internal data\r\n    private _internalMeshDataInfo = new _InternalMeshDataInfo();\r\n\r\n    /**\r\n     * Determines if the LOD levels are intended to be calculated using screen coverage (surface area ratio) instead of distance.\r\n     */\r\n    public get useLODScreenCoverage() {\r\n        return this._internalMeshDataInfo._useLODScreenCoverage;\r\n    }\r\n\r\n    public set useLODScreenCoverage(value: boolean) {\r\n        this._internalMeshDataInfo._useLODScreenCoverage = value;\r\n        this._sortLODLevels();\r\n    }\r\n\r\n    /**\r\n     * Will notify when the mesh is completely ready, including materials.\r\n     * Observers added to this observable will be removed once triggered\r\n     */\r\n    public onMeshReadyObservable: Observable<Mesh>;\r\n\r\n    public override get computeBonesUsingShaders(): boolean {\r\n        return this._internalAbstractMeshDataInfo._computeBonesUsingShaders;\r\n    }\r\n    public override set computeBonesUsingShaders(value: boolean) {\r\n        if (this._internalAbstractMeshDataInfo._computeBonesUsingShaders === value) {\r\n            return;\r\n        }\r\n\r\n        if (value && this._internalMeshDataInfo._sourcePositions) {\r\n            // switch from software to GPU computation: we need to reset the vertex and normal buffers that have been updated by the software process\r\n            this.setVerticesData(VertexBuffer.PositionKind, this._internalMeshDataInfo._sourcePositions, true);\r\n            if (this._internalMeshDataInfo._sourceNormals) {\r\n                this.setVerticesData(VertexBuffer.NormalKind, this._internalMeshDataInfo._sourceNormals, true);\r\n            }\r\n\r\n            this._internalMeshDataInfo._sourcePositions = null;\r\n            this._internalMeshDataInfo._sourceNormals = null;\r\n        }\r\n\r\n        this._internalAbstractMeshDataInfo._computeBonesUsingShaders = value;\r\n        this._markSubMeshesAsAttributesDirty();\r\n    }\r\n\r\n    /**\r\n     * An event triggered before rendering the mesh\r\n     */\r\n    public get onBeforeRenderObservable(): Observable<Mesh> {\r\n        if (!this._internalMeshDataInfo._onBeforeRenderObservable) {\r\n            this._internalMeshDataInfo._onBeforeRenderObservable = new Observable<Mesh>();\r\n        }\r\n\r\n        return this._internalMeshDataInfo._onBeforeRenderObservable;\r\n    }\r\n\r\n    /**\r\n     * An event triggered before binding the mesh\r\n     */\r\n    public get onBeforeBindObservable(): Observable<Mesh> {\r\n        if (!this._internalMeshDataInfo._onBeforeBindObservable) {\r\n            this._internalMeshDataInfo._onBeforeBindObservable = new Observable<Mesh>();\r\n        }\r\n\r\n        return this._internalMeshDataInfo._onBeforeBindObservable;\r\n    }\r\n\r\n    /**\r\n     * An event triggered after rendering the mesh\r\n     */\r\n    public get onAfterRenderObservable(): Observable<Mesh> {\r\n        if (!this._internalMeshDataInfo._onAfterRenderObservable) {\r\n            this._internalMeshDataInfo._onAfterRenderObservable = new Observable<Mesh>();\r\n        }\r\n\r\n        return this._internalMeshDataInfo._onAfterRenderObservable;\r\n    }\r\n\r\n    /**\r\n     * An event triggeredbetween rendering pass when using separateCullingPass = true\r\n     */\r\n    public get onBetweenPassObservable(): Observable<SubMesh> {\r\n        if (!this._internalMeshDataInfo._onBetweenPassObservable) {\r\n            this._internalMeshDataInfo._onBetweenPassObservable = new Observable<SubMesh>();\r\n        }\r\n\r\n        return this._internalMeshDataInfo._onBetweenPassObservable;\r\n    }\r\n\r\n    /**\r\n     * An event triggered before drawing the mesh\r\n     */\r\n    public get onBeforeDrawObservable(): Observable<Mesh> {\r\n        if (!this._internalMeshDataInfo._onBeforeDrawObservable) {\r\n            this._internalMeshDataInfo._onBeforeDrawObservable = new Observable<Mesh>();\r\n        }\r\n\r\n        return this._internalMeshDataInfo._onBeforeDrawObservable;\r\n    }\r\n\r\n    private _onBeforeDrawObserver: Nullable<Observer<Mesh>>;\r\n\r\n    /**\r\n     * Sets a callback to call before drawing the mesh. It is recommended to use onBeforeDrawObservable instead\r\n     */\r\n    public set onBeforeDraw(callback: () => void) {\r\n        if (this._onBeforeDrawObserver) {\r\n            this.onBeforeDrawObservable.remove(this._onBeforeDrawObserver);\r\n        }\r\n        this._onBeforeDrawObserver = this.onBeforeDrawObservable.add(callback);\r\n    }\r\n\r\n    public override get hasInstances(): boolean {\r\n        return this.instances.length > 0;\r\n    }\r\n\r\n    public override get hasThinInstances(): boolean {\r\n        return (this.forcedInstanceCount || this._thinInstanceDataStorage.instancesCount || 0) > 0;\r\n    }\r\n\r\n    // Members\r\n\r\n    /**\r\n     * Gets the delay loading state of the mesh (when delay loading is turned on)\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/importers/incrementalLoading\r\n     */\r\n    public delayLoadState = Constants.DELAYLOADSTATE_NONE;\r\n\r\n    /**\r\n     * Gets the list of instances created from this mesh\r\n     * it is not supposed to be modified manually.\r\n     * Note also that the order of the InstancedMesh wihin the array is not significant and might change.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/copies/instances\r\n     */\r\n    public instances: InstancedMesh[] = [];\r\n\r\n    /**\r\n     * Gets the file containing delay loading data for this mesh\r\n     */\r\n    public delayLoadingFile: string;\r\n\r\n    /** @internal */\r\n    public _binaryInfo: any;\r\n\r\n    /**\r\n     * User defined function used to change how LOD level selection is done\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/LOD\r\n     */\r\n    public onLODLevelSelection: (distance: number, mesh: Mesh, selectedLevel: Nullable<Mesh>) => void;\r\n\r\n    // Private\r\n    /** @internal */\r\n    public _creationDataStorage: Nullable<_CreationDataStorage> = null;\r\n\r\n    /** @internal */\r\n    public _geometry: Nullable<Geometry> = null;\r\n    /** @internal */\r\n    public _delayInfo: Array<string>;\r\n    /** @internal */\r\n    public _delayLoadingFunction: (any: any, mesh: Mesh) => void;\r\n\r\n    /**\r\n     * Gets or sets the forced number of instances to display.\r\n     * If 0 (default value), the number of instances is not forced and depends on the draw type\r\n     * (regular / instance / thin instances mesh)\r\n     */\r\n    public get forcedInstanceCount(): number {\r\n        return this._internalMeshDataInfo._forcedInstanceCount;\r\n    }\r\n\r\n    public set forcedInstanceCount(count: number) {\r\n        this._internalMeshDataInfo._forcedInstanceCount = count;\r\n    }\r\n\r\n    /** @internal */\r\n    public _instanceDataStorage: _InstanceDataStorage;\r\n\r\n    /** @internal */\r\n    public _thinInstanceDataStorage = new _ThinInstanceDataStorage();\r\n\r\n    /** @internal */\r\n    public _shouldGenerateFlatShading: boolean = false;\r\n\r\n    // Use by builder only to know what orientation were the mesh build in.\r\n    /** @internal */\r\n    public _originalBuilderSideOrientation: number = Mesh.DEFAULTSIDE;\r\n\r\n    /**\r\n     * Use this property to change the original side orientation defined at construction time\r\n     * Material.sideOrientation will override this value if set\r\n     * User will still be able to change the material sideOrientation afterwards if they really need it\r\n     */\r\n    public get sideOrientation(): number {\r\n        return this._internalMeshDataInfo._sideOrientation;\r\n    }\r\n\r\n    public set sideOrientation(value: number) {\r\n        this._internalMeshDataInfo._sideOrientation = value;\r\n\r\n        this._internalAbstractMeshDataInfo._sideOrientationHint =\r\n            (this._scene.useRightHandedSystem && value === Constants.MATERIAL_CounterClockWiseSideOrientation) ||\r\n            (!this._scene.useRightHandedSystem && value === Constants.MATERIAL_ClockWiseSideOrientation);\r\n    }\r\n\r\n    /** @internal */\r\n    public get _effectiveSideOrientation(): number {\r\n        return this._internalMeshDataInfo._effectiveSideOrientation;\r\n    }\r\n\r\n    /**\r\n     * @deprecated Please use sideOrientation instead.\r\n     * @see https://doc.babylonjs.com/breaking-changes#7110\r\n     */\r\n    public get overrideMaterialSideOrientation() {\r\n        return this.sideOrientation;\r\n    }\r\n\r\n    public set overrideMaterialSideOrientation(value: number) {\r\n        this.sideOrientation = value;\r\n        if (this.material) {\r\n            this.material.sideOrientation = null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Use this property to override the Material's fillMode value\r\n     */\r\n    public get overrideRenderingFillMode(): Nullable<number> {\r\n        return this._internalMeshDataInfo._overrideRenderingFillMode;\r\n    }\r\n\r\n    public set overrideRenderingFillMode(fillMode: Nullable<number>) {\r\n        this._internalMeshDataInfo._overrideRenderingFillMode = fillMode;\r\n    }\r\n\r\n    public override get material(): Nullable<Material> {\r\n        return this._internalAbstractMeshDataInfo._material;\r\n    }\r\n\r\n    public override set material(value: Nullable<Material>) {\r\n        if (value && ((this.material && this.material.sideOrientation === null) || this._internalAbstractMeshDataInfo._sideOrientationHint)) {\r\n            value.sideOrientation = null;\r\n        }\r\n        this._setMaterial(value);\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating whether to render ignoring the active camera's max z setting. (false by default)\r\n     * You should not mix meshes that have this property set to true with meshes that have it set to false if they all write\r\n     * to the depth buffer, because the z-values are not comparable in the two cases and you will get rendering artifacts if you do.\r\n     * You can set the property to true for meshes that do not write to the depth buffer, or set the same value (either false or true) otherwise.\r\n     * Note this will reduce performance when set to true.\r\n     */\r\n    public ignoreCameraMaxZ = false;\r\n\r\n    /**\r\n     * Gets the source mesh (the one used to clone this one from)\r\n     */\r\n    public get source(): Nullable<Mesh> {\r\n        return this._internalMeshDataInfo._source;\r\n    }\r\n\r\n    /**\r\n     * Gets the list of clones of this mesh\r\n     * The scene must have been constructed with useClonedMeshMap=true for this to work!\r\n     * Note that useClonedMeshMap=true is the default setting\r\n     */\r\n    public get cloneMeshMap(): Nullable<{ [id: string]: Mesh | undefined }> {\r\n        return this._internalMeshDataInfo.meshMap;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating that this mesh does not use index buffer\r\n     */\r\n    public get isUnIndexed(): boolean {\r\n        return this._unIndexed;\r\n    }\r\n\r\n    public set isUnIndexed(value: boolean) {\r\n        if (this._unIndexed !== value) {\r\n            this._unIndexed = value;\r\n            this._markSubMeshesAsAttributesDirty();\r\n        }\r\n    }\r\n\r\n    /** Gets the array buffer used to store the instanced buffer used for instances' world matrices */\r\n    public get worldMatrixInstancedBuffer(): Float32Array {\r\n        const instanceDataStorage = this._instanceDataStorage.renderPasses[this._instanceDataStorage.engine.isWebGPU ? this._instanceDataStorage.engine.currentRenderPassId : 0];\r\n        return instanceDataStorage ? instanceDataStorage.instancesData : (undefined as any);\r\n    }\r\n\r\n    /** Gets the array buffer used to store the instanced buffer used for instances' previous world matrices */\r\n    public get previousWorldMatrixInstancedBuffer(): Float32Array {\r\n        const instanceDataStorage = this._instanceDataStorage.renderPasses[this._instanceDataStorage.engine.isWebGPU ? this._instanceDataStorage.engine.currentRenderPassId : 0];\r\n        return instanceDataStorage ? instanceDataStorage.instancesPreviousData : (undefined as any);\r\n    }\r\n\r\n    /** Gets or sets a boolean indicating that the update of the instance buffer of the world matrices is manual */\r\n    public get manualUpdateOfWorldMatrixInstancedBuffer() {\r\n        return this._instanceDataStorage.manualUpdate;\r\n    }\r\n\r\n    public set manualUpdateOfWorldMatrixInstancedBuffer(value: boolean) {\r\n        this._instanceDataStorage.manualUpdate = value;\r\n    }\r\n\r\n    /** Gets or sets a boolean indicating that the update of the instance buffer of the world matrices is manual */\r\n    public get manualUpdateOfPreviousWorldMatrixInstancedBuffer() {\r\n        return this._instanceDataStorage.previousManualUpdate;\r\n    }\r\n\r\n    public set manualUpdateOfPreviousWorldMatrixInstancedBuffer(value: boolean) {\r\n        this._instanceDataStorage.previousManualUpdate = value;\r\n    }\r\n\r\n    /** Gets or sets a boolean indicating that the update of the instance buffer of the world matrices must be performed in all cases (and notably even in frozen mode) */\r\n    public get forceWorldMatrixInstancedBufferUpdate() {\r\n        return this._instanceDataStorage.forceMatrixUpdates;\r\n    }\r\n\r\n    public set forceWorldMatrixInstancedBufferUpdate(value: boolean) {\r\n        this._instanceDataStorage.forceMatrixUpdates = value;\r\n    }\r\n\r\n    protected _copySource(source: Mesh, doNotCloneChildren?: boolean, clonePhysicsImpostor: boolean = true, cloneThinInstances: boolean = false): void {\r\n        const scene = this.getScene();\r\n        // Geometry\r\n        if (source._geometry) {\r\n            source._geometry.applyToMesh(this);\r\n        }\r\n\r\n        // Deep copy\r\n        DeepCopier.DeepCopy(\r\n            source,\r\n            this,\r\n            [\r\n                \"name\",\r\n                \"material\",\r\n                \"skeleton\",\r\n                \"instances\",\r\n                \"parent\",\r\n                \"uniqueId\",\r\n                \"source\",\r\n                \"metadata\",\r\n                \"morphTargetManager\",\r\n                \"hasInstances\",\r\n                \"worldMatrixInstancedBuffer\",\r\n                \"previousWorldMatrixInstancedBuffer\",\r\n                \"hasLODLevels\",\r\n                \"geometry\",\r\n                \"isBlocked\",\r\n                \"areNormalsFrozen\",\r\n                \"facetNb\",\r\n                \"isFacetDataEnabled\",\r\n                \"lightSources\",\r\n                \"useBones\",\r\n                \"isAnInstance\",\r\n                \"collider\",\r\n                \"edgesRenderer\",\r\n                \"forward\",\r\n                \"up\",\r\n                \"right\",\r\n                \"absolutePosition\",\r\n                \"absoluteScaling\",\r\n                \"absoluteRotationQuaternion\",\r\n                \"isWorldMatrixFrozen\",\r\n                \"nonUniformScaling\",\r\n                \"behaviors\",\r\n                \"worldMatrixFromCache\",\r\n                \"hasThinInstances\",\r\n                \"cloneMeshMap\",\r\n                \"hasBoundingInfo\",\r\n                \"physicsBody\",\r\n                \"physicsImpostor\",\r\n            ],\r\n            [\"_poseMatrix\"]\r\n        );\r\n\r\n        // Source mesh\r\n        this._internalMeshDataInfo._source = source;\r\n        if (scene.useClonedMeshMap) {\r\n            if (!source._internalMeshDataInfo.meshMap) {\r\n                source._internalMeshDataInfo.meshMap = {};\r\n            }\r\n            source._internalMeshDataInfo.meshMap[this.uniqueId] = this;\r\n        }\r\n\r\n        // Construction Params\r\n        // Clone parameters allowing mesh to be updated in case of parametric shapes.\r\n        this._originalBuilderSideOrientation = source._originalBuilderSideOrientation;\r\n        this._creationDataStorage = source._creationDataStorage;\r\n\r\n        // Animation ranges\r\n        if (source._ranges) {\r\n            const ranges = source._ranges;\r\n            for (const name in ranges) {\r\n                if (!Object.prototype.hasOwnProperty.call(ranges, name)) {\r\n                    continue;\r\n                }\r\n\r\n                if (!ranges[name]) {\r\n                    continue;\r\n                }\r\n\r\n                this.createAnimationRange(name, ranges[name].from, ranges[name].to);\r\n            }\r\n        }\r\n\r\n        // Metadata\r\n        if (source.metadata && source.metadata.clone) {\r\n            this.metadata = source.metadata.clone();\r\n        } else {\r\n            this.metadata = source.metadata;\r\n        }\r\n        this._internalMetadata = source._internalMetadata;\r\n\r\n        // Tags\r\n        if (Tags && Tags.HasTags(source)) {\r\n            Tags.AddTagsTo(this, Tags.GetTags(source, true));\r\n        }\r\n\r\n        // Enabled. We shouldn't need to check the source's ancestors, as this mesh\r\n        // will have the same ones.\r\n        this.setEnabled(source.isEnabled(false));\r\n\r\n        // Parent\r\n        this.parent = source.parent;\r\n\r\n        // Pivot\r\n        this.setPivotMatrix(source.getPivotMatrix(), this._postMultiplyPivotMatrix);\r\n\r\n        this.id = this.name + \".\" + source.id;\r\n\r\n        // Material\r\n        this.material = source.material;\r\n\r\n        if (!doNotCloneChildren) {\r\n            // Children\r\n            const directDescendants = source.getDescendants(true);\r\n            for (let index = 0; index < directDescendants.length; index++) {\r\n                const child = directDescendants[index];\r\n\r\n                if ((<any>child)._isMesh) {\r\n                    MeshCreationOptions.parent = this;\r\n                    MeshCreationOptions.doNotCloneChildren = doNotCloneChildren;\r\n                    MeshCreationOptions.clonePhysicsImpostor = clonePhysicsImpostor;\r\n                    MeshCreationOptions.cloneThinInstances = cloneThinInstances;\r\n                    (<Mesh>child).clone(this.name + \".\" + child.name, MeshCreationOptions);\r\n                } else if ((<any>child).clone) {\r\n                    (<any>child).clone(this.name + \".\" + child.name, this);\r\n                }\r\n            }\r\n        }\r\n\r\n        // Morphs\r\n        if (source.morphTargetManager) {\r\n            this.morphTargetManager = source.morphTargetManager;\r\n        }\r\n\r\n        // Physics clone\r\n        if (scene.getPhysicsEngine) {\r\n            const physicsEngine = scene.getPhysicsEngine();\r\n            if (clonePhysicsImpostor && physicsEngine) {\r\n                if (physicsEngine.getPluginVersion() === 1) {\r\n                    const impostor = (physicsEngine as PhysicsEngineV1).getImpostorForPhysicsObject(source);\r\n                    if (impostor) {\r\n                        this.physicsImpostor = impostor.clone(this);\r\n                    }\r\n                } else if (physicsEngine.getPluginVersion() === 2) {\r\n                    if (source.physicsBody) {\r\n                        source.physicsBody.clone(this);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        // Particles\r\n        for (let index = 0; index < scene.particleSystems.length; index++) {\r\n            const system = scene.particleSystems[index];\r\n\r\n            if (system.emitter === source) {\r\n                system.clone(system.name, this);\r\n            }\r\n        }\r\n\r\n        // Skeleton\r\n        this.skeleton = source.skeleton;\r\n\r\n        // Thin instances\r\n        if (cloneThinInstances) {\r\n            if (source._thinInstanceDataStorage.matrixData) {\r\n                this.thinInstanceSetBuffer(\r\n                    \"matrix\",\r\n                    new Float32Array(source._thinInstanceDataStorage.matrixData),\r\n                    16,\r\n                    !source._thinInstanceDataStorage.matrixBuffer!.isUpdatable()\r\n                );\r\n                this._thinInstanceDataStorage.matrixBufferSize = source._thinInstanceDataStorage.matrixBufferSize;\r\n                this._thinInstanceDataStorage.instancesCount = source._thinInstanceDataStorage.instancesCount;\r\n            } else {\r\n                this._thinInstanceDataStorage.matrixBufferSize = source._thinInstanceDataStorage.matrixBufferSize;\r\n            }\r\n\r\n            if (source._userThinInstanceBuffersStorage) {\r\n                const userThinInstance = source._userThinInstanceBuffersStorage;\r\n                for (const kind in userThinInstance.data) {\r\n                    this.thinInstanceSetBuffer(\r\n                        kind,\r\n                        new Float32Array(userThinInstance.data[kind]),\r\n                        userThinInstance.strides[kind],\r\n                        !userThinInstance.vertexBuffers?.[kind]?.isUpdatable()\r\n                    );\r\n                    this._userThinInstanceBuffersStorage.sizes[kind] = userThinInstance.sizes[kind];\r\n                }\r\n            }\r\n        }\r\n\r\n        this.refreshBoundingInfo(true, true);\r\n        this.computeWorldMatrix(true);\r\n    }\r\n\r\n    /**\r\n     * Constructor\r\n     * @param name The value used by scene.getMeshByName() to do a lookup.\r\n     * @param scene The scene to add this mesh to.\r\n     * @param options Options used to create the mesh\r\n     */\r\n    constructor(name: string, scene?: Nullable<Scene>, options?: MeshCreationOptions);\r\n\r\n    /**\r\n     * Constructor\r\n     * @param name The value used by scene.getMeshByName() to do a lookup.\r\n     * @param scene The scene to add this mesh to.\r\n     * @param parent The parent of this mesh, if it has one\r\n     * @param source An optional Mesh from which geometry is shared, cloned.\r\n     * @param doNotCloneChildren When cloning, skip cloning child meshes of source, default False.\r\n     *                  When false, achieved by calling a clone(), also passing False.\r\n     *                  This will make creation of children, recursive.\r\n     * @param clonePhysicsImpostor When cloning, include cloning mesh physics impostor, default True.\r\n     */\r\n    constructor(name: string, scene?: Nullable<Scene>, parent?: Nullable<Node>, source?: Nullable<Mesh>, doNotCloneChildren?: boolean, clonePhysicsImpostor?: boolean);\r\n\r\n    /** @internal */\r\n    constructor(\r\n        name: string,\r\n        scene: Nullable<Scene> = null,\r\n        parentOrOptions: Nullable<Node> | MeshCreationOptions = null,\r\n        source: Nullable<Mesh> = null,\r\n        doNotCloneChildren?: boolean,\r\n        clonePhysicsImpostor: boolean = true\r\n    ) {\r\n        super(name, scene);\r\n\r\n        scene = this.getScene();\r\n\r\n        this._instanceDataStorage = new _InstanceDataStorage();\r\n        this._instanceDataStorage.engine = scene.getEngine();\r\n\r\n        if (this._scene.useRightHandedSystem) {\r\n            this.sideOrientation = Constants.MATERIAL_ClockWiseSideOrientation;\r\n        } else {\r\n            this.sideOrientation = Constants.MATERIAL_CounterClockWiseSideOrientation;\r\n        }\r\n\r\n        this._onBeforeDraw = (isInstance: boolean, world: Matrix, effectiveMaterial?: Material) => {\r\n            if (isInstance && effectiveMaterial) {\r\n                if (this._uniformBuffer) {\r\n                    this.transferToEffect(world);\r\n                } else {\r\n                    effectiveMaterial.bindOnlyWorldMatrix(world);\r\n                }\r\n            }\r\n        };\r\n\r\n        let parent: Nullable<Node> = null;\r\n        let cloneThinInstances = false;\r\n\r\n        if (parentOrOptions && (parentOrOptions as Node)._addToSceneRootNodes === undefined) {\r\n            const options = parentOrOptions as MeshCreationOptions;\r\n\r\n            parent = options.parent ?? null;\r\n            source = options.source ?? null;\r\n            doNotCloneChildren = options.doNotCloneChildren ?? false;\r\n            clonePhysicsImpostor = options.clonePhysicsImpostor ?? true;\r\n            cloneThinInstances = options.cloneThinInstances ?? false;\r\n        } else {\r\n            parent = parentOrOptions as Nullable<Node>;\r\n        }\r\n\r\n        if (source) {\r\n            this._copySource(source, doNotCloneChildren, clonePhysicsImpostor, cloneThinInstances);\r\n        }\r\n\r\n        // Parent\r\n        if (parent !== null) {\r\n            this.parent = parent;\r\n        }\r\n\r\n        this._instanceDataStorage.hardwareInstancedRendering = this.getEngine().getCaps().instancedArrays;\r\n\r\n        this._internalMeshDataInfo._onMeshReadyObserverAdded = (observer: Observer<Mesh>) => {\r\n            // only notify once! then unregister the observer\r\n            observer.unregisterOnNextCall = true;\r\n            if (this.isReady(true)) {\r\n                this.onMeshReadyObservable.notifyObservers(this);\r\n            } else {\r\n                if (!this._internalMeshDataInfo._checkReadinessObserver) {\r\n                    this._internalMeshDataInfo._checkReadinessObserver = this._scene.onBeforeRenderObservable.add(() => {\r\n                        // check for complete readiness\r\n                        if (this.isReady(true)) {\r\n                            this._scene.onBeforeRenderObservable.remove(this._internalMeshDataInfo._checkReadinessObserver);\r\n                            this._internalMeshDataInfo._checkReadinessObserver = null;\r\n                            this.onMeshReadyObservable.notifyObservers(this);\r\n                        }\r\n                    });\r\n                }\r\n            }\r\n        };\r\n\r\n        this.onMeshReadyObservable = new Observable(this._internalMeshDataInfo._onMeshReadyObserverAdded);\r\n\r\n        if (source) {\r\n            source.onClonedObservable.notifyObservers(this);\r\n        }\r\n    }\r\n\r\n    public override instantiateHierarchy(\r\n        newParent: Nullable<TransformNode> = null,\r\n        options?: { doNotInstantiate: boolean | ((node: TransformNode) => boolean) },\r\n        onNewNodeCreated?: (source: TransformNode, clone: TransformNode) => void\r\n    ): Nullable<TransformNode> {\r\n        const instance =\r\n            this.getTotalVertices() === 0 || (options && options.doNotInstantiate && (options.doNotInstantiate === true || options.doNotInstantiate(this)))\r\n                ? this.clone(\"Clone of \" + (this.name || this.id), newParent || this.parent, true)\r\n                : this.createInstance(\"instance of \" + (this.name || this.id));\r\n\r\n        instance.parent = newParent || this.parent;\r\n        instance.position = this.position.clone();\r\n        instance.scaling = this.scaling.clone();\r\n        if (this.rotationQuaternion) {\r\n            instance.rotationQuaternion = this.rotationQuaternion.clone();\r\n        } else {\r\n            instance.rotation = this.rotation.clone();\r\n        }\r\n\r\n        if (onNewNodeCreated) {\r\n            onNewNodeCreated(this, instance);\r\n        }\r\n\r\n        for (const child of this.getChildTransformNodes(true)) {\r\n            // instancedMesh should have a different sourced mesh\r\n            if (child.getClassName() === \"InstancedMesh\" && instance.getClassName() === \"Mesh\" && (child as InstancedMesh).sourceMesh === this) {\r\n                (child as InstancedMesh).instantiateHierarchy(\r\n                    instance,\r\n                    {\r\n                        doNotInstantiate: (options && options.doNotInstantiate) || false,\r\n                        newSourcedMesh: instance as Mesh,\r\n                    },\r\n                    onNewNodeCreated\r\n                );\r\n            } else {\r\n                child.instantiateHierarchy(instance, options, onNewNodeCreated);\r\n            }\r\n        }\r\n\r\n        return instance;\r\n    }\r\n\r\n    /**\r\n     * Gets the class name\r\n     * @returns the string \"Mesh\".\r\n     */\r\n    public override getClassName(): string {\r\n        return \"Mesh\";\r\n    }\r\n\r\n    /** @internal */\r\n    public get _isMesh() {\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Returns a description of this mesh\r\n     * @param fullDetails define if full details about this mesh must be used\r\n     * @returns a descriptive string representing this mesh\r\n     */\r\n    public override toString(fullDetails?: boolean): string {\r\n        let ret = super.toString(fullDetails);\r\n        ret += \", n vertices: \" + this.getTotalVertices();\r\n        ret += \", parent: \" + (this._waitingParentId ? this._waitingParentId : this.parent ? this.parent.name : \"NONE\");\r\n\r\n        if (this.animations) {\r\n            for (let i = 0; i < this.animations.length; i++) {\r\n                ret += \", animation[0]: \" + this.animations[i].toString(fullDetails);\r\n            }\r\n        }\r\n\r\n        if (fullDetails) {\r\n            if (this._geometry) {\r\n                const ib = this.getIndices();\r\n                const vb = this.getVerticesData(VertexBuffer.PositionKind);\r\n\r\n                if (vb && ib) {\r\n                    ret += \", flat shading: \" + (vb.length / 3 === ib.length ? \"YES\" : \"NO\");\r\n                }\r\n            } else {\r\n                ret += \", flat shading: UNKNOWN\";\r\n            }\r\n        }\r\n        return ret;\r\n    }\r\n\r\n    /** @internal */\r\n    public override _unBindEffect() {\r\n        super._unBindEffect();\r\n\r\n        for (const instance of this.instances) {\r\n            instance._unBindEffect();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if this mesh has LOD\r\n     */\r\n    public get hasLODLevels(): boolean {\r\n        return this._internalMeshDataInfo._LODLevels.length > 0;\r\n    }\r\n\r\n    /**\r\n     * Gets the list of MeshLODLevel associated with the current mesh\r\n     * @returns an array of MeshLODLevel\r\n     */\r\n    public getLODLevels(): MeshLODLevel[] {\r\n        return this._internalMeshDataInfo._LODLevels;\r\n    }\r\n\r\n    private _sortLODLevels(): void {\r\n        const sortingOrderFactor = this._internalMeshDataInfo._useLODScreenCoverage ? -1 : 1;\r\n        this._internalMeshDataInfo._LODLevels.sort((a, b) => {\r\n            if (a.distanceOrScreenCoverage < b.distanceOrScreenCoverage) {\r\n                return sortingOrderFactor;\r\n            }\r\n            if (a.distanceOrScreenCoverage > b.distanceOrScreenCoverage) {\r\n                return -sortingOrderFactor;\r\n            }\r\n\r\n            return 0;\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Add a mesh as LOD level triggered at the given distance.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/LOD\r\n     * @param distanceOrScreenCoverage Either distance from the center of the object to show this level or the screen coverage if `useScreenCoverage` is set to `true`.\r\n     * If screen coverage, value is a fraction of the screen's total surface, between 0 and 1.\r\n     * Example Playground for distance https://playground.babylonjs.com/#QE7KM#197\r\n     * Example Playground for screen coverage https://playground.babylonjs.com/#QE7KM#196\r\n     * @param mesh The mesh to be added as LOD level (can be null)\r\n     * @returns This mesh (for chaining)\r\n     */\r\n    public addLODLevel(distanceOrScreenCoverage: number, mesh: Nullable<Mesh>): Mesh {\r\n        if (mesh && mesh._masterMesh) {\r\n            Logger.Warn(\"You cannot use a mesh as LOD level twice\");\r\n            return this;\r\n        }\r\n\r\n        const level = new MeshLODLevel(distanceOrScreenCoverage, mesh);\r\n        this._internalMeshDataInfo._LODLevels.push(level);\r\n\r\n        if (mesh) {\r\n            mesh._masterMesh = this;\r\n        }\r\n\r\n        this._sortLODLevels();\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Returns the LOD level mesh at the passed distance or null if not found.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/LOD\r\n     * @param distance The distance from the center of the object to show this level\r\n     * @returns a Mesh or `null`\r\n     */\r\n    public getLODLevelAtDistance(distance: number): Nullable<Mesh> {\r\n        const internalDataInfo = this._internalMeshDataInfo;\r\n        for (let index = 0; index < internalDataInfo._LODLevels.length; index++) {\r\n            const level = internalDataInfo._LODLevels[index];\r\n\r\n            if (level.distanceOrScreenCoverage === distance) {\r\n                return level.mesh;\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Remove a mesh from the LOD array\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/LOD\r\n     * @param mesh defines the mesh to be removed\r\n     * @returns This mesh (for chaining)\r\n     */\r\n    public removeLODLevel(mesh: Nullable<Mesh>): Mesh {\r\n        const internalDataInfo = this._internalMeshDataInfo;\r\n        for (let index = 0; index < internalDataInfo._LODLevels.length; index++) {\r\n            if (internalDataInfo._LODLevels[index].mesh === mesh) {\r\n                internalDataInfo._LODLevels.splice(index, 1);\r\n                if (mesh) {\r\n                    mesh._masterMesh = null;\r\n                }\r\n            }\r\n        }\r\n\r\n        this._sortLODLevels();\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Returns the registered LOD mesh distant from the parameter `camera` position if any, else returns the current mesh.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/LOD\r\n     * @param camera defines the camera to use to compute distance\r\n     * @param boundingSphere defines a custom bounding sphere to use instead of the one from this mesh\r\n     * @returns This mesh (for chaining)\r\n     */\r\n    public override getLOD(camera: Camera, boundingSphere?: BoundingSphere): Nullable<AbstractMesh> {\r\n        const internalDataInfo = this._internalMeshDataInfo;\r\n        if (!internalDataInfo._LODLevels || internalDataInfo._LODLevels.length === 0) {\r\n            return this;\r\n        }\r\n\r\n        const bSphere = boundingSphere || this.getBoundingInfo().boundingSphere;\r\n\r\n        const distanceToCamera = camera.mode === Camera.ORTHOGRAPHIC_CAMERA ? camera.minZ : bSphere.centerWorld.subtract(camera.globalPosition).length();\r\n        let compareValue = distanceToCamera;\r\n        let compareSign = 1;\r\n\r\n        if (internalDataInfo._useLODScreenCoverage) {\r\n            const screenArea = camera.screenArea;\r\n            let meshArea = (bSphere.radiusWorld * camera.minZ) / distanceToCamera;\r\n            meshArea = meshArea * meshArea * Math.PI;\r\n            compareValue = meshArea / screenArea;\r\n            compareSign = -1;\r\n        }\r\n\r\n        if (compareSign * internalDataInfo._LODLevels[internalDataInfo._LODLevels.length - 1].distanceOrScreenCoverage > compareSign * compareValue) {\r\n            if (this.onLODLevelSelection) {\r\n                this.onLODLevelSelection(compareValue, this, this);\r\n            }\r\n            return this;\r\n        }\r\n\r\n        for (let index = 0; index < internalDataInfo._LODLevels.length; index++) {\r\n            const level = internalDataInfo._LODLevels[index];\r\n\r\n            if (compareSign * level.distanceOrScreenCoverage < compareSign * compareValue) {\r\n                if (level.mesh) {\r\n                    if (level.mesh.delayLoadState === Constants.DELAYLOADSTATE_NOTLOADED) {\r\n                        level.mesh._checkDelayState();\r\n                        return this;\r\n                    }\r\n\r\n                    if (level.mesh.delayLoadState === Constants.DELAYLOADSTATE_LOADING) {\r\n                        return this;\r\n                    }\r\n\r\n                    level.mesh._preActivate();\r\n                    level.mesh._updateSubMeshesBoundingInfo(this.worldMatrixFromCache);\r\n                }\r\n\r\n                if (this.onLODLevelSelection) {\r\n                    this.onLODLevelSelection(compareValue, this, level.mesh);\r\n                }\r\n\r\n                return level.mesh;\r\n            }\r\n        }\r\n\r\n        if (this.onLODLevelSelection) {\r\n            this.onLODLevelSelection(compareValue, this, this);\r\n        }\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Gets the mesh internal Geometry object\r\n     */\r\n    public override get geometry(): Nullable<Geometry> {\r\n        return this._geometry;\r\n    }\r\n\r\n    /**\r\n     * Returns the total number of vertices within the mesh geometry or zero if the mesh has no geometry.\r\n     * @returns the total number of vertices\r\n     */\r\n    public override getTotalVertices(): number {\r\n        if (this._geometry === null || this._geometry === undefined) {\r\n            return 0;\r\n        }\r\n        return this._geometry.getTotalVertices();\r\n    }\r\n\r\n    /**\r\n     * Returns the content of an associated vertex buffer\r\n     * @param kind defines which buffer to read from (positions, indices, normals, etc). Possible `kind` values :\r\n     * - VertexBuffer.PositionKind\r\n     * - VertexBuffer.UVKind\r\n     * - VertexBuffer.UV2Kind\r\n     * - VertexBuffer.UV3Kind\r\n     * - VertexBuffer.UV4Kind\r\n     * - VertexBuffer.UV5Kind\r\n     * - VertexBuffer.UV6Kind\r\n     * - VertexBuffer.ColorKind\r\n     * - VertexBuffer.MatricesIndicesKind\r\n     * - VertexBuffer.MatricesIndicesExtraKind\r\n     * - VertexBuffer.MatricesWeightsKind\r\n     * - VertexBuffer.MatricesWeightsExtraKind\r\n     * @param copyWhenShared defines a boolean indicating that if the mesh geometry is shared among some other meshes, the returned array is a copy of the internal one\r\n     * @param forceCopy defines a boolean forcing the copy of the buffer no matter what the value of copyWhenShared is\r\n     * @param bypassInstanceData defines a boolean indicating that the function should not take into account the instance data (applies only if the mesh has instances). Default: false\r\n     * @returns a FloatArray or null if the mesh has no geometry or no vertex buffer for this kind.\r\n     */\r\n    public override getVerticesData(kind: string, copyWhenShared?: boolean, forceCopy?: boolean, bypassInstanceData?: boolean): Nullable<FloatArray> {\r\n        if (!this._geometry) {\r\n            return null;\r\n        }\r\n        let data = bypassInstanceData\r\n            ? undefined\r\n            : this._userInstancedBuffersStorage?.vertexBuffers[kind]?.getFloatData(\r\n                  this.instances.length + 1, // +1 because the master mesh is not included in the instances array\r\n                  forceCopy || (copyWhenShared && this._geometry.meshes.length !== 1)\r\n              );\r\n        if (!data) {\r\n            data = this._geometry.getVerticesData(kind, copyWhenShared, forceCopy);\r\n        }\r\n        return data;\r\n    }\r\n\r\n    public override copyVerticesData(kind: string, vertexData: { [kind: string]: Float32Array }): void {\r\n        if (this._geometry) {\r\n            this._geometry.copyVerticesData(kind, vertexData);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Returns the mesh VertexBuffer object from the requested `kind`\r\n     * @param kind defines which buffer to read from (positions, indices, normals, etc). Possible `kind` values :\r\n     * - VertexBuffer.PositionKind\r\n     * - VertexBuffer.NormalKind\r\n     * - VertexBuffer.UVKind\r\n     * - VertexBuffer.UV2Kind\r\n     * - VertexBuffer.UV3Kind\r\n     * - VertexBuffer.UV4Kind\r\n     * - VertexBuffer.UV5Kind\r\n     * - VertexBuffer.UV6Kind\r\n     * - VertexBuffer.ColorKind\r\n     * - VertexBuffer.MatricesIndicesKind\r\n     * - VertexBuffer.MatricesIndicesExtraKind\r\n     * - VertexBuffer.MatricesWeightsKind\r\n     * - VertexBuffer.MatricesWeightsExtraKind\r\n     * @param bypassInstanceData defines a boolean indicating that the function should not take into account the instance data (applies only if the mesh has instances). Default: false\r\n     * @returns a FloatArray or null if the mesh has no vertex buffer for this kind.\r\n     */\r\n    public getVertexBuffer(kind: string, bypassInstanceData?: boolean): Nullable<VertexBuffer> {\r\n        if (!this._geometry) {\r\n            return null;\r\n        }\r\n\r\n        return (bypassInstanceData ? undefined : this._userInstancedBuffersStorage?.vertexBuffers[kind]) ?? this._geometry.getVertexBuffer(kind);\r\n    }\r\n\r\n    /**\r\n     * Tests if a specific vertex buffer is associated with this mesh\r\n     * @param kind defines which buffer to check (positions, indices, normals, etc). Possible `kind` values :\r\n     * - VertexBuffer.PositionKind\r\n     * - VertexBuffer.NormalKind\r\n     * - VertexBuffer.UVKind\r\n     * - VertexBuffer.UV2Kind\r\n     * - VertexBuffer.UV3Kind\r\n     * - VertexBuffer.UV4Kind\r\n     * - VertexBuffer.UV5Kind\r\n     * - VertexBuffer.UV6Kind\r\n     * - VertexBuffer.ColorKind\r\n     * - VertexBuffer.MatricesIndicesKind\r\n     * - VertexBuffer.MatricesIndicesExtraKind\r\n     * - VertexBuffer.MatricesWeightsKind\r\n     * - VertexBuffer.MatricesWeightsExtraKind\r\n     * @param bypassInstanceData defines a boolean indicating that the function should not take into account the instance data (applies only if the mesh has instances). Default: false\r\n     * @returns a boolean\r\n     */\r\n    public override isVerticesDataPresent(kind: string, bypassInstanceData?: boolean): boolean {\r\n        if (!this._geometry) {\r\n            if (this._delayInfo) {\r\n                return this._delayInfo.indexOf(kind) !== -1;\r\n            }\r\n            return false;\r\n        }\r\n        return (!bypassInstanceData && this._userInstancedBuffersStorage?.vertexBuffers[kind] !== undefined) || this._geometry.isVerticesDataPresent(kind);\r\n    }\r\n\r\n    /**\r\n     * Returns a boolean defining if the vertex data for the requested `kind` is updatable.\r\n     * @param kind defines which buffer to check (positions, indices, normals, etc). Possible `kind` values :\r\n     * - VertexBuffer.PositionKind\r\n     * - VertexBuffer.UVKind\r\n     * - VertexBuffer.UV2Kind\r\n     * - VertexBuffer.UV3Kind\r\n     * - VertexBuffer.UV4Kind\r\n     * - VertexBuffer.UV5Kind\r\n     * - VertexBuffer.UV6Kind\r\n     * - VertexBuffer.ColorKind\r\n     * - VertexBuffer.MatricesIndicesKind\r\n     * - VertexBuffer.MatricesIndicesExtraKind\r\n     * - VertexBuffer.MatricesWeightsKind\r\n     * - VertexBuffer.MatricesWeightsExtraKind\r\n     * @param bypassInstanceData defines a boolean indicating that the function should not take into account the instance data (applies only if the mesh has instances). Default: false\r\n     * @returns a boolean\r\n     */\r\n    public isVertexBufferUpdatable(kind: string, bypassInstanceData?: boolean): boolean {\r\n        if (!this._geometry) {\r\n            if (this._delayInfo) {\r\n                return this._delayInfo.indexOf(kind) !== -1;\r\n            }\r\n            return false;\r\n        }\r\n        if (!bypassInstanceData) {\r\n            const buffer = this._userInstancedBuffersStorage?.vertexBuffers[kind];\r\n            if (buffer) {\r\n                return buffer.isUpdatable();\r\n            }\r\n        }\r\n        return this._geometry.isVertexBufferUpdatable(kind);\r\n    }\r\n\r\n    /**\r\n     * Returns a string which contains the list of existing `kinds` of Vertex Data associated with this mesh.\r\n     * @param bypassInstanceData defines a boolean indicating that the function should not take into account the instance data (applies only if the mesh has instances). Default: false\r\n     * @returns an array of strings\r\n     */\r\n    public getVerticesDataKinds(bypassInstanceData?: boolean): string[] {\r\n        if (!this._geometry) {\r\n            const result: string[] = [];\r\n            if (this._delayInfo) {\r\n                for (const kind of this._delayInfo) {\r\n                    result.push(kind);\r\n                }\r\n            }\r\n            return result;\r\n        }\r\n        const kinds = this._geometry.getVerticesDataKinds();\r\n        if (!bypassInstanceData && this._userInstancedBuffersStorage) {\r\n            for (const kind in this._userInstancedBuffersStorage.vertexBuffers) {\r\n                if (kinds.indexOf(kind) === -1) {\r\n                    kinds.push(kind);\r\n                }\r\n            }\r\n        }\r\n        return kinds;\r\n    }\r\n\r\n    /**\r\n     * Returns a positive integer : the total number of indices in this mesh geometry.\r\n     * @returns the number of indices or zero if the mesh has no geometry.\r\n     */\r\n    public override getTotalIndices(): number {\r\n        if (!this._geometry) {\r\n            return 0;\r\n        }\r\n        return this._geometry.getTotalIndices();\r\n    }\r\n\r\n    /**\r\n     * Returns an array of integers or a typed array (Int32Array, Uint32Array, Uint16Array) populated with the mesh indices.\r\n     * @param copyWhenShared If true (default false) and and if the mesh geometry is shared among some other meshes, the returned array is a copy of the internal one.\r\n     * @param forceCopy defines a boolean indicating that the returned array must be cloned upon returning it\r\n     * @returns the indices array or an empty array if the mesh has no geometry\r\n     */\r\n    public override getIndices(copyWhenShared?: boolean, forceCopy?: boolean): Nullable<IndicesArray> {\r\n        if (!this._geometry) {\r\n            return [];\r\n        }\r\n        return this._geometry.getIndices(copyWhenShared, forceCopy);\r\n    }\r\n\r\n    public override get isBlocked(): boolean {\r\n        return this._masterMesh !== null && this._masterMesh !== undefined;\r\n    }\r\n\r\n    /**\r\n     * Determine if the current mesh is ready to be rendered\r\n     * @param completeCheck defines if a complete check (including materials and lights) has to be done (false by default)\r\n     * @param forceInstanceSupport will check if the mesh will be ready when used with instances (false by default)\r\n     * @returns true if all associated assets are ready (material, textures, shaders)\r\n     */\r\n    public override isReady(completeCheck = false, forceInstanceSupport = false): boolean {\r\n        if (this.delayLoadState === Constants.DELAYLOADSTATE_LOADING) {\r\n            return false;\r\n        }\r\n\r\n        if (!super.isReady(completeCheck)) {\r\n            return false;\r\n        }\r\n\r\n        if (!this.subMeshes || this.subMeshes.length === 0) {\r\n            return true;\r\n        }\r\n\r\n        if (!completeCheck) {\r\n            return true;\r\n        }\r\n\r\n        const engine = this.getEngine();\r\n        const scene = this.getScene();\r\n        const hardwareInstancedRendering = forceInstanceSupport || (engine.getCaps().instancedArrays && (this.instances.length > 0 || this.hasThinInstances));\r\n\r\n        this.computeWorldMatrix();\r\n\r\n        const mat = this.material || scene.defaultMaterial;\r\n        if (mat) {\r\n            if (mat._storeEffectOnSubMeshes) {\r\n                for (const subMesh of this.subMeshes) {\r\n                    const effectiveMaterial = subMesh.getMaterial();\r\n                    if (effectiveMaterial) {\r\n                        if (effectiveMaterial._storeEffectOnSubMeshes) {\r\n                            if (!effectiveMaterial.isReadyForSubMesh(this, subMesh, hardwareInstancedRendering)) {\r\n                                return false;\r\n                            }\r\n                        } else {\r\n                            if (!effectiveMaterial.isReady(this, hardwareInstancedRendering)) {\r\n                                return false;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            } else {\r\n                if (!mat.isReady(this, hardwareInstancedRendering)) {\r\n                    return false;\r\n                }\r\n            }\r\n        }\r\n\r\n        // Shadows\r\n        const currentRenderPassId = engine.currentRenderPassId;\r\n        for (const light of this.lightSources) {\r\n            const generators = light.getShadowGenerators();\r\n\r\n            if (!generators) {\r\n                continue;\r\n            }\r\n\r\n            const iterator = generators.values();\r\n            for (let key = iterator.next(); key.done !== true; key = iterator.next()) {\r\n                const generator = key.value;\r\n                if (generator && (!generator.getShadowMap()?.renderList || (generator.getShadowMap()?.renderList && generator.getShadowMap()?.renderList?.indexOf(this) !== -1))) {\r\n                    const shadowMap = generator.getShadowMap()!;\r\n                    const renderPassIds = shadowMap.renderPassIds ?? [engine.currentRenderPassId];\r\n                    for (let p = 0; p < renderPassIds.length; ++p) {\r\n                        engine.currentRenderPassId = renderPassIds[p];\r\n                        for (const subMesh of this.subMeshes) {\r\n                            if (!generator.isReady(subMesh, hardwareInstancedRendering, subMesh.getMaterial()?.needAlphaBlendingForMesh(this) ?? false)) {\r\n                                engine.currentRenderPassId = currentRenderPassId;\r\n                                return false;\r\n                            }\r\n                        }\r\n                    }\r\n                    engine.currentRenderPassId = currentRenderPassId;\r\n                }\r\n            }\r\n        }\r\n\r\n        // LOD\r\n        for (const lod of this._internalMeshDataInfo._LODLevels) {\r\n            if (lod.mesh && !lod.mesh.isReady(hardwareInstancedRendering)) {\r\n                return false;\r\n            }\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if the normals aren't to be recomputed on next mesh `positions` array update. This property is pertinent only for updatable parametric shapes.\r\n     */\r\n    public get areNormalsFrozen(): boolean {\r\n        return this._internalMeshDataInfo._areNormalsFrozen;\r\n    }\r\n\r\n    /**\r\n     * This function affects parametric shapes on vertex position update only : ribbons, tubes, etc. It has no effect at all on other shapes. It prevents the mesh normals from being recomputed on next `positions` array update.\r\n     * @returns the current mesh\r\n     */\r\n    public freezeNormals(): Mesh {\r\n        this._internalMeshDataInfo._areNormalsFrozen = true;\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * This function affects parametric shapes on vertex position update only : ribbons, tubes, etc. It has no effect at all on other shapes. It reactivates the mesh normals computation if it was previously frozen\r\n     * @returns the current mesh\r\n     */\r\n    public unfreezeNormals(): Mesh {\r\n        this._internalMeshDataInfo._areNormalsFrozen = false;\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets a value overriding the instance count. Only applicable when custom instanced InterleavedVertexBuffer are used rather than InstancedMeshs\r\n     */\r\n    public set overridenInstanceCount(count: number) {\r\n        this._instanceDataStorage.overridenInstanceCount = count;\r\n    }\r\n\r\n    /** @internal */\r\n    public _getInstanceDataStorage(): _InstanceDataStorageRenderPass {\r\n        const renderPassId = this._instanceDataStorage.engine.isWebGPU ? this._instanceDataStorage.engine.currentRenderPassId : 0;\r\n\r\n        let instanceDataStorage = this._instanceDataStorage.renderPasses[renderPassId];\r\n        if (!instanceDataStorage) {\r\n            instanceDataStorage = new _InstanceDataStorageRenderPass();\r\n            this._instanceDataStorage.renderPasses[renderPassId] = instanceDataStorage;\r\n        }\r\n\r\n        return instanceDataStorage;\r\n    }\r\n\r\n    // Methods\r\n    /** @internal */\r\n    public override _preActivate(): Mesh {\r\n        const internalDataInfo = this._internalMeshDataInfo;\r\n        const sceneRenderId = this.getScene().getRenderId();\r\n        if (internalDataInfo._preActivateId === sceneRenderId) {\r\n            return this;\r\n        }\r\n\r\n        internalDataInfo._preActivateId = sceneRenderId;\r\n        if (this.hasInstances) {\r\n            this._getInstanceDataStorage().visibleInstances = null;\r\n        }\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public override _preActivateForIntermediateRendering(renderId: number): Mesh {\r\n        if (!this.hasInstances) {\r\n            return this;\r\n        }\r\n        const instanceDataStorage = this._getInstanceDataStorage();\r\n        if (instanceDataStorage.visibleInstances) {\r\n            instanceDataStorage.visibleInstances.intermediateDefaultRenderId = renderId;\r\n        }\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _registerInstanceForRenderId(instance: InstancedMesh, renderId: number): Mesh {\r\n        const instanceDataStorage = this._getInstanceDataStorage();\r\n        if (!instanceDataStorage.visibleInstances) {\r\n            instanceDataStorage.visibleInstances = {\r\n                defaultRenderId: renderId,\r\n                selfDefaultRenderId: this._renderId,\r\n                intermediateDefaultRenderId: -1,\r\n            };\r\n        }\r\n\r\n        if (!instanceDataStorage.visibleInstances[renderId]) {\r\n            if (instanceDataStorage.previousRenderId !== undefined && this._instanceDataStorage.isFrozen) {\r\n                instanceDataStorage.visibleInstances[instanceDataStorage.previousRenderId] = null;\r\n            }\r\n            instanceDataStorage.previousRenderId = renderId;\r\n            instanceDataStorage.visibleInstances[renderId] = new Array<InstancedMesh>();\r\n        }\r\n\r\n        instanceDataStorage.visibleInstances[renderId].push(instance);\r\n        return this;\r\n    }\r\n\r\n    protected override _afterComputeWorldMatrix(): void {\r\n        super._afterComputeWorldMatrix();\r\n\r\n        if (!this.hasThinInstances) {\r\n            return;\r\n        }\r\n\r\n        if (!this.doNotSyncBoundingInfo) {\r\n            this.thinInstanceRefreshBoundingInfo(false);\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public override _postActivate(): void {\r\n        if (this.edgesShareWithInstances && this.edgesRenderer && this.edgesRenderer.isEnabled && this._renderingGroup) {\r\n            this._renderingGroup._edgesRenderers.pushNoDuplicate(this.edgesRenderer);\r\n            this.edgesRenderer.customInstances.push(this.getWorldMatrix());\r\n        }\r\n    }\r\n\r\n    /**\r\n     * This method recomputes and sets a new BoundingInfo to the mesh unless it is locked.\r\n     * This means the mesh underlying bounding box and sphere are recomputed.\r\n     * @param applySkeletonOrOptions defines whether to apply the skeleton before computing the bounding info or a set of options\r\n     * @param applyMorph defines whether to apply the morph target before computing the bounding info\r\n     * @returns the current mesh\r\n     */\r\n    public override refreshBoundingInfo(applySkeletonOrOptions: boolean | IMeshDataOptions = false, applyMorph: boolean = false): Mesh {\r\n        if (this.hasBoundingInfo && this.getBoundingInfo().isLocked) {\r\n            return this;\r\n        }\r\n\r\n        let options: IMeshDataOptions;\r\n        if (typeof applySkeletonOrOptions === \"object\") {\r\n            options = applySkeletonOrOptions;\r\n        } else {\r\n            options = {\r\n                applySkeleton: applySkeletonOrOptions,\r\n                applyMorph: applyMorph,\r\n            };\r\n        }\r\n\r\n        const bias = this.geometry ? this.geometry.boundingBias : null;\r\n        this._refreshBoundingInfo(this._getData(options, null, VertexBuffer.PositionKind), bias);\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _createGlobalSubMesh(force: boolean): Nullable<SubMesh> {\r\n        const totalVertices = this.getTotalVertices();\r\n        if (!totalVertices || !this.getIndices()) {\r\n            return null;\r\n        }\r\n\r\n        // Check if we need to recreate the submeshes\r\n        if (this.subMeshes && this.subMeshes.length > 0) {\r\n            const ib = this.getIndices();\r\n\r\n            if (!ib) {\r\n                return null;\r\n            }\r\n\r\n            const totalIndices = ib.length;\r\n            let needToRecreate = false;\r\n\r\n            if (force) {\r\n                needToRecreate = true;\r\n            } else {\r\n                for (const submesh of this.subMeshes) {\r\n                    if (submesh.indexStart + submesh.indexCount > totalIndices) {\r\n                        needToRecreate = true;\r\n                        break;\r\n                    }\r\n\r\n                    if (submesh.verticesStart + submesh.verticesCount > totalVertices) {\r\n                        needToRecreate = true;\r\n                        break;\r\n                    }\r\n                }\r\n            }\r\n\r\n            if (!needToRecreate) {\r\n                return this.subMeshes[0];\r\n            }\r\n        }\r\n\r\n        this.releaseSubMeshes();\r\n        return new SubMesh(0, 0, totalVertices, 0, this.getTotalIndices() || totalVertices, this); // getTotalIndices() can be zero if the mesh is unindexed\r\n    }\r\n\r\n    /**\r\n     * This function will subdivide the mesh into multiple submeshes\r\n     * @param count defines the expected number of submeshes\r\n     */\r\n    public subdivide(count: number): void {\r\n        if (count < 1) {\r\n            return;\r\n        }\r\n\r\n        const totalIndices = this.getTotalIndices();\r\n        let subdivisionSize = (totalIndices / count) | 0;\r\n        let offset = 0;\r\n\r\n        // Ensure that subdivisionSize is a multiple of 3\r\n        while (subdivisionSize % 3 !== 0) {\r\n            subdivisionSize++;\r\n        }\r\n\r\n        this.releaseSubMeshes();\r\n        for (let index = 0; index < count; index++) {\r\n            if (offset >= totalIndices) {\r\n                break;\r\n            }\r\n\r\n            SubMesh.CreateFromIndices(0, offset, index === count - 1 ? totalIndices - offset : subdivisionSize, this, undefined, false);\r\n\r\n            offset += subdivisionSize;\r\n        }\r\n\r\n        this.refreshBoundingInfo();\r\n        this.synchronizeInstances();\r\n    }\r\n\r\n    /**\r\n     * Copy a FloatArray into a specific associated vertex buffer\r\n     * @param kind defines which buffer to write to (positions, indices, normals, etc). Possible `kind` values :\r\n     * - VertexBuffer.PositionKind\r\n     * - VertexBuffer.UVKind\r\n     * - VertexBuffer.UV2Kind\r\n     * - VertexBuffer.UV3Kind\r\n     * - VertexBuffer.UV4Kind\r\n     * - VertexBuffer.UV5Kind\r\n     * - VertexBuffer.UV6Kind\r\n     * - VertexBuffer.ColorKind\r\n     * - VertexBuffer.MatricesIndicesKind\r\n     * - VertexBuffer.MatricesIndicesExtraKind\r\n     * - VertexBuffer.MatricesWeightsKind\r\n     * - VertexBuffer.MatricesWeightsExtraKind\r\n     * @param data defines the data source\r\n     * @param updatable defines if the updated vertex buffer must be flagged as updatable\r\n     * @param stride defines the data stride size (can be null)\r\n     * @returns the current mesh\r\n     */\r\n    public override setVerticesData(kind: string, data: FloatArray, updatable: boolean = false, stride?: number): AbstractMesh {\r\n        if (!this._geometry) {\r\n            const vertexData = new VertexData();\r\n            vertexData.set(data, kind);\r\n\r\n            const scene = this.getScene();\r\n\r\n            new Geometry(Geometry.RandomId(), scene, vertexData, updatable, this);\r\n        } else {\r\n            this._geometry.setVerticesData(kind, data, updatable, stride);\r\n        }\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Delete a vertex buffer associated with this mesh\r\n     * @param kind defines which buffer to delete (positions, indices, normals, etc). Possible `kind` values :\r\n     * - VertexBuffer.PositionKind\r\n     * - VertexBuffer.UVKind\r\n     * - VertexBuffer.UV2Kind\r\n     * - VertexBuffer.UV3Kind\r\n     * - VertexBuffer.UV4Kind\r\n     * - VertexBuffer.UV5Kind\r\n     * - VertexBuffer.UV6Kind\r\n     * - VertexBuffer.ColorKind\r\n     * - VertexBuffer.MatricesIndicesKind\r\n     * - VertexBuffer.MatricesIndicesExtraKind\r\n     * - VertexBuffer.MatricesWeightsKind\r\n     * - VertexBuffer.MatricesWeightsExtraKind\r\n     */\r\n    public removeVerticesData(kind: string) {\r\n        if (!this._geometry) {\r\n            return;\r\n        }\r\n\r\n        this._geometry.removeVerticesData(kind);\r\n    }\r\n\r\n    /**\r\n     * Flags an associated vertex buffer as updatable\r\n     * @param kind defines which buffer to use (positions, indices, normals, etc). Possible `kind` values :\r\n     * - VertexBuffer.PositionKind\r\n     * - VertexBuffer.UVKind\r\n     * - VertexBuffer.UV2Kind\r\n     * - VertexBuffer.UV3Kind\r\n     * - VertexBuffer.UV4Kind\r\n     * - VertexBuffer.UV5Kind\r\n     * - VertexBuffer.UV6Kind\r\n     * - VertexBuffer.ColorKind\r\n     * - VertexBuffer.MatricesIndicesKind\r\n     * - VertexBuffer.MatricesIndicesExtraKind\r\n     * - VertexBuffer.MatricesWeightsKind\r\n     * - VertexBuffer.MatricesWeightsExtraKind\r\n     * @param updatable defines if the updated vertex buffer must be flagged as updatable\r\n     */\r\n    public markVerticesDataAsUpdatable(kind: string, updatable = true) {\r\n        const vb = this.getVertexBuffer(kind);\r\n\r\n        if (!vb || vb.isUpdatable() === updatable) {\r\n            return;\r\n        }\r\n\r\n        this.setVerticesData(kind, <FloatArray>this.getVerticesData(kind), updatable);\r\n    }\r\n\r\n    /**\r\n     * Sets the mesh global Vertex Buffer\r\n     * @param buffer defines the buffer to use\r\n     * @param disposeExistingBuffer disposes the existing buffer, if any (default: true)\r\n     * @returns the current mesh\r\n     */\r\n    public setVerticesBuffer(buffer: VertexBuffer, disposeExistingBuffer = true): Mesh {\r\n        if (!this._geometry) {\r\n            this._geometry = Geometry.CreateGeometryForMesh(this);\r\n        }\r\n\r\n        this._geometry.setVerticesBuffer(buffer, null, disposeExistingBuffer);\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Update a specific associated vertex buffer\r\n     * @param kind defines which buffer to write to (positions, indices, normals, etc). Possible `kind` values :\r\n     * - VertexBuffer.PositionKind\r\n     * - VertexBuffer.UVKind\r\n     * - VertexBuffer.UV2Kind\r\n     * - VertexBuffer.UV3Kind\r\n     * - VertexBuffer.UV4Kind\r\n     * - VertexBuffer.UV5Kind\r\n     * - VertexBuffer.UV6Kind\r\n     * - VertexBuffer.ColorKind\r\n     * - VertexBuffer.MatricesIndicesKind\r\n     * - VertexBuffer.MatricesIndicesExtraKind\r\n     * - VertexBuffer.MatricesWeightsKind\r\n     * - VertexBuffer.MatricesWeightsExtraKind\r\n     * @param data defines the data source\r\n     * @param updateExtends defines if extends info of the mesh must be updated (can be null). This is mostly useful for \"position\" kind\r\n     * @param makeItUnique defines if the geometry associated with the mesh must be cloned to make the change only for this mesh (and not all meshes associated with the same geometry)\r\n     * @returns the current mesh\r\n     */\r\n    public override updateVerticesData(kind: string, data: FloatArray, updateExtends?: boolean, makeItUnique?: boolean): AbstractMesh {\r\n        if (!this._geometry) {\r\n            return this;\r\n        }\r\n        if (!makeItUnique) {\r\n            this._geometry.updateVerticesData(kind, data, updateExtends);\r\n        } else {\r\n            this.makeGeometryUnique();\r\n            this.updateVerticesData(kind, data, updateExtends, false);\r\n        }\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * This method updates the vertex positions of an updatable mesh according to the `positionFunction` returned values.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/dynamicMeshMorph#other-shapes-updatemeshpositions\r\n     * @param positionFunction is a simple JS function what is passed the mesh `positions` array. It doesn't need to return anything\r\n     * @param computeNormals is a boolean (default true) to enable/disable the mesh normal recomputation after the vertex position update\r\n     * @returns the current mesh\r\n     */\r\n    public updateMeshPositions(positionFunction: (data: FloatArray) => void, computeNormals: boolean = true): Mesh {\r\n        const positions = this.getVerticesData(VertexBuffer.PositionKind);\r\n        if (!positions) {\r\n            return this;\r\n        }\r\n\r\n        positionFunction(positions);\r\n        this.updateVerticesData(VertexBuffer.PositionKind, positions, false, false);\r\n\r\n        if (computeNormals) {\r\n            const indices = this.getIndices();\r\n            const normals = this.getVerticesData(VertexBuffer.NormalKind);\r\n\r\n            if (!normals) {\r\n                return this;\r\n            }\r\n\r\n            VertexData.ComputeNormals(positions, indices, normals);\r\n            this.updateVerticesData(VertexBuffer.NormalKind, normals, false, false);\r\n        }\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Creates a un-shared specific occurence of the geometry for the mesh.\r\n     * @returns the current mesh\r\n     */\r\n    public makeGeometryUnique(): Mesh {\r\n        if (!this._geometry) {\r\n            return this;\r\n        }\r\n\r\n        if (this._geometry.meshes.length === 1) {\r\n            return this;\r\n        }\r\n\r\n        const oldGeometry = this._geometry;\r\n        const geometry = this._geometry.copy(Geometry.RandomId());\r\n        oldGeometry.releaseForMesh(this, true);\r\n        geometry.applyToMesh(this);\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets the index buffer of this mesh.\r\n     * @param indexBuffer Defines the index buffer to use for this mesh\r\n     * @param totalVertices Defines the total number of vertices used by the buffer\r\n     * @param totalIndices Defines the total number of indices in the index buffer\r\n     * @param is32Bits Defines if the indices are 32 bits. If null (default), the value is guessed from the number of vertices\r\n     */\r\n    public setIndexBuffer(indexBuffer: DataBuffer, totalVertices: number, totalIndices: number, is32Bits: Nullable<boolean> = null): void {\r\n        let geometry = this._geometry;\r\n        if (!geometry) {\r\n            geometry = new Geometry(Geometry.RandomId(), this.getScene(), undefined, undefined, this);\r\n        }\r\n        geometry.setIndexBuffer(indexBuffer, totalVertices, totalIndices, is32Bits);\r\n    }\r\n\r\n    /**\r\n     * Set the index buffer of this mesh\r\n     * @param indices defines the source data\r\n     * @param totalVertices defines the total number of vertices referenced by this index data (can be null)\r\n     * @param updatable defines if the updated index buffer must be flagged as updatable (default is false)\r\n     * @param dontForceSubMeshRecreation defines a boolean indicating that we don't want to force the recreation of sub-meshes if we don't have to (false by default)\r\n     * @returns the current mesh\r\n     */\r\n    public override setIndices(indices: IndicesArray, totalVertices: Nullable<number> = null, updatable: boolean = false, dontForceSubMeshRecreation = false): AbstractMesh {\r\n        if (!this._geometry) {\r\n            const vertexData = new VertexData();\r\n            vertexData.indices = indices;\r\n\r\n            const scene = this.getScene();\r\n\r\n            new Geometry(Geometry.RandomId(), scene, vertexData, updatable, this);\r\n        } else {\r\n            this._geometry.setIndices(indices, totalVertices, updatable, dontForceSubMeshRecreation);\r\n        }\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Update the current index buffer\r\n     * @param indices defines the source data\r\n     * @param offset defines the offset in the index buffer where to store the new data (can be null)\r\n     * @param gpuMemoryOnly defines a boolean indicating that only the GPU memory must be updated leaving the CPU version of the indices unchanged (false by default)\r\n     * @returns the current mesh\r\n     */\r\n    public override updateIndices(indices: IndicesArray, offset?: number, gpuMemoryOnly = false): AbstractMesh {\r\n        if (!this._geometry) {\r\n            return this;\r\n        }\r\n\r\n        this._geometry.updateIndices(indices, offset, gpuMemoryOnly);\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Invert the geometry to move from a right handed system to a left handed one.\r\n     * @returns the current mesh\r\n     */\r\n    public toLeftHanded(): Mesh {\r\n        if (!this._geometry) {\r\n            return this;\r\n        }\r\n        this._geometry.toLeftHanded();\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _bind(subMesh: SubMesh, effect: Effect, fillMode: number, allowInstancedRendering = true): Mesh {\r\n        if (!this._geometry) {\r\n            return this;\r\n        }\r\n\r\n        const engine = this.getScene().getEngine();\r\n\r\n        // Wireframe\r\n        let indexToBind;\r\n        if (this._unIndexed) {\r\n            switch (this._getRenderingFillMode(fillMode)) {\r\n                case Material.WireFrameFillMode:\r\n                    indexToBind = subMesh._getLinesIndexBuffer(<IndicesArray>this.getIndices(), engine);\r\n                    break;\r\n                default:\r\n                    indexToBind = null;\r\n                    break;\r\n            }\r\n        } else {\r\n            switch (this._getRenderingFillMode(fillMode)) {\r\n                case Material.PointFillMode:\r\n                    indexToBind = null;\r\n                    break;\r\n                case Material.WireFrameFillMode:\r\n                    indexToBind = subMesh._getLinesIndexBuffer(<IndicesArray>this.getIndices(), engine);\r\n                    break;\r\n                default:\r\n                case Material.TriangleFillMode:\r\n                    indexToBind = this._geometry.getIndexBuffer();\r\n                    break;\r\n            }\r\n        }\r\n\r\n        return this._bindDirect(effect, indexToBind, allowInstancedRendering);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _bindDirect(effect: Effect, indexToBind: Nullable<DataBuffer>, allowInstancedRendering = true): Mesh {\r\n        if (!this._geometry) {\r\n            return this;\r\n        }\r\n\r\n        // Morph targets\r\n        if (this.morphTargetManager && this.morphTargetManager.isUsingTextureForTargets) {\r\n            this.morphTargetManager._bind(effect);\r\n        }\r\n\r\n        // VBOs\r\n        if (!allowInstancedRendering || !this._userInstancedBuffersStorage || this.hasThinInstances) {\r\n            this._geometry._bind(effect, indexToBind);\r\n        } else {\r\n            if (\r\n                this._instanceDataStorage.engine.isWebGPU &&\r\n                this._userInstancedBuffersStorage.renderPasses &&\r\n                this._userInstancedBuffersStorage.renderPasses[this._instanceDataStorage.engine.currentRenderPassId]\r\n            ) {\r\n                const vertexBuffers = this._userInstancedBuffersStorage.renderPasses[this._instanceDataStorage.engine.currentRenderPassId];\r\n                for (const kind in vertexBuffers) {\r\n                    this._userInstancedBuffersStorage.vertexBuffers[kind] = vertexBuffers[kind];\r\n                }\r\n            }\r\n            this._geometry._bind(effect, indexToBind, this._userInstancedBuffersStorage.vertexBuffers, this._userInstancedBuffersStorage.vertexArrayObjects);\r\n        }\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _draw(subMesh: SubMesh, fillMode: number, instancesCount?: number): Mesh {\r\n        if (!this._geometry || !this._geometry.getVertexBuffers() || (!this._unIndexed && !this._geometry.getIndexBuffer())) {\r\n            return this;\r\n        }\r\n\r\n        if (this._internalMeshDataInfo._onBeforeDrawObservable) {\r\n            this._internalMeshDataInfo._onBeforeDrawObservable.notifyObservers(this);\r\n        }\r\n\r\n        const scene = this.getScene();\r\n        const engine = scene.getEngine();\r\n\r\n        if ((this._unIndexed && fillMode !== Material.WireFrameFillMode) || fillMode == Material.PointFillMode) {\r\n            // or triangles as points\r\n            engine.drawArraysType(fillMode, subMesh.verticesStart, subMesh.verticesCount, this.forcedInstanceCount || instancesCount);\r\n        } else if (fillMode == Material.WireFrameFillMode) {\r\n            // Triangles as wireframe\r\n            engine.drawElementsType(fillMode, 0, subMesh._linesIndexCount, this.forcedInstanceCount || instancesCount);\r\n        } else {\r\n            engine.drawElementsType(fillMode, subMesh.indexStart, subMesh.indexCount, this.forcedInstanceCount || instancesCount);\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Registers for this mesh a javascript function called just before the rendering process\r\n     * @param func defines the function to call before rendering this mesh\r\n     * @returns the current mesh\r\n     */\r\n    public registerBeforeRender(func: (mesh: AbstractMesh) => void): Mesh {\r\n        this.onBeforeRenderObservable.add(func);\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Disposes a previously registered javascript function called before the rendering\r\n     * @param func defines the function to remove\r\n     * @returns the current mesh\r\n     */\r\n    public unregisterBeforeRender(func: (mesh: AbstractMesh) => void): Mesh {\r\n        this.onBeforeRenderObservable.removeCallback(func);\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Registers for this mesh a javascript function called just after the rendering is complete\r\n     * @param func defines the function to call after rendering this mesh\r\n     * @returns the current mesh\r\n     */\r\n    public registerAfterRender(func: (mesh: AbstractMesh) => void): Mesh {\r\n        this.onAfterRenderObservable.add(func);\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Disposes a previously registered javascript function called after the rendering.\r\n     * @param func defines the function to remove\r\n     * @returns the current mesh\r\n     */\r\n    public unregisterAfterRender(func: (mesh: AbstractMesh) => void): Mesh {\r\n        this.onAfterRenderObservable.removeCallback(func);\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getInstancesRenderList(subMeshId: number, isReplacementMode: boolean = false): _InstancesBatch {\r\n        const instanceDataStorage = this._getInstanceDataStorage();\r\n        if (this._instanceDataStorage.isFrozen) {\r\n            if (isReplacementMode) {\r\n                instanceDataStorage.batchCacheReplacementModeInFrozenMode.hardwareInstancedRendering[subMeshId] = false;\r\n                instanceDataStorage.batchCacheReplacementModeInFrozenMode.renderSelf[subMeshId] = true;\r\n                return instanceDataStorage.batchCacheReplacementModeInFrozenMode;\r\n            }\r\n            if (instanceDataStorage.previousBatch) {\r\n                return instanceDataStorage.previousBatch;\r\n            }\r\n        }\r\n        const scene = this.getScene();\r\n        const isInIntermediateRendering = scene._isInIntermediateRendering();\r\n        const onlyForInstances = isInIntermediateRendering\r\n            ? this._internalAbstractMeshDataInfo._onlyForInstancesIntermediate\r\n            : this._internalAbstractMeshDataInfo._onlyForInstances;\r\n        const batchCache = instanceDataStorage.batchCache;\r\n        batchCache.mustReturn = false;\r\n        batchCache.renderSelf[subMeshId] = isReplacementMode || (!onlyForInstances && this.isEnabled() && this.isVisible);\r\n        batchCache.visibleInstances[subMeshId] = null;\r\n\r\n        if (instanceDataStorage.visibleInstances && !isReplacementMode) {\r\n            const visibleInstances = instanceDataStorage.visibleInstances;\r\n            const currentRenderId = scene.getRenderId();\r\n            const defaultRenderId = isInIntermediateRendering ? visibleInstances.intermediateDefaultRenderId : visibleInstances.defaultRenderId;\r\n            batchCache.visibleInstances[subMeshId] = visibleInstances[currentRenderId];\r\n\r\n            if (!batchCache.visibleInstances[subMeshId] && defaultRenderId) {\r\n                batchCache.visibleInstances[subMeshId] = visibleInstances[defaultRenderId];\r\n            }\r\n        }\r\n        batchCache.hardwareInstancedRendering[subMeshId] =\r\n            !isReplacementMode &&\r\n            this._instanceDataStorage.hardwareInstancedRendering &&\r\n            batchCache.visibleInstances[subMeshId] !== null &&\r\n            batchCache.visibleInstances[subMeshId] !== undefined;\r\n        instanceDataStorage.previousBatch = batchCache;\r\n\r\n        return batchCache;\r\n    }\r\n\r\n    /**\r\n     * This method will also draw the instances if fillMode and effect are passed\r\n     * @internal\r\n     */\r\n    public _updateInstancedBuffers(subMesh: SubMesh, batch: _InstancesBatch, currentInstancesBufferSize: number, engine: AbstractEngine, fillMode?: number, effect?: Effect) {\r\n        const visibleInstances = batch.visibleInstances[subMesh._id];\r\n        const visibleInstanceCount = visibleInstances ? visibleInstances.length : 0;\r\n\r\n        const instanceStorage = batch.parent;\r\n        const instanceDataStorage = this._instanceDataStorage;\r\n        let instancesBuffer = instanceStorage.instancesBuffer;\r\n        let instancesPreviousBuffer = instanceStorage.instancesPreviousBuffer;\r\n\r\n        let offset = 0;\r\n        let instancesCount = 0;\r\n\r\n        const renderSelf = batch.renderSelf[subMesh._id];\r\n\r\n        const needUpdateBuffer =\r\n            !instancesBuffer ||\r\n            currentInstancesBufferSize !== instanceStorage.instancesBufferSize ||\r\n            (this._scene.needsPreviousWorldMatrices && !instanceStorage.instancesPreviousBuffer);\r\n\r\n        if (!this._instanceDataStorage.manualUpdate && (!instanceDataStorage.isFrozen || needUpdateBuffer)) {\r\n            const world = this.getWorldMatrix();\r\n            if (renderSelf) {\r\n                if (this._scene.needsPreviousWorldMatrices) {\r\n                    if (!instanceDataStorage.masterMeshPreviousWorldMatrix) {\r\n                        instanceDataStorage.masterMeshPreviousWorldMatrix = world.clone();\r\n                        instanceDataStorage.masterMeshPreviousWorldMatrix.copyToArray(instanceStorage.instancesPreviousData, offset);\r\n                    } else {\r\n                        instanceDataStorage.masterMeshPreviousWorldMatrix.copyToArray(instanceStorage.instancesPreviousData, offset);\r\n                        instanceDataStorage.masterMeshPreviousWorldMatrix.copyFrom(world);\r\n                    }\r\n                }\r\n                world.copyToArray(instanceStorage.instancesData, offset);\r\n                offset += 16;\r\n                instancesCount++;\r\n            }\r\n\r\n            if (visibleInstances) {\r\n                if (Mesh.INSTANCEDMESH_SORT_TRANSPARENT && this._scene.activeCamera && subMesh.getMaterial()?.needAlphaBlendingForMesh(subMesh.getRenderingMesh())) {\r\n                    const cameraPosition = this._scene.activeCamera.globalPosition;\r\n                    for (let instanceIndex = 0; instanceIndex < visibleInstances.length; instanceIndex++) {\r\n                        const instanceMesh = visibleInstances[instanceIndex];\r\n                        instanceMesh._distanceToCamera = Vector3.Distance(instanceMesh.getBoundingInfo().boundingSphere.centerWorld, cameraPosition);\r\n                    }\r\n                    visibleInstances.sort((m1, m2) => {\r\n                        return m1._distanceToCamera > m2._distanceToCamera ? -1 : m1._distanceToCamera < m2._distanceToCamera ? 1 : 0;\r\n                    });\r\n                }\r\n                for (let instanceIndex = 0; instanceIndex < visibleInstances.length; instanceIndex++) {\r\n                    const instance = visibleInstances[instanceIndex];\r\n                    const matrix = instance.getWorldMatrix();\r\n                    matrix.copyToArray(instanceStorage.instancesData, offset);\r\n\r\n                    if (this._scene.needsPreviousWorldMatrices) {\r\n                        if (!instance._previousWorldMatrix) {\r\n                            instance._previousWorldMatrix = matrix.clone();\r\n                            instance._previousWorldMatrix.copyToArray(instanceStorage.instancesPreviousData, offset);\r\n                        } else {\r\n                            instance._previousWorldMatrix.copyToArray(instanceStorage.instancesPreviousData, offset);\r\n                            instance._previousWorldMatrix.copyFrom(matrix);\r\n                        }\r\n                    }\r\n\r\n                    offset += 16;\r\n                    instancesCount++;\r\n                }\r\n            }\r\n        } else {\r\n            instancesCount = (renderSelf ? 1 : 0) + visibleInstanceCount;\r\n        }\r\n\r\n        if (needUpdateBuffer) {\r\n            if (instancesBuffer) {\r\n                instancesBuffer.dispose();\r\n            }\r\n\r\n            if (instancesPreviousBuffer) {\r\n                instancesPreviousBuffer.dispose();\r\n            }\r\n\r\n            instancesBuffer = new Buffer(engine, instanceStorage.instancesData, true, 16, false, true);\r\n            instanceStorage.instancesBuffer = instancesBuffer;\r\n            if (!this._userInstancedBuffersStorage) {\r\n                this._userInstancedBuffersStorage = {\r\n                    data: {},\r\n                    vertexBuffers: {},\r\n                    strides: {},\r\n                    sizes: {},\r\n                    vertexArrayObjects: this.getEngine().getCaps().vertexArrayObject ? {} : undefined,\r\n                };\r\n            }\r\n\r\n            let vertexAndArrayObjectBuffers;\r\n            if (this._instanceDataStorage.engine.isWebGPU) {\r\n                if (!this._userInstancedBuffersStorage.renderPasses) {\r\n                    this._userInstancedBuffersStorage.renderPasses = {};\r\n                }\r\n\r\n                const currentRenderPassId = this._instanceDataStorage.engine.currentRenderPassId;\r\n                vertexAndArrayObjectBuffers = this._userInstancedBuffersStorage.renderPasses[currentRenderPassId];\r\n                if (!vertexAndArrayObjectBuffers) {\r\n                    this._userInstancedBuffersStorage.renderPasses[currentRenderPassId] = vertexAndArrayObjectBuffers = {};\r\n                }\r\n            } else {\r\n                vertexAndArrayObjectBuffers = this._userInstancedBuffersStorage.vertexBuffers;\r\n            }\r\n\r\n            vertexAndArrayObjectBuffers[\"world0\"] = instancesBuffer.createVertexBuffer(\"world0\", 0, 4);\r\n            vertexAndArrayObjectBuffers[\"world1\"] = instancesBuffer.createVertexBuffer(\"world1\", 4, 4);\r\n            vertexAndArrayObjectBuffers[\"world2\"] = instancesBuffer.createVertexBuffer(\"world2\", 8, 4);\r\n            vertexAndArrayObjectBuffers[\"world3\"] = instancesBuffer.createVertexBuffer(\"world3\", 12, 4);\r\n\r\n            if (this._scene.needsPreviousWorldMatrices) {\r\n                instancesPreviousBuffer = new Buffer(engine, instanceStorage.instancesPreviousData, true, 16, false, true);\r\n                instanceStorage.instancesPreviousBuffer = instancesPreviousBuffer;\r\n\r\n                vertexAndArrayObjectBuffers[\"previousWorld0\"] = instancesPreviousBuffer.createVertexBuffer(\"previousWorld0\", 0, 4);\r\n                vertexAndArrayObjectBuffers[\"previousWorld1\"] = instancesPreviousBuffer.createVertexBuffer(\"previousWorld1\", 4, 4);\r\n                vertexAndArrayObjectBuffers[\"previousWorld2\"] = instancesPreviousBuffer.createVertexBuffer(\"previousWorld2\", 8, 4);\r\n                vertexAndArrayObjectBuffers[\"previousWorld3\"] = instancesPreviousBuffer.createVertexBuffer(\"previousWorld3\", 12, 4);\r\n            }\r\n            this._invalidateInstanceVertexArrayObject();\r\n        } else {\r\n            if (!this._instanceDataStorage.isFrozen || this._instanceDataStorage.forceMatrixUpdates) {\r\n                instancesBuffer!.updateDirectly(instanceStorage.instancesData, 0, instancesCount);\r\n                if (this._scene.needsPreviousWorldMatrices && (!this._instanceDataStorage.manualUpdate || this._instanceDataStorage.previousManualUpdate)) {\r\n                    instancesPreviousBuffer!.updateDirectly(instanceStorage.instancesPreviousData, 0, instancesCount);\r\n                }\r\n            }\r\n        }\r\n\r\n        this._processInstancedBuffers(visibleInstances, renderSelf);\r\n\r\n        if (effect && fillMode !== undefined) {\r\n            // Stats\r\n            this.getScene()._activeIndices.addCount(subMesh.indexCount * instancesCount, false);\r\n\r\n            // Draw\r\n            if (engine._currentDrawContext) {\r\n                engine._currentDrawContext.useInstancing = true;\r\n            }\r\n            this._bind(subMesh, effect, fillMode);\r\n            this._draw(subMesh, fillMode, instancesCount);\r\n        }\r\n\r\n        // Write current matrices as previous matrices in case of manual update\r\n        // Default behaviour when previous matrices are not specified explicitly\r\n        // Will break if instances number/order changes\r\n        if (\r\n            this._scene.needsPreviousWorldMatrices &&\r\n            !needUpdateBuffer &&\r\n            this._instanceDataStorage.manualUpdate &&\r\n            (!this._instanceDataStorage.isFrozen || this._instanceDataStorage.forceMatrixUpdates) &&\r\n            !this._instanceDataStorage.previousManualUpdate\r\n        ) {\r\n            instancesPreviousBuffer!.updateDirectly(instanceStorage.instancesData, 0, instancesCount);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _renderWithInstances(subMesh: SubMesh, fillMode: number, batch: _InstancesBatch, effect: Effect, engine: AbstractEngine): Mesh {\r\n        const visibleInstances = batch.visibleInstances[subMesh._id];\r\n        const visibleInstanceCount = visibleInstances ? visibleInstances.length : 0;\r\n\r\n        const instanceStorage = batch.parent;\r\n        const currentInstancesBufferSize = instanceStorage.instancesBufferSize;\r\n        const matricesCount = visibleInstanceCount + 1;\r\n        const bufferSize = matricesCount * 16 * 4;\r\n\r\n        while (instanceStorage.instancesBufferSize < bufferSize) {\r\n            instanceStorage.instancesBufferSize *= 2;\r\n        }\r\n\r\n        if (!instanceStorage.instancesData || currentInstancesBufferSize != instanceStorage.instancesBufferSize) {\r\n            instanceStorage.instancesData = new Float32Array(instanceStorage.instancesBufferSize / 4);\r\n        }\r\n        if ((this._scene.needsPreviousWorldMatrices && !instanceStorage.instancesPreviousData) || currentInstancesBufferSize != instanceStorage.instancesBufferSize) {\r\n            instanceStorage.instancesPreviousData = new Float32Array(instanceStorage.instancesBufferSize / 4);\r\n        }\r\n\r\n        this._updateInstancedBuffers(subMesh, batch, currentInstancesBufferSize, engine, fillMode, effect);\r\n\r\n        engine.unbindInstanceAttributes();\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _renderWithThinInstances(subMesh: SubMesh, fillMode: number, effect: Effect, engine: AbstractEngine) {\r\n        // Stats\r\n        const instancesCount = this._thinInstanceDataStorage?.instancesCount ?? 0;\r\n\r\n        this.getScene()._activeIndices.addCount(subMesh.indexCount * instancesCount, false);\r\n\r\n        // Draw\r\n        if (engine._currentDrawContext) {\r\n            engine._currentDrawContext.useInstancing = true;\r\n        }\r\n        this._bind(subMesh, effect, fillMode);\r\n        this._draw(subMesh, fillMode, instancesCount);\r\n\r\n        // Write current matrices as previous matrices\r\n        // Default behaviour when previous matrices are not specified explicitly\r\n        // Will break if instances number/order changes\r\n        if (this._scene.needsPreviousWorldMatrices && !this._thinInstanceDataStorage.previousMatrixData && this._thinInstanceDataStorage.matrixData) {\r\n            if (!this._thinInstanceDataStorage.previousMatrixBuffer) {\r\n                this._thinInstanceDataStorage.previousMatrixBuffer = this._thinInstanceCreateMatrixBuffer(\"previousWorld\", this._thinInstanceDataStorage.matrixData, false);\r\n            } else {\r\n                this._thinInstanceDataStorage.previousMatrixBuffer.updateDirectly(this._thinInstanceDataStorage.matrixData, 0, instancesCount);\r\n            }\r\n        }\r\n\r\n        engine.unbindInstanceAttributes();\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public _processInstancedBuffers(visibleInstances: Nullable<InstancedMesh[]>, renderSelf: boolean) {\r\n        // Do nothing\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _processRendering(\r\n        renderingMesh: AbstractMesh,\r\n        subMesh: SubMesh,\r\n        effect: Effect,\r\n        fillMode: number,\r\n        batch: _InstancesBatch,\r\n        hardwareInstancedRendering: boolean,\r\n        onBeforeDraw: (isInstance: boolean, world: Matrix, effectiveMaterial?: Material) => void,\r\n        effectiveMaterial?: Material\r\n    ): Mesh {\r\n        const scene = this.getScene();\r\n        const engine = scene.getEngine();\r\n        fillMode = this._getRenderingFillMode(fillMode);\r\n\r\n        if (hardwareInstancedRendering && subMesh.getRenderingMesh().hasThinInstances) {\r\n            this._renderWithThinInstances(subMesh, fillMode, effect, engine);\r\n            return this;\r\n        }\r\n\r\n        if (hardwareInstancedRendering) {\r\n            this._renderWithInstances(subMesh, fillMode, batch, effect, engine);\r\n        } else {\r\n            if (engine._currentDrawContext) {\r\n                engine._currentDrawContext.useInstancing = false;\r\n            }\r\n\r\n            let instanceCount = 0;\r\n            if (batch.renderSelf[subMesh._id]) {\r\n                // Draw\r\n                if (onBeforeDraw) {\r\n                    onBeforeDraw(false, renderingMesh.getWorldMatrix(), effectiveMaterial);\r\n                }\r\n                instanceCount++;\r\n\r\n                this._draw(subMesh, fillMode, this._instanceDataStorage.overridenInstanceCount);\r\n            }\r\n\r\n            const visibleInstancesForSubMesh = batch.visibleInstances[subMesh._id];\r\n\r\n            if (visibleInstancesForSubMesh) {\r\n                const visibleInstanceCount = visibleInstancesForSubMesh.length;\r\n                instanceCount += visibleInstanceCount;\r\n\r\n                // Stats\r\n                for (let instanceIndex = 0; instanceIndex < visibleInstanceCount; instanceIndex++) {\r\n                    const instance = visibleInstancesForSubMesh[instanceIndex];\r\n\r\n                    // World\r\n                    const world = instance.getWorldMatrix();\r\n                    if (onBeforeDraw) {\r\n                        onBeforeDraw(true, world, effectiveMaterial);\r\n                    }\r\n                    // Draw\r\n                    this._draw(subMesh, fillMode);\r\n                }\r\n            }\r\n\r\n            // Stats\r\n            scene._activeIndices.addCount(subMesh.indexCount * instanceCount, false);\r\n        }\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public override _rebuild(dispose = false): void {\r\n        for (const renderPassId in this._instanceDataStorage.renderPasses) {\r\n            const instanceDataStorage = this._instanceDataStorage.renderPasses[renderPassId];\r\n            if (instanceDataStorage.instancesBuffer) {\r\n                // Dispose instance buffer to be recreated in _renderWithInstances when rendered\r\n                if (dispose) {\r\n                    instanceDataStorage.instancesBuffer.dispose();\r\n                }\r\n                instanceDataStorage.instancesBuffer = null;\r\n            }\r\n        }\r\n        if (this._userInstancedBuffersStorage) {\r\n            for (const kind in this._userInstancedBuffersStorage.vertexBuffers) {\r\n                const buffer = this._userInstancedBuffersStorage.vertexBuffers[kind];\r\n                if (buffer) {\r\n                    // Dispose instance buffer to be recreated in _renderWithInstances when rendered\r\n                    if (dispose) {\r\n                        buffer.dispose();\r\n                    }\r\n                    this._userInstancedBuffersStorage.vertexBuffers[kind] = null;\r\n                }\r\n            }\r\n            if (this._userInstancedBuffersStorage.vertexArrayObjects) {\r\n                this._userInstancedBuffersStorage.vertexArrayObjects = {};\r\n            }\r\n        }\r\n        this._internalMeshDataInfo._effectiveMaterial = null;\r\n        super._rebuild(dispose);\r\n    }\r\n\r\n    /** @internal */\r\n    public override _freeze() {\r\n        if (!this.subMeshes) {\r\n            return;\r\n        }\r\n\r\n        // Prepare batches\r\n        for (let index = 0; index < this.subMeshes.length; index++) {\r\n            this._getInstancesRenderList(index);\r\n        }\r\n\r\n        this._internalMeshDataInfo._effectiveMaterial = null;\r\n        this._instanceDataStorage.isFrozen = true;\r\n    }\r\n\r\n    /** @internal */\r\n    public override _unFreeze() {\r\n        this._instanceDataStorage.isFrozen = false;\r\n        for (const renderPassId in this._instanceDataStorage.renderPasses) {\r\n            const instanceDataStorage = this._instanceDataStorage.renderPasses[renderPassId];\r\n            instanceDataStorage.previousBatch = null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Triggers the draw call for the mesh (or a submesh), for a specific render pass id\r\n     * @param renderPassId defines the render pass id to use to draw the mesh / submesh. If not provided, use the current renderPassId of the engine.\r\n     * @param enableAlphaMode defines if alpha mode can be changed (default: false)\r\n     * @param effectiveMeshReplacement defines an optional mesh used to provide info for the rendering (default: undefined)\r\n     * @param subMesh defines the subMesh to render. If not provided, draw all mesh submeshes (default: undefined)\r\n     * @param checkFrustumCulling defines if frustum culling must be checked (default: true). If you know the mesh is in the frustum (or if you don't care!), you can pass false to optimize.\r\n     * @returns the current mesh\r\n     */\r\n    public renderWithRenderPassId(renderPassId?: number, enableAlphaMode?: boolean, effectiveMeshReplacement?: AbstractMesh, subMesh?: SubMesh, checkFrustumCulling = true) {\r\n        const engine = this._scene.getEngine();\r\n        const currentRenderPassId = engine.currentRenderPassId;\r\n\r\n        if (renderPassId !== undefined) {\r\n            engine.currentRenderPassId = renderPassId;\r\n        }\r\n\r\n        if (subMesh) {\r\n            if (!checkFrustumCulling || (checkFrustumCulling && subMesh.isInFrustum(this._scene._frustumPlanes))) {\r\n                this.render(subMesh, !!enableAlphaMode, effectiveMeshReplacement);\r\n            }\r\n        } else {\r\n            for (let s = 0; s < this.subMeshes.length; s++) {\r\n                const subMesh = this.subMeshes[s];\r\n                if (!checkFrustumCulling || (checkFrustumCulling && subMesh.isInFrustum(this._scene._frustumPlanes))) {\r\n                    this.render(subMesh, !!enableAlphaMode, effectiveMeshReplacement);\r\n                }\r\n            }\r\n        }\r\n\r\n        if (renderPassId !== undefined) {\r\n            engine.currentRenderPassId = currentRenderPassId;\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Render a complete mesh by going through all submeshes\r\n     * @returns the current mesh\r\n     * @see [simple test](https://playground.babylonjs.com/#5SPY1V#2)\r\n     * @see [perf test](https://playground.babylonjs.com/#5SPY1V#5)\r\n     */\r\n    public directRender(): Mesh {\r\n        if (!this.subMeshes) {\r\n            return this;\r\n        }\r\n\r\n        for (const submesh of this.subMeshes) {\r\n            this.render(submesh, false);\r\n        }\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Triggers the draw call for the mesh. Usually, you don't need to call this method by your own because the mesh rendering is handled by the scene rendering manager\r\n     * @param subMesh defines the subMesh to render\r\n     * @param enableAlphaMode defines if alpha mode can be changed\r\n     * @param effectiveMeshReplacement defines an optional mesh used to provide info for the rendering\r\n     * @returns the current mesh\r\n     */\r\n    public render(subMesh: SubMesh, enableAlphaMode: boolean, effectiveMeshReplacement?: AbstractMesh): Mesh {\r\n        const scene = this.getScene();\r\n\r\n        if (this._internalAbstractMeshDataInfo._isActiveIntermediate) {\r\n            this._internalAbstractMeshDataInfo._isActiveIntermediate = false;\r\n        } else {\r\n            this._internalAbstractMeshDataInfo._isActive = false;\r\n        }\r\n\r\n        const numActiveCameras = scene.activeCameras?.length ?? 0;\r\n        const canCheckOcclusionQuery = (numActiveCameras > 1 && scene.activeCamera === scene.activeCameras![0]) || numActiveCameras <= 1;\r\n\r\n        if (canCheckOcclusionQuery && this._checkOcclusionQuery() && !this._occlusionDataStorage.forceRenderingWhenOccluded) {\r\n            return this;\r\n        }\r\n\r\n        // Managing instances\r\n        const batch = this._getInstancesRenderList(subMesh._id, !!effectiveMeshReplacement);\r\n\r\n        if (batch.mustReturn) {\r\n            return this;\r\n        }\r\n\r\n        // Checking geometry state\r\n        if (!this._geometry || !this._geometry.getVertexBuffers() || (!this._unIndexed && !this._geometry.getIndexBuffer())) {\r\n            return this;\r\n        }\r\n\r\n        const engine = scene.getEngine();\r\n        let oldCameraMaxZ = 0;\r\n        let oldCamera: Nullable<Camera> = null;\r\n        if (this.ignoreCameraMaxZ && scene.activeCamera && !scene._isInIntermediateRendering()) {\r\n            oldCameraMaxZ = scene.activeCamera.maxZ;\r\n            oldCamera = scene.activeCamera;\r\n            scene.activeCamera.maxZ = 0;\r\n            scene.updateTransformMatrix(true);\r\n        }\r\n\r\n        if (this._internalMeshDataInfo._onBeforeRenderObservable) {\r\n            this._internalMeshDataInfo._onBeforeRenderObservable.notifyObservers(this);\r\n        }\r\n\r\n        const renderingMesh = subMesh.getRenderingMesh();\r\n        const hardwareInstancedRendering =\r\n            batch.hardwareInstancedRendering[subMesh._id] ||\r\n            renderingMesh.hasThinInstances ||\r\n            (!!this._userInstancedBuffersStorage && !subMesh.getMesh()._internalAbstractMeshDataInfo._actAsRegularMesh);\r\n        const instanceDataStorage = this._instanceDataStorage;\r\n\r\n        const material = subMesh.getMaterial();\r\n        if (!material) {\r\n            if (oldCamera) {\r\n                oldCamera.maxZ = oldCameraMaxZ;\r\n                scene.updateTransformMatrix(true);\r\n            }\r\n            return this;\r\n        }\r\n\r\n        // Material\r\n        if (!instanceDataStorage.isFrozen || !this._internalMeshDataInfo._effectiveMaterial || this._internalMeshDataInfo._effectiveMaterial !== material) {\r\n            if (material._storeEffectOnSubMeshes) {\r\n                if (!material.isReadyForSubMesh(this, subMesh, hardwareInstancedRendering)) {\r\n                    if (oldCamera) {\r\n                        oldCamera.maxZ = oldCameraMaxZ;\r\n                        scene.updateTransformMatrix(true);\r\n                    }\r\n                    return this;\r\n                }\r\n            } else if (!material.isReady(this, hardwareInstancedRendering)) {\r\n                if (oldCamera) {\r\n                    oldCamera.maxZ = oldCameraMaxZ;\r\n                    scene.updateTransformMatrix(true);\r\n                }\r\n                return this;\r\n            }\r\n\r\n            this._internalMeshDataInfo._effectiveMaterial = material;\r\n        } else if (\r\n            (material._storeEffectOnSubMeshes && !subMesh._drawWrapper?._wasPreviouslyReady) ||\r\n            (!material._storeEffectOnSubMeshes && !material._getDrawWrapper()._wasPreviouslyReady)\r\n        ) {\r\n            if (oldCamera) {\r\n                oldCamera.maxZ = oldCameraMaxZ;\r\n                scene.updateTransformMatrix(true);\r\n            }\r\n            return this;\r\n        }\r\n\r\n        // Alpha mode\r\n        if (enableAlphaMode) {\r\n            const effectiveMaterial = this._internalMeshDataInfo._effectiveMaterial;\r\n            if (effectiveMaterial.alphaModes.length === 1) {\r\n                engine.setAlphaMode(effectiveMaterial.alphaMode);\r\n            } else {\r\n                for (let i = 0; i < effectiveMaterial.alphaModes.length; i++) {\r\n                    const alphaMode = effectiveMaterial.alphaModes[i];\r\n                    engine.setAlphaMode(alphaMode !== undefined ? alphaMode : Constants.ALPHA_COMBINE, false, i);\r\n                }\r\n            }\r\n        }\r\n\r\n        let drawWrapper: Nullable<DrawWrapper>;\r\n        if (this._internalMeshDataInfo._effectiveMaterial._storeEffectOnSubMeshes) {\r\n            drawWrapper = subMesh._drawWrapper;\r\n        } else {\r\n            drawWrapper = this._internalMeshDataInfo._effectiveMaterial._getDrawWrapper();\r\n        }\r\n\r\n        const effect = drawWrapper?.effect ?? null;\r\n\r\n        for (const step of scene._beforeRenderingMeshStage) {\r\n            step.action(this, subMesh, batch, effect);\r\n        }\r\n\r\n        if (!drawWrapper || !effect) {\r\n            if (oldCamera) {\r\n                oldCamera.maxZ = oldCameraMaxZ;\r\n                scene.updateTransformMatrix(true);\r\n            }\r\n            return this;\r\n        }\r\n\r\n        const effectiveMesh = effectiveMeshReplacement || this;\r\n\r\n        let sideOrientation: Nullable<number>;\r\n\r\n        if (\r\n            !instanceDataStorage.isFrozen &&\r\n            (this._internalMeshDataInfo._effectiveMaterial.backFaceCulling ||\r\n                this._internalMeshDataInfo._effectiveMaterial.sideOrientation !== null ||\r\n                (this._internalMeshDataInfo._effectiveMaterial as any).twoSidedLighting)\r\n        ) {\r\n            // Note: if two sided lighting is enabled, we need to ensure that the normal will point in the right direction even if the determinant of the world matrix is negative\r\n            const mainDeterminant = effectiveMesh._getWorldMatrixDeterminant();\r\n            sideOrientation = this._internalMeshDataInfo._effectiveMaterial._getEffectiveOrientation(this);\r\n\r\n            if (mainDeterminant < 0) {\r\n                sideOrientation = sideOrientation === Material.ClockWiseSideOrientation ? Material.CounterClockWiseSideOrientation : Material.ClockWiseSideOrientation;\r\n            }\r\n            this._internalMeshDataInfo._effectiveSideOrientation = sideOrientation!;\r\n        } else if (this.hasInstances) {\r\n            sideOrientation = this._internalMeshDataInfo._effectiveSideOrientation;\r\n        }\r\n\r\n        const reverse = this._internalMeshDataInfo._effectiveMaterial._preBind(drawWrapper, this._internalMeshDataInfo._effectiveSideOrientation);\r\n\r\n        if (this._internalMeshDataInfo._effectiveMaterial.forceDepthWrite) {\r\n            engine.setDepthWrite(true);\r\n        }\r\n\r\n        // Bind\r\n        const effectiveMaterial = this._internalMeshDataInfo._effectiveMaterial;\r\n        const fillMode = effectiveMaterial.fillMode;\r\n\r\n        if (this._internalMeshDataInfo._onBeforeBindObservable) {\r\n            this._internalMeshDataInfo._onBeforeBindObservable.notifyObservers(this);\r\n        }\r\n\r\n        if (!hardwareInstancedRendering) {\r\n            // Binding will be done later because we need to add more info to the VB\r\n            this._bind(subMesh, effect, fillMode, false);\r\n        }\r\n\r\n        const world = effectiveMesh.getWorldMatrix();\r\n        if (effectiveMaterial._storeEffectOnSubMeshes) {\r\n            effectiveMaterial.bindForSubMesh(world, this, subMesh);\r\n        } else {\r\n            effectiveMaterial.bind(world, this);\r\n        }\r\n\r\n        if (!effectiveMaterial.backFaceCulling && effectiveMaterial.separateCullingPass) {\r\n            engine.setState(true, effectiveMaterial.zOffset, false, !reverse, effectiveMaterial.cullBackFaces, effectiveMaterial.stencil, effectiveMaterial.zOffsetUnits);\r\n            this._processRendering(this, subMesh, effect, fillMode, batch, hardwareInstancedRendering, this._onBeforeDraw, this._internalMeshDataInfo._effectiveMaterial);\r\n            engine.setState(true, effectiveMaterial.zOffset, false, reverse, effectiveMaterial.cullBackFaces, effectiveMaterial.stencil, effectiveMaterial.zOffsetUnits);\r\n\r\n            if (this._internalMeshDataInfo._onBetweenPassObservable) {\r\n                this._internalMeshDataInfo._onBetweenPassObservable.notifyObservers(subMesh);\r\n            }\r\n        }\r\n\r\n        // Draw\r\n        this._processRendering(this, subMesh, effect, fillMode, batch, hardwareInstancedRendering, this._onBeforeDraw, this._internalMeshDataInfo._effectiveMaterial);\r\n\r\n        // Unbind\r\n        this._internalMeshDataInfo._effectiveMaterial.unbind();\r\n\r\n        for (const step of scene._afterRenderingMeshStage) {\r\n            step.action(this, subMesh, batch, effect);\r\n        }\r\n\r\n        if (this._internalMeshDataInfo._onAfterRenderObservable) {\r\n            this._internalMeshDataInfo._onAfterRenderObservable.notifyObservers(this);\r\n        }\r\n\r\n        if (oldCamera) {\r\n            oldCamera.maxZ = oldCameraMaxZ;\r\n            scene.updateTransformMatrix(true);\r\n        }\r\n\r\n        if (scene.performancePriority === ScenePerformancePriority.Aggressive && !instanceDataStorage.isFrozen) {\r\n            this._freeze();\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    private _onBeforeDraw: (isInstance: boolean, world: Matrix, effectiveMaterial?: Material) => void;\r\n\r\n    /**\r\n     *   Renormalize the mesh and patch it up if there are no weights\r\n     *   Similar to normalization by adding the weights compute the reciprocal and multiply all elements, this wil ensure that everything adds to 1.\r\n     *   However in the case of zero weights then we set just a single influence to 1.\r\n     *   We check in the function for extra's present and if so we use the normalizeSkinWeightsWithExtras rather than the FourWeights version.\r\n     */\r\n    public cleanMatrixWeights(): void {\r\n        if (this.isVerticesDataPresent(VertexBuffer.MatricesWeightsKind)) {\r\n            if (this.isVerticesDataPresent(VertexBuffer.MatricesWeightsExtraKind)) {\r\n                this._normalizeSkinWeightsAndExtra();\r\n            } else {\r\n                this._normalizeSkinFourWeights();\r\n            }\r\n        }\r\n    }\r\n\r\n    // faster 4 weight version.\r\n    private _normalizeSkinFourWeights(): void {\r\n        const matricesWeights = <FloatArray>this.getVerticesData(VertexBuffer.MatricesWeightsKind);\r\n        const numWeights = matricesWeights.length;\r\n\r\n        for (let a = 0; a < numWeights; a += 4) {\r\n            // accumulate weights\r\n            const t = matricesWeights[a] + matricesWeights[a + 1] + matricesWeights[a + 2] + matricesWeights[a + 3];\r\n            // check for invalid weight and just set it to 1.\r\n            if (t === 0) {\r\n                matricesWeights[a] = 1;\r\n            } else {\r\n                // renormalize so everything adds to 1 use reciprocal\r\n                const recip = 1 / t;\r\n                matricesWeights[a] *= recip;\r\n                matricesWeights[a + 1] *= recip;\r\n                matricesWeights[a + 2] *= recip;\r\n                matricesWeights[a + 3] *= recip;\r\n            }\r\n        }\r\n        this.setVerticesData(VertexBuffer.MatricesWeightsKind, matricesWeights);\r\n    }\r\n    // handle special case of extra verts.  (in theory gltf can handle 12 influences)\r\n    private _normalizeSkinWeightsAndExtra(): void {\r\n        const matricesWeightsExtra = <FloatArray>this.getVerticesData(VertexBuffer.MatricesWeightsExtraKind);\r\n        const matricesWeights = <FloatArray>this.getVerticesData(VertexBuffer.MatricesWeightsKind);\r\n        const numWeights = matricesWeights.length;\r\n\r\n        for (let a = 0; a < numWeights; a += 4) {\r\n            // accumulate weights\r\n            let t = matricesWeights[a] + matricesWeights[a + 1] + matricesWeights[a + 2] + matricesWeights[a + 3];\r\n            t += matricesWeightsExtra[a] + matricesWeightsExtra[a + 1] + matricesWeightsExtra[a + 2] + matricesWeightsExtra[a + 3];\r\n            // check for invalid weight and just set it to 1.\r\n            if (t === 0) {\r\n                matricesWeights[a] = 1;\r\n            } else {\r\n                // renormalize so everything adds to 1 use reciprocal\r\n                const recip = 1 / t;\r\n                matricesWeights[a] *= recip;\r\n                matricesWeights[a + 1] *= recip;\r\n                matricesWeights[a + 2] *= recip;\r\n                matricesWeights[a + 3] *= recip;\r\n                // same goes for extras\r\n                matricesWeightsExtra[a] *= recip;\r\n                matricesWeightsExtra[a + 1] *= recip;\r\n                matricesWeightsExtra[a + 2] *= recip;\r\n                matricesWeightsExtra[a + 3] *= recip;\r\n            }\r\n        }\r\n        this.setVerticesData(VertexBuffer.MatricesWeightsKind, matricesWeights);\r\n        this.setVerticesData(VertexBuffer.MatricesWeightsKind, matricesWeightsExtra);\r\n    }\r\n\r\n    /**\r\n     * ValidateSkinning is used to determine that a mesh has valid skinning data along with skin metrics, if missing weights,\r\n     * or not normalized it is returned as invalid mesh the string can be used for console logs, or on screen messages to let\r\n     * the user know there was an issue with importing the mesh\r\n     * @returns a validation object with skinned, valid and report string\r\n     */\r\n    public validateSkinning(): { skinned: boolean; valid: boolean; report: string } {\r\n        const matricesWeightsExtra = <FloatArray>this.getVerticesData(VertexBuffer.MatricesWeightsExtraKind);\r\n        const matricesWeights = <FloatArray>this.getVerticesData(VertexBuffer.MatricesWeightsKind);\r\n        if (matricesWeights === null || this.skeleton == null) {\r\n            return { skinned: false, valid: true, report: \"not skinned\" };\r\n        }\r\n\r\n        const numWeights = matricesWeights.length;\r\n        let numberNotSorted: number = 0;\r\n        let missingWeights: number = 0;\r\n        let maxUsedWeights: number = 0;\r\n        let numberNotNormalized: number = 0;\r\n        const numInfluences: number = matricesWeightsExtra === null ? 4 : 8;\r\n        const usedWeightCounts: number[] = [];\r\n        for (let a = 0; a <= numInfluences; a++) {\r\n            usedWeightCounts[a] = 0;\r\n        }\r\n        const toleranceEpsilon: number = 0.001;\r\n\r\n        for (let a = 0; a < numWeights; a += 4) {\r\n            let lastWeight: number = matricesWeights[a];\r\n            let t = lastWeight;\r\n            let usedWeights: number = t === 0 ? 0 : 1;\r\n\r\n            for (let b = 1; b < numInfluences; b++) {\r\n                const d = b < 4 ? matricesWeights[a + b] : matricesWeightsExtra[a + b - 4];\r\n                if (d > lastWeight) {\r\n                    numberNotSorted++;\r\n                }\r\n                if (d !== 0) {\r\n                    usedWeights++;\r\n                }\r\n                t += d;\r\n                lastWeight = d;\r\n            }\r\n            // count the buffer weights usage\r\n            usedWeightCounts[usedWeights]++;\r\n\r\n            // max influences\r\n            if (usedWeights > maxUsedWeights) {\r\n                maxUsedWeights = usedWeights;\r\n            }\r\n\r\n            // check for invalid weight and just set it to 1.\r\n            if (t === 0) {\r\n                missingWeights++;\r\n            } else {\r\n                // renormalize so everything adds to 1 use reciprocal\r\n                const recip = 1 / t;\r\n                let tolerance = 0;\r\n                for (let b = 0; b < numInfluences; b++) {\r\n                    if (b < 4) {\r\n                        tolerance += Math.abs(matricesWeights[a + b] - matricesWeights[a + b] * recip);\r\n                    } else {\r\n                        tolerance += Math.abs(matricesWeightsExtra[a + b - 4] - matricesWeightsExtra[a + b - 4] * recip);\r\n                    }\r\n                }\r\n                // arbitrary epsilon value for dictating not normalized\r\n                if (tolerance > toleranceEpsilon) {\r\n                    numberNotNormalized++;\r\n                }\r\n            }\r\n        }\r\n\r\n        // validate bone indices are in range of the skeleton\r\n        const numBones: number = this.skeleton.bones.length;\r\n        const matricesIndices = <FloatArray>this.getVerticesData(VertexBuffer.MatricesIndicesKind);\r\n        const matricesIndicesExtra = <FloatArray>this.getVerticesData(VertexBuffer.MatricesIndicesExtraKind);\r\n        let numBadBoneIndices: number = 0;\r\n        for (let a = 0; a < numWeights; a += 4) {\r\n            for (let b = 0; b < numInfluences; b++) {\r\n                const index = b < 4 ? matricesIndices[a + b] : matricesIndicesExtra[a + b - 4];\r\n                if (index >= numBones || index < 0) {\r\n                    numBadBoneIndices++;\r\n                }\r\n            }\r\n        }\r\n\r\n        // log mesh stats\r\n        const output =\r\n            \"Number of Weights = \" +\r\n            numWeights / 4 +\r\n            \"\\nMaximum influences = \" +\r\n            maxUsedWeights +\r\n            \"\\nMissing Weights = \" +\r\n            missingWeights +\r\n            \"\\nNot Sorted = \" +\r\n            numberNotSorted +\r\n            \"\\nNot Normalized = \" +\r\n            numberNotNormalized +\r\n            \"\\nWeightCounts = [\" +\r\n            usedWeightCounts +\r\n            \"]\" +\r\n            \"\\nNumber of bones = \" +\r\n            numBones +\r\n            \"\\nBad Bone Indices = \" +\r\n            numBadBoneIndices;\r\n\r\n        return { skinned: true, valid: missingWeights === 0 && numberNotNormalized === 0 && numBadBoneIndices === 0, report: output };\r\n    }\r\n\r\n    /** @internal */\r\n    public _checkDelayState(): Mesh {\r\n        const scene = this.getScene();\r\n        if (this._geometry) {\r\n            this._geometry.load(scene);\r\n        } else if (this.delayLoadState === Constants.DELAYLOADSTATE_NOTLOADED) {\r\n            this.delayLoadState = Constants.DELAYLOADSTATE_LOADING;\r\n\r\n            this._queueLoad(scene);\r\n        }\r\n        return this;\r\n    }\r\n\r\n    private _queueLoad(scene: Scene): Mesh {\r\n        scene.addPendingData(this);\r\n\r\n        const getBinaryData = this.delayLoadingFile.indexOf(\".babylonbinarymeshdata\") !== -1;\r\n\r\n        Tools.LoadFile(\r\n            this.delayLoadingFile,\r\n            (data) => {\r\n                if (data instanceof ArrayBuffer) {\r\n                    this._delayLoadingFunction(data, this);\r\n                } else {\r\n                    this._delayLoadingFunction(JSON.parse(data), this);\r\n                }\r\n\r\n                for (const instance of this.instances) {\r\n                    instance.refreshBoundingInfo();\r\n                    instance._syncSubMeshes();\r\n                }\r\n\r\n                this.delayLoadState = Constants.DELAYLOADSTATE_LOADED;\r\n                scene.removePendingData(this);\r\n            },\r\n            () => {},\r\n            scene.offlineProvider,\r\n            getBinaryData\r\n        );\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Returns `true` if the mesh is within the frustum defined by the passed array of planes.\r\n     * A mesh is in the frustum if its bounding box intersects the frustum\r\n     * @param frustumPlanes defines the frustum to test\r\n     * @returns true if the mesh is in the frustum planes\r\n     */\r\n    public override isInFrustum(frustumPlanes: Plane[]): boolean {\r\n        if (this.delayLoadState === Constants.DELAYLOADSTATE_LOADING) {\r\n            return false;\r\n        }\r\n\r\n        if (!super.isInFrustum(frustumPlanes)) {\r\n            return false;\r\n        }\r\n\r\n        this._checkDelayState();\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Sets the mesh material by the material or multiMaterial `id` property\r\n     * @param id is a string identifying the material or the multiMaterial\r\n     * @returns the current mesh\r\n     */\r\n    public setMaterialById(id: string): Mesh {\r\n        const materials = this.getScene().materials;\r\n        let index: number;\r\n        for (index = materials.length - 1; index > -1; index--) {\r\n            if (materials[index].id === id) {\r\n                this.material = materials[index];\r\n                return this;\r\n            }\r\n        }\r\n\r\n        // Multi\r\n        const multiMaterials = this.getScene().multiMaterials;\r\n        for (index = multiMaterials.length - 1; index > -1; index--) {\r\n            if (multiMaterials[index].id === id) {\r\n                this.material = multiMaterials[index];\r\n                return this;\r\n            }\r\n        }\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Returns as a new array populated with the mesh material and/or skeleton, if any.\r\n     * @returns an array of IAnimatable\r\n     */\r\n    public getAnimatables(): IAnimatable[] {\r\n        const results: IAnimatable[] = [];\r\n\r\n        if (this.material) {\r\n            results.push(this.material);\r\n        }\r\n\r\n        if (this.skeleton) {\r\n            results.push(this.skeleton);\r\n        }\r\n\r\n        return results;\r\n    }\r\n\r\n    /**\r\n     * Modifies the mesh geometry according to the passed transformation matrix.\r\n     * This method returns nothing, but it really modifies the mesh even if it's originally not set as updatable.\r\n     * The mesh normals are modified using the same transformation.\r\n     * Note that, under the hood, this method sets a new VertexBuffer each call.\r\n     * @param transform defines the transform matrix to use\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/transforms/center_origin/bakingTransforms\r\n     * @returns the current mesh\r\n     */\r\n    public bakeTransformIntoVertices(transform: DeepImmutable<Matrix>): Mesh {\r\n        // Position\r\n        if (!this.isVerticesDataPresent(VertexBuffer.PositionKind)) {\r\n            return this;\r\n        }\r\n\r\n        const submeshes = this.subMeshes.splice(0);\r\n\r\n        this._resetPointsArrayCache();\r\n\r\n        let data = <FloatArray>this.getVerticesData(VertexBuffer.PositionKind);\r\n\r\n        const temp = Vector3.Zero();\r\n        let index: number;\r\n        for (index = 0; index < data.length; index += 3) {\r\n            Vector3.TransformCoordinatesFromFloatsToRef(data[index], data[index + 1], data[index + 2], transform, temp).toArray(data, index);\r\n        }\r\n\r\n        this.setVerticesData(VertexBuffer.PositionKind, data, (<VertexBuffer>this.getVertexBuffer(VertexBuffer.PositionKind)).isUpdatable());\r\n\r\n        // Normals\r\n        if (this.isVerticesDataPresent(VertexBuffer.NormalKind)) {\r\n            data = <FloatArray>this.getVerticesData(VertexBuffer.NormalKind);\r\n            for (index = 0; index < data.length; index += 3) {\r\n                Vector3.TransformNormalFromFloatsToRef(data[index], data[index + 1], data[index + 2], transform, temp)\r\n                    .normalize()\r\n                    .toArray(data, index);\r\n            }\r\n            this.setVerticesData(VertexBuffer.NormalKind, data, (<VertexBuffer>this.getVertexBuffer(VertexBuffer.NormalKind)).isUpdatable());\r\n        }\r\n\r\n        // Tangents\r\n        if (this.isVerticesDataPresent(VertexBuffer.TangentKind)) {\r\n            data = <FloatArray>this.getVerticesData(VertexBuffer.TangentKind);\r\n            for (index = 0; index < data.length; index += 4) {\r\n                Vector3.TransformNormalFromFloatsToRef(data[index], data[index + 1], data[index + 2], transform, temp)\r\n                    .normalize()\r\n                    .toArray(data, index);\r\n            }\r\n            this.setVerticesData(VertexBuffer.TangentKind, data, (<VertexBuffer>this.getVertexBuffer(VertexBuffer.TangentKind)).isUpdatable());\r\n        }\r\n\r\n        // flip faces?\r\n        if (transform.determinant() < 0) {\r\n            this.flipFaces();\r\n        }\r\n\r\n        // Restore submeshes\r\n        this.releaseSubMeshes();\r\n        this.subMeshes = submeshes;\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Modifies the mesh geometry according to its own current World Matrix.\r\n     * The mesh World Matrix is then reset.\r\n     * This method returns nothing but really modifies the mesh even if it's originally not set as updatable.\r\n     * Note that, under the hood, this method sets a new VertexBuffer each call.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/transforms/center_origin/bakingTransforms\r\n     * @param bakeIndependentlyOfChildren indicates whether to preserve all child nodes' World Matrix during baking\r\n     * @param forceUnique indicates whether to force the mesh geometry to be unique\r\n     * @returns the current mesh\r\n     */\r\n    public bakeCurrentTransformIntoVertices(bakeIndependentlyOfChildren: boolean = true, forceUnique: boolean = false): Mesh {\r\n        if (forceUnique) {\r\n            this.makeGeometryUnique();\r\n        }\r\n        this.bakeTransformIntoVertices(this.computeWorldMatrix(true));\r\n        this.resetLocalMatrix(bakeIndependentlyOfChildren);\r\n        return this;\r\n    }\r\n\r\n    // Cache\r\n\r\n    /** @internal */\r\n    public override get _positions(): Nullable<Vector3[]> {\r\n        return this._internalAbstractMeshDataInfo._positions || (this._geometry && this._geometry._positions) || null;\r\n    }\r\n\r\n    /** @internal */\r\n    public _resetPointsArrayCache(): Mesh {\r\n        if (this._geometry) {\r\n            this._geometry._resetPointsArrayCache();\r\n        }\r\n        return this;\r\n    }\r\n\r\n    /** @internal */\r\n    public override _generatePointsArray(): boolean {\r\n        if (this._geometry) {\r\n            return this._geometry._generatePointsArray();\r\n        }\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Returns a new Mesh object generated from the current mesh properties.\r\n     * This method must not get confused with createInstance()\r\n     * @param name is a string, the name given to the new mesh\r\n     * @param newParent can be any Node object (default `null`) or an instance of MeshCloneOptions. If the latter, doNotCloneChildren and clonePhysicsImpostor are unused.\r\n     * @param doNotCloneChildren allows/denies the recursive cloning of the original mesh children if any (default `false`)\r\n     * @param clonePhysicsImpostor allows/denies the cloning in the same time of the original mesh `body` used by the physics engine, if any (default `true`)\r\n     * @returns a new mesh\r\n     */\r\n    public override clone(name: string = \"\", newParent: Nullable<Node> | MeshCloneOptions = null, doNotCloneChildren?: boolean, clonePhysicsImpostor: boolean = true): Mesh {\r\n        if (newParent && (newParent as Node)._addToSceneRootNodes === undefined) {\r\n            const cloneOptions = newParent as MeshCloneOptions;\r\n\r\n            MeshCreationOptions.source = this;\r\n            MeshCreationOptions.doNotCloneChildren = cloneOptions.doNotCloneChildren;\r\n            MeshCreationOptions.clonePhysicsImpostor = cloneOptions.clonePhysicsImpostor;\r\n            MeshCreationOptions.cloneThinInstances = cloneOptions.cloneThinInstances;\r\n\r\n            return new Mesh(name, this.getScene(), MeshCreationOptions);\r\n        }\r\n\r\n        return new Mesh(name, this.getScene(), newParent as Nullable<Node>, this, doNotCloneChildren, clonePhysicsImpostor);\r\n    }\r\n\r\n    /**\r\n     * Releases resources associated with this mesh.\r\n     * @param doNotRecurse Set to true to not recurse into each children (recurse into each children by default)\r\n     * @param disposeMaterialAndTextures Set to true to also dispose referenced materials and textures (false by default)\r\n     */\r\n    public override dispose(doNotRecurse?: boolean, disposeMaterialAndTextures = false): void {\r\n        this.morphTargetManager = null;\r\n\r\n        if (this._geometry) {\r\n            this._geometry.releaseForMesh(this, true);\r\n        }\r\n\r\n        const internalDataInfo = this._internalMeshDataInfo;\r\n\r\n        if (internalDataInfo._onBeforeDrawObservable) {\r\n            internalDataInfo._onBeforeDrawObservable.clear();\r\n        }\r\n\r\n        if (internalDataInfo._onBeforeBindObservable) {\r\n            internalDataInfo._onBeforeBindObservable.clear();\r\n        }\r\n\r\n        if (internalDataInfo._onBeforeRenderObservable) {\r\n            internalDataInfo._onBeforeRenderObservable.clear();\r\n        }\r\n\r\n        if (internalDataInfo._onAfterRenderObservable) {\r\n            internalDataInfo._onAfterRenderObservable.clear();\r\n        }\r\n\r\n        if (internalDataInfo._onBetweenPassObservable) {\r\n            internalDataInfo._onBetweenPassObservable.clear();\r\n        }\r\n\r\n        // Sources\r\n        if (this._scene.useClonedMeshMap) {\r\n            if (internalDataInfo.meshMap) {\r\n                for (const uniqueId in internalDataInfo.meshMap) {\r\n                    const mesh = internalDataInfo.meshMap[uniqueId];\r\n                    if (mesh) {\r\n                        mesh._internalMeshDataInfo._source = null;\r\n                        internalDataInfo.meshMap[uniqueId] = undefined;\r\n                    }\r\n                }\r\n            }\r\n\r\n            if (internalDataInfo._source && internalDataInfo._source._internalMeshDataInfo.meshMap) {\r\n                internalDataInfo._source._internalMeshDataInfo.meshMap[this.uniqueId] = undefined;\r\n            }\r\n        } else {\r\n            const meshes = this.getScene().meshes;\r\n            for (const abstractMesh of meshes) {\r\n                const mesh = abstractMesh as Mesh;\r\n                if (mesh._internalMeshDataInfo && mesh._internalMeshDataInfo._source && mesh._internalMeshDataInfo._source === this) {\r\n                    mesh._internalMeshDataInfo._source = null;\r\n                }\r\n            }\r\n        }\r\n\r\n        internalDataInfo._source = null;\r\n\r\n        // Instances\r\n        this._disposeInstanceSpecificData();\r\n\r\n        // Thin instances\r\n        this._disposeThinInstanceSpecificData();\r\n\r\n        if (this._internalMeshDataInfo._checkReadinessObserver) {\r\n            this._scene.onBeforeRenderObservable.remove(this._internalMeshDataInfo._checkReadinessObserver);\r\n        }\r\n\r\n        super.dispose(doNotRecurse, disposeMaterialAndTextures);\r\n    }\r\n\r\n    /** @internal */\r\n    public _disposeInstanceSpecificData() {\r\n        // Do nothing\r\n    }\r\n\r\n    /** @internal */\r\n    public _disposeThinInstanceSpecificData() {\r\n        // Do nothing\r\n    }\r\n\r\n    /** @internal */\r\n    public _invalidateInstanceVertexArrayObject() {\r\n        // Do nothing\r\n    }\r\n\r\n    /**\r\n     * Modifies the mesh geometry according to a displacement map.\r\n     * A displacement map is a colored image. Each pixel color value (actually a gradient computed from red, green, blue values) will give the displacement to apply to each mesh vertex.\r\n     * The mesh must be set as updatable. Its internal geometry is directly modified, no new buffer are allocated.\r\n     * @param url is a string, the URL from the image file is to be downloaded.\r\n     * @param minHeight is the lower limit of the displacement.\r\n     * @param maxHeight is the upper limit of the displacement.\r\n     * @param onSuccess is an optional Javascript function to be called just after the mesh is modified. It is passed the modified mesh and must return nothing.\r\n     * @param uvOffset is an optional vector2 used to offset UV.\r\n     * @param uvScale is an optional vector2 used to scale UV.\r\n     * @param forceUpdate defines whether or not to force an update of the generated buffers. This is useful to apply on a deserialized model for instance.\r\n     * @param onError defines a callback called when an error occurs during the processing of the request.\r\n     * @returns the Mesh.\r\n     */\r\n    public applyDisplacementMap(\r\n        url: string,\r\n        minHeight: number,\r\n        maxHeight: number,\r\n        onSuccess?: (mesh: Mesh) => void,\r\n        uvOffset?: Vector2,\r\n        uvScale?: Vector2,\r\n        forceUpdate = false,\r\n        onError?: (message?: string, exception?: any) => void\r\n    ): Mesh {\r\n        const scene = this.getScene();\r\n\r\n        const onload = (img: HTMLImageElement | ImageBitmap) => {\r\n            // Getting height map data\r\n            const heightMapWidth = img.width;\r\n            const heightMapHeight = img.height;\r\n            const canvas = this.getEngine().createCanvas(heightMapWidth, heightMapHeight);\r\n            const context = <CanvasRenderingContext2D>canvas.getContext(\"2d\");\r\n\r\n            context.drawImage(img, 0, 0);\r\n\r\n            // Create VertexData from map data\r\n            //Cast is due to wrong definition in lib.d.ts from ts 1.3 - https://github.com/Microsoft/TypeScript/issues/949\r\n            const buffer = <Uint8Array>(<any>context.getImageData(0, 0, heightMapWidth, heightMapHeight).data);\r\n\r\n            this.applyDisplacementMapFromBuffer(buffer, heightMapWidth, heightMapHeight, minHeight, maxHeight, uvOffset, uvScale, forceUpdate);\r\n            //execute success callback, if set\r\n            if (onSuccess) {\r\n                onSuccess(this);\r\n            }\r\n        };\r\n\r\n        Tools.LoadImage(url, onload, onError ? onError : () => {}, scene.offlineProvider);\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Modifies the mesh geometry according to a displacementMap buffer.\r\n     * A displacement map is a colored image. Each pixel color value (actually a gradient computed from red, green, blue values) will give the displacement to apply to each mesh vertex.\r\n     * The mesh must be set as updatable. Its internal geometry is directly modified, no new buffer are allocated.\r\n     * @param buffer is a `Uint8Array` buffer containing series of `Uint8` lower than 255, the red, green, blue and alpha values of each successive pixel.\r\n     * @param heightMapWidth is the width of the buffer image.\r\n     * @param heightMapHeight is the height of the buffer image.\r\n     * @param minHeight is the lower limit of the displacement.\r\n     * @param maxHeight is the upper limit of the displacement.\r\n     * @param uvOffset is an optional vector2 used to offset UV.\r\n     * @param uvScale is an optional vector2 used to scale UV.\r\n     * @param forceUpdate defines whether or not to force an update of the generated buffers. This is useful to apply on a deserialized model for instance.\r\n     * @returns the Mesh.\r\n     */\r\n    public applyDisplacementMapFromBuffer(\r\n        buffer: Uint8Array,\r\n        heightMapWidth: number,\r\n        heightMapHeight: number,\r\n        minHeight: number,\r\n        maxHeight: number,\r\n        uvOffset?: Vector2,\r\n        uvScale?: Vector2,\r\n        forceUpdate = false\r\n    ): Mesh {\r\n        if (!this.isVerticesDataPresent(VertexBuffer.PositionKind) || !this.isVerticesDataPresent(VertexBuffer.NormalKind) || !this.isVerticesDataPresent(VertexBuffer.UVKind)) {\r\n            Logger.Warn(\"Cannot call applyDisplacementMap: Given mesh is not complete. Position, Normal or UV are missing\");\r\n            return this;\r\n        }\r\n\r\n        const positions = <FloatArray>this.getVerticesData(VertexBuffer.PositionKind, true, true);\r\n        const normals = <FloatArray>this.getVerticesData(VertexBuffer.NormalKind);\r\n        const uvs = <number[]>this.getVerticesData(VertexBuffer.UVKind);\r\n        let position = Vector3.Zero();\r\n        const normal = Vector3.Zero();\r\n        const uv = Vector2.Zero();\r\n\r\n        uvOffset = uvOffset || Vector2.Zero();\r\n        uvScale = uvScale || new Vector2(1, 1);\r\n\r\n        for (let index = 0; index < positions.length; index += 3) {\r\n            Vector3.FromArrayToRef(positions, index, position);\r\n            Vector3.FromArrayToRef(normals, index, normal);\r\n            Vector2.FromArrayToRef(uvs, (index / 3) * 2, uv);\r\n\r\n            // Compute height\r\n            const u = (Math.abs(uv.x * uvScale.x + (uvOffset.x % 1)) * (heightMapWidth - 1)) % heightMapWidth | 0;\r\n            const v = (Math.abs(uv.y * uvScale.y + (uvOffset.y % 1)) * (heightMapHeight - 1)) % heightMapHeight | 0;\r\n\r\n            const pos = (u + v * heightMapWidth) * 4;\r\n            const r = buffer[pos] / 255.0;\r\n            const g = buffer[pos + 1] / 255.0;\r\n            const b = buffer[pos + 2] / 255.0;\r\n\r\n            const gradient = r * 0.3 + g * 0.59 + b * 0.11;\r\n\r\n            normal.normalize();\r\n            normal.scaleInPlace(minHeight + (maxHeight - minHeight) * gradient);\r\n            position = position.add(normal);\r\n\r\n            position.toArray(positions, index);\r\n        }\r\n\r\n        VertexData.ComputeNormals(positions, this.getIndices(), normals);\r\n\r\n        if (forceUpdate) {\r\n            this.setVerticesData(VertexBuffer.PositionKind, positions);\r\n            this.setVerticesData(VertexBuffer.NormalKind, normals);\r\n            this.setVerticesData(VertexBuffer.UVKind, uvs);\r\n        } else {\r\n            this.updateVerticesData(VertexBuffer.PositionKind, positions);\r\n            this.updateVerticesData(VertexBuffer.NormalKind, normals);\r\n        }\r\n        return this;\r\n    }\r\n\r\n    private _getFlattenedNormals(indices: IndicesArray, positions: FloatArray): Float32Array {\r\n        const normals = new Float32Array(indices.length * 3);\r\n        let normalsCount = 0;\r\n\r\n        // Decide if normals should be flipped\r\n        const flipNormalGeneration =\r\n            this.sideOrientation === (this._scene.useRightHandedSystem ? Constants.MATERIAL_CounterClockWiseSideOrientation : Constants.MATERIAL_ClockWiseSideOrientation);\r\n\r\n        // Generate new normals\r\n        for (let index = 0; index < indices.length; index += 3) {\r\n            const p1 = Vector3.FromArray(positions, indices[index] * 3);\r\n            const p2 = Vector3.FromArray(positions, indices[index + 1] * 3);\r\n            const p3 = Vector3.FromArray(positions, indices[index + 2] * 3);\r\n\r\n            const p1p2 = p1.subtract(p2);\r\n            const p3p2 = p3.subtract(p2);\r\n\r\n            const normal = Vector3.Normalize(Vector3.Cross(p1p2, p3p2));\r\n            if (flipNormalGeneration) {\r\n                normal.scaleInPlace(-1);\r\n            }\r\n\r\n            // Store same normals for every vertex\r\n            for (let localIndex = 0; localIndex < 3; localIndex++) {\r\n                normals[normalsCount++] = normal.x;\r\n                normals[normalsCount++] = normal.y;\r\n                normals[normalsCount++] = normal.z;\r\n            }\r\n        }\r\n\r\n        return normals;\r\n    }\r\n\r\n    private _convertToUnIndexedMesh(flattenNormals: boolean = false): Mesh {\r\n        const kinds = this.getVerticesDataKinds().filter((kind) => !this.getVertexBuffer(kind)?.getIsInstanced());\r\n        const indices = this.getIndices()!;\r\n        const data: { [kind: string]: FloatArray } = {};\r\n\r\n        const separateVertices = (data: FloatArray, size: number): Float32Array => {\r\n            const newData = new Float32Array(indices.length * size);\r\n            let count = 0;\r\n            for (let index = 0; index < indices.length; index++) {\r\n                for (let offset = 0; offset < size; offset++) {\r\n                    newData[count++] = data[indices[index] * size + offset];\r\n                }\r\n            }\r\n            return newData;\r\n        };\r\n\r\n        // Save mesh bounding info\r\n        const meshBoundingInfo = this.getBoundingInfo();\r\n\r\n        // Save previous submeshes\r\n        const previousSubmeshes = this.geometry ? this.subMeshes.slice(0) : [];\r\n\r\n        // Cache vertex data\r\n        for (const kind of kinds) {\r\n            data[kind] = this.getVerticesData(kind)!;\r\n        }\r\n\r\n        // Update vertex data\r\n        for (const kind of kinds) {\r\n            const vertexBuffer = this.getVertexBuffer(kind)!;\r\n            const size = vertexBuffer.getSize();\r\n\r\n            if (flattenNormals && kind === VertexBuffer.NormalKind) {\r\n                const normals = this._getFlattenedNormals(indices, data[VertexBuffer.PositionKind]);\r\n                this.setVerticesData(VertexBuffer.NormalKind, normals, vertexBuffer.isUpdatable(), size);\r\n            } else {\r\n                this.setVerticesData(kind, separateVertices(data[kind], size), vertexBuffer.isUpdatable(), size);\r\n            }\r\n        }\r\n\r\n        // Update morph targets\r\n        if (this.morphTargetManager) {\r\n            for (let targetIndex = 0; targetIndex < this.morphTargetManager.numTargets; targetIndex++) {\r\n                const target = this.morphTargetManager.getTarget(targetIndex);\r\n\r\n                const positions = target.getPositions()!;\r\n                target.setPositions(separateVertices(positions, 3));\r\n\r\n                const normals = target.getNormals();\r\n                if (normals) {\r\n                    target.setNormals(flattenNormals ? this._getFlattenedNormals(indices, positions) : separateVertices(normals, 3));\r\n                }\r\n\r\n                const tangents = target.getTangents();\r\n                if (tangents) {\r\n                    target.setTangents(separateVertices(tangents, 3));\r\n                }\r\n\r\n                const uvs = target.getUVs();\r\n                if (uvs) {\r\n                    target.setUVs(separateVertices(uvs, 2));\r\n                }\r\n\r\n                const colors = target.getColors();\r\n                if (colors) {\r\n                    target.setColors(separateVertices(colors, 4));\r\n                }\r\n            }\r\n            this.morphTargetManager.synchronize();\r\n        }\r\n\r\n        // Update indices\r\n        for (let index = 0; index < indices.length; index++) {\r\n            indices[index] = index;\r\n        }\r\n        this.setIndices(indices);\r\n\r\n        this._unIndexed = true;\r\n\r\n        // Update submeshes\r\n        this.releaseSubMeshes();\r\n        for (const previousOne of previousSubmeshes) {\r\n            const boundingInfo = previousOne.getBoundingInfo();\r\n            const subMesh = SubMesh.AddToMesh(previousOne.materialIndex, previousOne.indexStart, previousOne.indexCount, previousOne.indexStart, previousOne.indexCount, this);\r\n            subMesh.setBoundingInfo(boundingInfo);\r\n        }\r\n\r\n        this.setBoundingInfo(meshBoundingInfo);\r\n\r\n        this.synchronizeInstances();\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Modify the mesh to get a flat shading rendering.\r\n     * This means each mesh facet will then have its own normals. Usually new vertices are added in the mesh geometry to get this result.\r\n     * Warning : the mesh is really modified even if not set originally as updatable and, under the hood, a new VertexBuffer is allocated.\r\n     * @returns current mesh\r\n     */\r\n    public convertToFlatShadedMesh(): Mesh {\r\n        return this._convertToUnIndexedMesh(true);\r\n    }\r\n\r\n    /**\r\n     * This method removes all the mesh indices and add new vertices (duplication) in order to unfold facets into buffers.\r\n     * In other words, more vertices, no more indices and a single bigger VBO.\r\n     * The mesh is really modified even if not set originally as updatable. Under the hood, a new VertexBuffer is allocated.\r\n     * @returns current mesh\r\n     */\r\n    public convertToUnIndexedMesh(): Mesh {\r\n        return this._convertToUnIndexedMesh();\r\n    }\r\n\r\n    /**\r\n     * Inverses facet orientations.\r\n     * Warning : the mesh is really modified even if not set originally as updatable. A new VertexBuffer is created under the hood each call.\r\n     * @param flipNormals will also inverts the normals\r\n     * @returns current mesh\r\n     */\r\n    public flipFaces(flipNormals: boolean = false): Mesh {\r\n        const vertexData = VertexData.ExtractFromMesh(this);\r\n        let i: number;\r\n        if (flipNormals && this.isVerticesDataPresent(VertexBuffer.NormalKind) && vertexData.normals) {\r\n            for (i = 0; i < vertexData.normals.length; i++) {\r\n                vertexData.normals[i] *= -1;\r\n            }\r\n            this.setVerticesData(VertexBuffer.NormalKind, vertexData.normals, this.isVertexBufferUpdatable(VertexBuffer.NormalKind));\r\n        }\r\n\r\n        if (vertexData.indices) {\r\n            let temp;\r\n            for (i = 0; i < vertexData.indices.length; i += 3) {\r\n                // reassign indices\r\n                temp = vertexData.indices[i + 1];\r\n                vertexData.indices[i + 1] = vertexData.indices[i + 2];\r\n                vertexData.indices[i + 2] = temp;\r\n            }\r\n            this.setIndices(vertexData.indices, null, this.isVertexBufferUpdatable(VertexBuffer.PositionKind), true);\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Increase the number of facets and hence vertices in a mesh\r\n     * Vertex normals are interpolated from existing vertex normals\r\n     * Warning : the mesh is really modified even if not set originally as updatable. A new VertexBuffer is created under the hood each call.\r\n     * @param numberPerEdge the number of new vertices to add to each edge of a facet, optional default 1\r\n     */\r\n    public increaseVertices(numberPerEdge: number = 1): void {\r\n        const vertexData = VertexData.ExtractFromMesh(this);\r\n        const currentIndices = vertexData.indices && !Array.isArray(vertexData.indices) && Array.from ? Array.from(vertexData.indices) : vertexData.indices;\r\n        const positions = vertexData.positions && !Array.isArray(vertexData.positions) && Array.from ? Array.from(vertexData.positions) : vertexData.positions;\r\n        const uvs = vertexData.uvs && !Array.isArray(vertexData.uvs) && Array.from ? Array.from(vertexData.uvs) : vertexData.uvs;\r\n        const normals = vertexData.normals && !Array.isArray(vertexData.normals) && Array.from ? Array.from(vertexData.normals) : vertexData.normals;\r\n\r\n        if (!currentIndices || !positions) {\r\n            Logger.Warn(\"Couldn't increase number of vertices : VertexData must contain at least indices and positions\");\r\n        } else {\r\n            vertexData.indices = currentIndices;\r\n            vertexData.positions = positions;\r\n            if (uvs) {\r\n                vertexData.uvs = uvs;\r\n            }\r\n            if (normals) {\r\n                vertexData.normals = normals;\r\n            }\r\n\r\n            const segments: number = numberPerEdge + 1; //segments per current facet edge, become sides of new facets\r\n            const tempIndices: Array<Array<number>> = [];\r\n            for (let i = 0; i < segments + 1; i++) {\r\n                tempIndices[i] = [];\r\n            }\r\n            let a: number; //vertex index of one end of a side\r\n            let b: number; //vertex index of other end of the side\r\n            const deltaPosition: Vector3 = new Vector3(0, 0, 0);\r\n            const deltaNormal: Vector3 = new Vector3(0, 0, 0);\r\n            const deltaUV: Vector2 = new Vector2(0, 0);\r\n            const indices: number[] = [];\r\n            const vertexIndex: number[] = [];\r\n            const side: Array<Array<Array<number>>> = [];\r\n            let len: number;\r\n            let positionPtr: number = positions.length;\r\n            let uvPtr: number;\r\n            if (uvs) {\r\n                uvPtr = uvs.length;\r\n            }\r\n            let normalsPtr: number;\r\n            if (normals) {\r\n                normalsPtr = normals.length;\r\n            }\r\n\r\n            for (let i = 0; i < currentIndices.length; i += 3) {\r\n                vertexIndex[0] = currentIndices[i];\r\n                vertexIndex[1] = currentIndices[i + 1];\r\n                vertexIndex[2] = currentIndices[i + 2];\r\n                for (let j = 0; j < 3; j++) {\r\n                    a = vertexIndex[j];\r\n                    b = vertexIndex[(j + 1) % 3];\r\n                    if (side[a] === undefined && side[b] === undefined) {\r\n                        side[a] = [];\r\n                        side[b] = [];\r\n                    } else {\r\n                        if (side[a] === undefined) {\r\n                            side[a] = [];\r\n                        }\r\n                        if (side[b] === undefined) {\r\n                            side[b] = [];\r\n                        }\r\n                    }\r\n                    if (side[a][b] === undefined && side[b][a] === undefined) {\r\n                        side[a][b] = [];\r\n                        deltaPosition.x = (positions[3 * b] - positions[3 * a]) / segments;\r\n                        deltaPosition.y = (positions[3 * b + 1] - positions[3 * a + 1]) / segments;\r\n                        deltaPosition.z = (positions[3 * b + 2] - positions[3 * a + 2]) / segments;\r\n                        if (normals) {\r\n                            deltaNormal.x = (normals[3 * b] - normals[3 * a]) / segments;\r\n                            deltaNormal.y = (normals[3 * b + 1] - normals[3 * a + 1]) / segments;\r\n                            deltaNormal.z = (normals[3 * b + 2] - normals[3 * a + 2]) / segments;\r\n                        }\r\n                        if (uvs) {\r\n                            deltaUV.x = (uvs[2 * b] - uvs[2 * a]) / segments;\r\n                            deltaUV.y = (uvs[2 * b + 1] - uvs[2 * a + 1]) / segments;\r\n                        }\r\n                        side[a][b].push(a);\r\n                        for (let k = 1; k < segments; k++) {\r\n                            side[a][b].push(positions.length / 3);\r\n                            positions[positionPtr++] = positions[3 * a] + k * deltaPosition.x;\r\n                            positions[positionPtr++] = positions[3 * a + 1] + k * deltaPosition.y;\r\n                            positions[positionPtr++] = positions[3 * a + 2] + k * deltaPosition.z;\r\n                            if (normals) {\r\n                                normals[normalsPtr!++] = normals[3 * a] + k * deltaNormal.x;\r\n                                normals[normalsPtr!++] = normals[3 * a + 1] + k * deltaNormal.y;\r\n                                normals[normalsPtr!++] = normals[3 * a + 2] + k * deltaNormal.z;\r\n                            }\r\n                            if (uvs) {\r\n                                uvs[uvPtr!++] = uvs[2 * a] + k * deltaUV.x;\r\n                                uvs[uvPtr!++] = uvs[2 * a + 1] + k * deltaUV.y;\r\n                            }\r\n                        }\r\n                        side[a][b].push(b);\r\n                        side[b][a] = [];\r\n                        len = side[a][b].length;\r\n                        for (let idx = 0; idx < len; idx++) {\r\n                            side[b][a][idx] = side[a][b][len - 1 - idx];\r\n                        }\r\n                    }\r\n                }\r\n                //Calculate positions, normals and uvs of new internal vertices\r\n                tempIndices[0][0] = currentIndices[i];\r\n                tempIndices[1][0] = side[currentIndices[i]][currentIndices[i + 1]][1];\r\n                tempIndices[1][1] = side[currentIndices[i]][currentIndices[i + 2]][1];\r\n                for (let k = 2; k < segments; k++) {\r\n                    tempIndices[k][0] = side[currentIndices[i]][currentIndices[i + 1]][k];\r\n                    tempIndices[k][k] = side[currentIndices[i]][currentIndices[i + 2]][k];\r\n                    deltaPosition.x = (positions[3 * tempIndices[k][k]] - positions[3 * tempIndices[k][0]]) / k;\r\n                    deltaPosition.y = (positions[3 * tempIndices[k][k] + 1] - positions[3 * tempIndices[k][0] + 1]) / k;\r\n                    deltaPosition.z = (positions[3 * tempIndices[k][k] + 2] - positions[3 * tempIndices[k][0] + 2]) / k;\r\n                    if (normals) {\r\n                        deltaNormal.x = (normals[3 * tempIndices[k][k]] - normals[3 * tempIndices[k][0]]) / k;\r\n                        deltaNormal.y = (normals[3 * tempIndices[k][k] + 1] - normals[3 * tempIndices[k][0] + 1]) / k;\r\n                        deltaNormal.z = (normals[3 * tempIndices[k][k] + 2] - normals[3 * tempIndices[k][0] + 2]) / k;\r\n                    }\r\n                    if (uvs) {\r\n                        deltaUV.x = (uvs[2 * tempIndices[k][k]] - uvs[2 * tempIndices[k][0]]) / k;\r\n                        deltaUV.y = (uvs[2 * tempIndices[k][k] + 1] - uvs[2 * tempIndices[k][0] + 1]) / k;\r\n                    }\r\n                    for (let j = 1; j < k; j++) {\r\n                        tempIndices[k][j] = positions.length / 3;\r\n                        positions[positionPtr++] = positions[3 * tempIndices[k][0]] + j * deltaPosition.x;\r\n                        positions[positionPtr++] = positions[3 * tempIndices[k][0] + 1] + j * deltaPosition.y;\r\n                        positions[positionPtr++] = positions[3 * tempIndices[k][0] + 2] + j * deltaPosition.z;\r\n                        if (normals) {\r\n                            normals[normalsPtr!++] = normals[3 * tempIndices[k][0]] + j * deltaNormal.x;\r\n                            normals[normalsPtr!++] = normals[3 * tempIndices[k][0] + 1] + j * deltaNormal.y;\r\n                            normals[normalsPtr!++] = normals[3 * tempIndices[k][0] + 2] + j * deltaNormal.z;\r\n                        }\r\n                        if (uvs) {\r\n                            uvs[uvPtr!++] = uvs[2 * tempIndices[k][0]] + j * deltaUV.x;\r\n                            uvs[uvPtr!++] = uvs[2 * tempIndices[k][0] + 1] + j * deltaUV.y;\r\n                        }\r\n                    }\r\n                }\r\n                tempIndices[segments] = side[currentIndices[i + 1]][currentIndices[i + 2]];\r\n\r\n                // reform indices\r\n                indices.push(tempIndices[0][0], tempIndices[1][0], tempIndices[1][1]);\r\n                for (let k = 1; k < segments; k++) {\r\n                    let j: number;\r\n                    for (j = 0; j < k; j++) {\r\n                        indices.push(tempIndices[k][j], tempIndices[k + 1][j], tempIndices[k + 1][j + 1]);\r\n                        indices.push(tempIndices[k][j], tempIndices[k + 1][j + 1], tempIndices[k][j + 1]);\r\n                    }\r\n                    indices.push(tempIndices[k][j], tempIndices[k + 1][j], tempIndices[k + 1][j + 1]);\r\n                }\r\n            }\r\n\r\n            vertexData.indices = indices;\r\n            vertexData.applyToMesh(this, this.isVertexBufferUpdatable(VertexBuffer.PositionKind));\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Force adjacent facets to share vertices and remove any facets that have all vertices in a line\r\n     * This will undo any application of covertToFlatShadedMesh\r\n     * Warning : the mesh is really modified even if not set originally as updatable. A new VertexBuffer is created under the hood each call.\r\n     */\r\n    public forceSharedVertices(): void {\r\n        const vertexData = VertexData.ExtractFromMesh(this);\r\n        const currentUVs = vertexData.uvs;\r\n        const currentIndices = vertexData.indices;\r\n        const currentPositions = vertexData.positions;\r\n        const currentColors = vertexData.colors;\r\n        const currentMatrixIndices = vertexData.matricesIndices;\r\n        const currentMatrixWeights = vertexData.matricesWeights;\r\n        const currentMatrixIndicesExtra = vertexData.matricesIndicesExtra;\r\n        const currentMatrixWeightsExtra = vertexData.matricesWeightsExtra;\r\n\r\n        if (currentIndices === void 0 || currentPositions === void 0 || currentIndices === null || currentPositions === null) {\r\n            Logger.Warn(\"VertexData contains empty entries\");\r\n        } else {\r\n            const positions: Array<number> = [];\r\n            const indices: Array<number> = [];\r\n            const uvs: Array<number> = [];\r\n            const colors: Array<number> = [];\r\n            const matrixIndices: Array<number> = [];\r\n            const matrixWeights: Array<number> = [];\r\n            const matrixIndicesExtra: Array<number> = [];\r\n            const matrixWeightsExtra: Array<number> = [];\r\n            let pstring: Array<string> = []; //lists facet vertex positions (a,b,c) as string \"a|b|c\"\r\n\r\n            let indexPtr: number = 0; // pointer to next available index value\r\n            const uniquePositions: { [key: string]: number } = {}; // unique vertex positions\r\n            let ptr: number; // pointer to element in uniquePositions\r\n            let facet: Array<number>;\r\n\r\n            for (let i = 0; i < currentIndices.length; i += 3) {\r\n                facet = [currentIndices[i], currentIndices[i + 1], currentIndices[i + 2]]; //facet vertex indices\r\n                pstring = [];\r\n                for (let j = 0; j < 3; j++) {\r\n                    pstring[j] = \"\";\r\n                    for (let k = 0; k < 3; k++) {\r\n                        //small values make 0\r\n                        if (Math.abs(currentPositions[3 * facet[j] + k]) < 0.00000001) {\r\n                            currentPositions[3 * facet[j] + k] = 0;\r\n                        }\r\n                        pstring[j] += currentPositions[3 * facet[j] + k] + \"|\";\r\n                    }\r\n                }\r\n                //check facet vertices to see that none are repeated\r\n                // do not process any facet that has a repeated vertex, ie is a line\r\n                if (!(pstring[0] == pstring[1] || pstring[0] == pstring[2] || pstring[1] == pstring[2])) {\r\n                    //for each facet position check if already listed in uniquePositions\r\n                    // if not listed add to uniquePositions and set index pointer\r\n                    // if listed use its index in uniquePositions and new index pointer\r\n                    for (let j = 0; j < 3; j++) {\r\n                        ptr = uniquePositions[pstring[j]];\r\n                        if (ptr === undefined) {\r\n                            uniquePositions[pstring[j]] = indexPtr;\r\n                            ptr = indexPtr++;\r\n                            //not listed so add individual x, y, z coordinates to positions\r\n                            for (let k = 0; k < 3; k++) {\r\n                                positions.push(currentPositions[3 * facet[j] + k]);\r\n                            }\r\n                            if (currentColors !== null && currentColors !== void 0) {\r\n                                for (let k = 0; k < 4; k++) {\r\n                                    colors.push(currentColors[4 * facet[j] + k]);\r\n                                }\r\n                            }\r\n                            if (currentUVs !== null && currentUVs !== void 0) {\r\n                                for (let k = 0; k < 2; k++) {\r\n                                    uvs.push(currentUVs[2 * facet[j] + k]);\r\n                                }\r\n                            }\r\n                            if (currentMatrixIndices !== null && currentMatrixIndices !== void 0) {\r\n                                for (let k = 0; k < 4; k++) {\r\n                                    matrixIndices.push(currentMatrixIndices[4 * facet[j] + k]);\r\n                                }\r\n                            }\r\n                            if (currentMatrixWeights !== null && currentMatrixWeights !== void 0) {\r\n                                for (let k = 0; k < 4; k++) {\r\n                                    matrixWeights.push(currentMatrixWeights[4 * facet[j] + k]);\r\n                                }\r\n                            }\r\n                            if (currentMatrixIndicesExtra !== null && currentMatrixIndicesExtra !== void 0) {\r\n                                for (let k = 0; k < 4; k++) {\r\n                                    matrixIndicesExtra.push(currentMatrixIndicesExtra[4 * facet[j] + k]);\r\n                                }\r\n                            }\r\n                            if (currentMatrixWeightsExtra !== null && currentMatrixWeightsExtra !== void 0) {\r\n                                for (let k = 0; k < 4; k++) {\r\n                                    matrixWeightsExtra.push(currentMatrixWeightsExtra[4 * facet[j] + k]);\r\n                                }\r\n                            }\r\n                        }\r\n                        // add new index pointer to indices array\r\n                        indices.push(ptr);\r\n                    }\r\n                }\r\n            }\r\n\r\n            const normals: Array<number> = [];\r\n            VertexData.ComputeNormals(positions, indices, normals);\r\n\r\n            //create new vertex data object and update\r\n            vertexData.positions = positions;\r\n            vertexData.indices = indices;\r\n            vertexData.normals = normals;\r\n            if (currentUVs !== null && currentUVs !== void 0) {\r\n                vertexData.uvs = uvs;\r\n            }\r\n            if (currentColors !== null && currentColors !== void 0) {\r\n                vertexData.colors = colors;\r\n            }\r\n            if (currentMatrixIndices !== null && currentMatrixIndices !== void 0) {\r\n                vertexData.matricesIndices = matrixIndices;\r\n            }\r\n            if (currentMatrixWeights !== null && currentMatrixWeights !== void 0) {\r\n                vertexData.matricesWeights = matrixWeights;\r\n            }\r\n            if (currentMatrixIndicesExtra !== null && currentMatrixIndicesExtra !== void 0) {\r\n                vertexData.matricesIndicesExtra = matrixIndicesExtra;\r\n            }\r\n            if (currentMatrixWeights !== null && currentMatrixWeights !== void 0) {\r\n                vertexData.matricesWeightsExtra = matrixWeightsExtra;\r\n            }\r\n\r\n            vertexData.applyToMesh(this, this.isVertexBufferUpdatable(VertexBuffer.PositionKind));\r\n        }\r\n    }\r\n\r\n    // Instances\r\n    /**\r\n     * @internal\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/naming-convention\r\n    public static _instancedMeshFactory(name: string, mesh: Mesh): InstancedMesh {\r\n        throw _WarnImport(\"InstancedMesh\");\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public static _PhysicsImpostorParser(scene: Scene, physicObject: IPhysicsEnabledObject, jsonObject: any): PhysicsImpostor {\r\n        throw _WarnImport(\"PhysicsImpostor\");\r\n    }\r\n\r\n    /**\r\n     * Creates a new InstancedMesh object from the mesh model.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/copies/instances\r\n     * @param name defines the name of the new instance\r\n     * @returns a new InstancedMesh\r\n     */\r\n    public createInstance(name: string): InstancedMesh {\r\n        const instance = Mesh._instancedMeshFactory(name, this);\r\n\r\n        instance.parent = this.parent;\r\n\r\n        return instance;\r\n    }\r\n\r\n    /**\r\n     * Synchronises all the mesh instance submeshes to the current mesh submeshes, if any.\r\n     * After this call, all the mesh instances have the same submeshes than the current mesh.\r\n     * @returns the current mesh\r\n     */\r\n    public synchronizeInstances(): Mesh {\r\n        for (let instanceIndex = 0; instanceIndex < this.instances.length; instanceIndex++) {\r\n            const instance = this.instances[instanceIndex];\r\n            instance._syncSubMeshes();\r\n        }\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Optimization of the mesh's indices, in case a mesh has duplicated vertices.\r\n     * The function will only reorder the indices and will not remove unused vertices to avoid problems with submeshes.\r\n     * This should be used together with the simplification to avoid disappearing triangles.\r\n     * @param successCallback an optional success callback to be called after the optimization finished.\r\n     * @returns the current mesh\r\n     */\r\n    public optimizeIndices(successCallback?: (mesh?: Mesh) => void): Mesh {\r\n        const indices = <IndicesArray>this.getIndices();\r\n        const positions = this.getVerticesData(VertexBuffer.PositionKind);\r\n\r\n        if (!positions || !indices) {\r\n            return this;\r\n        }\r\n\r\n        const vectorPositions: Vector3[] = [];\r\n        for (let pos = 0; pos < positions.length; pos = pos + 3) {\r\n            vectorPositions.push(Vector3.FromArray(positions, pos));\r\n        }\r\n        const dupes: number[] = [];\r\n\r\n        AsyncLoop.SyncAsyncForLoop(\r\n            vectorPositions.length,\r\n            40,\r\n            (iteration) => {\r\n                const realPos = vectorPositions.length - 1 - iteration;\r\n                const testedPosition = vectorPositions[realPos];\r\n                for (let j = 0; j < realPos; ++j) {\r\n                    const againstPosition = vectorPositions[j];\r\n                    if (testedPosition.equals(againstPosition)) {\r\n                        dupes[realPos] = j;\r\n                        break;\r\n                    }\r\n                }\r\n            },\r\n            () => {\r\n                for (let i = 0; i < indices.length; ++i) {\r\n                    indices[i] = dupes[indices[i]] || indices[i];\r\n                }\r\n\r\n                //indices are now reordered\r\n                const originalSubMeshes = this.subMeshes.slice(0);\r\n                this.setIndices(indices);\r\n                this.subMeshes = originalSubMeshes;\r\n                if (successCallback) {\r\n                    successCallback(this);\r\n                }\r\n            }\r\n        );\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Serialize current mesh\r\n     * @param serializationObject defines the object which will receive the serialization data\r\n     * @returns the serialized object\r\n     */\r\n    public override serialize(serializationObject: any = {}): any {\r\n        serializationObject.name = this.name;\r\n        serializationObject.id = this.id;\r\n        serializationObject.uniqueId = this.uniqueId;\r\n        serializationObject.type = this.getClassName();\r\n\r\n        if (Tags && Tags.HasTags(this)) {\r\n            serializationObject.tags = Tags.GetTags(this);\r\n        }\r\n\r\n        serializationObject.position = this.position.asArray();\r\n\r\n        if (this.rotationQuaternion) {\r\n            serializationObject.rotationQuaternion = this.rotationQuaternion.asArray();\r\n        } else if (this.rotation) {\r\n            serializationObject.rotation = this.rotation.asArray();\r\n        }\r\n\r\n        serializationObject.scaling = this.scaling.asArray();\r\n        if (this._postMultiplyPivotMatrix) {\r\n            serializationObject.pivotMatrix = this.getPivotMatrix().asArray();\r\n        } else {\r\n            serializationObject.localMatrix = this.getPivotMatrix().asArray();\r\n        }\r\n\r\n        serializationObject.isEnabled = this.isEnabled(false);\r\n        serializationObject.isVisible = this.isVisible;\r\n        serializationObject.infiniteDistance = this.infiniteDistance;\r\n        serializationObject.pickable = this.isPickable;\r\n\r\n        serializationObject.receiveShadows = this.receiveShadows;\r\n\r\n        serializationObject.billboardMode = this.billboardMode;\r\n        serializationObject.visibility = this.visibility;\r\n        serializationObject.alwaysSelectAsActiveMesh = this.alwaysSelectAsActiveMesh;\r\n\r\n        serializationObject.checkCollisions = this.checkCollisions;\r\n        serializationObject.ellipsoid = this.ellipsoid.asArray();\r\n        serializationObject.ellipsoidOffset = this.ellipsoidOffset.asArray();\r\n        serializationObject.doNotSyncBoundingInfo = this.doNotSyncBoundingInfo;\r\n        serializationObject.isBlocker = this.isBlocker;\r\n        serializationObject.sideOrientation = this.sideOrientation;\r\n\r\n        // Parent\r\n        if (this.parent) {\r\n            this.parent._serializeAsParent(serializationObject);\r\n        }\r\n\r\n        // Geometry\r\n        serializationObject.isUnIndexed = this.isUnIndexed;\r\n        const geometry = this._geometry;\r\n        if (geometry && this.subMeshes) {\r\n            serializationObject.geometryUniqueId = geometry.uniqueId;\r\n            serializationObject.geometryId = geometry.id;\r\n\r\n            // SubMeshes\r\n            serializationObject.subMeshes = [];\r\n            for (let subIndex = 0; subIndex < this.subMeshes.length; subIndex++) {\r\n                const subMesh = this.subMeshes[subIndex];\r\n\r\n                serializationObject.subMeshes.push({\r\n                    materialIndex: subMesh.materialIndex,\r\n                    verticesStart: subMesh.verticesStart,\r\n                    verticesCount: subMesh.verticesCount,\r\n                    indexStart: subMesh.indexStart,\r\n                    indexCount: subMesh.indexCount,\r\n                });\r\n            }\r\n        }\r\n\r\n        // Material\r\n        if (this.material) {\r\n            if (!this.material.doNotSerialize) {\r\n                serializationObject.materialUniqueId = this.material.uniqueId;\r\n                serializationObject.materialId = this.material.id; // back compat\r\n            }\r\n        } else {\r\n            this.material = null;\r\n            serializationObject.materialUniqueId = this._scene.defaultMaterial.uniqueId;\r\n            serializationObject.materialId = this._scene.defaultMaterial.id; // back compat\r\n        }\r\n\r\n        // Morph targets\r\n        if (this.morphTargetManager) {\r\n            serializationObject.morphTargetManagerId = this.morphTargetManager.uniqueId;\r\n        }\r\n\r\n        // Skeleton\r\n        if (this.skeleton) {\r\n            serializationObject.skeletonId = this.skeleton.id;\r\n            serializationObject.numBoneInfluencers = this.numBoneInfluencers;\r\n        }\r\n\r\n        // Physics\r\n        //TODO implement correct serialization for physics impostors.\r\n        if (this.getScene()._getComponent(SceneComponentConstants.NAME_PHYSICSENGINE)) {\r\n            const impostor = this.getPhysicsImpostor();\r\n            if (impostor) {\r\n                serializationObject.physicsMass = impostor.getParam(\"mass\");\r\n                serializationObject.physicsFriction = impostor.getParam(\"friction\");\r\n                serializationObject.physicsRestitution = impostor.getParam(\"mass\");\r\n                serializationObject.physicsImpostor = impostor.type;\r\n            }\r\n        }\r\n\r\n        // Metadata\r\n        if (this.metadata) {\r\n            serializationObject.metadata = this.metadata;\r\n        }\r\n\r\n        // Instances\r\n        serializationObject.instances = [];\r\n        for (let index = 0; index < this.instances.length; index++) {\r\n            const instance = this.instances[index];\r\n            if (instance.doNotSerialize) {\r\n                continue;\r\n            }\r\n\r\n            const serializationInstance: any = {\r\n                name: instance.name,\r\n                id: instance.id,\r\n                isEnabled: instance.isEnabled(false),\r\n                isVisible: instance.isVisible,\r\n                isPickable: instance.isPickable,\r\n                checkCollisions: instance.checkCollisions,\r\n                position: instance.position.asArray(),\r\n                scaling: instance.scaling.asArray(),\r\n            };\r\n\r\n            if (instance.parent) {\r\n                instance.parent._serializeAsParent(serializationInstance);\r\n            }\r\n\r\n            if (instance.rotationQuaternion) {\r\n                serializationInstance.rotationQuaternion = instance.rotationQuaternion.asArray();\r\n            } else if (instance.rotation) {\r\n                serializationInstance.rotation = instance.rotation.asArray();\r\n            }\r\n\r\n            // Physics\r\n            //TODO implement correct serialization for physics impostors.\r\n            if (this.getScene()._getComponent(SceneComponentConstants.NAME_PHYSICSENGINE)) {\r\n                const impostor = instance.getPhysicsImpostor();\r\n                if (impostor) {\r\n                    serializationInstance.physicsMass = impostor.getParam(\"mass\");\r\n                    serializationInstance.physicsFriction = impostor.getParam(\"friction\");\r\n                    serializationInstance.physicsRestitution = impostor.getParam(\"mass\");\r\n                    serializationInstance.physicsImpostor = impostor.type;\r\n                }\r\n            }\r\n\r\n            // Metadata\r\n            if (instance.metadata) {\r\n                serializationInstance.metadata = instance.metadata;\r\n            }\r\n\r\n            // Action Manager\r\n            if (instance.actionManager) {\r\n                serializationInstance.actions = instance.actionManager.serialize(instance.name);\r\n            }\r\n\r\n            serializationObject.instances.push(serializationInstance);\r\n\r\n            // Animations\r\n            SerializationHelper.AppendSerializedAnimations(instance, serializationInstance);\r\n            serializationInstance.ranges = instance.serializeAnimationRanges();\r\n        }\r\n\r\n        // Thin instances\r\n        if (this._thinInstanceDataStorage.instancesCount && this._thinInstanceDataStorage.matrixData) {\r\n            serializationObject.thinInstances = {\r\n                instancesCount: this._thinInstanceDataStorage.instancesCount,\r\n                matrixData: Array.from(this._thinInstanceDataStorage.matrixData),\r\n                matrixBufferSize: this._thinInstanceDataStorage.matrixBufferSize,\r\n                enablePicking: this.thinInstanceEnablePicking,\r\n            };\r\n\r\n            if (this._userThinInstanceBuffersStorage) {\r\n                const userThinInstance: any = {\r\n                    data: {},\r\n                    sizes: {},\r\n                    strides: {},\r\n                };\r\n\r\n                for (const kind in this._userThinInstanceBuffersStorage.data) {\r\n                    userThinInstance.data[kind] = Array.from(this._userThinInstanceBuffersStorage.data[kind]);\r\n                    userThinInstance.sizes[kind] = this._userThinInstanceBuffersStorage.sizes[kind];\r\n                    userThinInstance.strides[kind] = this._userThinInstanceBuffersStorage.strides[kind];\r\n                }\r\n\r\n                serializationObject.thinInstances.userThinInstance = userThinInstance;\r\n            }\r\n        }\r\n\r\n        // Animations\r\n        SerializationHelper.AppendSerializedAnimations(this, serializationObject);\r\n        serializationObject.ranges = this.serializeAnimationRanges();\r\n\r\n        // Layer mask\r\n        serializationObject.layerMask = this.layerMask;\r\n\r\n        // Alpha\r\n        serializationObject.alphaIndex = this.alphaIndex;\r\n        serializationObject.hasVertexAlpha = this.hasVertexAlpha;\r\n\r\n        // Overlay\r\n        serializationObject.overlayAlpha = this.overlayAlpha;\r\n        serializationObject.overlayColor = this.overlayColor.asArray();\r\n        serializationObject.renderOverlay = this.renderOverlay;\r\n\r\n        // Fog\r\n        serializationObject.applyFog = this.applyFog;\r\n\r\n        // Action Manager\r\n        if (this.actionManager) {\r\n            serializationObject.actions = this.actionManager.serialize(this.name);\r\n        }\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /** @internal */\r\n    public override _syncGeometryWithMorphTargetManager() {\r\n        if (!this.geometry) {\r\n            return;\r\n        }\r\n\r\n        this._markSubMeshesAsAttributesDirty();\r\n\r\n        const morphTargetManager = this._internalAbstractMeshDataInfo._morphTargetManager;\r\n        if (morphTargetManager && morphTargetManager.vertexCount) {\r\n            if (morphTargetManager.vertexCount !== this.getTotalVertices()) {\r\n                Logger.Error(\"Mesh is incompatible with morph targets. Targets and mesh must all have the same vertices count.\");\r\n                this.morphTargetManager = null;\r\n                return;\r\n            }\r\n\r\n            if (morphTargetManager.isUsingTextureForTargets) {\r\n                return;\r\n            }\r\n\r\n            for (let index = 0; index < morphTargetManager.numInfluencers; index++) {\r\n                const morphTarget = morphTargetManager.getActiveTarget(index);\r\n\r\n                const positions = morphTarget.getPositions();\r\n                if (!positions) {\r\n                    Logger.Error(\"Invalid morph target. Target must have positions.\");\r\n                    return;\r\n                }\r\n\r\n                this.geometry.setVerticesData(VertexBuffer.PositionKind + index, positions, false, 3);\r\n\r\n                const normals = morphTarget.getNormals();\r\n                if (normals) {\r\n                    this.geometry.setVerticesData(VertexBuffer.NormalKind + index, normals, false, 3);\r\n                }\r\n\r\n                const tangents = morphTarget.getTangents();\r\n                if (tangents) {\r\n                    this.geometry.setVerticesData(VertexBuffer.TangentKind + index, tangents, false, 3);\r\n                }\r\n\r\n                const uvs = morphTarget.getUVs();\r\n                if (uvs) {\r\n                    this.geometry.setVerticesData(VertexBuffer.UVKind + \"_\" + index, uvs, false, 2);\r\n                }\r\n\r\n                const uv2s = morphTarget.getUV2s();\r\n                if (uv2s) {\r\n                    this.geometry.setVerticesData(VertexBuffer.UV2Kind + \"_\" + index, uv2s, false, 2);\r\n                }\r\n\r\n                const colors = morphTarget.getColors();\r\n                if (colors) {\r\n                    this.geometry.setVerticesData(VertexBuffer.ColorKind + index, colors, false, 4);\r\n                }\r\n            }\r\n        } else {\r\n            let index = 0;\r\n\r\n            // Positions\r\n            while (this.geometry.isVerticesDataPresent(VertexBuffer.PositionKind + index)) {\r\n                this.geometry.removeVerticesData(VertexBuffer.PositionKind + index);\r\n\r\n                if (this.geometry.isVerticesDataPresent(VertexBuffer.NormalKind + index)) {\r\n                    this.geometry.removeVerticesData(VertexBuffer.NormalKind + index);\r\n                }\r\n                if (this.geometry.isVerticesDataPresent(VertexBuffer.TangentKind + index)) {\r\n                    this.geometry.removeVerticesData(VertexBuffer.TangentKind + index);\r\n                }\r\n                if (this.geometry.isVerticesDataPresent(VertexBuffer.UVKind + index)) {\r\n                    this.geometry.removeVerticesData(VertexBuffer.UVKind + \"_\" + index);\r\n                }\r\n                if (this.geometry.isVerticesDataPresent(VertexBuffer.UV2Kind + index)) {\r\n                    this.geometry.removeVerticesData(VertexBuffer.UV2Kind + \"_\" + index);\r\n                }\r\n                if (this.geometry.isVerticesDataPresent(VertexBuffer.ColorKind + index)) {\r\n                    this.geometry.removeVerticesData(VertexBuffer.ColorKind + index);\r\n                }\r\n                index++;\r\n            }\r\n        }\r\n    }\r\n\r\n    // Statics\r\n    /**\r\n     * @internal\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public static _GroundMeshParser = (parsedMesh: any, scene: Scene): Mesh => {\r\n        throw _WarnImport(\"GroundMesh\");\r\n    };\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public static _GoldbergMeshParser = (parsedMesh: any, scene: Scene): GoldbergMesh => {\r\n        throw _WarnImport(\"GoldbergMesh\");\r\n    };\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public static _LinesMeshParser = (parsedMesh: any, scene: Scene): Mesh => {\r\n        throw _WarnImport(\"LinesMesh\");\r\n    };\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public static _GreasedLineMeshParser = (parsedMesh: any, scene: Scene): Mesh => {\r\n        throw _WarnImport(\"GreasedLineMesh\");\r\n    };\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public static _GreasedLineRibbonMeshParser = (parsedMesh: any, scene: Scene): Mesh => {\r\n        throw _WarnImport(\"GreasedLineRibbonMesh\");\r\n    };\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public static _TrailMeshParser = (parsedMesh: any, scene: Scene): Mesh => {\r\n        throw _WarnImport(\"TrailMesh\");\r\n    };\r\n\r\n    /**\r\n     * Returns a new Mesh object parsed from the source provided.\r\n     * @param parsedMesh is the source\r\n     * @param scene defines the hosting scene\r\n     * @param rootUrl is the root URL to prefix the `delayLoadingFile` property with\r\n     * @returns a new Mesh\r\n     */\r\n    public static override Parse(parsedMesh: any, scene: Scene, rootUrl: string): Mesh {\r\n        let mesh: Mesh;\r\n\r\n        if (parsedMesh.type && parsedMesh.type === \"LinesMesh\") {\r\n            mesh = Mesh._LinesMeshParser(parsedMesh, scene);\r\n        } else if (parsedMesh.type && parsedMesh.type === \"GroundMesh\") {\r\n            mesh = Mesh._GroundMeshParser(parsedMesh, scene);\r\n        } else if (parsedMesh.type && parsedMesh.type === \"GoldbergMesh\") {\r\n            mesh = Mesh._GoldbergMeshParser(parsedMesh, scene);\r\n        } else if (parsedMesh.type && parsedMesh.type === \"GreasedLineMesh\") {\r\n            mesh = Mesh._GreasedLineMeshParser(parsedMesh, scene);\r\n        } else if (parsedMesh.type && parsedMesh.type === \"TrailMesh\") {\r\n            mesh = Mesh._TrailMeshParser(parsedMesh, scene);\r\n        } else {\r\n            mesh = new Mesh(parsedMesh.name, scene);\r\n        }\r\n        mesh.id = parsedMesh.id;\r\n        mesh._waitingParsedUniqueId = parsedMesh.uniqueId;\r\n\r\n        if (Tags) {\r\n            Tags.AddTagsTo(mesh, parsedMesh.tags);\r\n        }\r\n\r\n        mesh.position = Vector3.FromArray(parsedMesh.position);\r\n\r\n        if (parsedMesh.metadata !== undefined) {\r\n            mesh.metadata = parsedMesh.metadata;\r\n        }\r\n\r\n        if (parsedMesh.rotationQuaternion) {\r\n            mesh.rotationQuaternion = Quaternion.FromArray(parsedMesh.rotationQuaternion);\r\n        } else if (parsedMesh.rotation) {\r\n            mesh.rotation = Vector3.FromArray(parsedMesh.rotation);\r\n        }\r\n\r\n        mesh.scaling = Vector3.FromArray(parsedMesh.scaling);\r\n\r\n        if (parsedMesh.localMatrix) {\r\n            mesh.setPreTransformMatrix(Matrix.FromArray(parsedMesh.localMatrix));\r\n        } else if (parsedMesh.pivotMatrix) {\r\n            mesh.setPivotMatrix(Matrix.FromArray(parsedMesh.pivotMatrix));\r\n        }\r\n\r\n        mesh.setEnabled(parsedMesh.isEnabled);\r\n        mesh.isVisible = parsedMesh.isVisible;\r\n        mesh.infiniteDistance = parsedMesh.infiniteDistance;\r\n        mesh.alwaysSelectAsActiveMesh = !!parsedMesh.alwaysSelectAsActiveMesh;\r\n\r\n        mesh.showBoundingBox = parsedMesh.showBoundingBox;\r\n        mesh.showSubMeshesBoundingBox = parsedMesh.showSubMeshesBoundingBox;\r\n\r\n        if (parsedMesh.applyFog !== undefined) {\r\n            mesh.applyFog = parsedMesh.applyFog;\r\n        }\r\n\r\n        if (parsedMesh.pickable !== undefined) {\r\n            mesh.isPickable = parsedMesh.pickable;\r\n        }\r\n\r\n        if (parsedMesh.alphaIndex !== undefined) {\r\n            mesh.alphaIndex = parsedMesh.alphaIndex;\r\n        }\r\n\r\n        mesh.receiveShadows = parsedMesh.receiveShadows;\r\n\r\n        if (parsedMesh.billboardMode !== undefined) {\r\n            mesh.billboardMode = parsedMesh.billboardMode;\r\n        }\r\n\r\n        if (parsedMesh.visibility !== undefined) {\r\n            mesh.visibility = parsedMesh.visibility;\r\n        }\r\n\r\n        mesh.checkCollisions = parsedMesh.checkCollisions;\r\n        mesh.doNotSyncBoundingInfo = !!parsedMesh.doNotSyncBoundingInfo;\r\n\r\n        if (parsedMesh.ellipsoid) {\r\n            mesh.ellipsoid = Vector3.FromArray(parsedMesh.ellipsoid);\r\n        }\r\n\r\n        if (parsedMesh.ellipsoidOffset) {\r\n            mesh.ellipsoidOffset = Vector3.FromArray(parsedMesh.ellipsoidOffset);\r\n        }\r\n\r\n        // For Backward compatibility (\"!=\" to exclude null and undefined)\r\n        if (parsedMesh.overrideMaterialSideOrientation != null) {\r\n            mesh.sideOrientation = parsedMesh.overrideMaterialSideOrientation;\r\n        }\r\n\r\n        if (parsedMesh.sideOrientation !== undefined) {\r\n            mesh.sideOrientation = parsedMesh.sideOrientation;\r\n        }\r\n\r\n        if (parsedMesh.isBlocker !== undefined) {\r\n            mesh.isBlocker = parsedMesh.isBlocker;\r\n        }\r\n\r\n        mesh._shouldGenerateFlatShading = parsedMesh.useFlatShading;\r\n\r\n        // freezeWorldMatrix\r\n        if (parsedMesh.freezeWorldMatrix) {\r\n            mesh._waitingData.freezeWorldMatrix = parsedMesh.freezeWorldMatrix;\r\n        }\r\n\r\n        // Parent\r\n        if (parsedMesh.parentId !== undefined) {\r\n            mesh._waitingParentId = parsedMesh.parentId;\r\n        }\r\n\r\n        if (parsedMesh.parentInstanceIndex !== undefined) {\r\n            mesh._waitingParentInstanceIndex = parsedMesh.parentInstanceIndex;\r\n        }\r\n\r\n        // Actions\r\n        if (parsedMesh.actions !== undefined) {\r\n            mesh._waitingData.actions = parsedMesh.actions;\r\n        }\r\n\r\n        // Overlay\r\n        if (parsedMesh.overlayAlpha !== undefined) {\r\n            mesh.overlayAlpha = parsedMesh.overlayAlpha;\r\n        }\r\n\r\n        if (parsedMesh.overlayColor !== undefined) {\r\n            mesh.overlayColor = Color3.FromArray(parsedMesh.overlayColor);\r\n        }\r\n\r\n        if (parsedMesh.renderOverlay !== undefined) {\r\n            mesh.renderOverlay = parsedMesh.renderOverlay;\r\n        }\r\n\r\n        // Geometry\r\n        mesh.isUnIndexed = !!parsedMesh.isUnIndexed;\r\n        mesh.hasVertexAlpha = parsedMesh.hasVertexAlpha;\r\n\r\n        if (parsedMesh.delayLoadingFile) {\r\n            mesh.delayLoadState = Constants.DELAYLOADSTATE_NOTLOADED;\r\n            mesh.delayLoadingFile = rootUrl + parsedMesh.delayLoadingFile;\r\n            mesh.buildBoundingInfo(Vector3.FromArray(parsedMesh.boundingBoxMinimum), Vector3.FromArray(parsedMesh.boundingBoxMaximum));\r\n\r\n            if (parsedMesh._binaryInfo) {\r\n                mesh._binaryInfo = parsedMesh._binaryInfo;\r\n            }\r\n\r\n            mesh._delayInfo = [];\r\n            if (parsedMesh.hasUVs) {\r\n                mesh._delayInfo.push(VertexBuffer.UVKind);\r\n            }\r\n\r\n            if (parsedMesh.hasUVs2) {\r\n                mesh._delayInfo.push(VertexBuffer.UV2Kind);\r\n            }\r\n\r\n            if (parsedMesh.hasUVs3) {\r\n                mesh._delayInfo.push(VertexBuffer.UV3Kind);\r\n            }\r\n\r\n            if (parsedMesh.hasUVs4) {\r\n                mesh._delayInfo.push(VertexBuffer.UV4Kind);\r\n            }\r\n\r\n            if (parsedMesh.hasUVs5) {\r\n                mesh._delayInfo.push(VertexBuffer.UV5Kind);\r\n            }\r\n\r\n            if (parsedMesh.hasUVs6) {\r\n                mesh._delayInfo.push(VertexBuffer.UV6Kind);\r\n            }\r\n\r\n            if (parsedMesh.hasColors) {\r\n                mesh._delayInfo.push(VertexBuffer.ColorKind);\r\n            }\r\n\r\n            if (parsedMesh.hasMatricesIndices) {\r\n                mesh._delayInfo.push(VertexBuffer.MatricesIndicesKind);\r\n            }\r\n\r\n            if (parsedMesh.hasMatricesWeights) {\r\n                mesh._delayInfo.push(VertexBuffer.MatricesWeightsKind);\r\n            }\r\n\r\n            mesh._delayLoadingFunction = Geometry._ImportGeometry;\r\n\r\n            if (SceneLoaderFlags.ForceFullSceneLoadingForIncremental) {\r\n                mesh._checkDelayState();\r\n            }\r\n        } else {\r\n            Geometry._ImportGeometry(parsedMesh, mesh);\r\n        }\r\n\r\n        // Material\r\n        if (parsedMesh.materialUniqueId) {\r\n            mesh._waitingMaterialId = parsedMesh.materialUniqueId;\r\n        } else if (parsedMesh.materialId) {\r\n            mesh._waitingMaterialId = parsedMesh.materialId;\r\n        }\r\n\r\n        // Morph targets\r\n        if (parsedMesh.morphTargetManagerId > -1) {\r\n            mesh._waitingMorphTargetManagerId = parsedMesh.morphTargetManagerId;\r\n        }\r\n\r\n        // Skeleton\r\n        if (parsedMesh.skeletonId !== undefined && parsedMesh.skeletonId !== null) {\r\n            mesh.skeleton = scene.getLastSkeletonById(parsedMesh.skeletonId);\r\n            if (parsedMesh.numBoneInfluencers) {\r\n                mesh.numBoneInfluencers = parsedMesh.numBoneInfluencers;\r\n            }\r\n        }\r\n\r\n        // Animations\r\n        if (parsedMesh.animations) {\r\n            for (let animationIndex = 0; animationIndex < parsedMesh.animations.length; animationIndex++) {\r\n                const parsedAnimation = parsedMesh.animations[animationIndex];\r\n                const internalClass = GetClass(\"BABYLON.Animation\");\r\n                if (internalClass) {\r\n                    mesh.animations.push(internalClass.Parse(parsedAnimation));\r\n                }\r\n            }\r\n            Node.ParseAnimationRanges(mesh, parsedMesh, scene);\r\n        }\r\n\r\n        if (parsedMesh.autoAnimate) {\r\n            scene.beginAnimation(mesh, parsedMesh.autoAnimateFrom, parsedMesh.autoAnimateTo, parsedMesh.autoAnimateLoop, parsedMesh.autoAnimateSpeed || 1.0);\r\n        }\r\n\r\n        // Layer Mask\r\n        if (parsedMesh.layerMask && !isNaN(parsedMesh.layerMask)) {\r\n            mesh.layerMask = Math.abs(parseInt(parsedMesh.layerMask));\r\n        } else {\r\n            mesh.layerMask = 0x0fffffff;\r\n        }\r\n\r\n        // Physics\r\n        if (parsedMesh.physicsImpostor) {\r\n            mesh.physicsImpostor = Mesh._PhysicsImpostorParser(scene, mesh, parsedMesh);\r\n        }\r\n\r\n        // Levels\r\n        if (parsedMesh.lodMeshIds) {\r\n            mesh._waitingData.lods = {\r\n                ids: parsedMesh.lodMeshIds,\r\n                distances: parsedMesh.lodDistances ? parsedMesh.lodDistances : null,\r\n                coverages: parsedMesh.lodCoverages ? parsedMesh.lodCoverages : null,\r\n            };\r\n        }\r\n\r\n        // Instances\r\n        if (parsedMesh.instances) {\r\n            for (let index = 0; index < parsedMesh.instances.length; index++) {\r\n                const parsedInstance = parsedMesh.instances[index];\r\n                const instance = mesh.createInstance(parsedInstance.name);\r\n\r\n                if (parsedInstance.id) {\r\n                    instance.id = parsedInstance.id;\r\n                }\r\n\r\n                if (Tags) {\r\n                    if (parsedInstance.tags) {\r\n                        Tags.AddTagsTo(instance, parsedInstance.tags);\r\n                    } else {\r\n                        Tags.AddTagsTo(instance, parsedMesh.tags);\r\n                    }\r\n                }\r\n\r\n                instance.position = Vector3.FromArray(parsedInstance.position);\r\n\r\n                if (parsedInstance.metadata !== undefined) {\r\n                    instance.metadata = parsedInstance.metadata;\r\n                }\r\n\r\n                if (parsedInstance.parentId !== undefined) {\r\n                    instance._waitingParentId = parsedInstance.parentId;\r\n                }\r\n\r\n                if (parsedInstance.parentInstanceIndex !== undefined) {\r\n                    instance._waitingParentInstanceIndex = parsedInstance.parentInstanceIndex;\r\n                }\r\n\r\n                if (parsedInstance.isEnabled !== undefined && parsedInstance.isEnabled !== null) {\r\n                    instance.setEnabled(parsedInstance.isEnabled);\r\n                }\r\n\r\n                if (parsedInstance.isVisible !== undefined && parsedInstance.isVisible !== null) {\r\n                    instance.isVisible = parsedInstance.isVisible;\r\n                }\r\n\r\n                if (parsedInstance.isPickable !== undefined && parsedInstance.isPickable !== null) {\r\n                    instance.isPickable = parsedInstance.isPickable;\r\n                }\r\n\r\n                if (parsedInstance.rotationQuaternion) {\r\n                    instance.rotationQuaternion = Quaternion.FromArray(parsedInstance.rotationQuaternion);\r\n                } else if (parsedInstance.rotation) {\r\n                    instance.rotation = Vector3.FromArray(parsedInstance.rotation);\r\n                }\r\n\r\n                instance.scaling = Vector3.FromArray(parsedInstance.scaling);\r\n\r\n                if (parsedInstance.checkCollisions != undefined && parsedInstance.checkCollisions != null) {\r\n                    instance.checkCollisions = parsedInstance.checkCollisions;\r\n                }\r\n                if (parsedInstance.pickable != undefined && parsedInstance.pickable != null) {\r\n                    instance.isPickable = parsedInstance.pickable;\r\n                }\r\n                if (parsedInstance.showBoundingBox != undefined && parsedInstance.showBoundingBox != null) {\r\n                    instance.showBoundingBox = parsedInstance.showBoundingBox;\r\n                }\r\n                if (parsedInstance.showSubMeshesBoundingBox != undefined && parsedInstance.showSubMeshesBoundingBox != null) {\r\n                    instance.showSubMeshesBoundingBox = parsedInstance.showSubMeshesBoundingBox;\r\n                }\r\n                if (parsedInstance.alphaIndex != undefined && parsedInstance.showSubMeshesBoundingBox != null) {\r\n                    instance.alphaIndex = parsedInstance.alphaIndex;\r\n                }\r\n\r\n                // Physics\r\n                if (parsedInstance.physicsImpostor) {\r\n                    instance.physicsImpostor = Mesh._PhysicsImpostorParser(scene, instance, parsedInstance);\r\n                }\r\n\r\n                // Actions\r\n                if (parsedInstance.actions !== undefined) {\r\n                    instance._waitingData.actions = parsedInstance.actions;\r\n                }\r\n\r\n                // Animation\r\n                if (parsedInstance.animations) {\r\n                    for (let animationIndex = 0; animationIndex < parsedInstance.animations.length; animationIndex++) {\r\n                        const parsedAnimation = parsedInstance.animations[animationIndex];\r\n                        const internalClass = GetClass(\"BABYLON.Animation\");\r\n                        if (internalClass) {\r\n                            instance.animations.push(internalClass.Parse(parsedAnimation));\r\n                        }\r\n                    }\r\n                    Node.ParseAnimationRanges(instance, parsedInstance, scene);\r\n\r\n                    if (parsedInstance.autoAnimate) {\r\n                        scene.beginAnimation(\r\n                            instance,\r\n                            parsedInstance.autoAnimateFrom,\r\n                            parsedInstance.autoAnimateTo,\r\n                            parsedInstance.autoAnimateLoop,\r\n                            parsedInstance.autoAnimateSpeed || 1.0\r\n                        );\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        // Thin instances\r\n        if (parsedMesh.thinInstances) {\r\n            const thinInstances = parsedMesh.thinInstances;\r\n\r\n            mesh.thinInstanceEnablePicking = !!thinInstances.enablePicking;\r\n\r\n            if (thinInstances.matrixData) {\r\n                mesh.thinInstanceSetBuffer(\"matrix\", new Float32Array(thinInstances.matrixData), 16, false);\r\n\r\n                mesh._thinInstanceDataStorage.matrixBufferSize = thinInstances.matrixBufferSize;\r\n                mesh._thinInstanceDataStorage.instancesCount = thinInstances.instancesCount;\r\n            } else {\r\n                mesh._thinInstanceDataStorage.matrixBufferSize = thinInstances.matrixBufferSize;\r\n            }\r\n\r\n            if (parsedMesh.thinInstances.userThinInstance) {\r\n                const userThinInstance = parsedMesh.thinInstances.userThinInstance;\r\n\r\n                for (const kind in userThinInstance.data) {\r\n                    mesh.thinInstanceSetBuffer(kind, new Float32Array(userThinInstance.data[kind]), userThinInstance.strides[kind], false);\r\n                    mesh._userThinInstanceBuffersStorage.sizes[kind] = userThinInstance.sizes[kind];\r\n                }\r\n            }\r\n        }\r\n\r\n        return mesh;\r\n    }\r\n\r\n    // Skeletons\r\n\r\n    /**\r\n     * Prepare internal position array for software CPU skinning\r\n     * @returns original positions used for CPU skinning. Useful for integrating Morphing with skeletons in same mesh\r\n     */\r\n    public setPositionsForCPUSkinning(): Nullable<Float32Array> {\r\n        const internalDataInfo = this._internalMeshDataInfo;\r\n        if (!internalDataInfo._sourcePositions) {\r\n            const source = this.getVerticesData(VertexBuffer.PositionKind);\r\n            if (!source) {\r\n                return internalDataInfo._sourcePositions;\r\n            }\r\n\r\n            internalDataInfo._sourcePositions = new Float32Array(<any>source);\r\n\r\n            if (!this.isVertexBufferUpdatable(VertexBuffer.PositionKind)) {\r\n                this.setVerticesData(VertexBuffer.PositionKind, source, true);\r\n            }\r\n        }\r\n        return internalDataInfo._sourcePositions;\r\n    }\r\n\r\n    /**\r\n     * Prepare internal normal array for software CPU skinning\r\n     * @returns original normals used for CPU skinning. Useful for integrating Morphing with skeletons in same mesh.\r\n     */\r\n    public setNormalsForCPUSkinning(): Nullable<Float32Array> {\r\n        const internalDataInfo = this._internalMeshDataInfo;\r\n\r\n        if (!internalDataInfo._sourceNormals) {\r\n            const source = this.getVerticesData(VertexBuffer.NormalKind);\r\n\r\n            if (!source) {\r\n                return internalDataInfo._sourceNormals;\r\n            }\r\n\r\n            internalDataInfo._sourceNormals = new Float32Array(<any>source);\r\n\r\n            if (!this.isVertexBufferUpdatable(VertexBuffer.NormalKind)) {\r\n                this.setVerticesData(VertexBuffer.NormalKind, source, true);\r\n            }\r\n        }\r\n        return internalDataInfo._sourceNormals;\r\n    }\r\n\r\n    /**\r\n     * Updates the vertex buffer by applying transformation from the bones\r\n     * @param skeleton defines the skeleton to apply to current mesh\r\n     * @returns the current mesh\r\n     */\r\n    public applySkeleton(skeleton: Skeleton): Mesh {\r\n        if (!this.geometry) {\r\n            return this;\r\n        }\r\n\r\n        if (this.geometry._softwareSkinningFrameId == this.getScene().getFrameId()) {\r\n            return this;\r\n        }\r\n\r\n        this.geometry._softwareSkinningFrameId = this.getScene().getFrameId();\r\n\r\n        if (!this.isVerticesDataPresent(VertexBuffer.PositionKind)) {\r\n            return this;\r\n        }\r\n        if (!this.isVerticesDataPresent(VertexBuffer.MatricesIndicesKind)) {\r\n            return this;\r\n        }\r\n        if (!this.isVerticesDataPresent(VertexBuffer.MatricesWeightsKind)) {\r\n            return this;\r\n        }\r\n\r\n        const hasNormals = this.isVerticesDataPresent(VertexBuffer.NormalKind);\r\n\r\n        const internalDataInfo = this._internalMeshDataInfo;\r\n\r\n        if (!internalDataInfo._sourcePositions) {\r\n            const submeshes = this.subMeshes.slice();\r\n            this.setPositionsForCPUSkinning();\r\n            this.subMeshes = submeshes;\r\n        }\r\n\r\n        if (hasNormals && !internalDataInfo._sourceNormals) {\r\n            this.setNormalsForCPUSkinning();\r\n        }\r\n\r\n        // positionsData checks for not being Float32Array will only pass at most once\r\n        let positionsData = this.getVerticesData(VertexBuffer.PositionKind);\r\n\r\n        if (!positionsData) {\r\n            return this;\r\n        }\r\n\r\n        if (!(positionsData instanceof Float32Array)) {\r\n            positionsData = new Float32Array(positionsData);\r\n        }\r\n\r\n        // normalsData checks for not being Float32Array will only pass at most once\r\n        let normalsData = this.getVerticesData(VertexBuffer.NormalKind);\r\n\r\n        if (hasNormals) {\r\n            if (!normalsData) {\r\n                return this;\r\n            }\r\n\r\n            if (!(normalsData instanceof Float32Array)) {\r\n                normalsData = new Float32Array(normalsData);\r\n            }\r\n        }\r\n\r\n        const matricesIndicesData = this.getVerticesData(VertexBuffer.MatricesIndicesKind);\r\n        const matricesWeightsData = this.getVerticesData(VertexBuffer.MatricesWeightsKind);\r\n\r\n        if (!matricesWeightsData || !matricesIndicesData) {\r\n            return this;\r\n        }\r\n\r\n        const needExtras = this.numBoneInfluencers > 4;\r\n        const matricesIndicesExtraData = needExtras ? this.getVerticesData(VertexBuffer.MatricesIndicesExtraKind) : null;\r\n        const matricesWeightsExtraData = needExtras ? this.getVerticesData(VertexBuffer.MatricesWeightsExtraKind) : null;\r\n\r\n        const skeletonMatrices = skeleton.getTransformMatrices(this);\r\n\r\n        const tempVector3 = Vector3.Zero();\r\n        const finalMatrix = new Matrix();\r\n        const tempMatrix = new Matrix();\r\n\r\n        let matWeightIdx = 0;\r\n        let inf: number;\r\n        for (let index = 0; index < positionsData.length; index += 3, matWeightIdx += 4) {\r\n            let weight: number;\r\n            for (inf = 0; inf < 4; inf++) {\r\n                weight = matricesWeightsData[matWeightIdx + inf];\r\n                if (weight > 0) {\r\n                    Matrix.FromFloat32ArrayToRefScaled(skeletonMatrices, Math.floor(matricesIndicesData[matWeightIdx + inf] * 16), weight, tempMatrix);\r\n                    finalMatrix.addToSelf(tempMatrix);\r\n                }\r\n            }\r\n            if (needExtras) {\r\n                for (inf = 0; inf < 4; inf++) {\r\n                    weight = matricesWeightsExtraData![matWeightIdx + inf];\r\n                    if (weight > 0) {\r\n                        Matrix.FromFloat32ArrayToRefScaled(skeletonMatrices, Math.floor(matricesIndicesExtraData![matWeightIdx + inf] * 16), weight, tempMatrix);\r\n                        finalMatrix.addToSelf(tempMatrix);\r\n                    }\r\n                }\r\n            }\r\n\r\n            Vector3.TransformCoordinatesFromFloatsToRef(\r\n                internalDataInfo._sourcePositions![index],\r\n                internalDataInfo._sourcePositions![index + 1],\r\n                internalDataInfo._sourcePositions![index + 2],\r\n                finalMatrix,\r\n                tempVector3\r\n            );\r\n            tempVector3.toArray(positionsData, index);\r\n\r\n            if (hasNormals) {\r\n                Vector3.TransformNormalFromFloatsToRef(\r\n                    internalDataInfo._sourceNormals![index],\r\n                    internalDataInfo._sourceNormals![index + 1],\r\n                    internalDataInfo._sourceNormals![index + 2],\r\n                    finalMatrix,\r\n                    tempVector3\r\n                );\r\n                tempVector3.toArray(normalsData!, index);\r\n            }\r\n\r\n            finalMatrix.reset();\r\n        }\r\n\r\n        this.updateVerticesData(VertexBuffer.PositionKind, positionsData);\r\n        if (hasNormals) {\r\n            this.updateVerticesData(VertexBuffer.NormalKind, normalsData!);\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    // Tools\r\n\r\n    /**\r\n     * Returns an object containing a min and max Vector3 which are the minimum and maximum vectors of each mesh bounding box from the passed array, in the world coordinates\r\n     * @param meshes defines the list of meshes to scan\r\n     * @returns an object `{min:` Vector3`, max:` Vector3`}`\r\n     */\r\n    public static MinMax(meshes: AbstractMesh[]): { min: Vector3; max: Vector3 } {\r\n        let minVector: Nullable<Vector3> = null;\r\n        let maxVector: Nullable<Vector3> = null;\r\n\r\n        for (const mesh of meshes) {\r\n            const boundingInfo = mesh.getBoundingInfo();\r\n\r\n            const boundingBox = boundingInfo.boundingBox;\r\n            if (!minVector || !maxVector) {\r\n                minVector = boundingBox.minimumWorld;\r\n                maxVector = boundingBox.maximumWorld;\r\n            } else {\r\n                minVector.minimizeInPlace(boundingBox.minimumWorld);\r\n                maxVector.maximizeInPlace(boundingBox.maximumWorld);\r\n            }\r\n        }\r\n\r\n        if (!minVector || !maxVector) {\r\n            return {\r\n                min: Vector3.Zero(),\r\n                max: Vector3.Zero(),\r\n            };\r\n        }\r\n\r\n        return {\r\n            min: minVector,\r\n            max: maxVector,\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Returns the center of the `{min:` Vector3`, max:` Vector3`}` or the center of MinMax vector3 computed from a mesh array\r\n     * @param meshesOrMinMaxVector could be an array of meshes or a `{min:` Vector3`, max:` Vector3`}` object\r\n     * @returns a vector3\r\n     */\r\n    public static Center(meshesOrMinMaxVector: { min: Vector3; max: Vector3 } | AbstractMesh[]): Vector3 {\r\n        const minMaxVector = meshesOrMinMaxVector instanceof Array ? Mesh.MinMax(meshesOrMinMaxVector) : meshesOrMinMaxVector;\r\n        return Vector3.Center(minMaxVector.min, minMaxVector.max);\r\n    }\r\n\r\n    /**\r\n     * Merge the array of meshes into a single mesh for performance reasons.\r\n     * @param meshes array of meshes with the vertices to merge. Entries cannot be empty meshes.\r\n     * @param disposeSource when true (default), dispose of the vertices from the source meshes.\r\n     * @param allow32BitsIndices when the sum of the vertices > 64k, this must be set to true.\r\n     * @param meshSubclass (optional) can be set to a Mesh where the merged vertices will be inserted.\r\n     * @param subdivideWithSubMeshes when true (false default), subdivide mesh into subMeshes.\r\n     * @param multiMultiMaterials when true (false default), subdivide mesh into subMeshes with multiple materials, ignores subdivideWithSubMeshes.\r\n     * @returns a new mesh\r\n     */\r\n    public static MergeMeshes(\r\n        meshes: Array<Mesh>,\r\n        disposeSource = true,\r\n        allow32BitsIndices?: boolean,\r\n        meshSubclass?: Mesh,\r\n        subdivideWithSubMeshes?: boolean,\r\n        multiMultiMaterials?: boolean\r\n    ) {\r\n        return runCoroutineSync(Mesh._MergeMeshesCoroutine(meshes, disposeSource, allow32BitsIndices, meshSubclass, subdivideWithSubMeshes, multiMultiMaterials, false));\r\n    }\r\n\r\n    /**\r\n     * Merge the array of meshes into a single mesh for performance reasons.\r\n     * @param meshes array of meshes with the vertices to merge. Entries cannot be empty meshes.\r\n     * @param disposeSource when true (default), dispose of the vertices from the source meshes.\r\n     * @param allow32BitsIndices when the sum of the vertices > 64k, this must be set to true.\r\n     * @param meshSubclass (optional) can be set to a Mesh where the merged vertices will be inserted.\r\n     * @param subdivideWithSubMeshes when true (false default), subdivide mesh into subMeshes.\r\n     * @param multiMultiMaterials when true (false default), subdivide mesh into subMeshes with multiple materials, ignores subdivideWithSubMeshes.\r\n     * @returns a new mesh\r\n     */\r\n    public static async MergeMeshesAsync(\r\n        meshes: Array<Mesh>,\r\n        disposeSource = true,\r\n        allow32BitsIndices?: boolean,\r\n        meshSubclass?: Mesh,\r\n        subdivideWithSubMeshes?: boolean,\r\n        multiMultiMaterials?: boolean\r\n    ) {\r\n        return await runCoroutineAsync(\r\n            Mesh._MergeMeshesCoroutine(meshes, disposeSource, allow32BitsIndices, meshSubclass, subdivideWithSubMeshes, multiMultiMaterials, true),\r\n            createYieldingScheduler()\r\n        );\r\n    }\r\n\r\n    private static *_MergeMeshesCoroutine(\r\n        meshes: Array<Mesh>,\r\n        disposeSource = true,\r\n        allow32BitsIndices: boolean | undefined,\r\n        meshSubclass: Mesh | undefined,\r\n        subdivideWithSubMeshes: boolean | undefined,\r\n        multiMultiMaterials: boolean | undefined,\r\n        isAsync: boolean\r\n    ): Coroutine<Nullable<Mesh>> {\r\n        // Remove any null/undefined entries from the mesh array\r\n        meshes = meshes.filter(Boolean);\r\n\r\n        if (meshes.length === 0) {\r\n            return null;\r\n        }\r\n\r\n        let index: number;\r\n        if (!allow32BitsIndices) {\r\n            let totalVertices = 0;\r\n\r\n            // Counting vertices\r\n            for (index = 0; index < meshes.length; index++) {\r\n                totalVertices += meshes[index].getTotalVertices();\r\n\r\n                if (totalVertices >= 65536) {\r\n                    Logger.Warn(\"Cannot merge meshes because resulting mesh will have more than 65536 vertices. Please use allow32BitsIndices = true to use 32 bits indices\");\r\n                    return null;\r\n                }\r\n            }\r\n        }\r\n        if (multiMultiMaterials) {\r\n            subdivideWithSubMeshes = false;\r\n        }\r\n        const materialArray: Array<Material> = new Array<Material>();\r\n        const materialIndexArray: Array<number> = new Array<number>();\r\n        // Merge\r\n        const indiceArray: Array<number> = new Array<number>();\r\n        const currentsideOrientation = meshes[0].sideOrientation;\r\n\r\n        for (index = 0; index < meshes.length; index++) {\r\n            const mesh = meshes[index];\r\n            if (mesh.isAnInstance) {\r\n                Logger.Warn(\"Cannot merge instance meshes.\");\r\n                return null;\r\n            }\r\n\r\n            if (currentsideOrientation !== mesh.sideOrientation) {\r\n                Logger.Warn(\"Cannot merge meshes with different sideOrientation values.\");\r\n                return null;\r\n            }\r\n\r\n            if (subdivideWithSubMeshes) {\r\n                indiceArray.push(mesh.getTotalIndices());\r\n            }\r\n\r\n            if (multiMultiMaterials) {\r\n                if (mesh.material) {\r\n                    const material = mesh.material;\r\n                    if (material instanceof MultiMaterial) {\r\n                        for (let matIndex = 0; matIndex < material.subMaterials.length; matIndex++) {\r\n                            if (materialArray.indexOf(<Material>material.subMaterials[matIndex]) < 0) {\r\n                                materialArray.push(<Material>material.subMaterials[matIndex]);\r\n                            }\r\n                        }\r\n                        for (let subIndex = 0; subIndex < mesh.subMeshes.length; subIndex++) {\r\n                            materialIndexArray.push(materialArray.indexOf(<Material>material.subMaterials[mesh.subMeshes[subIndex].materialIndex]));\r\n                            indiceArray.push(mesh.subMeshes[subIndex].indexCount);\r\n                        }\r\n                    } else {\r\n                        if (materialArray.indexOf(material) < 0) {\r\n                            materialArray.push(material);\r\n                        }\r\n                        for (let subIndex = 0; subIndex < mesh.subMeshes.length; subIndex++) {\r\n                            materialIndexArray.push(materialArray.indexOf(material));\r\n                            indiceArray.push(mesh.subMeshes[subIndex].indexCount);\r\n                        }\r\n                    }\r\n                } else {\r\n                    for (let subIndex = 0; subIndex < mesh.subMeshes.length; subIndex++) {\r\n                        materialIndexArray.push(0);\r\n                        indiceArray.push(mesh.subMeshes[subIndex].indexCount);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        const source = meshes[0];\r\n\r\n        const getVertexDataFromMesh = (mesh: Mesh) => {\r\n            const wm = mesh.computeWorldMatrix(true);\r\n            const vertexData = VertexData.ExtractFromMesh(mesh, false, false);\r\n            return { vertexData, transform: wm };\r\n        };\r\n\r\n        const { vertexData: sourceVertexData, transform: sourceTransform } = getVertexDataFromMesh(source);\r\n        if (isAsync) {\r\n            yield;\r\n        }\r\n\r\n        const meshVertexDatas = new Array<{ vertexData: VertexData; transform?: Matrix }>(meshes.length - 1);\r\n        for (let i = 1; i < meshes.length; i++) {\r\n            meshVertexDatas[i - 1] = getVertexDataFromMesh(meshes[i]);\r\n            if (isAsync) {\r\n                yield;\r\n            }\r\n        }\r\n\r\n        const mergeCoroutine = sourceVertexData._mergeCoroutine(sourceTransform, meshVertexDatas, allow32BitsIndices, isAsync, !disposeSource);\r\n        let mergeCoroutineStep = mergeCoroutine.next();\r\n        while (!mergeCoroutineStep.done) {\r\n            if (isAsync) {\r\n                yield;\r\n            }\r\n            mergeCoroutineStep = mergeCoroutine.next();\r\n        }\r\n        const vertexData = mergeCoroutineStep.value;\r\n\r\n        if (!meshSubclass) {\r\n            meshSubclass = new Mesh(source.name + \"_merged\", source.getScene());\r\n        }\r\n\r\n        const applyToCoroutine = vertexData._applyToCoroutine(meshSubclass, undefined, isAsync);\r\n        let applyToCoroutineStep = applyToCoroutine.next();\r\n        while (!applyToCoroutineStep.done) {\r\n            if (isAsync) {\r\n                yield;\r\n            }\r\n            applyToCoroutineStep = applyToCoroutine.next();\r\n        }\r\n\r\n        // Setting properties\r\n        meshSubclass.checkCollisions = source.checkCollisions;\r\n        meshSubclass.sideOrientation = source.sideOrientation;\r\n\r\n        // Cleaning\r\n        if (disposeSource) {\r\n            for (index = 0; index < meshes.length; index++) {\r\n                meshes[index].dispose();\r\n            }\r\n        }\r\n\r\n        // Subdivide\r\n        if (subdivideWithSubMeshes || multiMultiMaterials) {\r\n            //-- removal of global submesh\r\n            meshSubclass.releaseSubMeshes();\r\n            index = 0;\r\n            let offset = 0;\r\n\r\n            //-- apply subdivision according to index table\r\n            while (index < indiceArray.length) {\r\n                SubMesh.CreateFromIndices(0, offset, indiceArray[index], meshSubclass, undefined, false);\r\n                offset += indiceArray[index];\r\n                index++;\r\n            }\r\n\r\n            for (const subMesh of meshSubclass.subMeshes) {\r\n                subMesh.refreshBoundingInfo();\r\n            }\r\n\r\n            meshSubclass.computeWorldMatrix(true);\r\n        }\r\n\r\n        if (multiMultiMaterials) {\r\n            const newMultiMaterial = new MultiMaterial(source.name + \"_merged\", source.getScene());\r\n            newMultiMaterial.subMaterials = materialArray;\r\n            for (let subIndex = 0; subIndex < meshSubclass.subMeshes.length; subIndex++) {\r\n                meshSubclass.subMeshes[subIndex].materialIndex = materialIndexArray[subIndex];\r\n            }\r\n            meshSubclass.material = newMultiMaterial;\r\n        } else {\r\n            meshSubclass.material = source.material;\r\n        }\r\n\r\n        return meshSubclass;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public addInstance(instance: InstancedMesh) {\r\n        instance._indexInSourceMeshInstanceArray = this.instances.length;\r\n        this.instances.push(instance);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public removeInstance(instance: InstancedMesh) {\r\n        // Remove from mesh\r\n        const index = instance._indexInSourceMeshInstanceArray;\r\n        if (index != -1) {\r\n            if (index !== this.instances.length - 1) {\r\n                const last = this.instances[this.instances.length - 1];\r\n                this.instances[index] = last;\r\n                last._indexInSourceMeshInstanceArray = index;\r\n            }\r\n\r\n            instance._indexInSourceMeshInstanceArray = -1;\r\n            this.instances.pop();\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public override _shouldConvertRHS() {\r\n        return this._scene.useRightHandedSystem && this.sideOrientation === Material.CounterClockWiseSideOrientation;\r\n    }\r\n\r\n    /** @internal */\r\n    public _getRenderingFillMode(fillMode: number): number {\r\n        const scene = this.getScene();\r\n\r\n        if (scene.forcePointsCloud) {\r\n            return Material.PointFillMode;\r\n        }\r\n\r\n        if (scene.forceWireframe) {\r\n            return Material.WireFrameFillMode;\r\n        }\r\n\r\n        return this.overrideRenderingFillMode ?? fillMode;\r\n    }\r\n\r\n    // deprecated methods\r\n    /**\r\n     * Sets the mesh material by the material or multiMaterial `id` property\r\n     * @param id is a string identifying the material or the multiMaterial\r\n     * @returns the current mesh\r\n     * @deprecated Please use MeshBuilder instead Please use setMaterialById instead\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public setMaterialByID(id: string): Mesh {\r\n        return this.setMaterialById(id);\r\n    }\r\n\r\n    /**\r\n     * Creates a ribbon mesh.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/param\r\n     * @param name defines the name of the mesh to create\r\n     * @param pathArray is a required array of paths, what are each an array of successive Vector3. The pathArray parameter depicts the ribbon geometry.\r\n     * @param closeArray creates a seam between the first and the last paths of the path array (default is false)\r\n     * @param closePath creates a seam between the first and the last points of each path of the path array\r\n     * @param offset is taken in account only if the `pathArray` is containing a single path\r\n     * @param scene defines the hosting scene\r\n     * @param updatable defines if the mesh must be flagged as updatable\r\n     * @param sideOrientation defines the mesh side orientation (https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/set#side-orientation)\r\n     * @param instance defines an instance of an existing Ribbon object to be updated with the passed `pathArray` parameter (https://doc.babylonjs.com/how_to/How_to_dynamically_morph_a_mesh#ribbon)\r\n     * @returns a new Mesh\r\n     * @deprecated Please use MeshBuilder instead\r\n     */\r\n    public static CreateRibbon(\r\n        name: string,\r\n        pathArray: Vector3[][],\r\n        closeArray: boolean,\r\n        closePath: boolean,\r\n        offset: number,\r\n        scene?: Scene,\r\n        updatable?: boolean,\r\n        sideOrientation?: number,\r\n        instance?: Mesh\r\n    ): Mesh {\r\n        throw new Error(\"Import MeshBuilder to populate this function\");\r\n    }\r\n\r\n    /**\r\n     * Creates a plane polygonal mesh.  By default, this is a disc.\r\n     * @param name defines the name of the mesh to create\r\n     * @param radius sets the radius size (float) of the polygon (default 0.5)\r\n     * @param tessellation sets the number of polygon sides (positive integer, default 64). So a tessellation valued to 3 will build a triangle, to 4 a square, etc\r\n     * @param scene defines the hosting scene\r\n     * @param updatable defines if the mesh must be flagged as updatable\r\n     * @param sideOrientation defines the mesh side orientation (https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/set#side-orientation)\r\n     * @returns a new Mesh\r\n     * @deprecated Please use MeshBuilder instead\r\n     */\r\n    public static CreateDisc(name: string, radius: number, tessellation: number, scene: Nullable<Scene>, updatable?: boolean, sideOrientation?: number): Mesh {\r\n        throw new Error(\"Import MeshBuilder to populate this function\");\r\n    }\r\n\r\n    /**\r\n     * Creates a box mesh.\r\n     * @param name defines the name of the mesh to create\r\n     * @param size sets the size (float) of each box side (default 1)\r\n     * @param scene defines the hosting scene\r\n     * @param updatable defines if the mesh must be flagged as updatable\r\n     * @param sideOrientation defines the mesh side orientation (https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/set#side-orientation)\r\n     * @returns a new Mesh\r\n     * @deprecated Please use MeshBuilder instead\r\n     */\r\n    public static CreateBox(name: string, size: number, scene: Nullable<Scene>, updatable?: boolean, sideOrientation?: number): Mesh {\r\n        throw new Error(\"Import MeshBuilder to populate this function\");\r\n    }\r\n\r\n    /**\r\n     * Creates a sphere mesh.\r\n     * @param name defines the name of the mesh to create\r\n     * @param segments sets the sphere number of horizontal stripes (positive integer, default 32)\r\n     * @param diameter sets the diameter size (float) of the sphere (default 1)\r\n     * @param scene defines the hosting scene\r\n     * @param updatable defines if the mesh must be flagged as updatable\r\n     * @param sideOrientation defines the mesh side orientation (https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/set#side-orientation)\r\n     * @returns a new Mesh\r\n     * @deprecated Please use MeshBuilder instead\r\n     */\r\n    public static CreateSphere(name: string, segments: number, diameter: number, scene?: Scene, updatable?: boolean, sideOrientation?: number): Mesh {\r\n        throw new Error(\"Import MeshBuilder to populate this function\");\r\n    }\r\n\r\n    /**\r\n     * Creates a hemisphere mesh.\r\n     * @param name defines the name of the mesh to create\r\n     * @param segments sets the sphere number of horizontal stripes (positive integer, default 32)\r\n     * @param diameter sets the diameter size (float) of the sphere (default 1)\r\n     * @param scene defines the hosting scene\r\n     * @returns a new Mesh\r\n     * @deprecated Please use MeshBuilder instead\r\n     */\r\n    public static CreateHemisphere(name: string, segments: number, diameter: number, scene?: Scene): Mesh {\r\n        throw new Error(\"Import MeshBuilder to populate this function\");\r\n    }\r\n\r\n    /**\r\n     * Creates a cylinder or a cone mesh.\r\n     * @param name defines the name of the mesh to create\r\n     * @param height sets the height size (float) of the cylinder/cone (float, default 2)\r\n     * @param diameterTop set the top cap diameter (floats, default 1)\r\n     * @param diameterBottom set the bottom cap diameter (floats, default 1). This value can't be zero\r\n     * @param tessellation sets the number of cylinder sides (positive integer, default 24). Set it to 3 to get a prism for instance\r\n     * @param subdivisions sets the number of rings along the cylinder height (positive integer, default 1)\r\n     * @param scene defines the hosting scene\r\n     * @param updatable defines if the mesh must be flagged as updatable\r\n     * @param sideOrientation defines the mesh side orientation (https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/set#side-orientation)\r\n     * @returns a new Mesh\r\n     * @deprecated Please use MeshBuilder instead\r\n     */\r\n    public static CreateCylinder(\r\n        name: string,\r\n        height: number,\r\n        diameterTop: number,\r\n        diameterBottom: number,\r\n        tessellation: number,\r\n        subdivisions: any,\r\n        scene?: Scene,\r\n        updatable?: any,\r\n        sideOrientation?: number\r\n    ): Mesh {\r\n        throw new Error(\"Import MeshBuilder to populate this function\");\r\n    }\r\n\r\n    // Torus  (Code from SharpDX.org)\r\n    /**\r\n     * Creates a torus mesh.\r\n     * @param name defines the name of the mesh to create\r\n     * @param diameter sets the diameter size (float) of the torus (default 1)\r\n     * @param thickness sets the diameter size of the tube of the torus (float, default 0.5)\r\n     * @param tessellation sets the number of torus sides (positive integer, default 16)\r\n     * @param scene defines the hosting scene\r\n     * @param updatable defines if the mesh must be flagged as updatable\r\n     * @param sideOrientation defines the mesh side orientation (https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/set#side-orientation)\r\n     * @returns a new Mesh\r\n     * @deprecated Please use MeshBuilder instead\r\n     */\r\n    public static CreateTorus(name: string, diameter: number, thickness: number, tessellation: number, scene?: Scene, updatable?: boolean, sideOrientation?: number): Mesh {\r\n        throw new Error(\"Import MeshBuilder to populate this function\");\r\n    }\r\n\r\n    /**\r\n     * Creates a torus knot mesh.\r\n     * @param name defines the name of the mesh to create\r\n     * @param radius sets the global radius size (float) of the torus knot (default 2)\r\n     * @param tube sets the diameter size of the tube of the torus (float, default 0.5)\r\n     * @param radialSegments sets the number of sides on each tube segments (positive integer, default 32)\r\n     * @param tubularSegments sets the number of tubes to decompose the knot into (positive integer, default 32)\r\n     * @param p the number of windings on X axis (positive integers, default 2)\r\n     * @param q the number of windings on Y axis (positive integers, default 3)\r\n     * @param scene defines the hosting scene\r\n     * @param updatable defines if the mesh must be flagged as updatable\r\n     * @param sideOrientation defines the mesh side orientation (https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/set#side-orientation)\r\n     * @returns a new Mesh\r\n     * @deprecated Please use MeshBuilder instead\r\n     */\r\n    public static CreateTorusKnot(\r\n        name: string,\r\n        radius: number,\r\n        tube: number,\r\n        radialSegments: number,\r\n        tubularSegments: number,\r\n        p: number,\r\n        q: number,\r\n        scene?: Scene,\r\n        updatable?: boolean,\r\n        sideOrientation?: number\r\n    ): Mesh {\r\n        throw new Error(\"Import MeshBuilder to populate this function\");\r\n    }\r\n\r\n    /**\r\n     * Creates a line mesh..\r\n     * @param name defines the name of the mesh to create\r\n     * @param points is an array successive Vector3\r\n     * @param scene defines the hosting scene\r\n     * @param updatable defines if the mesh must be flagged as updatable\r\n     * @param instance is an instance of an existing LineMesh object to be updated with the passed `points` parameter (https://doc.babylonjs.com/how_to/How_to_dynamically_morph_a_mesh#lines-and-dashedlines).\r\n     * @returns a new Mesh\r\n     * @deprecated Please use MeshBuilder instead\r\n     */\r\n    public static CreateLines(name: string, points: Vector3[], scene: Nullable<Scene>, updatable: boolean, instance?: Nullable<LinesMesh>): LinesMesh {\r\n        throw new Error(\"Import MeshBuilder to populate this function\");\r\n    }\r\n\r\n    /**\r\n     * Creates a dashed line mesh.\r\n     * @param name defines the name of the mesh to create\r\n     * @param points is an array successive Vector3\r\n     * @param dashSize is the size of the dashes relatively the dash number (positive float, default 3)\r\n     * @param gapSize is the size of the gap between two successive dashes relatively the dash number (positive float, default 1)\r\n     * @param dashNb is the intended total number of dashes (positive integer, default 200)\r\n     * @param scene defines the hosting scene\r\n     * @param updatable defines if the mesh must be flagged as updatable\r\n     * @param instance is an instance of an existing LineMesh object to be updated with the passed `points` parameter (https://doc.babylonjs.com/how_to/How_to_dynamically_morph_a_mesh#lines-and-dashedlines)\r\n     * @returns a new Mesh\r\n     * @deprecated Please use MeshBuilder instead\r\n     */\r\n    public static CreateDashedLines(\r\n        name: string,\r\n        points: Vector3[],\r\n        dashSize: number,\r\n        gapSize: number,\r\n        dashNb: number,\r\n        scene: Nullable<Scene>,\r\n        updatable?: boolean,\r\n        instance?: LinesMesh\r\n    ): LinesMesh {\r\n        throw new Error(\"Import MeshBuilder to populate this function\");\r\n    }\r\n\r\n    /**\r\n     * Creates a polygon mesh.Please consider using the same method from the MeshBuilder class instead\r\n     * The polygon's shape will depend on the input parameters and is constructed parallel to a ground mesh.\r\n     * The parameter `shape` is a required array of successive Vector3 representing the corners of the polygon in th XoZ plane, that is y = 0 for all vectors.\r\n     * You can set the mesh side orientation with the values : Mesh.FRONTSIDE (default), Mesh.BACKSIDE or Mesh.DOUBLESIDE\r\n     * The mesh can be set to updatable with the boolean parameter `updatable` (default false) if its internal geometry is supposed to change once created.\r\n     * Remember you can only change the shape positions, not their number when updating a polygon.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/param#non-regular-polygon\r\n     * @param name defines the name of the mesh to create\r\n     * @param shape is a required array of successive Vector3 representing the corners of the polygon in th XoZ plane, that is y = 0 for all vectors\r\n     * @param scene defines the hosting scene\r\n     * @param holes is a required array of arrays of successive Vector3 used to defines holes in the polygon\r\n     * @param updatable defines if the mesh must be flagged as updatable\r\n     * @param sideOrientation defines the mesh side orientation (https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/set#side-orientation)\r\n     * @param earcutInjection can be used to inject your own earcut reference\r\n     * @returns a new Mesh\r\n     * @deprecated Please use MeshBuilder instead\r\n     */\r\n    public static CreatePolygon(name: string, shape: Vector3[], scene: Scene, holes?: Vector3[][], updatable?: boolean, sideOrientation?: number, earcutInjection?: any): Mesh {\r\n        throw new Error(\"Import MeshBuilder to populate this function\");\r\n    }\r\n\r\n    /**\r\n     * Creates an extruded polygon mesh, with depth in the Y direction..\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/param#extruded-non-regular-polygon\r\n     * @param name defines the name of the mesh to create\r\n     * @param shape is a required array of successive Vector3 representing the corners of the polygon in th XoZ plane, that is y = 0 for all vectors\r\n     * @param depth defines the height of extrusion\r\n     * @param scene defines the hosting scene\r\n     * @param holes is a required array of arrays of successive Vector3 used to defines holes in the polygon\r\n     * @param updatable defines if the mesh must be flagged as updatable\r\n     * @param sideOrientation defines the mesh side orientation (https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/set#side-orientation)\r\n     * @param earcutInjection can be used to inject your own earcut reference\r\n     * @returns a new Mesh\r\n     * @deprecated Please use MeshBuilder instead\r\n     */\r\n    public static ExtrudePolygon(\r\n        name: string,\r\n        shape: Vector3[],\r\n        depth: number,\r\n        scene: Scene,\r\n        holes?: Vector3[][],\r\n        updatable?: boolean,\r\n        sideOrientation?: number,\r\n        earcutInjection?: any\r\n    ): Mesh {\r\n        throw new Error(\"Import MeshBuilder to populate this function\");\r\n    }\r\n\r\n    /**\r\n     * Creates an extruded shape mesh.\r\n     * The extrusion is a parametric shape. It has no predefined shape. Its final shape will depend on the input parameters.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/param\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/param#extruded-shapes\r\n     * @param name defines the name of the mesh to create\r\n     * @param shape is a required array of successive Vector3. This array depicts the shape to be extruded in its local space : the shape must be designed in the xOy plane and will be extruded along the Z axis\r\n     * @param path is a required array of successive Vector3. This is the axis curve the shape is extruded along\r\n     * @param scale is the value to scale the shape\r\n     * @param rotation is the angle value to rotate the shape each step (each path point), from the former step (so rotation added each step) along the curve\r\n     * @param cap sets the way the extruded shape is capped. Possible values : Mesh.NO_CAP (default), Mesh.CAP_START, Mesh.CAP_END, Mesh.CAP_ALL\r\n     * @param scene defines the hosting scene\r\n     * @param updatable defines if the mesh must be flagged as updatable\r\n     * @param sideOrientation defines the mesh side orientation (https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/set#side-orientation)\r\n     * @param instance is an instance of an existing ExtrudedShape object to be updated with the passed `shape`, `path`, `scale` or `rotation` parameters (https://doc.babylonjs.com/how_to/How_to_dynamically_morph_a_mesh#extruded-shape)\r\n     * @returns a new Mesh\r\n     * @deprecated Please use MeshBuilder instead\r\n     */\r\n    public static ExtrudeShape(\r\n        name: string,\r\n        shape: Vector3[],\r\n        path: Vector3[],\r\n        scale: number,\r\n        rotation: number,\r\n        cap: number,\r\n        scene: Nullable<Scene>,\r\n        updatable?: boolean,\r\n        sideOrientation?: number,\r\n        instance?: Mesh\r\n    ): Mesh {\r\n        throw new Error(\"Import MeshBuilder to populate this function\");\r\n    }\r\n\r\n    /**\r\n     * Creates an custom extruded shape mesh.\r\n     * The custom extrusion is a parametric shape.\r\n     * It has no predefined shape. Its final shape will depend on the input parameters.\r\n     *\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/param#extruded-shapes\r\n     * @param name defines the name of the mesh to create\r\n     * @param shape is a required array of successive Vector3. This array depicts the shape to be extruded in its local space : the shape must be designed in the xOy plane and will be extruded along the Z axis\r\n     * @param path is a required array of successive Vector3. This is the axis curve the shape is extruded along\r\n     * @param scaleFunction is a custom Javascript function called on each path point\r\n     * @param rotationFunction is a custom Javascript function called on each path point\r\n     * @param ribbonCloseArray forces the extrusion underlying ribbon to close all the paths in its `pathArray`\r\n     * @param ribbonClosePath forces the extrusion underlying ribbon to close its `pathArray`\r\n     * @param cap sets the way the extruded shape is capped. Possible values : Mesh.NO_CAP (default), Mesh.CAP_START, Mesh.CAP_END, Mesh.CAP_ALL\r\n     * @param scene defines the hosting scene\r\n     * @param updatable defines if the mesh must be flagged as updatable\r\n     * @param sideOrientation defines the mesh side orientation (https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/set#side-orientation)\r\n     * @param instance is an instance of an existing ExtrudedShape object to be updated with the passed `shape`, `path`, `scale` or `rotation` parameters (https://doc.babylonjs.com/features/featuresDeepDive/mesh/dynamicMeshMorph#extruded-shape)\r\n     * @returns a new Mesh\r\n     * @deprecated Please use MeshBuilder instead\r\n     */\r\n    public static ExtrudeShapeCustom(\r\n        name: string,\r\n        shape: Vector3[],\r\n        path: Vector3[],\r\n        scaleFunction: Nullable<{ (i: number, distance: number): number }>,\r\n        rotationFunction: Nullable<{ (i: number, distance: number): number }>,\r\n        ribbonCloseArray: boolean,\r\n        ribbonClosePath: boolean,\r\n        cap: number,\r\n        scene: Scene,\r\n        updatable?: boolean,\r\n        sideOrientation?: number,\r\n        instance?: Mesh\r\n    ): Mesh {\r\n        throw new Error(\"Import MeshBuilder to populate this function\");\r\n    }\r\n\r\n    /**\r\n     * Creates lathe mesh.\r\n     * The lathe is a shape with a symmetry axis : a 2D model shape is rotated around this axis to design the lathe.\r\n     * @param name defines the name of the mesh to create\r\n     * @param shape is a required array of successive Vector3. This array depicts the shape to be rotated in its local space : the shape must be designed in the xOy plane and will be rotated around the Y axis. It's usually a 2D shape, so the Vector3 z coordinates are often set to zero\r\n     * @param radius is the radius value of the lathe\r\n     * @param tessellation is the side number of the lathe.\r\n     * @param scene defines the hosting scene\r\n     * @param updatable defines if the mesh must be flagged as updatable\r\n     * @param sideOrientation defines the mesh side orientation (https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/set#side-orientation)\r\n     * @returns a new Mesh\r\n     * @deprecated Please use MeshBuilder instead\r\n     */\r\n    public static CreateLathe(name: string, shape: Vector3[], radius: number, tessellation: number, scene: Scene, updatable?: boolean, sideOrientation?: number): Mesh {\r\n        throw new Error(\"Import MeshBuilder to populate this function\");\r\n    }\r\n\r\n    /**\r\n     * Creates a plane mesh.\r\n     * @param name defines the name of the mesh to create\r\n     * @param size sets the size (float) of both sides of the plane at once (default 1)\r\n     * @param scene defines the hosting scene\r\n     * @param updatable defines if the mesh must be flagged as updatable\r\n     * @param sideOrientation defines the mesh side orientation (https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/set#side-orientation)\r\n     * @returns a new Mesh\r\n     * @deprecated Please use MeshBuilder instead\r\n     */\r\n    public static CreatePlane(name: string, size: number, scene: Scene, updatable?: boolean, sideOrientation?: number): Mesh {\r\n        throw new Error(\"Import MeshBuilder to populate this function\");\r\n    }\r\n\r\n    /**\r\n     * Creates a ground mesh.\r\n     * @param name defines the name of the mesh to create\r\n     * @param width set the width of the ground\r\n     * @param height set the height of the ground\r\n     * @param subdivisions sets the number of subdivisions per side\r\n     * @param scene defines the hosting scene\r\n     * @param updatable defines if the mesh must be flagged as updatable\r\n     * @returns a new Mesh\r\n     * @deprecated Please use MeshBuilder instead\r\n     */\r\n    public static CreateGround(name: string, width: number, height: number, subdivisions: number, scene?: Scene, updatable?: boolean): Mesh {\r\n        throw new Error(\"Import MeshBuilder to populate this function\");\r\n    }\r\n\r\n    /**\r\n     * Creates a tiled ground mesh.\r\n     * @param name defines the name of the mesh to create\r\n     * @param xmin set the ground minimum X coordinate\r\n     * @param zmin set the ground minimum Y coordinate\r\n     * @param xmax set the ground maximum X coordinate\r\n     * @param zmax set the ground maximum Z coordinate\r\n     * @param subdivisions is an object `{w: positive integer, h: positive integer}` (default `{w: 6, h: 6}`). `w` and `h` are the numbers of subdivisions on the ground width and height. Each subdivision is called a tile\r\n     * @param precision is an object `{w: positive integer, h: positive integer}` (default `{w: 2, h: 2}`). `w` and `h` are the numbers of subdivisions on the ground width and height of each tile\r\n     * @param scene defines the hosting scene\r\n     * @param updatable defines if the mesh must be flagged as updatable\r\n     * @returns a new Mesh\r\n     * @deprecated Please use MeshBuilder instead\r\n     */\r\n    public static CreateTiledGround(\r\n        name: string,\r\n        xmin: number,\r\n        zmin: number,\r\n        xmax: number,\r\n        zmax: number,\r\n        subdivisions: { w: number; h: number },\r\n        precision: { w: number; h: number },\r\n        scene: Scene,\r\n        updatable?: boolean\r\n    ): Mesh {\r\n        throw new Error(\"Import MeshBuilder to populate this function\");\r\n    }\r\n\r\n    /**\r\n     * Creates a ground mesh from a height map.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/set/height_map\r\n     * @param name defines the name of the mesh to create\r\n     * @param url sets the URL of the height map image resource\r\n     * @param width set the ground width size\r\n     * @param height set the ground height size\r\n     * @param subdivisions sets the number of subdivision per side\r\n     * @param minHeight is the minimum altitude on the ground\r\n     * @param maxHeight is the maximum altitude on the ground\r\n     * @param scene defines the hosting scene\r\n     * @param updatable defines if the mesh must be flagged as updatable\r\n     * @param onReady  is a callback function that will be called  once the mesh is built (the height map download can last some time)\r\n     * @param alphaFilter will filter any data where the alpha channel is below this value, defaults 0 (all data visible)\r\n     * @returns a new Mesh\r\n     * @deprecated Please use MeshBuilder instead\r\n     */\r\n    public static CreateGroundFromHeightMap(\r\n        name: string,\r\n        url: string,\r\n        width: number,\r\n        height: number,\r\n        subdivisions: number,\r\n        minHeight: number,\r\n        maxHeight: number,\r\n        scene: Scene,\r\n        updatable?: boolean,\r\n        onReady?: (mesh: GroundMesh) => void,\r\n        alphaFilter?: number\r\n    ): GroundMesh {\r\n        throw new Error(\"Import MeshBuilder to populate this function\");\r\n    }\r\n\r\n    /**\r\n     * Creates a tube mesh.\r\n     * The tube is a parametric shape.\r\n     * It has no predefined shape. Its final shape will depend on the input parameters.\r\n     *\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/param\r\n     * @param name defines the name of the mesh to create\r\n     * @param path is a required array of successive Vector3. It is the curve used as the axis of the tube\r\n     * @param radius sets the tube radius size\r\n     * @param tessellation is the number of sides on the tubular surface\r\n     * @param radiusFunction is a custom function. If it is not null, it overrides the parameter `radius`. This function is called on each point of the tube path and is passed the index `i` of the i-th point and the distance of this point from the first point of the path\r\n     * @param cap sets the way the extruded shape is capped. Possible values : Mesh.NO_CAP (default), Mesh.CAP_START, Mesh.CAP_END, Mesh.CAP_ALL\r\n     * @param scene defines the hosting scene\r\n     * @param updatable defines if the mesh must be flagged as updatable\r\n     * @param sideOrientation defines the mesh side orientation (https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/set#side-orientation)\r\n     * @param instance is an instance of an existing Tube object to be updated with the passed `pathArray` parameter (https://doc.babylonjs.com/how_to/How_to_dynamically_morph_a_mesh#tube)\r\n     * @returns a new Mesh\r\n     * @deprecated Please use MeshBuilder instead\r\n     */\r\n    public static CreateTube(\r\n        name: string,\r\n        path: Vector3[],\r\n        radius: number,\r\n        tessellation: number,\r\n        radiusFunction: { (i: number, distance: number): number },\r\n        cap: number,\r\n        scene: Scene,\r\n        updatable?: boolean,\r\n        sideOrientation?: number,\r\n        instance?: Mesh\r\n    ): Mesh {\r\n        throw new Error(\"Import MeshBuilder to populate this function\");\r\n    }\r\n\r\n    /**\r\n     * Creates a polyhedron mesh.\r\n     *.\r\n     * * The parameter `type` (positive integer, max 14, default 0) sets the polyhedron type to build among the 15 embedded types. Please refer to the type sheet in the tutorial to choose the wanted type\r\n     * * The parameter `size` (positive float, default 1) sets the polygon size\r\n     * * You can overwrite the `size` on each dimension bu using the parameters `sizeX`, `sizeY` or `sizeZ` (positive floats, default to `size` value)\r\n     * * You can build other polyhedron types than the 15 embbeded ones by setting the parameter `custom` (`polyhedronObject`, default null). If you set the parameter `custom`, this overwrittes the parameter `type`\r\n     * * A `polyhedronObject` is a formatted javascript object. You'll find a full file with pre-set polyhedra here : https://github.com/BabylonJS/Extensions/tree/master/Polyhedron\r\n     * * You can set the color and the UV of each side of the polyhedron with the parameters `faceColors` (Color4, default `(1, 1, 1, 1)`) and faceUV (Vector4, default `(0, 0, 1, 1)`)\r\n     * * To understand how to set `faceUV` or `faceColors`, please read this by considering the right number of faces of your polyhedron, instead of only 6 for the box : https://doc.babylonjs.com/features/featuresDeepDive/materials/using/texturePerBoxFace\r\n     * * The parameter `flat` (boolean, default true). If set to false, it gives the polyhedron a single global face, so less vertices and shared normals. In this case, `faceColors` and `faceUV` are ignored\r\n     * * You can also set the mesh side orientation with the values : Mesh.FRONTSIDE (default), Mesh.BACKSIDE or Mesh.DOUBLESIDE\r\n     * * If you create a double-sided mesh, you can choose what parts of the texture image to crop and stick respectively on the front and the back sides with the parameters `frontUVs` and `backUVs` (Vector4). Detail here : https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/set#side-orientation\r\n     * * The mesh can be set to updatable with the boolean parameter `updatable` (default false) if its internal geometry is supposed to change once created\r\n     * @param name defines the name of the mesh to create\r\n     * @param options defines the options used to create the mesh\r\n     * @param scene defines the hosting scene\r\n     * @returns a new Mesh\r\n     * @deprecated Please use MeshBuilder instead\r\n     */\r\n    public static CreatePolyhedron(\r\n        name: string,\r\n        options: {\r\n            type?: number;\r\n            size?: number;\r\n            sizeX?: number;\r\n            sizeY?: number;\r\n            sizeZ?: number;\r\n            custom?: any;\r\n            faceUV?: Vector4[];\r\n            faceColors?: Color4[];\r\n            updatable?: boolean;\r\n            sideOrientation?: number;\r\n        },\r\n        scene: Scene\r\n    ): Mesh {\r\n        throw new Error(\"Import MeshBuilder to populate this function\");\r\n    }\r\n\r\n    /**\r\n     * Creates a sphere based upon an icosahedron with 20 triangular faces which can be subdivided\r\n     * * The parameter `radius` sets the radius size (float) of the icosphere (default 1)\r\n     * * You can set some different icosphere dimensions, for instance to build an ellipsoid, by using the parameters `radiusX`, `radiusY` and `radiusZ` (all by default have the same value than `radius`)\r\n     * * The parameter `subdivisions` sets the number of subdivisions (positive integer, default 4). The more subdivisions, the more faces on the icosphere whatever its size\r\n     * * The parameter `flat` (boolean, default true) gives each side its own normals. Set it to false to get a smooth continuous light reflection on the surface\r\n     * * You can also set the mesh side orientation with the values : Mesh.FRONTSIDE (default), Mesh.BACKSIDE or Mesh.DOUBLESIDE\r\n     * * If you create a double-sided mesh, you can choose what parts of the texture image to crop and stick respectively on the front and the back sides with the parameters `frontUVs` and `backUVs` (Vector4). Detail here : https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/set#side-orientation\r\n     * * The mesh can be set to updatable with the boolean parameter `updatable` (default false) if its internal geometry is supposed to change once created\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/polyhedra#icosphere\r\n     * @param name defines the name of the mesh\r\n     * @param options defines the options used to create the mesh\r\n     * @param scene defines the hosting scene\r\n     * @returns a new Mesh\r\n     * @deprecated Please use MeshBuilder instead\r\n     */\r\n    public static CreateIcoSphere(\r\n        name: string,\r\n        options: { radius?: number; flat?: boolean; subdivisions?: number; sideOrientation?: number; updatable?: boolean },\r\n        scene: Scene\r\n    ): Mesh {\r\n        throw new Error(\"Import MeshBuilder to populate this function\");\r\n    }\r\n\r\n    /**\r\n     * Creates a decal mesh.\r\n     *.\r\n     * A decal is a mesh usually applied as a model onto the surface of another mesh\r\n     * @param name  defines the name of the mesh\r\n     * @param sourceMesh defines the mesh receiving the decal\r\n     * @param position sets the position of the decal in world coordinates\r\n     * @param normal sets the normal of the mesh where the decal is applied onto in world coordinates\r\n     * @param size sets the decal scaling\r\n     * @param angle sets the angle to rotate the decal\r\n     * @returns a new Mesh\r\n     * @deprecated Please use MeshBuilder instead\r\n     */\r\n    public static CreateDecal(name: string, sourceMesh: AbstractMesh, position: Vector3, normal: Vector3, size: Vector3, angle: number): Mesh {\r\n        throw new Error(\"Import MeshBuilder to populate this function\");\r\n    }\r\n\r\n    /** Creates a Capsule Mesh\r\n     * @param name defines the name of the mesh.\r\n     * @param options the constructors options used to shape the mesh.\r\n     * @param scene defines the scene the mesh is scoped to.\r\n     * @returns the capsule mesh\r\n     * @see https://doc.babylonjs.com/how_to/capsule_shape\r\n     * @deprecated Please use MeshBuilder instead\r\n     */\r\n    public static CreateCapsule(name: string, options: ICreateCapsuleOptions, scene: Scene): Mesh {\r\n        throw new Error(\"Import MeshBuilder to populate this function\");\r\n    }\r\n\r\n    /**\r\n     * Extends a mesh to a Goldberg mesh\r\n     * Warning  the mesh to convert MUST be an import of a perviously exported Goldberg mesh\r\n     * @param mesh the mesh to convert\r\n     * @returns the extended mesh\r\n     * @deprecated Please use ExtendMeshToGoldberg instead\r\n     */\r\n    public static ExtendToGoldberg(mesh: Mesh): Mesh {\r\n        throw new Error(\"Import MeshBuilder to populate this function\");\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.Mesh\", Mesh);\r\n"], "names": [], "mappings": ";;;;;AAEA,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,eAAe,CAAC;AAEjD,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,IAAI,EAAE,MAAM,cAAc,CAAC;AAEpC,OAAO,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,uBAAuB,EAAE,MAAM,mBAAmB,CAAC;AAEjG,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAI3C,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAE5E,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAC/B,OAAO,EAAE,YAAY,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAEzD,OAAO,EAAE,UAAU,EAAE,MAAM,mBAAmB,CAAC;AAE/C,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AAEtC,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAC9C,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AAGpC,OAAO,EAAE,QAAQ,EAAE,MAAM,uBAAuB,CAAC;AACjD,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,gBAAgB,EAAE,MAAM,6BAA6B,CAAC;AAG/D,OAAO,EAAE,mBAAmB,EAAE,MAAM,kCAAkC,CAAC;AACvE,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AACxC,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAC;AAC/C,OAAO,EAAE,uBAAuB,EAAE,MAAM,mBAAmB,CAAC;AAC5D,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;AAmBxC,MAAO,oBAAoB;CAYhC;AAYD;;IAEI,CACJ,MAAM,8BAA8B;IAApC,aAAA;QAEW,IAAA,CAAA,UAAU,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,CAAC;QACvC,IAAA,CAAA,qCAAqC,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,CAAC;QAClE,IAAA,CAAA,mBAAmB,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,6CAA6C;IAO3F,CAAC;CAAA;AAED;;IAEI,CACJ,MAAM,oBAAoB;IAA1B,aAAA;QACW,IAAA,CAAA,YAAY,GAAqD,CAAA,CAAE,CAAC;IAS/E,CAAC;CAAA;AAKK,MAAO,eAAe;IASxB,YAAmB,MAAsC,CAAA;QAAtC,IAAA,CAAA,MAAM,GAAN,MAAM,CAAgC;QARlD,IAAA,CAAA,UAAU,GAAG,KAAK,CAAC;QAEnB,IAAA,CAAA,gBAAgB,GAAG,IAAI,KAAK,EAAkC,CAAC;QAE/D,IAAA,CAAA,UAAU,GAAc,EAAE,CAAC;QAE3B,IAAA,CAAA,0BAA0B,GAAc,EAAE,CAAC;IAEU,CAAC;CAChE;AAED;;IAEI,CACJ,MAAM,wBAAwB;IAA9B,aAAA;QACW,IAAA,CAAA,cAAc,GAAW,CAAC,CAAC;QAC3B,IAAA,CAAA,YAAY,GAAqB,IAAI,CAAC;QACtC,IAAA,CAAA,oBAAoB,GAAqB,IAAI,CAAC;QAC9C,IAAA,CAAA,gBAAgB,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,kDAAkD;QAC9E,IAAA,CAAA,UAAU,GAA2B,IAAI,CAAC;QAE1C,IAAA,CAAA,eAAe,GAAmB,EAAE,CAAC;QACrC,IAAA,CAAA,aAAa,GAAuB,IAAI,CAAC;IAEpD,CAAC;CAAA;AAED;;IAEI,CACJ,MAAM,qBAAqB;IAA3B,aAAA;QAQW,IAAA,CAAA,iBAAiB,GAAY,KAAK,CAAC,CAAC,iCAAiC;QAI5E,uDAAuD;QAChD,IAAA,CAAA,OAAO,GAAmB,IAAI,CAAC;QACtC,8CAA8C;QACvC,IAAA,CAAA,OAAO,GAAiD,IAAI,CAAC;QAE7D,IAAA,CAAA,cAAc,GAAW,CAAC,CAAC,CAAC;QACnC,gEAAgE;QACzD,IAAA,CAAA,UAAU,GAAG,IAAI,KAAK,EAAgB,CAAC;QAC9C,mFAAA,EAAqF,CAC9E,IAAA,CAAA,qBAAqB,GAAY,KAAK,CAAC;QAKvC,IAAA,CAAA,kBAAkB,GAAuB,IAAI,CAAC;QAE9C,IAAA,CAAA,oBAAoB,GAAW,CAAC,CAAC;QAEjC,IAAA,CAAA,0BAA0B,GAAqB,IAAI,CAAC;IAK/D,CAAC;CAAA;AA6BD,MAAM,mBAAmB,GAAwB;IAC7C,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,IAAI;IACZ,kBAAkB,EAAE,KAAK;IACzB,oBAAoB,EAAE,IAAI;IAC1B,kBAAkB,EAAE,KAAK;CAC5B,CAAC;AAKI,MAAO,IAAK,0KAAQ,eAAY;IA0FlC;;;;;OAKG,CACI,MAAM,CAAC,0BAA0B,CAAC,WAAoB,EAAA;QACzD,OAAO,WAAW,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,+BAA+B;IACzE,CAAC;IAKD;;OAEG,CACH,IAAW,oBAAoB,GAAA;QAC3B,OAAO,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,CAAC;IAC5D,CAAC;IAED,IAAW,oBAAoB,CAAC,KAAc,EAAA;QAC1C,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,GAAG,KAAK,CAAC;QACzD,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAQD,IAAoB,wBAAwB,GAAA;QACxC,OAAO,IAAI,CAAC,6BAA6B,CAAC,yBAAyB,CAAC;IACxE,CAAC;IACD,IAAoB,wBAAwB,CAAC,KAAc,EAAA;QACvD,IAAI,IAAI,CAAC,6BAA6B,CAAC,yBAAyB,KAAK,KAAK,EAAE,CAAC;YACzE,OAAO;QACX,CAAC;QAED,IAAI,KAAK,IAAI,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,EAAE,CAAC;YACvD,yIAAyI;YACzI,IAAI,CAAC,eAAe,6JAAC,eAAY,CAAC,YAAY,EAAE,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;YACnG,IAAI,IAAI,CAAC,qBAAqB,CAAC,cAAc,EAAE,CAAC;gBAC5C,IAAI,CAAC,eAAe,6JAAC,eAAY,CAAC,UAAU,EAAE,IAAI,CAAC,qBAAqB,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;YACnG,CAAC;YAED,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,GAAG,IAAI,CAAC;YACnD,IAAI,CAAC,qBAAqB,CAAC,cAAc,GAAG,IAAI,CAAC;QACrD,CAAC;QAED,IAAI,CAAC,6BAA6B,CAAC,yBAAyB,GAAG,KAAK,CAAC;QACrE,IAAI,CAAC,+BAA+B,EAAE,CAAC;IAC3C,CAAC;IAED;;OAEG,CACH,IAAW,wBAAwB,GAAA;QAC/B,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,EAAE,CAAC;YACxD,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,GAAG,iKAAI,aAAU,EAAQ,CAAC;QAClF,CAAC;QAED,OAAO,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,CAAC;IAChE,CAAC;IAED;;OAEG,CACH,IAAW,sBAAsB,GAAA;QAC7B,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,EAAE,CAAC;YACtD,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,GAAG,IAAI,0KAAU,EAAQ,CAAC;QAChF,CAAC;QAED,OAAO,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,CAAC;IAC9D,CAAC;IAED;;OAEG,CACH,IAAW,uBAAuB,GAAA;QAC9B,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,EAAE,CAAC;YACvD,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,GAAG,iKAAI,aAAU,EAAQ,CAAC;QACjF,CAAC;QAED,OAAO,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC;IAC/D,CAAC;IAED;;OAEG,CACH,IAAW,uBAAuB,GAAA;QAC9B,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,EAAE,CAAC;YACvD,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,GAAG,iKAAI,aAAU,EAAW,CAAC;QACpF,CAAC;QAED,OAAO,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC;IAC/D,CAAC;IAED;;OAEG,CACH,IAAW,sBAAsB,GAAA;QAC7B,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,EAAE,CAAC;YACtD,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,GAAG,iKAAI,aAAU,EAAQ,CAAC;QAChF,CAAC;QAED,OAAO,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,CAAC;IAC9D,CAAC;IAID;;OAEG,CACH,IAAW,YAAY,CAAC,QAAoB,EAAA;QACxC,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACnE,CAAC;QACD,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC3E,CAAC;IAED,IAAoB,YAAY,GAAA;QAC5B,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;IACrC,CAAC;IAED,IAAoB,gBAAgB,GAAA;QAChC,OAAO,CAAC,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,wBAAwB,CAAC,cAAc,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IAC/F,CAAC;IA2CD;;;;OAIG,CACH,IAAW,mBAAmB,GAAA;QAC1B,OAAO,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC;IAC3D,CAAC;IAED,IAAW,mBAAmB,CAAC,KAAa,EAAA;QACxC,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,GAAG,KAAK,CAAC;IAC5D,CAAC;IAeD;;;;OAIG,CACH,IAAW,eAAe,GAAA;QACtB,OAAO,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,CAAC;IACvD,CAAC;IAED,IAAW,eAAe,CAAC,KAAa,EAAA;QACpC,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAEpD,IAAI,CAAC,6BAA6B,CAAC,oBAAoB,GACnD,AAAC,IAAI,CAAC,MAAM,CAAC,oBAAoB,IAAI,KAAK,KAAK,KAC9C,CAAC,GADsD,CAClD,AADmD,CAClD,MAAM,CAAC,oBAAoB,IAAI,KAAK,GADsD,CAAC,CAClD,SAAS,CAAC,iCAAiC,CAAC,CAAC;IACrG,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,yBAAyB,GAAA;QAChC,OAAO,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,CAAC;IAChE,CAAC;IAED;;;OAGG,CACH,IAAW,+BAA+B,GAAA;QACtC,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED,IAAW,+BAA+B,CAAC,KAAa,EAAA;QACpD,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;QAC7B,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,IAAI,CAAC,QAAQ,CAAC,eAAe,GAAG,IAAI,CAAC;QACzC,CAAC;IACL,CAAC;IAED;;OAEG,CACH,IAAW,yBAAyB,GAAA;QAChC,OAAO,IAAI,CAAC,qBAAqB,CAAC,0BAA0B,CAAC;IACjE,CAAC;IAED,IAAW,yBAAyB,CAAC,QAA0B,EAAA;QAC3D,IAAI,CAAC,qBAAqB,CAAC,0BAA0B,GAAG,QAAQ,CAAC;IACrE,CAAC;IAED,IAAoB,QAAQ,GAAA;QACxB,OAAO,IAAI,CAAC,6BAA6B,CAAC,SAAS,CAAC;IACxD,CAAC;IAED,IAAoB,QAAQ,CAAC,KAAyB,EAAA;QAClD,IAAI,KAAK,IAAI,CAAC,AAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,eAAe,KAAK,IAAI,CAAC,GAAI,IAAI,CAAC,6BAA6B,CAAC,oBAAoB,CAAC,EAAE,CAAC;YAClI,KAAK,CAAC,eAAe,GAAG,IAAI,CAAC;QACjC,CAAC;QACD,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAWD;;OAEG,CACH,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;IAC9C,CAAC;IAED;;;;OAIG,CACH,IAAW,YAAY,GAAA;QACnB,OAAO,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;IAC9C,CAAC;IAED;;OAEG,CACH,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED,IAAW,WAAW,CAAC,KAAc,EAAA;QACjC,IAAI,IAAI,CAAC,UAAU,KAAK,KAAK,EAAE,CAAC;YAC5B,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;YACxB,IAAI,CAAC,+BAA+B,EAAE,CAAC;QAC3C,CAAC;IACL,CAAC;IAED,gGAAA,EAAkG,CAClG,IAAW,0BAA0B,GAAA;QACjC,MAAM,mBAAmB,GAAG,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzK,OAAO,mBAAmB,CAAC,CAAC,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC,CAAE,SAAiB,CAAC;IACxF,CAAC;IAED,yGAAA,EAA2G,CAC3G,IAAW,kCAAkC,GAAA;QACzC,MAAM,mBAAmB,GAAG,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzK,OAAO,mBAAmB,CAAC,CAAC,CAAC,mBAAmB,CAAC,qBAAqB,CAAC,CAAC,CAAE,SAAiB,CAAC;IAChG,CAAC;IAED,6GAAA,EAA+G,CAC/G,IAAW,wCAAwC,GAAA;QAC/C,OAAO,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC;IAClD,CAAC;IAED,IAAW,wCAAwC,CAAC,KAAc,EAAA;QAC9D,IAAI,CAAC,oBAAoB,CAAC,YAAY,GAAG,KAAK,CAAC;IACnD,CAAC;IAED,6GAAA,EAA+G,CAC/G,IAAW,gDAAgD,GAAA;QACvD,OAAO,IAAI,CAAC,oBAAoB,CAAC,oBAAoB,CAAC;IAC1D,CAAC;IAED,IAAW,gDAAgD,CAAC,KAAc,EAAA;QACtE,IAAI,CAAC,oBAAoB,CAAC,oBAAoB,GAAG,KAAK,CAAC;IAC3D,CAAC;IAED,oKAAA,EAAsK,CACtK,IAAW,qCAAqC,GAAA;QAC5C,OAAO,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,CAAC;IACxD,CAAC;IAED,IAAW,qCAAqC,CAAC,KAAc,EAAA;QAC3D,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,GAAG,KAAK,CAAC;IACzD,CAAC;IAES,WAAW,CAAC,MAAY,EAAE,kBAA4B,EAA2E;mCAAzE,iEAAgC,IAAI,uBAAE,iEAA8B,KAAK;QACvI,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,WAAW;QACX,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YACnB,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QACvC,CAAC;QAED,YAAY;qKACZ,aAAU,CAAC,QAAQ,CACf,MAAM,EACN,IAAI,EACJ;YACI,MAAM;YACN,UAAU;YACV,UAAU;YACV,WAAW;YACX,QAAQ;YACR,UAAU;YACV,QAAQ;YACR,UAAU;YACV,oBAAoB;YACpB,cAAc;YACd,4BAA4B;YAC5B,oCAAoC;YACpC,cAAc;YACd,UAAU;YACV,WAAW;YACX,kBAAkB;YAClB,SAAS;YACT,oBAAoB;YACpB,cAAc;YACd,UAAU;YACV,cAAc;YACd,UAAU;YACV,eAAe;YACf,SAAS;YACT,IAAI;YACJ,OAAO;YACP,kBAAkB;YAClB,iBAAiB;YACjB,4BAA4B;YAC5B,qBAAqB;YACrB,mBAAmB;YACnB,WAAW;YACX,sBAAsB;YACtB,kBAAkB;YAClB,cAAc;YACd,iBAAiB;YACjB,aAAa;YACb,iBAAiB;SACpB,EACD;YAAC,aAAa;SAAC,CAClB,CAAC;QAEF,cAAc;QACd,IAAI,CAAC,qBAAqB,CAAC,OAAO,GAAG,MAAM,CAAC;QAC5C,IAAI,KAAK,CAAC,gBAAgB,EAAE,CAAC;YACzB,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,OAAO,EAAE,CAAC;gBACxC,MAAM,CAAC,qBAAqB,CAAC,OAAO,GAAG,CAAA,CAAE,CAAC;YAC9C,CAAC;YACD,MAAM,CAAC,qBAAqB,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;QAC/D,CAAC;QAED,sBAAsB;QACtB,6EAA6E;QAC7E,IAAI,CAAC,+BAA+B,GAAG,MAAM,CAAC,+BAA+B,CAAC;QAC9E,IAAI,CAAC,oBAAoB,GAAG,MAAM,CAAC,oBAAoB,CAAC;QAExD,mBAAmB;QACnB,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC;YAC9B,IAAK,MAAM,IAAI,IAAI,MAAM,CAAE,CAAC;gBACxB,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC;oBACtD,SAAS;gBACb,CAAC;gBAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;oBAChB,SAAS;gBACb,CAAC;gBAED,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;YACxE,CAAC;QACL,CAAC;QAED,WAAW;QACX,IAAI,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;YAC3C,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QAC5C,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;QACpC,CAAC;QACD,IAAI,CAAC,iBAAiB,GAAG,MAAM,CAAC,iBAAiB,CAAC;QAElD,OAAO;QACP,2JAAI,OAAI,2JAAI,OAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;mKAC/B,OAAI,CAAC,SAAS,CAAC,IAAI,yJAAE,OAAI,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;QACrD,CAAC;QAED,2EAA2E;QAC3E,2BAA2B;QAC3B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;QAEzC,SAAS;QACT,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;QAE5B,QAAQ;QACR,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAE5E,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,MAAM,CAAC,EAAE,CAAC;QAEtC,WAAW;QACX,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;QAEhC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACtB,WAAW;YACX,MAAM,iBAAiB,GAAG,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACtD,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBAC5D,MAAM,KAAK,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC;gBAEvC,IAAU,KAAM,CAAC,OAAO,EAAE,CAAC;oBACvB,mBAAmB,CAAC,MAAM,GAAG,IAAI,CAAC;oBAClC,mBAAmB,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;oBAC5D,mBAAmB,CAAC,oBAAoB,GAAG,oBAAoB,CAAC;oBAChE,mBAAmB,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;oBACrD,KAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAC;gBAC3E,CAAC,MAAM,IAAU,KAAM,CAAC,KAAK,EAAE,CAAC;oBACtB,KAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBAC3D,CAAC;YACL,CAAC;QACL,CAAC;QAED,SAAS;QACT,IAAI,MAAM,CAAC,kBAAkB,EAAE,CAAC;YAC5B,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC,kBAAkB,CAAC;QACxD,CAAC;QAED,gBAAgB;QAChB,IAAI,KAAK,CAAC,gBAAgB,EAAE,CAAC;YACzB,MAAM,aAAa,GAAG,KAAK,CAAC,gBAAgB,EAAE,CAAC;YAC/C,IAAI,oBAAoB,IAAI,aAAa,EAAE,CAAC;gBACxC,IAAI,aAAa,CAAC,gBAAgB,EAAE,KAAK,CAAC,EAAE,CAAC;oBACzC,MAAM,QAAQ,GAAI,aAAiC,CAAC,2BAA2B,CAAC,MAAM,CAAC,CAAC;oBACxF,IAAI,QAAQ,EAAE,CAAC;wBACX,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;oBAChD,CAAC;gBACL,CAAC,MAAM,IAAI,aAAa,CAAC,gBAAgB,EAAE,KAAK,CAAC,EAAE,CAAC;oBAChD,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;wBACrB,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;oBACnC,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;QAED,YAAY;QACZ,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,eAAe,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAChE,MAAM,MAAM,GAAG,KAAK,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YAE5C,IAAI,MAAM,CAAC,OAAO,KAAK,MAAM,EAAE,CAAC;gBAC5B,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACpC,CAAC;QACL,CAAC;QAED,WAAW;QACX,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;QAEhC,iBAAiB;QACjB,IAAI,kBAAkB,EAAE,CAAC;YACrB,IAAI,MAAM,CAAC,wBAAwB,CAAC,UAAU,EAAE,CAAC;gBAC7C,IAAI,CAAC,qBAAqB,CACtB,QAAQ,EACR,IAAI,YAAY,CAAC,MAAM,CAAC,wBAAwB,CAAC,UAAU,CAAC,EAC5D,EAAE,EACF,CAAC,MAAM,CAAC,wBAAwB,CAAC,YAAa,CAAC,WAAW,EAAE,CAC/D,CAAC;gBACF,IAAI,CAAC,wBAAwB,CAAC,gBAAgB,GAAG,MAAM,CAAC,wBAAwB,CAAC,gBAAgB,CAAC;gBAClG,IAAI,CAAC,wBAAwB,CAAC,cAAc,GAAG,MAAM,CAAC,wBAAwB,CAAC,cAAc,CAAC;YAClG,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,wBAAwB,CAAC,gBAAgB,GAAG,MAAM,CAAC,wBAAwB,CAAC,gBAAgB,CAAC;YACtG,CAAC;YAED,IAAI,MAAM,CAAC,+BAA+B,EAAE,CAAC;gBACzC,MAAM,gBAAgB,GAAG,MAAM,CAAC,+BAA+B,CAAC;gBAChE,IAAK,MAAM,IAAI,IAAI,gBAAgB,CAAC,IAAI,CAAE,CAAC;;oBACvC,IAAI,CAAC,qBAAqB,CACtB,IAAI,EACJ,IAAI,YAAY,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAC7C,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,EAC9B,GAAC,gBAAgB,mCAAC,aAAa,2IAAE,CAAC,IAAI,CAAC,8GAAE,WAAW,EAAE,CACzD,CAAC;oBACF,IAAI,CAAC,+BAA+B,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACpF,CAAC;YACL,CAAC;QACL,CAAC;QAED,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACrC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IA2Ge,oBAAoB,GAGwC;wBAFxE,iEAAqC,IAAI,EACzC,OAA4E,iDAC5E,gBAAwE;QAExE,MAAM,QAAQ,GACV,IAAI,CAAC,gBAAgB,EAAE,KAAK,CAAC,IAAI,AAAC,OAAO,IAAI,OAAO,CAAC,gBAAgB,IAAI,CAAC,OAAO,CAAC,gBAAgB,KAAK,IAAI,IAAI,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,EACzI,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC,EAAE,SAAS,IAAI,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,GAChF,IAAI,CAAC,cAAc,CAAC,cAAc,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;QAEvE,QAAQ,CAAC,MAAM,GAAG,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC;QAC3C,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QAC1C,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;QACxC,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,QAAQ,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;QAClE,CAAC,MAAM,CAAC;YACJ,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QAC9C,CAAC;QAED,IAAI,gBAAgB,EAAE,CAAC;YACnB,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QACrC,CAAC;QAED,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAE,CAAC;YACpD,qDAAqD;YACrD,IAAI,KAAK,CAAC,YAAY,EAAE,KAAK,eAAe,IAAI,QAAQ,CAAC,YAAY,EAAE,KAAK,MAAM,IAAK,KAAuB,CAAC,UAAU,KAAK,IAAI,EAAE,CAAC;gBAChI,KAAuB,CAAC,oBAAoB,CACzC,QAAQ,EACR;oBACI,gBAAgB,EAAE,AAAC,OAAO,IAAI,OAAO,CAAC,gBAAgB,CAAC,GAAI,KAAK;oBAChE,cAAc,EAAE,QAAgB;iBACnC,EACD,gBAAgB,CACnB,CAAC;YACN,CAAC,MAAM,CAAC;gBACJ,KAAK,CAAC,oBAAoB,CAAC,QAAQ,EAAE,OAAO,EAAE,gBAAgB,CAAC,CAAC;YACpE,CAAC;QACL,CAAC;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,OAAO,GAAA;QACd,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACa,QAAQ,CAAC,WAAqB,EAAA;QAC1C,IAAI,GAAG,GAAG,KAAK,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QACtC,GAAG,IAAI,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAClD,GAAG,IAAI,YAAY,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;QAEhH,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;gBAC9C,GAAG,IAAI,kBAAkB,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YACzE,CAAC;QACL,CAAC;QAED,IAAI,WAAW,EAAE,CAAC;YACd,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjB,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;gBAC7B,MAAM,EAAE,GAAG,IAAI,CAAC,eAAe,6JAAC,eAAY,CAAC,YAAY,CAAC,CAAC;gBAE3D,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC;oBACX,GAAG,IAAI,kBAAkB,GAAG,CAAC,EAAE,CAAC,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;gBAC7E,CAAC;YACL,CAAC,MAAM,CAAC;gBACJ,GAAG,IAAI,yBAAyB,CAAC;YACrC,CAAC;QACL,CAAC;QACD,OAAO,GAAG,CAAC;IACf,CAAC;IAED,cAAA,EAAgB,CACA,aAAa,GAAA;QACzB,KAAK,CAAC,aAAa,EAAE,CAAC;QAEtB,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAE,CAAC;YACpC,QAAQ,CAAC,aAAa,EAAE,CAAC;QAC7B,CAAC;IACL,CAAC;IAED;;OAEG,CACH,IAAW,YAAY,GAAA;QACnB,OAAO,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;IAC5D,CAAC;IAED;;;OAGG,CACI,YAAY,GAAA;QACf,OAAO,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC;IACjD,CAAC;IAEO,cAAc,GAAA;QAClB,MAAM,kBAAkB,GAAG,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrF,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAChD,IAAI,CAAC,CAAC,wBAAwB,GAAG,CAAC,CAAC,wBAAwB,EAAE,CAAC;gBAC1D,OAAO,kBAAkB,CAAC;YAC9B,CAAC;YACD,IAAI,CAAC,CAAC,wBAAwB,GAAG,CAAC,CAAC,wBAAwB,EAAE,CAAC;gBAC1D,OAAO,CAAC,kBAAkB,CAAC;YAC/B,CAAC;YAED,OAAO,CAAC,CAAC;QACb,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;;;OASG,CACI,WAAW,CAAC,wBAAgC,EAAE,IAAoB,EAAA;QACrE,IAAI,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;qKAC3B,SAAM,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;YACxD,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,KAAK,GAAG,qKAAI,eAAY,CAAC,wBAAwB,EAAE,IAAI,CAAC,CAAC;QAC/D,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAElD,IAAI,IAAI,EAAE,CAAC;YACP,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QAC5B,CAAC;QAED,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG,CACI,qBAAqB,CAAC,QAAgB,EAAA;QACzC,MAAM,gBAAgB,GAAG,IAAI,CAAC,qBAAqB,CAAC;QACpD,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,gBAAgB,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACtE,MAAM,KAAK,GAAG,gBAAgB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAEjD,IAAI,KAAK,CAAC,wBAAwB,KAAK,QAAQ,EAAE,CAAC;gBAC9C,OAAO,KAAK,CAAC,IAAI,CAAC;YACtB,CAAC;QACL,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG,CACI,cAAc,CAAC,IAAoB,EAAA;QACtC,MAAM,gBAAgB,GAAG,IAAI,CAAC,qBAAqB,CAAC;QACpD,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,gBAAgB,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACtE,IAAI,gBAAgB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;gBACnD,gBAAgB,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBAC7C,IAAI,IAAI,EAAE,CAAC;oBACP,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;gBAC5B,CAAC;YACL,CAAC;QACL,CAAC;QAED,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG,CACa,MAAM,CAAC,MAAc,EAAE,cAA+B,EAAA;QAClE,MAAM,gBAAgB,GAAG,IAAI,CAAC,qBAAqB,CAAC;QACpD,IAAI,CAAC,gBAAgB,CAAC,UAAU,IAAI,gBAAgB,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3E,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,OAAO,GAAG,cAAc,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC,cAAc,CAAC;QAExE,MAAM,gBAAgB,GAAG,MAAM,CAAC,IAAI,iKAAK,SAAM,CAAC,mBAAmB,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,MAAM,EAAE,CAAC;QACjJ,IAAI,YAAY,GAAG,gBAAgB,CAAC;QACpC,IAAI,WAAW,GAAG,CAAC,CAAC;QAEpB,IAAI,gBAAgB,CAAC,qBAAqB,EAAE,CAAC;YACzC,MAAM,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;YACrC,IAAI,QAAQ,GAAG,AAAC,OAAO,CAAC,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,EAAG,gBAAgB,CAAC;YACtE,QAAQ,GAAG,QAAQ,GAAG,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC;YACzC,YAAY,GAAG,QAAQ,GAAG,UAAU,CAAC;YACrC,WAAW,GAAG,CAAC,CAAC,CAAC;QACrB,CAAC;QAED,IAAI,WAAW,GAAG,gBAAgB,CAAC,UAAU,CAAC,gBAAgB,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,wBAAwB,GAAG,WAAW,GAAG,YAAY,EAAE,CAAC;YAC1I,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC3B,IAAI,CAAC,mBAAmB,CAAC,YAAY,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YACvD,CAAC;YACD,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,gBAAgB,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACtE,MAAM,KAAK,GAAG,gBAAgB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAEjD,IAAI,WAAW,GAAG,KAAK,CAAC,wBAAwB,GAAG,WAAW,GAAG,YAAY,EAAE,CAAC;gBAC5E,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;oBACb,IAAI,KAAK,CAAC,IAAI,CAAC,cAAc,KAAK,GAAA,MAAS,CAAC,wBAAwB,EAAE,CAAC;wBACnE,KAAK,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;wBAC9B,OAAO,IAAI,CAAC;oBAChB,CAAC;oBAED,IAAI,KAAK,CAAC,IAAI,CAAC,cAAc,KAAK,GAAA,MAAS,CAAC,sBAAsB,EAAE,CAAC;wBACjE,OAAO,IAAI,CAAC;oBAChB,CAAC;oBAED,KAAK,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;oBAC1B,KAAK,CAAC,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBACvE,CAAC;gBAED,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;oBAC3B,IAAI,CAAC,mBAAmB,CAAC,YAAY,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;gBAC7D,CAAC;gBAED,OAAO,KAAK,CAAC,IAAI,CAAC;YACtB,CAAC;QACL,CAAC;QAED,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,IAAI,CAAC,mBAAmB,CAAC,YAAY,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QACvD,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG,CACH,IAAoB,QAAQ,GAAA;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;;OAGG,CACa,gBAAgB,GAAA;QAC5B,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;YAC1D,OAAO,CAAC,CAAC;QACb,CAAC;QACD,OAAO,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC;IAC7C,CAAC;IAED;;;;;;;;;;;;;;;;;;;OAmBG,CACa,eAAe,CAAC,IAAY,EAAE,cAAwB,EAAE,SAAmB,EAAE,kBAA4B,EAAA;YAM/G;QALN,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAClB,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,IAAI,IAAI,GAAG,kBAAkB,GACvB,SAAS,6CACL,CAAC,4BAA4B,mKAAE,aAAa,CAAC,IAAI,CAAC,gJAAE,YAAY,CAChE,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,AAC3B,SAAS,IAAI,AAAC,cAAc,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC,CACtE,CAAC,AAFiG;QAGzG,IAAI,CAAC,IAAI,EAAE,CAAC;YACR,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI,EAAE,cAAc,EAAE,SAAS,CAAC,CAAC;QAC3E,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEe,gBAAgB,CAAC,IAAY,EAAE,UAA4C,EAAA;QACvF,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QACtD,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;OAkBG,CACI,eAAe,CAAC,IAAY,EAAE,kBAA4B,EAAA;;QAC7D,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAClB,OAAO,IAAI,CAAC;QAChB,CAAC;;QAED,eAAQ,kBAAkB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,2CAAK,CAAC,4BAA4B,uEAAjC,mCAAmC,aAAa,CAAC,IAAI,CAAC,CAAC,+BAAzF,OAA6F,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IAC7I,CAAC;IAED;;;;;;;;;;;;;;;;;;OAkBG,CACa,qBAAqB,CAAC,IAAY,EAAE,kBAA4B,EAAA;;QAC5E,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAClB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YAChD,CAAC;YACD,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,OAAO,AAAC,CAAC,kBAAkB,+CAAQ,CAAC,4BAA4B,uEAAjC,mCAAmC,aAAa,CAAC,IAAI,CAAC,MAAK,SAAS,CAAC,GAAI,IAAI,CAAC,SAAS,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;IACvJ,CAAC;IAED;;;;;;;;;;;;;;;;;OAiBG,CACI,uBAAuB,CAAC,IAAY,EAAE,kBAA4B,EAAA;QACrE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAClB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YAChD,CAAC;YACD,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,IAAI,CAAC,kBAAkB,EAAE,CAAC;;YACtB,MAAM,MAAM,6CAAO,CAAC,4BAA4B,uEAAjC,mCAAmC,aAAa,CAAC,IAAI,CAAC,CAAC;YACtE,IAAI,MAAM,EAAE,CAAC;gBACT,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC;YAChC,CAAC;QACL,CAAC;QACD,OAAO,IAAI,CAAC,SAAS,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;IACxD,CAAC;IAED;;;;OAIG,CACI,oBAAoB,CAAC,kBAA4B,EAAA;QACpD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAClB,MAAM,MAAM,GAAa,EAAE,CAAC;YAC5B,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,UAAU,CAAE,CAAC;oBACjC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACtB,CAAC;YACL,CAAC;YACD,OAAO,MAAM,CAAC;QAClB,CAAC;QACD,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,oBAAoB,EAAE,CAAC;QACpD,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;YAC3D,IAAK,MAAM,IAAI,IAAI,IAAI,CAAC,4BAA4B,CAAC,aAAa,CAAE,CAAC;gBACjE,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;oBAC7B,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACrB,CAAC;YACL,CAAC;QACL,CAAC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;OAGG,CACa,eAAe,GAAA;QAC3B,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAClB,OAAO,CAAC,CAAC;QACb,CAAC;QACD,OAAO,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,CAAC;IAC5C,CAAC;IAED;;;;;OAKG,CACa,UAAU,CAAC,cAAwB,EAAE,SAAmB,EAAA;QACpE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAClB,OAAO,EAAE,CAAC;QACd,CAAC;QACD,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;IAChE,CAAC;IAED,IAAoB,SAAS,GAAA;QACzB,OAAO,IAAI,CAAC,WAAW,KAAK,IAAI,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,CAAC;IACvE,CAAC;IAED;;;;;OAKG,CACa,OAAO,GAAoD;YAAnD,aAAa,oEAAG,KAAK,yBAAE,oBAAoB,6CAAG,KAAK;QACvE,IAAI,IAAI,CAAC,cAAc,KAAK,GAAA,MAAS,CAAC,sBAAsB,EAAE,CAAC;YAC3D,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,CAAC;YAChC,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjD,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,CAAC,aAAa,EAAE,CAAC;YACjB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAChC,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,MAAM,0BAA0B,GAAG,oBAAoB,IAAI,AAAC,MAAM,CAAC,OAAO,EAAE,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;QAEtJ,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE1B,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,eAAe,CAAC;QACnD,IAAI,GAAG,EAAE,CAAC;YACN,IAAI,GAAG,CAAC,uBAAuB,EAAE,CAAC;gBAC9B,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,SAAS,CAAE,CAAC;oBACnC,MAAM,iBAAiB,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;oBAChD,IAAI,iBAAiB,EAAE,CAAC;wBACpB,IAAI,iBAAiB,CAAC,uBAAuB,EAAE,CAAC;4BAC5C,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,0BAA0B,CAAC,EAAE,CAAC;gCAClF,OAAO,KAAK,CAAC;4BACjB,CAAC;wBACL,CAAC,MAAM,CAAC;4BACJ,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,IAAI,EAAE,0BAA0B,CAAC,EAAE,CAAC;gCAC/D,OAAO,KAAK,CAAC;4BACjB,CAAC;wBACL,CAAC;oBACL,CAAC;gBACL,CAAC;YACL,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,0BAA0B,CAAC,EAAE,CAAC;oBACjD,OAAO,KAAK,CAAC;gBACjB,CAAC;YACL,CAAC;QACL,CAAC;QAED,UAAU;QACV,MAAM,mBAAmB,GAAG,MAAM,CAAC,mBAAmB,CAAC;QACvD,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,YAAY,CAAE,CAAC;YACpC,MAAM,UAAU,GAAG,KAAK,CAAC,mBAAmB,EAAE,CAAC;YAE/C,IAAI,CAAC,UAAU,EAAE,CAAC;gBACd,SAAS;YACb,CAAC;YAED,MAAM,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC;YACrC,IAAK,IAAI,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,KAAK,IAAI,EAAE,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAE,CAAC;uEAE6B,SAAS;gBAD7G,MAAM,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC;gBAC5B,IAAI,SAAS,IAAI,CAAC,uCAAW,YAAY,EAAE,4DAAxB,SAAS,eAAiB,UAAU,IAAI,wCAAW,YAAY,EAAE,6DAAxB,SAAS,gBAAiB,UAAU,4CAAc,YAAY,EAAE,4HAAE,UAAU,0GAAE,OAAO,CAAC,IAAI,CAAC,MAAK,CAAC,CAAC,AAAC,CAAC,EAAE,CAAC;oBAC/J,MAAM,SAAS,GAAG,SAAS,CAAC,YAAY,EAAG,CAAC;;oBAC5C,MAAM,aAAa,yCAAa,aAAa,oDAAvB,SAAS,kBAAkB;wBAAC,MAAM,CAAC,mBAAmB;qBAAC,CAAC;oBAC9E,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,CAAE,CAAC;wBAC5C,MAAM,CAAC,mBAAmB,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;wBAC9C,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,SAAS,CAAE,CAAC;;gCACyB,OAAO;4BAAnE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,EAAE,0BAA0B,mFAAU,WAAW,EAAE,8EAAE,wBAAwB,CAAC,IAAI,CAAC,yHAAI,KAAK,CAAC,EAAE,CAAC;gCAC1H,MAAM,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;gCACjD,OAAO,KAAK,CAAC;4BACjB,CAAC;wBACL,CAAC;oBACL,CAAC;oBACD,MAAM,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;gBACrD,CAAC;YACL,CAAC;QACL,CAAC;QAED,MAAM;QACN,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAE,CAAC;YACtD,IAAI,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,0BAA0B,CAAC,EAAE,CAAC;gBAC5D,OAAO,KAAK,CAAC;YACjB,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG,CACH,IAAW,gBAAgB,GAAA;QACvB,OAAO,IAAI,CAAC,qBAAqB,CAAC,iBAAiB,CAAC;IACxD,CAAC;IAED;;;OAGG,CACI,aAAa,GAAA;QAChB,IAAI,CAAC,qBAAqB,CAAC,iBAAiB,GAAG,IAAI,CAAC;QACpD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG,CACI,eAAe,GAAA;QAClB,IAAI,CAAC,qBAAqB,CAAC,iBAAiB,GAAG,KAAK,CAAC;QACrD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG,CACH,IAAW,sBAAsB,CAAC,KAAa,EAAA;QAC3C,IAAI,CAAC,oBAAoB,CAAC,sBAAsB,GAAG,KAAK,CAAC;IAC7D,CAAC;IAED,cAAA,EAAgB,CACT,uBAAuB,GAAA;QAC1B,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC;QAE1H,IAAI,mBAAmB,GAAG,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;QAC/E,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACvB,mBAAmB,GAAG,IAAI,8BAA8B,EAAE,CAAC;YAC3D,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,YAAY,CAAC,GAAG,mBAAmB,CAAC;QAC/E,CAAC;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED,UAAU;IACV,cAAA,EAAgB,CACA,YAAY,GAAA;QACxB,MAAM,gBAAgB,GAAG,IAAI,CAAC,qBAAqB,CAAC;QACpD,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,WAAW,EAAE,CAAC;QACpD,IAAI,gBAAgB,CAAC,cAAc,KAAK,aAAa,EAAE,CAAC;YACpD,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,gBAAgB,CAAC,cAAc,GAAG,aAAa,CAAC;QAChD,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,uBAAuB,EAAE,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC3D,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG,CACa,oCAAoC,CAAC,QAAgB,EAAA;QACjE,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACrB,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,MAAM,mBAAmB,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC3D,IAAI,mBAAmB,CAAC,gBAAgB,EAAE,CAAC;YACvC,mBAAmB,CAAC,gBAAgB,CAAC,2BAA2B,GAAG,QAAQ,CAAC;QAChF,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG,CACI,4BAA4B,CAAC,QAAuB,EAAE,QAAgB,EAAA;QACzE,MAAM,mBAAmB,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC3D,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,CAAC;YACxC,mBAAmB,CAAC,gBAAgB,GAAG;gBACnC,eAAe,EAAE,QAAQ;gBACzB,mBAAmB,EAAE,IAAI,CAAC,SAAS;gBACnC,2BAA2B,EAAE,CAAC,CAAC;aAClC,CAAC;QACN,CAAC;QAED,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,EAAE,CAAC;YAClD,IAAI,mBAAmB,CAAC,gBAAgB,KAAK,SAAS,IAAI,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,CAAC;gBAC3F,mBAAmB,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC;YACtF,CAAC;YACD,mBAAmB,CAAC,gBAAgB,GAAG,QAAQ,CAAC;YAChD,mBAAmB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,GAAG,IAAI,KAAK,EAAiB,CAAC;QAChF,CAAC;QAED,mBAAmB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC9D,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,wBAAwB,GAAA;QACvC,KAAK,CAAC,wBAAwB,EAAE,CAAC;QAEjC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACzB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC9B,IAAI,CAAC,+BAA+B,CAAC,KAAK,CAAC,CAAC;QAChD,CAAC;IACL,CAAC;IAED,cAAA,EAAgB,CACA,aAAa,GAAA;QACzB,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YAC7G,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACzE,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;QACnE,CAAC;IACL,CAAC;IAED;;;;;;OAMG,CACa,mBAAmB,GAAwF;qCAAvF,iEAAqD,KAAK,eAAE,iEAAsB,KAAK;QACvH,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC,QAAQ,EAAE,CAAC;YAC1D,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,OAAyB,CAAC;QAC9B,IAAI,OAAO,sBAAsB,KAAK,QAAQ,EAAE,CAAC;YAC7C,OAAO,GAAG,sBAAsB,CAAC;QACrC,CAAC,MAAM,CAAC;YACJ,OAAO,GAAG;gBACN,aAAa,EAAE,sBAAsB;gBACrC,UAAU,EAAE,UAAU;aACzB,CAAC;QACN,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC;QAC/D,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,8JAAE,eAAY,CAAC,YAAY,CAAC,EAAE,IAAI,CAAC,CAAC;QACzF,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG,CACI,oBAAoB,CAAC,KAAc,EAAA;QACtC,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9C,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC;YACvC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,6CAA6C;QAC7C,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9C,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;YAE7B,IAAI,CAAC,EAAE,EAAE,CAAC;gBACN,OAAO,IAAI,CAAC;YAChB,CAAC;YAED,MAAM,YAAY,GAAG,EAAE,CAAC,MAAM,CAAC;YAC/B,IAAI,cAAc,GAAG,KAAK,CAAC;YAE3B,IAAI,KAAK,EAAE,CAAC;gBACR,cAAc,GAAG,IAAI,CAAC;YAC1B,CAAC,MAAM,CAAC;gBACJ,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,SAAS,CAAE,CAAC;oBACnC,IAAI,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,GAAG,YAAY,EAAE,CAAC;wBACzD,cAAc,GAAG,IAAI,CAAC;wBACtB,MAAM;oBACV,CAAC;oBAED,IAAI,OAAO,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,GAAG,aAAa,EAAE,CAAC;wBAChE,cAAc,GAAG,IAAI,CAAC;wBACtB,MAAM;oBACV,CAAC;gBACL,CAAC;YACL,CAAC;YAED,IAAI,CAAC,cAAc,EAAE,CAAC;gBAClB,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC;QACL,CAAC;QAED,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,OAAO,gKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,aAAa,EAAE,CAAC,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,aAAa,EAAE,IAAI,CAAC,CAAC,CAAC,yDAAyD;IACxJ,CAAC;IAED;;;OAGG,CACI,SAAS,CAAC,KAAa,EAAA;QAC1B,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;YACZ,OAAO;QACX,CAAC;QAED,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAC5C,IAAI,eAAe,GAAI,AAAD,YAAa,GAAG,KAAK,CAAC,EAAG,CAAC,CAAC;QACjD,IAAI,MAAM,GAAG,CAAC,CAAC;QAEf,iDAAiD;QACjD,MAAO,eAAe,GAAG,CAAC,KAAK,CAAC,CAAE,CAAC;YAC/B,eAAe,EAAE,CAAC;QACtB,CAAC;QAED,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,CAAE,CAAC;YACzC,IAAI,MAAM,IAAI,YAAY,EAAE,CAAC;gBACzB,MAAM;YACV,CAAC;wKAED,UAAO,CAAC,iBAAiB,CAAC,CAAC,EAAE,MAAM,EAAE,KAAK,KAAK,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,MAAM,CAAC,CAAC,CAAC,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;YAE5H,MAAM,IAAI,eAAe,CAAC;QAC9B,CAAC;QAED,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAChC,CAAC;IAED;;;;;;;;;;;;;;;;;;;OAmBG,CACa,eAAe,CAAC,IAAY,EAAE,IAAgB,EAA6C;wBAA3C,iEAAqB,KAAK,EAAE,MAAe;QACvG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAClB,MAAM,UAAU,GAAG,2KAAI,aAAU,EAAE,CAAC;YACpC,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAE3B,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YAE9B,iKAAI,WAAQ,8JAAC,WAAQ,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;QAC1E,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;QAClE,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;;;;;;;;;OAeG,CACI,kBAAkB,CAAC,IAAY,EAAA;QAClC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAClB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;IAC5C,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG,CACI,2BAA2B,CAAC,IAAY,EAAkB;wBAAhB,SAAS,wDAAG,IAAI;QAC7D,MAAM,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAEtC,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,WAAW,EAAE,KAAK,SAAS,EAAE,CAAC;YACxC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,eAAe,CAAC,IAAI,EAAc,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,CAAC;IAClF,CAAC;IAED;;;;;OAKG,CACI,iBAAiB,CAAC,MAAoB,EAA8B;oCAA5B,qBAAqB,4CAAG,IAAI;QACvE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAClB,IAAI,CAAC,SAAS,gKAAG,WAAQ,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,MAAM,EAAE,IAAI,EAAE,qBAAqB,CAAC,CAAC;QACtE,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;;;;;;;;;;;;;OAmBG,CACa,kBAAkB,CAAC,IAAY,EAAE,IAAgB,EAAE,aAAuB,EAAE,YAAsB,EAAA;QAC9G,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAClB,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,IAAI,CAAC,YAAY,EAAE,CAAC;YAChB,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;QACjE,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;QAC9D,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG,CACI,mBAAmB,CAAC,gBAA4C,EAAgC;YAA9B,kFAA0B,IAAI;QACnG,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,6JAAC,eAAY,CAAC,YAAY,CAAC,CAAC;QAClE,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,gBAAgB,CAAC,SAAS,CAAC,CAAC;QAC5B,IAAI,CAAC,kBAAkB,6JAAC,eAAY,CAAC,YAAY,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QAE5E,IAAI,cAAc,EAAE,CAAC;YACjB,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;YAClC,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,6JAAC,eAAY,CAAC,UAAU,CAAC,CAAC;YAE9D,IAAI,CAAC,OAAO,EAAE,CAAC;gBACX,OAAO,IAAI,CAAC;YAChB,CAAC;mLAED,aAAU,CAAC,cAAc,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;YACvD,IAAI,CAAC,kBAAkB,CAAC,2KAAY,CAAC,UAAU,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QAC5E,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG,CACI,kBAAkB,GAAA;QACrB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAClB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC;QACnC,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,8JAAC,WAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC1D,WAAW,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACvC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAC3B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG,CACI,cAAc,CAAC,WAAuB,EAAE,aAAqB,EAAE,YAAoB,EAAoC;uBAAlC,iEAA8B,IAAI;QAC1H,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;QAC9B,IAAI,CAAC,QAAQ,EAAE,CAAC;YACZ,QAAQ,GAAG,iKAAI,WAAQ,8JAAC,WAAQ,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;QAC9F,CAAC;QACD,QAAQ,CAAC,cAAc,CAAC,WAAW,EAAE,aAAa,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;IAChF,CAAC;IAED;;;;;;;OAOG,CACa,UAAU,CAAC,OAAqB,EAAwG;4BAAtG,iEAAkC,IAAI,EAAE,6EAAqB,KAAK,+BAAE,0BAA0B,uCAAG,KAAK;QACpJ,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAClB,MAAM,UAAU,GAAG,2KAAI,aAAU,EAAE,CAAC;YACpC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;YAE7B,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YAE9B,iKAAI,WAAQ,8JAAC,WAAQ,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;QAC1E,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,OAAO,EAAE,aAAa,EAAE,SAAS,EAAE,0BAA0B,CAAC,CAAC;QAC7F,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG,CACa,aAAa,CAAC,OAAqB,EAAE,MAAe,EAAuB;4BAArB,aAAa,oDAAG,KAAK;QACvF,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAClB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,OAAO,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;QAC7D,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG,CACI,YAAY,GAAA;QACf,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAClB,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC;QAC9B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG,CACI,KAAK,CAAC,OAAgB,EAAE,MAAc,EAAE,QAAgB,EAAgC;sCAA9B,uBAAuB,0CAAG,IAAI;QAC3F,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAClB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC;QAE3C,YAAY;QACZ,IAAI,WAAW,CAAC;QAChB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,OAAQ,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC3C,qKAAK,WAAQ,CAAC,iBAAiB;oBAC3B,WAAW,GAAG,OAAO,CAAC,oBAAoB,CAAe,IAAI,CAAC,UAAU,EAAE,EAAE,MAAM,CAAC,CAAC;oBACpF,MAAM;gBACV;oBACI,WAAW,GAAG,IAAI,CAAC;oBACnB,MAAM;YACd,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,OAAQ,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC3C,oKAAK,YAAQ,CAAC,aAAa;oBACvB,WAAW,GAAG,IAAI,CAAC;oBACnB,MAAM;gBACV,qKAAK,WAAQ,CAAC,iBAAiB;oBAC3B,WAAW,GAAG,OAAO,CAAC,oBAAoB,CAAe,IAAI,CAAC,UAAU,EAAE,EAAE,MAAM,CAAC,CAAC;oBACpF,MAAM;gBACV,QAAQ;gBACR,qKAAK,WAAQ,CAAC,gBAAgB;oBAC1B,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC;oBAC9C,MAAM;YACd,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,WAAW,EAAE,uBAAuB,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG,CACI,WAAW,CAAC,MAAc,EAAE,WAAiC,EAAgC;YAA9B,uBAAuB,oEAAG,IAAI;QAChG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAClB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,gBAAgB;QAChB,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,wBAAwB,EAAE,CAAC;YAC9E,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAC1C,CAAC;QAED,OAAO;QACP,IAAI,CAAC,uBAAuB,IAAI,CAAC,IAAI,CAAC,4BAA4B,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1F,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;QAC9C,CAAC,MAAM,CAAC;YACJ,IACI,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,QAAQ,IACzC,IAAI,CAAC,4BAA4B,CAAC,YAAY,IAC9C,IAAI,CAAC,4BAA4B,CAAC,YAAY,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,mBAAmB,CAAC,EACtG,CAAC;gBACC,MAAM,aAAa,GAAG,IAAI,CAAC,4BAA4B,CAAC,YAAY,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;gBAC3H,IAAK,MAAM,IAAI,IAAI,aAAa,CAAE,CAAC;oBAC/B,IAAI,CAAC,4BAA4B,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;gBAChF,CAAC;YACL,CAAC;YACD,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,EAAE,WAAW,EAAE,IAAI,CAAC,4BAA4B,CAAC,aAAa,EAAE,IAAI,CAAC,4BAA4B,CAAC,kBAAkB,CAAC,CAAC;QACrJ,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG,CACI,KAAK,CAAC,OAAgB,EAAE,QAAgB,EAAE,cAAuB,EAAA;QACpE,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAAE,IAAK,AAAD,CAAE,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAE,CAAC;YAClH,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,EAAE,CAAC;YACrD,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC7E,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAEjC,IAAI,AAAC,IAAI,CAAC,UAAU,IAAI,QAAQ,KAAK,2KAAQ,CAAC,iBAAiB,CAAC,GAAI,QAAQ,oKAAI,WAAQ,CAAC,aAAa,EAAE,CAAC;YACrG,yBAAyB;YACzB,MAAM,CAAC,cAAc,CAAC,QAAQ,EAAE,OAAO,CAAC,aAAa,EAAE,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC,mBAAmB,IAAI,cAAc,CAAC,CAAC;QAC9H,CAAC,MAAM,IAAI,QAAQ,oKAAI,WAAQ,CAAC,iBAAiB,EAAE,CAAC;YAChD,yBAAyB;YACzB,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,EAAE,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAAC,mBAAmB,IAAI,cAAc,CAAC,CAAC;QAC/G,CAAC,MAAM,CAAC;YACJ,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,mBAAmB,IAAI,cAAc,CAAC,CAAC;QAC1H,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,oBAAoB,CAAC,IAAkC,EAAA;QAC1D,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACxC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,sBAAsB,CAAC,IAAkC,EAAA;QAC5D,IAAI,CAAC,wBAAwB,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QACnD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,mBAAmB,CAAC,IAAkC,EAAA;QACzD,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACvC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,qBAAqB,CAAC,IAAkC,EAAA;QAC3D,IAAI,CAAC,uBAAuB,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAClD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG,CACI,uBAAuB,CAAC,SAAiB,EAAoC;gCAAlC,iEAA6B,KAAK;QAChF,MAAM,mBAAmB,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC3D,IAAI,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,CAAC;YACrC,IAAI,iBAAiB,EAAE,CAAC;gBACpB,mBAAmB,CAAC,qCAAqC,CAAC,0BAA0B,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;gBACxG,mBAAmB,CAAC,qCAAqC,CAAC,UAAU,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;gBACvF,OAAO,mBAAmB,CAAC,qCAAqC,CAAC;YACrE,CAAC;YACD,IAAI,mBAAmB,CAAC,aAAa,EAAE,CAAC;gBACpC,OAAO,mBAAmB,CAAC,aAAa,CAAC;YAC7C,CAAC;QACL,CAAC;QACD,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,MAAM,yBAAyB,GAAG,KAAK,CAAC,0BAA0B,EAAE,CAAC;QACrE,MAAM,gBAAgB,GAAG,yBAAyB,GAC5C,IAAI,CAAC,6BAA6B,CAAC,6BAA6B,GAChE,IAAI,CAAC,6BAA6B,CAAC,iBAAiB,CAAC;QAC3D,MAAM,UAAU,GAAG,mBAAmB,CAAC,UAAU,CAAC;QAClD,UAAU,CAAC,UAAU,GAAG,KAAK,CAAC;QAC9B,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,GAAG,iBAAiB,IAAI,AAAC,CAAC,gBAAgB,IAAI,IAAI,CAAC,SAAS,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC;QAClH,UAAU,CAAC,gBAAgB,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;QAE9C,IAAI,mBAAmB,CAAC,gBAAgB,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC7D,MAAM,gBAAgB,GAAG,mBAAmB,CAAC,gBAAgB,CAAC;YAC9D,MAAM,eAAe,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;YAC5C,MAAM,eAAe,GAAG,yBAAyB,CAAC,CAAC,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,CAAC,CAAC,gBAAgB,CAAC,eAAe,CAAC;YACpI,UAAU,CAAC,gBAAgB,CAAC,SAAS,CAAC,GAAG,gBAAgB,CAAC,eAAe,CAAC,CAAC;YAE3E,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,SAAS,CAAC,IAAI,eAAe,EAAE,CAAC;gBAC7D,UAAU,CAAC,gBAAgB,CAAC,SAAS,CAAC,GAAG,gBAAgB,CAAC,eAAe,CAAC,CAAC;YAC/E,CAAC;QACL,CAAC;QACD,UAAU,CAAC,0BAA0B,CAAC,SAAS,CAAC,GAC5C,CAAC,iBAAiB,IAClB,IAAI,CAAC,oBAAoB,CAAC,0BAA0B,IACpD,UAAU,CAAC,gBAAgB,CAAC,SAAS,CAAC,KAAK,IAAI,IAC/C,UAAU,CAAC,gBAAgB,CAAC,SAAS,CAAC,KAAK,SAAS,CAAC;QACzD,mBAAmB,CAAC,aAAa,GAAG,UAAU,CAAC;QAE/C,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;OAGG,CACI,uBAAuB,CAAC,OAAgB,EAAE,KAAsB,EAAE,0BAAkC,EAAE,MAAsB,EAAE,QAAiB,EAAE,MAAe,EAAA;QACnK,MAAM,gBAAgB,GAAG,KAAK,CAAC,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAC7D,MAAM,oBAAoB,GAAG,gBAAgB,CAAC,CAAC,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAE5E,MAAM,eAAe,GAAG,KAAK,CAAC,MAAM,CAAC;QACrC,MAAM,mBAAmB,GAAG,IAAI,CAAC,oBAAoB,CAAC;QACtD,IAAI,eAAe,GAAG,eAAe,CAAC,eAAe,CAAC;QACtD,IAAI,uBAAuB,GAAG,eAAe,CAAC,uBAAuB,CAAC;QAEtE,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,IAAI,cAAc,GAAG,CAAC,CAAC;QAEvB,MAAM,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAEjD,MAAM,gBAAgB,GAClB,CAAC,eAAe,IAChB,0BAA0B,KAAK,eAAe,CAAC,mBAAmB,IACjE,IAAI,CAAC,MAAM,CAAC,0BAA0B,IAAI,CAAC,eAAe,CAAC,uBAAuB,CAAC,CAAC;QAEzF,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,YAAY,IAAI,CAAC,CAAC,mBAAmB,CAAC,QAAQ,IAAI,gBAAgB,CAAC,EAAE,CAAC;YACjG,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YACpC,IAAI,UAAU,EAAE,CAAC;gBACb,IAAI,IAAI,CAAC,MAAM,CAAC,0BAA0B,EAAE,CAAC;oBACzC,IAAI,CAAC,mBAAmB,CAAC,6BAA6B,EAAE,CAAC;wBACrD,mBAAmB,CAAC,6BAA6B,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;wBAClE,mBAAmB,CAAC,6BAA6B,CAAC,WAAW,CAAC,eAAe,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;oBACjH,CAAC,MAAM,CAAC;wBACJ,mBAAmB,CAAC,6BAA6B,CAAC,WAAW,CAAC,eAAe,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;wBAC7G,mBAAmB,CAAC,6BAA6B,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;oBACtE,CAAC;gBACL,CAAC;gBACD,KAAK,CAAC,WAAW,CAAC,eAAe,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;gBACzD,MAAM,IAAI,EAAE,CAAC;gBACb,cAAc,EAAE,CAAC;YACrB,CAAC;YAED,IAAI,gBAAgB,EAAE,CAAC;;gBACnB,IAAI,IAAI,CAAC,8BAA8B,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,qCAAY,WAAW,EAAE,yDAArB,OAAO,cAAgB,wBAAwB,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC,GAAE,CAAC;oBACjJ,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,cAAc,CAAC;oBAC/D,IAAK,IAAI,aAAa,GAAG,CAAC,EAAE,aAAa,GAAG,gBAAgB,CAAC,MAAM,EAAE,aAAa,EAAE,CAAE,CAAC;wBACnF,MAAM,YAAY,GAAG,gBAAgB,CAAC,aAAa,CAAC,CAAC;wBACrD,YAAY,CAAC,iBAAiB,qKAAG,UAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC,cAAc,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;oBACjI,CAAC;oBACD,gBAAgB,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE;wBAC7B,OAAO,EAAE,CAAC,iBAAiB,GAAG,EAAE,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,iBAAiB,GAAG,EAAE,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClH,CAAC,CAAC,CAAC;gBACP,CAAC;gBACD,IAAK,IAAI,aAAa,GAAG,CAAC,EAAE,aAAa,GAAG,gBAAgB,CAAC,MAAM,EAAE,aAAa,EAAE,CAAE,CAAC;oBACnF,MAAM,QAAQ,GAAG,gBAAgB,CAAC,aAAa,CAAC,CAAC;oBACjD,MAAM,MAAM,GAAG,QAAQ,CAAC,cAAc,EAAE,CAAC;oBACzC,MAAM,CAAC,WAAW,CAAC,eAAe,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;oBAE1D,IAAI,IAAI,CAAC,MAAM,CAAC,0BAA0B,EAAE,CAAC;wBACzC,IAAI,CAAC,QAAQ,CAAC,oBAAoB,EAAE,CAAC;4BACjC,QAAQ,CAAC,oBAAoB,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;4BAC/C,QAAQ,CAAC,oBAAoB,CAAC,WAAW,CAAC,eAAe,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;wBAC7F,CAAC,MAAM,CAAC;4BACJ,QAAQ,CAAC,oBAAoB,CAAC,WAAW,CAAC,eAAe,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;4BACzF,QAAQ,CAAC,oBAAoB,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;wBACnD,CAAC;oBACL,CAAC;oBAED,MAAM,IAAI,EAAE,CAAC;oBACb,cAAc,EAAE,CAAC;gBACrB,CAAC;YACL,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,cAAc,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC;QACjE,CAAC;QAED,IAAI,gBAAgB,EAAE,CAAC;YACnB,IAAI,eAAe,EAAE,CAAC;gBAClB,eAAe,CAAC,OAAO,EAAE,CAAC;YAC9B,CAAC;YAED,IAAI,uBAAuB,EAAE,CAAC;gBAC1B,uBAAuB,CAAC,OAAO,EAAE,CAAC;YACtC,CAAC;YAED,eAAe,GAAG,gKAAI,SAAM,CAAC,MAAM,EAAE,eAAe,CAAC,aAAa,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;YAC3F,eAAe,CAAC,eAAe,GAAG,eAAe,CAAC;YAClD,IAAI,CAAC,IAAI,CAAC,4BAA4B,EAAE,CAAC;gBACrC,IAAI,CAAC,4BAA4B,GAAG;oBAChC,IAAI,EAAE,CAAA,CAAE;oBACR,aAAa,EAAE,CAAA,CAAE;oBACjB,OAAO,EAAE,CAAA,CAAE;oBACX,KAAK,EAAE,CAAA,CAAE;oBACT,kBAAkB,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAA,CAAE,CAAC,CAAC,CAAC,SAAS;iBACpF,CAAC;YACN,CAAC;YAED,IAAI,2BAA2B,CAAC;YAChC,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;gBAC5C,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,YAAY,EAAE,CAAC;oBAClD,IAAI,CAAC,4BAA4B,CAAC,YAAY,GAAG,CAAA,CAAE,CAAC;gBACxD,CAAC;gBAED,MAAM,mBAAmB,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,mBAAmB,CAAC;gBACjF,2BAA2B,GAAG,IAAI,CAAC,4BAA4B,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;gBAClG,IAAI,CAAC,2BAA2B,EAAE,CAAC;oBAC/B,IAAI,CAAC,4BAA4B,CAAC,YAAY,CAAC,mBAAmB,CAAC,GAAG,2BAA2B,GAAG,CAAA,CAAE,CAAC;gBAC3G,CAAC;YACL,CAAC,MAAM,CAAC;gBACJ,2BAA2B,GAAG,IAAI,CAAC,4BAA4B,CAAC,aAAa,CAAC;YAClF,CAAC;YAED,2BAA2B,CAAC,QAAQ,CAAC,GAAG,eAAe,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC3F,2BAA2B,CAAC,QAAQ,CAAC,GAAG,eAAe,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC3F,2BAA2B,CAAC,QAAQ,CAAC,GAAG,eAAe,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC3F,2BAA2B,CAAC,QAAQ,CAAC,GAAG,eAAe,CAAC,kBAAkB,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;YAE5F,IAAI,IAAI,CAAC,MAAM,CAAC,0BAA0B,EAAE,CAAC;gBACzC,uBAAuB,GAAG,gKAAI,SAAM,CAAC,MAAM,EAAE,eAAe,CAAC,qBAAqB,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;gBAC3G,eAAe,CAAC,uBAAuB,GAAG,uBAAuB,CAAC;gBAElE,2BAA2B,CAAC,gBAAgB,CAAC,GAAG,uBAAuB,CAAC,kBAAkB,CAAC,gBAAgB,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBACnH,2BAA2B,CAAC,gBAAgB,CAAC,GAAG,uBAAuB,CAAC,kBAAkB,CAAC,gBAAgB,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBACnH,2BAA2B,CAAC,gBAAgB,CAAC,GAAG,uBAAuB,CAAC,kBAAkB,CAAC,gBAAgB,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBACnH,2BAA2B,CAAC,gBAAgB,CAAC,GAAG,uBAAuB,CAAC,kBAAkB,CAAC,gBAAgB,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;YACxH,CAAC;YACD,IAAI,CAAC,oCAAoC,EAAE,CAAC;QAChD,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,QAAQ,IAAI,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,EAAE,CAAC;gBACtF,eAAgB,CAAC,cAAc,CAAC,eAAe,CAAC,aAAa,EAAE,CAAC,EAAE,cAAc,CAAC,CAAC;gBAClF,IAAI,IAAI,CAAC,MAAM,CAAC,0BAA0B,IAAI,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,YAAY,IAAI,IAAI,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,EAAE,CAAC;oBACxI,uBAAwB,CAAC,cAAc,CAAC,eAAe,CAAC,qBAAqB,EAAE,CAAC,EAAE,cAAc,CAAC,CAAC;gBACtG,CAAC;YACL,CAAC;QACL,CAAC;QAED,IAAI,CAAC,wBAAwB,CAAC,gBAAgB,EAAE,UAAU,CAAC,CAAC;QAE5D,IAAI,MAAM,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YACnC,QAAQ;YACR,IAAI,CAAC,QAAQ,EAAE,CAAC,cAAc,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,GAAG,cAAc,EAAE,KAAK,CAAC,CAAC;YAEpF,OAAO;YACP,IAAI,MAAM,CAAC,mBAAmB,EAAE,CAAC;gBAC7B,MAAM,CAAC,mBAAmB,CAAC,aAAa,GAAG,IAAI,CAAC;YACpD,CAAC;YACD,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;YACtC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC;QAClD,CAAC;QAED,uEAAuE;QACvE,wEAAwE;QACxE,+CAA+C;QAC/C,IACI,IAAI,CAAC,MAAM,CAAC,0BAA0B,IACtC,CAAC,gBAAgB,IACjB,IAAI,CAAC,oBAAoB,CAAC,YAAY,IACtC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,QAAQ,IAAI,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,IACrF,CAAC,IAAI,CAAC,oBAAoB,CAAC,oBAAoB,EACjD,CAAC;YACC,uBAAwB,CAAC,cAAc,CAAC,eAAe,CAAC,aAAa,EAAE,CAAC,EAAE,cAAc,CAAC,CAAC;QAC9F,CAAC;IACL,CAAC;IAED;;OAEG,CACI,oBAAoB,CAAC,OAAgB,EAAE,QAAgB,EAAE,KAAsB,EAAE,MAAc,EAAE,MAAsB,EAAA;QAC1H,MAAM,gBAAgB,GAAG,KAAK,CAAC,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAC7D,MAAM,oBAAoB,GAAG,gBAAgB,CAAC,CAAC,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAE5E,MAAM,eAAe,GAAG,KAAK,CAAC,MAAM,CAAC;QACrC,MAAM,0BAA0B,GAAG,eAAe,CAAC,mBAAmB,CAAC;QACvE,MAAM,aAAa,GAAG,oBAAoB,GAAG,CAAC,CAAC;QAC/C,MAAM,UAAU,GAAG,aAAa,GAAG,EAAE,GAAG,CAAC,CAAC;QAE1C,MAAO,eAAe,CAAC,mBAAmB,GAAG,UAAU,CAAE,CAAC;YACtD,eAAe,CAAC,mBAAmB,IAAI,CAAC,CAAC;QAC7C,CAAC;QAED,IAAI,CAAC,eAAe,CAAC,aAAa,IAAI,0BAA0B,IAAI,eAAe,CAAC,mBAAmB,EAAE,CAAC;YACtG,eAAe,CAAC,aAAa,GAAG,IAAI,YAAY,CAAC,eAAe,CAAC,mBAAmB,GAAG,CAAC,CAAC,CAAC;QAC9F,CAAC;QACD,IAAK,AAAD,IAAK,CAAC,MAAM,CAAC,0BAA0B,IAAI,CAAC,eAAe,CAAC,qBAAqB,CAAC,GAAI,0BAA0B,IAAI,eAAe,CAAC,mBAAmB,EAAE,CAAC;YAC1J,eAAe,CAAC,qBAAqB,GAAG,IAAI,YAAY,CAAC,eAAe,CAAC,mBAAmB,GAAG,CAAC,CAAC,CAAC;QACtG,CAAC;QAED,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,KAAK,EAAE,0BAA0B,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;QAEnG,MAAM,CAAC,wBAAwB,EAAE,CAAC;QAClC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG,CACI,wBAAwB,CAAC,OAAgB,EAAE,QAAgB,EAAE,MAAc,EAAE,MAAsB,EAAA;YAE/E;;QADvB,QAAQ;QACR,MAAM,cAAc,0FAAO,CAAC,wBAAwB,kGAAE,cAAc,yHAAI,CAAC,CAAC;QAE1E,IAAI,CAAC,QAAQ,EAAE,CAAC,cAAc,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,GAAG,cAAc,EAAE,KAAK,CAAC,CAAC;QAEpF,OAAO;QACP,IAAI,MAAM,CAAC,mBAAmB,EAAE,CAAC;YAC7B,MAAM,CAAC,mBAAmB,CAAC,aAAa,GAAG,IAAI,CAAC;QACpD,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;QACtC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC;QAE9C,8CAA8C;QAC9C,wEAAwE;QACxE,+CAA+C;QAC/C,IAAI,IAAI,CAAC,MAAM,CAAC,0BAA0B,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,kBAAkB,IAAI,IAAI,CAAC,wBAAwB,CAAC,UAAU,EAAE,CAAC;YAC1I,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,oBAAoB,EAAE,CAAC;gBACtD,IAAI,CAAC,wBAAwB,CAAC,oBAAoB,GAAG,IAAI,CAAC,+BAA+B,CAAC,eAAe,EAAE,IAAI,CAAC,wBAAwB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;YAChK,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,wBAAwB,CAAC,oBAAoB,CAAC,cAAc,CAAC,IAAI,CAAC,wBAAwB,CAAC,UAAU,EAAE,CAAC,EAAE,cAAc,CAAC,CAAC;YACnI,CAAC;QACL,CAAC;QAED,MAAM,CAAC,wBAAwB,EAAE,CAAC;IACtC,CAAC;IAED;;OAEG,CACH,6DAA6D;IACtD,wBAAwB,CAAC,gBAA2C,EAAE,UAAmB,EAAA;IAC5F,aAAa;IACjB,CAAC;IAED;;OAEG,CACI,iBAAiB,CACpB,aAA2B,EAC3B,OAAgB,EAChB,MAAc,EACd,QAAgB,EAChB,KAAsB,EACtB,0BAAmC,EACnC,YAAwF,EACxF,iBAA4B,EAAA;QAE5B,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QACjC,QAAQ,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;QAEhD,IAAI,0BAA0B,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC,gBAAgB,EAAE,CAAC;YAC5E,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;YACjE,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,0BAA0B,EAAE,CAAC;YAC7B,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;QACxE,CAAC,MAAM,CAAC;YACJ,IAAI,MAAM,CAAC,mBAAmB,EAAE,CAAC;gBAC7B,MAAM,CAAC,mBAAmB,CAAC,aAAa,GAAG,KAAK,CAAC;YACrD,CAAC;YAED,IAAI,aAAa,GAAG,CAAC,CAAC;YACtB,IAAI,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;gBAChC,OAAO;gBACP,IAAI,YAAY,EAAE,CAAC;oBACf,YAAY,CAAC,KAAK,EAAE,aAAa,CAAC,cAAc,EAAE,EAAE,iBAAiB,CAAC,CAAC;gBAC3E,CAAC;gBACD,aAAa,EAAE,CAAC;gBAEhB,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,oBAAoB,CAAC,sBAAsB,CAAC,CAAC;YACpF,CAAC;YAED,MAAM,0BAA0B,GAAG,KAAK,CAAC,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAEvE,IAAI,0BAA0B,EAAE,CAAC;gBAC7B,MAAM,oBAAoB,GAAG,0BAA0B,CAAC,MAAM,CAAC;gBAC/D,aAAa,IAAI,oBAAoB,CAAC;gBAEtC,QAAQ;gBACR,IAAK,IAAI,aAAa,GAAG,CAAC,EAAE,aAAa,GAAG,oBAAoB,EAAE,aAAa,EAAE,CAAE,CAAC;oBAChF,MAAM,QAAQ,GAAG,0BAA0B,CAAC,aAAa,CAAC,CAAC;oBAE3D,QAAQ;oBACR,MAAM,KAAK,GAAG,QAAQ,CAAC,cAAc,EAAE,CAAC;oBACxC,IAAI,YAAY,EAAE,CAAC;wBACf,YAAY,CAAC,IAAI,EAAE,KAAK,EAAE,iBAAiB,CAAC,CAAC;oBACjD,CAAC;oBACD,OAAO;oBACP,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;gBAClC,CAAC;YACL,CAAC;YAED,QAAQ;YACR,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,GAAG,aAAa,EAAE,KAAK,CAAC,CAAC;QAC7E,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG,CACa,QAAQ,GAAgB;sBAAf,OAAO,0DAAG,KAAK;QACpC,IAAK,MAAM,YAAY,IAAI,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAE,CAAC;YAChE,MAAM,mBAAmB,GAAG,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;YACjF,IAAI,mBAAmB,CAAC,eAAe,EAAE,CAAC;gBACtC,gFAAgF;gBAChF,IAAI,OAAO,EAAE,CAAC;oBACV,mBAAmB,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;gBAClD,CAAC;gBACD,mBAAmB,CAAC,eAAe,GAAG,IAAI,CAAC;YAC/C,CAAC;QACL,CAAC;QACD,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;YACpC,IAAK,MAAM,IAAI,IAAI,IAAI,CAAC,4BAA4B,CAAC,aAAa,CAAE,CAAC;gBACjE,MAAM,MAAM,GAAG,IAAI,CAAC,4BAA4B,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;gBACrE,IAAI,MAAM,EAAE,CAAC;oBACT,gFAAgF;oBAChF,IAAI,OAAO,EAAE,CAAC;wBACV,MAAM,CAAC,OAAO,EAAE,CAAC;oBACrB,CAAC;oBACD,IAAI,CAAC,4BAA4B,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;gBACjE,CAAC;YACL,CAAC;YACD,IAAI,IAAI,CAAC,4BAA4B,CAAC,kBAAkB,EAAE,CAAC;gBACvD,IAAI,CAAC,4BAA4B,CAAC,kBAAkB,GAAG,CAAA,CAAE,CAAC;YAC9D,CAAC;QACL,CAAC;QACD,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,GAAG,IAAI,CAAC;QACrD,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IAC5B,CAAC;IAED,cAAA,EAAgB,CACA,OAAO,GAAA;QACnB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAClB,OAAO;QACX,CAAC;QAED,kBAAkB;QAClB,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACzD,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC;QACxC,CAAC;QAED,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,GAAG,IAAI,CAAC;QACrD,IAAI,CAAC,oBAAoB,CAAC,QAAQ,GAAG,IAAI,CAAC;IAC9C,CAAC;IAED,cAAA,EAAgB,CACA,SAAS,GAAA;QACrB,IAAI,CAAC,oBAAoB,CAAC,QAAQ,GAAG,KAAK,CAAC;QAC3C,IAAK,MAAM,YAAY,IAAI,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAE,CAAC;YAChE,MAAM,mBAAmB,GAAG,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;YACjF,mBAAmB,CAAC,aAAa,GAAG,IAAI,CAAC;QAC7C,CAAC;IACL,CAAC;IAED;;;;;;;;OAQG,CACI,sBAAsB,CAAC,YAAqB,EAAE,eAAyB,EAAE,wBAAuC,EAAE,OAAiB,EAA4B;kCAA1B,mBAAmB,8CAAG,IAAI;QAClK,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QACvC,MAAM,mBAAmB,GAAG,MAAM,CAAC,mBAAmB,CAAC;QAEvD,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;YAC7B,MAAM,CAAC,mBAAmB,GAAG,YAAY,CAAC;QAC9C,CAAC;QAED,IAAI,OAAO,EAAE,CAAC;YACV,IAAI,CAAC,mBAAmB,IAAI,AAAC,mBAAmB,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAE,CAAC;gBACnG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,eAAe,EAAE,wBAAwB,CAAC,CAAC;YACtE,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;gBAC7C,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBAClC,IAAI,CAAC,mBAAmB,IAAI,AAAC,mBAAmB,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAE,CAAC;oBACnG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,eAAe,EAAE,wBAAwB,CAAC,CAAC;gBACtE,CAAC;YACL,CAAC;QACL,CAAC;QAED,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;YAC7B,MAAM,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;QACrD,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG,CACI,YAAY,GAAA;QACf,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAClB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,SAAS,CAAE,CAAC;YACnC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAChC,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG,CACI,MAAM,CAAC,OAAgB,EAAE,eAAwB,EAAE,wBAAuC,EAAA;;QAC7F,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,IAAI,IAAI,CAAC,6BAA6B,CAAC,qBAAqB,EAAE,CAAC;YAC3D,IAAI,CAAC,6BAA6B,CAAC,qBAAqB,GAAG,KAAK,CAAC;QACrE,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,6BAA6B,CAAC,SAAS,GAAG,KAAK,CAAC;QACzD,CAAC;;QAED,MAAM,gBAAgB,GAAG,KAAK,wDAAC,aAAa,8EAAE,MAAM,qFAAI,CAAC,CAAC;QAC1D,MAAM,sBAAsB,GAAG,AAAC,gBAAgB,GAAG,CAAC,IAAI,KAAK,CAAC,YAAY,KAAK,KAAK,CAAC,aAAc,CAAC,CAAC,CAAC,CAAC,GAAI,gBAAgB,IAAI,CAAC,CAAC;QAEjI,IAAI,sBAAsB,IAAI,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,0BAA0B,EAAE,CAAC;YAClH,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,qBAAqB;QACrB,MAAM,KAAK,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,wBAAwB,CAAC,CAAC;QAEpF,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,0BAA0B;QAC1B,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAAE,IAAI,AAAC,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAE,CAAC;YAClH,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QACjC,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,SAAS,GAAqB,IAAI,CAAC;QACvC,IAAI,IAAI,CAAC,gBAAgB,IAAI,KAAK,CAAC,YAAY,IAAI,CAAC,KAAK,CAAC,0BAA0B,EAAE,EAAE,CAAC;YACrF,aAAa,GAAG,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC;YACxC,SAAS,GAAG,KAAK,CAAC,YAAY,CAAC;YAC/B,KAAK,CAAC,YAAY,CAAC,IAAI,GAAG,CAAC,CAAC;YAC5B,KAAK,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;QACtC,CAAC;QAED,IAAI,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,EAAE,CAAC;YACvD,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC/E,CAAC;QAED,MAAM,aAAa,GAAG,OAAO,CAAC,gBAAgB,EAAE,CAAC;QACjD,MAAM,0BAA0B,GAC5B,KAAK,CAAC,0BAA0B,CAAC,OAAO,CAAC,GAAG,CAAC,IAC7C,aAAa,CAAC,gBAAgB,IAC7B,CAAC,CAAC,IAAI,CAAC,4BAA4B,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,6BAA6B,CAAC,iBAAiB,CAAC,CAAC;QAChH,MAAM,mBAAmB,GAAG,IAAI,CAAC,oBAAoB,CAAC;QAEtD,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QACvC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACZ,IAAI,SAAS,EAAE,CAAC;gBACZ,SAAS,CAAC,IAAI,GAAG,aAAa,CAAC;gBAC/B,KAAK,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;YACtC,CAAC;YACD,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,WAAW;QACX,IAAI,CAAC,mBAAmB,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,IAAI,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,KAAK,QAAQ,EAAE,CAAC;YAChJ,IAAI,QAAQ,CAAC,uBAAuB,EAAE,CAAC;gBACnC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,0BAA0B,CAAC,EAAE,CAAC;oBACzE,IAAI,SAAS,EAAE,CAAC;wBACZ,SAAS,CAAC,IAAI,GAAG,aAAa,CAAC;wBAC/B,KAAK,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;oBACtC,CAAC;oBACD,OAAO,IAAI,CAAC;gBAChB,CAAC;YACL,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,0BAA0B,CAAC,EAAE,CAAC;gBAC7D,IAAI,SAAS,EAAE,CAAC;oBACZ,SAAS,CAAC,IAAI,GAAG,aAAa,CAAC;oBAC/B,KAAK,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;gBACtC,CAAC;gBACD,OAAO,IAAI,CAAC;YAChB,CAAC;YAED,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,GAAG,QAAQ,CAAC;QAC7D,CAAC,MAAM,IACH,AAAC,QAAQ,CAAC,uBAAuB,IAAI,mCAAS,YAAY,0DAApB,OAAO,eAAe,mBAAmB,CAAC,IAC/E,CAAC,QAAQ,CAAC,uBAAuB,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC,mBAAmB,CAAC,CACxF,CAAC;YACC,IAAI,SAAS,EAAE,CAAC;gBACZ,SAAS,CAAC,IAAI,GAAG,aAAa,CAAC;gBAC/B,KAAK,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;YACtC,CAAC;YACD,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,aAAa;QACb,IAAI,eAAe,EAAE,CAAC;YAClB,MAAM,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,CAAC;YACxE,IAAI,iBAAiB,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC5C,MAAM,CAAC,YAAY,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YACrD,CAAC,MAAM,CAAC;gBACJ,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,iBAAiB,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;oBAC3D,MAAM,SAAS,GAAG,iBAAiB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;oBAClD,MAAM,CAAC,YAAY,CAAC,SAAS,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,GAAA,MAAS,CAAC,aAAa,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;gBACjG,CAAC;YACL,CAAC;QACL,CAAC;QAED,IAAI,WAAkC,CAAC;QACvC,IAAI,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,uBAAuB,EAAE,CAAC;YACxE,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC;QACvC,CAAC,MAAM,CAAC;YACJ,WAAW,GAAG,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,eAAe,EAAE,CAAC;QAClF,CAAC;;QAED,MAAM,MAAM,gGAAgB,MAAM,+CAAnB,WAAW,WAAY,IAAI,CAAC;QAE3C,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,yBAAyB,CAAE,CAAC;YACjD,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC,WAAW,IAAI,CAAC,MAAM,EAAE,CAAC;YAC1B,IAAI,SAAS,EAAE,CAAC;gBACZ,SAAS,CAAC,IAAI,GAAG,aAAa,CAAC;gBAC/B,KAAK,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;YACtC,CAAC;YACD,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,aAAa,GAAG,wBAAwB,IAAI,IAAI,CAAC;QAEvD,IAAI,eAAiC,CAAC;QAEtC,IACI,CAAC,mBAAmB,CAAC,QAAQ,IAC7B,CAAC,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,eAAe,IAC1D,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,eAAe,KAAK,IAAI,IACrE,IAAI,CAAC,qBAAqB,CAAC,kBAA0B,CAAC,gBAAgB,CAAC,EAC9E,CAAC;YACC,sKAAsK;YACtK,MAAM,eAAe,GAAG,aAAa,CAAC,0BAA0B,EAAE,CAAC;YACnE,eAAe,GAAG,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;YAE/F,IAAI,eAAe,GAAG,CAAC,EAAE,CAAC;gBACtB,eAAe,GAAG,eAAe,qKAAK,WAAQ,CAAC,wBAAwB,CAAC,CAAC,iKAAC,WAAQ,CAAC,+BAA+B,CAAC,CAAC,iKAAC,WAAQ,CAAC,wBAAwB,CAAC;YAC3J,CAAC;YACD,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,GAAG,eAAgB,CAAC;QAC5E,CAAC,MAAM,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YAC3B,eAAe,GAAG,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,CAAC;QAC3E,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,CAAC,CAAC;QAE1I,IAAI,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,eAAe,EAAE,CAAC;YAChE,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAC/B,CAAC;QAED,OAAO;QACP,MAAM,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,CAAC;QACxE,MAAM,QAAQ,GAAG,iBAAiB,CAAC,QAAQ,CAAC;QAE5C,IAAI,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,EAAE,CAAC;YACrD,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC7E,CAAC;QAED,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAC9B,wEAAwE;YACxE,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QACjD,CAAC;QAED,MAAM,KAAK,GAAG,aAAa,CAAC,cAAc,EAAE,CAAC;QAC7C,IAAI,iBAAiB,CAAC,uBAAuB,EAAE,CAAC;YAC5C,iBAAiB,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QAC3D,CAAC,MAAM,CAAC;YACJ,iBAAiB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACxC,CAAC;QAED,IAAI,CAAC,iBAAiB,CAAC,eAAe,IAAI,iBAAiB,CAAC,mBAAmB,EAAE,CAAC;YAC9E,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,iBAAiB,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC,OAAO,EAAE,iBAAiB,CAAC,aAAa,EAAE,iBAAiB,CAAC,OAAO,EAAE,iBAAiB,CAAC,YAAY,CAAC,CAAC;YAC9J,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,0BAA0B,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,CAAC;YAC9J,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,iBAAiB,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,iBAAiB,CAAC,aAAa,EAAE,iBAAiB,CAAC,OAAO,EAAE,iBAAiB,CAAC,YAAY,CAAC,CAAC;YAE7J,IAAI,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,EAAE,CAAC;gBACtD,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YACjF,CAAC;QACL,CAAC;QAED,OAAO;QACP,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,0BAA0B,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,CAAC;QAE9J,SAAS;QACT,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC;QAEvD,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,wBAAwB,CAAE,CAAC;YAChD,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,EAAE,CAAC;YACtD,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC9E,CAAC;QAED,IAAI,SAAS,EAAE,CAAC;YACZ,SAAS,CAAC,IAAI,GAAG,aAAa,CAAC;YAC/B,KAAK,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;QACtC,CAAC;QAED,IAAI,KAAK,CAAC,mBAAmB,KAAA,EAAA,uCAAA,EAAwC,KAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,CAAC;YACrG,IAAI,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAID;;;;;OAKG,CACI,kBAAkB,GAAA;QACrB,IAAI,IAAI,CAAC,qBAAqB,6JAAC,eAAY,CAAC,mBAAmB,CAAC,EAAE,CAAC;YAC/D,IAAI,IAAI,CAAC,qBAAqB,6JAAC,eAAY,CAAC,wBAAwB,CAAC,EAAE,CAAC;gBACpE,IAAI,CAAC,6BAA6B,EAAE,CAAC;YACzC,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,yBAAyB,EAAE,CAAC;YACrC,CAAC;QACL,CAAC;IACL,CAAC;IAED,2BAA2B;IACnB,yBAAyB,GAAA;QAC7B,MAAM,eAAe,GAAe,IAAI,CAAC,eAAe,6JAAC,eAAY,CAAC,mBAAmB,CAAC,CAAC;QAC3F,MAAM,UAAU,GAAG,eAAe,CAAC,MAAM,CAAC;QAE1C,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,IAAI,CAAC,CAAE,CAAC;YACrC,qBAAqB;YACrB,MAAM,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACxG,iDAAiD;YACjD,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBACV,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YAC3B,CAAC,MAAM,CAAC;gBACJ,qDAAqD;gBACrD,MAAM,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;gBACpB,eAAe,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC;gBAC5B,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC;gBAChC,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC;gBAChC,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC;YACpC,CAAC;QACL,CAAC;QACD,IAAI,CAAC,eAAe,6JAAC,eAAY,CAAC,mBAAmB,EAAE,eAAe,CAAC,CAAC;IAC5E,CAAC;IACD,iFAAiF;IACzE,6BAA6B,GAAA;QACjC,MAAM,oBAAoB,GAAe,IAAI,CAAC,eAAe,6JAAC,eAAY,CAAC,wBAAwB,CAAC,CAAC;QACrG,MAAM,eAAe,GAAe,IAAI,CAAC,eAAe,6JAAC,eAAY,CAAC,mBAAmB,CAAC,CAAC;QAC3F,MAAM,UAAU,GAAG,eAAe,CAAC,MAAM,CAAC;QAE1C,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,IAAI,CAAC,CAAE,CAAC;YACrC,qBAAqB;YACrB,IAAI,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACtG,CAAC,IAAI,oBAAoB,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACvH,iDAAiD;YACjD,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBACV,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YAC3B,CAAC,MAAM,CAAC;gBACJ,qDAAqD;gBACrD,MAAM,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;gBACpB,eAAe,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC;gBAC5B,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC;gBAChC,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC;gBAChC,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC;gBAChC,uBAAuB;gBACvB,oBAAoB,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC;gBACjC,oBAAoB,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC;gBACrC,oBAAoB,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC;gBACrC,oBAAoB,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC;YACzC,CAAC;QACL,CAAC;QACD,IAAI,CAAC,eAAe,6JAAC,eAAY,CAAC,mBAAmB,EAAE,eAAe,CAAC,CAAC;QACxE,IAAI,CAAC,eAAe,6JAAC,eAAY,CAAC,mBAAmB,EAAE,oBAAoB,CAAC,CAAC;IACjF,CAAC;IAED;;;;;OAKG,CACI,gBAAgB,GAAA;QACnB,MAAM,oBAAoB,GAAe,IAAI,CAAC,eAAe,6JAAC,eAAY,CAAC,wBAAwB,CAAC,CAAC;QACrG,MAAM,eAAe,GAAe,IAAI,CAAC,eAAe,6JAAC,eAAY,CAAC,mBAAmB,CAAC,CAAC;QAC3F,IAAI,eAAe,KAAK,IAAI,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE,CAAC;YACpD,OAAO;gBAAE,OAAO,EAAE,KAAK;gBAAE,KAAK,EAAE,IAAI;gBAAE,MAAM,EAAE,aAAa;YAAA,CAAE,CAAC;QAClE,CAAC;QAED,MAAM,UAAU,GAAG,eAAe,CAAC,MAAM,CAAC;QAC1C,IAAI,eAAe,GAAW,CAAC,CAAC;QAChC,IAAI,cAAc,GAAW,CAAC,CAAC;QAC/B,IAAI,cAAc,GAAW,CAAC,CAAC;QAC/B,IAAI,mBAAmB,GAAW,CAAC,CAAC;QACpC,MAAM,aAAa,GAAW,oBAAoB,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpE,MAAM,gBAAgB,GAAa,EAAE,CAAC;QACtC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,aAAa,EAAE,CAAC,EAAE,CAAE,CAAC;YACtC,gBAAgB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC5B,CAAC;QACD,MAAM,gBAAgB,GAAW,KAAK,CAAC;QAEvC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,IAAI,CAAC,CAAE,CAAC;YACrC,IAAI,UAAU,GAAW,eAAe,CAAC,CAAC,CAAC,CAAC;YAC5C,IAAI,CAAC,GAAG,UAAU,CAAC;YACnB,IAAI,WAAW,GAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAE1C,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,EAAE,CAAC,EAAE,CAAE,CAAC;gBACrC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;gBAC3E,IAAI,CAAC,GAAG,UAAU,EAAE,CAAC;oBACjB,eAAe,EAAE,CAAC;gBACtB,CAAC;gBACD,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;oBACV,WAAW,EAAE,CAAC;gBAClB,CAAC;gBACD,CAAC,IAAI,CAAC,CAAC;gBACP,UAAU,GAAG,CAAC,CAAC;YACnB,CAAC;YACD,iCAAiC;YACjC,gBAAgB,CAAC,WAAW,CAAC,EAAE,CAAC;YAEhC,iBAAiB;YACjB,IAAI,WAAW,GAAG,cAAc,EAAE,CAAC;gBAC/B,cAAc,GAAG,WAAW,CAAC;YACjC,CAAC;YAED,iDAAiD;YACjD,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBACV,cAAc,EAAE,CAAC;YACrB,CAAC,MAAM,CAAC;gBACJ,qDAAqD;gBACrD,MAAM,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;gBACpB,IAAI,SAAS,GAAG,CAAC,CAAC;gBAClB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,EAAE,CAAC,EAAE,CAAE,CAAC;oBACrC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;wBACR,SAAS,IAAI,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;oBACnF,CAAC,MAAM,CAAC;wBACJ,SAAS,IAAI,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;oBACrG,CAAC;gBACL,CAAC;gBACD,uDAAuD;gBACvD,IAAI,SAAS,GAAG,gBAAgB,EAAE,CAAC;oBAC/B,mBAAmB,EAAE,CAAC;gBAC1B,CAAC;YACL,CAAC;QACL,CAAC;QAED,qDAAqD;QACrD,MAAM,QAAQ,GAAW,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC;QACpD,MAAM,eAAe,GAAe,IAAI,CAAC,eAAe,6JAAC,eAAY,CAAC,mBAAmB,CAAC,CAAC;QAC3F,MAAM,oBAAoB,GAAe,IAAI,CAAC,eAAe,CAAC,2KAAY,CAAC,wBAAwB,CAAC,CAAC;QACrG,IAAI,iBAAiB,GAAW,CAAC,CAAC;QAClC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,IAAI,CAAC,CAAE,CAAC;YACrC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,EAAE,CAAC,EAAE,CAAE,CAAC;gBACrC,MAAM,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;gBAC/E,IAAI,KAAK,IAAI,QAAQ,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;oBACjC,iBAAiB,EAAE,CAAC;gBACxB,CAAC;YACL,CAAC;QACL,CAAC;QAED,iBAAiB;QACjB,MAAM,MAAM,GACR,sBAAsB,GACtB,UAAU,GAAG,CAAC,GACd,yBAAyB,GACzB,cAAc,GACd,sBAAsB,GACtB,cAAc,GACd,iBAAiB,GACjB,eAAe,GACf,qBAAqB,GACrB,mBAAmB,GACnB,oBAAoB,GACpB,gBAAgB,GAChB,GAAG,GACH,sBAAsB,GACtB,QAAQ,GACR,uBAAuB,GACvB,iBAAiB,CAAC;QAEtB,OAAO;YAAE,OAAO,EAAE,IAAI;YAAE,KAAK,EAAE,cAAc,KAAK,CAAC,IAAI,mBAAmB,KAAK,CAAC,IAAI,iBAAiB,KAAK,CAAC;YAAE,MAAM,EAAE,MAAM;QAAA,CAAE,CAAC;IAClI,CAAC;IAED,cAAA,EAAgB,CACT,gBAAgB,GAAA;QACnB,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/B,CAAC,MAAM,IAAI,IAAI,CAAC,cAAc,KAAK,GAAA,MAAS,CAAC,wBAAwB,EAAE,CAAC;YACpE,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC,sBAAsB,CAAC;YAEvD,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAC3B,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,UAAU,CAAC,KAAY,EAAA;QAC3B,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAE3B,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC,CAAC;gKAErF,QAAK,CAAC,QAAQ,CACV,IAAI,CAAC,gBAAgB,EACrB,CAAC,IAAI,EAAE,EAAE;YACL,IAAI,IAAI,YAAY,WAAW,EAAE,CAAC;gBAC9B,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAC3C,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;YACvD,CAAC;YAED,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAE,CAAC;gBACpC,QAAQ,CAAC,mBAAmB,EAAE,CAAC;gBAC/B,QAAQ,CAAC,cAAc,EAAE,CAAC;YAC9B,CAAC;YAED,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC,qBAAqB,CAAC;YACtD,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAClC,CAAC,EACD,GAAG,EAAE,AAAE,CAAC,EACR,KAAK,CAAC,eAAe,EACrB,aAAa,CAChB,CAAC;QACF,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG,CACa,WAAW,CAAC,aAAsB,EAAA;QAC9C,IAAI,IAAI,CAAC,cAAc,KAAK,GAAA,MAAS,CAAC,sBAAsB,EAAE,CAAC;YAC3D,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,aAAa,CAAC,EAAE,CAAC;YACpC,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,eAAe,CAAC,EAAU,EAAA;QAC7B,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,CAAC;QAC5C,IAAI,KAAa,CAAC;QAClB,IAAK,KAAK,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,CAAE,CAAC;YACrD,IAAI,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;gBAC7B,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;gBACjC,OAAO,IAAI,CAAC;YAChB,CAAC;QACL,CAAC;QAED,QAAQ;QACR,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,cAAc,CAAC;QACtD,IAAK,KAAK,GAAG,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,CAAE,CAAC;YAC1D,IAAI,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;gBAClC,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;gBACtC,OAAO,IAAI,CAAC;YAChB,CAAC;QACL,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG,CACI,cAAc,GAAA;QACjB,MAAM,OAAO,GAAkB,EAAE,CAAC;QAElC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAChC,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAChC,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;;;;;;OAQG,CACI,yBAAyB,CAAC,SAAgC,EAAA;QAC7D,WAAW;QACX,IAAI,CAAC,IAAI,CAAC,qBAAqB,6JAAC,eAAY,CAAC,YAAY,CAAC,EAAE,CAAC;YACzD,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAE3C,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAE9B,IAAI,IAAI,GAAe,IAAI,CAAC,eAAe,6JAAC,eAAY,CAAC,YAAY,CAAC,CAAC;QAEvE,MAAM,IAAI,qKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;QAC5B,IAAI,KAAa,CAAC;QAClB,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,CAAE,CAAC;8KAC9C,UAAO,CAAC,mCAAmC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACrI,CAAC;QAED,IAAI,CAAC,eAAe,CAAC,2KAAY,CAAC,YAAY,EAAE,IAAI,EAAiB,IAAI,CAAC,eAAe,6JAAC,eAAY,CAAC,YAAY,CAAE,CAAC,WAAW,EAAE,CAAC,CAAC;QAErI,UAAU;QACV,IAAI,IAAI,CAAC,qBAAqB,CAAC,2KAAY,CAAC,UAAU,CAAC,EAAE,CAAC;YACtD,IAAI,GAAe,IAAI,CAAC,eAAe,6JAAC,eAAY,CAAC,UAAU,CAAC,CAAC;YACjE,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,CAAE,CAAC;kLAC9C,UAAO,CAAC,8BAA8B,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC,CACjG,SAAS,EAAE,CACX,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAC9B,CAAC;YACD,IAAI,CAAC,eAAe,CAAC,2KAAY,CAAC,UAAU,EAAE,IAAI,EAAiB,IAAI,CAAC,eAAe,6JAAC,eAAY,CAAC,UAAU,CAAE,CAAC,WAAW,EAAE,CAAC,CAAC;QACrI,CAAC;QAED,WAAW;QACX,IAAI,IAAI,CAAC,qBAAqB,6JAAC,eAAY,CAAC,WAAW,CAAC,EAAE,CAAC;YACvD,IAAI,GAAe,IAAI,CAAC,eAAe,6JAAC,eAAY,CAAC,WAAW,CAAC,CAAC;YAClE,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,CAAE,CAAC;kLAC9C,UAAO,CAAC,8BAA8B,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC,CACjG,SAAS,EAAE,CACX,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAC9B,CAAC;YACD,IAAI,CAAC,eAAe,6JAAC,eAAY,CAAC,WAAW,EAAE,IAAI,EAAiB,IAAI,CAAC,eAAe,CAAC,2KAAY,CAAC,WAAW,CAAE,CAAC,WAAW,EAAE,CAAC,CAAC;QACvI,CAAC;QAED,cAAc;QACd,IAAI,SAAS,CAAC,WAAW,EAAE,GAAG,CAAC,EAAE,CAAC;YAC9B,IAAI,CAAC,SAAS,EAAE,CAAC;QACrB,CAAC;QAED,oBAAoB;QACpB,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;;;OASG,CACI,gCAAgC,GAA0E;0CAAzE,iEAAuC,IAAI,gBAAE,iEAAuB,KAAK;QAC7G,IAAI,WAAW,EAAE,CAAC;YACd,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC9B,CAAC;QACD,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;QAC9D,IAAI,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,CAAC;QACnD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,QAAQ;IAER,cAAA,EAAgB,CAChB,IAAoB,UAAU,GAAA;QAC1B,OAAO,IAAI,CAAC,6BAA6B,CAAC,UAAU,IAAI,AAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,GAAI,IAAI,CAAC;IAClH,CAAC;IAED,cAAA,EAAgB,CACT,sBAAsB,GAAA;QACzB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,SAAS,CAAC,sBAAsB,EAAE,CAAC;QAC5C,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,cAAA,EAAgB,CACA,oBAAoB,GAAA;QAChC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,OAAO,IAAI,CAAC,SAAS,CAAC,oBAAoB,EAAE,CAAC;QACjD,CAAC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;;;;OAQG,CACa,KAAK,GAA2I;mBAA1I,iEAAe,EAAE,cAAE,iEAA+C,IAAI,EAAE,kBAA4B,wEAAE,iEAAgC,IAAI;QAC5J,IAAI,SAAS,IAAK,SAAkB,CAAC,oBAAoB,KAAK,SAAS,EAAE,CAAC;YACtE,MAAM,YAAY,GAAG,SAA6B,CAAC;YAEnD,mBAAmB,CAAC,MAAM,GAAG,IAAI,CAAC;YAClC,mBAAmB,CAAC,kBAAkB,GAAG,YAAY,CAAC,kBAAkB,CAAC;YACzE,mBAAmB,CAAC,oBAAoB,GAAG,YAAY,CAAC,oBAAoB,CAAC;YAC7E,mBAAmB,CAAC,kBAAkB,GAAG,YAAY,CAAC,kBAAkB,CAAC;YAEzE,OAAO,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,mBAAmB,CAAC,CAAC;QAChE,CAAC;QAED,OAAO,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,SAA2B,EAAE,IAAI,EAAE,kBAAkB,EAAE,oBAAoB,CAAC,CAAC;IACxH,CAAC;IAED;;;;OAIG,CACa,OAAO,CAAC,YAAsB,EAAoC;yCAAlC,0BAA0B,uCAAG,KAAK;QAC9E,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAE/B,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC9C,CAAC;QAED,MAAM,gBAAgB,GAAG,IAAI,CAAC,qBAAqB,CAAC;QAEpD,IAAI,gBAAgB,CAAC,uBAAuB,EAAE,CAAC;YAC3C,gBAAgB,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAC;QACrD,CAAC;QAED,IAAI,gBAAgB,CAAC,uBAAuB,EAAE,CAAC;YAC3C,gBAAgB,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAC;QACrD,CAAC;QAED,IAAI,gBAAgB,CAAC,yBAAyB,EAAE,CAAC;YAC7C,gBAAgB,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;QACvD,CAAC;QAED,IAAI,gBAAgB,CAAC,wBAAwB,EAAE,CAAC;YAC5C,gBAAgB,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;QACtD,CAAC;QAED,IAAI,gBAAgB,CAAC,wBAAwB,EAAE,CAAC;YAC5C,gBAAgB,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;QACtD,CAAC;QAED,UAAU;QACV,IAAI,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;YAC/B,IAAI,gBAAgB,CAAC,OAAO,EAAE,CAAC;gBAC3B,IAAK,MAAM,QAAQ,IAAI,gBAAgB,CAAC,OAAO,CAAE,CAAC;oBAC9C,MAAM,IAAI,GAAG,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;oBAChD,IAAI,IAAI,EAAE,CAAC;wBACP,IAAI,CAAC,qBAAqB,CAAC,OAAO,GAAG,IAAI,CAAC;wBAC1C,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC;oBACnD,CAAC;gBACL,CAAC;YACL,CAAC;YAED,IAAI,gBAAgB,CAAC,OAAO,IAAI,gBAAgB,CAAC,OAAO,CAAC,qBAAqB,CAAC,OAAO,EAAE,CAAC;gBACrF,gBAAgB,CAAC,OAAO,CAAC,qBAAqB,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC;YACtF,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,MAAM,CAAC;YACtC,KAAK,MAAM,YAAY,IAAI,MAAM,CAAE,CAAC;gBAChC,MAAM,IAAI,GAAG,YAAoB,CAAC;gBAClC,IAAI,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,qBAAqB,CAAC,OAAO,IAAI,IAAI,CAAC,qBAAqB,CAAC,OAAO,KAAK,IAAI,EAAE,CAAC;oBAClH,IAAI,CAAC,qBAAqB,CAAC,OAAO,GAAG,IAAI,CAAC;gBAC9C,CAAC;YACL,CAAC;QACL,CAAC;QAED,gBAAgB,CAAC,OAAO,GAAG,IAAI,CAAC;QAEhC,YAAY;QACZ,IAAI,CAAC,4BAA4B,EAAE,CAAC;QAEpC,iBAAiB;QACjB,IAAI,CAAC,gCAAgC,EAAE,CAAC;QAExC,IAAI,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,EAAE,CAAC;YACrD,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,CAAC,CAAC;QACpG,CAAC;QAED,KAAK,CAAC,OAAO,CAAC,YAAY,EAAE,0BAA0B,CAAC,CAAC;IAC5D,CAAC;IAED,cAAA,EAAgB,CACT,4BAA4B,GAAA;IAC/B,aAAa;IACjB,CAAC;IAED,cAAA,EAAgB,CACT,gCAAgC,GAAA;IACnC,aAAa;IACjB,CAAC;IAED,cAAA,EAAgB,CACT,oCAAoC,GAAA;IACvC,aAAa;IACjB,CAAC;IAED;;;;;;;;;;;;;OAaG,CACI,oBAAoB,CACvB,GAAW,EACX,SAAiB,EACjB,SAAiB,EACjB,SAAgC,EAChC,QAAkB,EAClB,OAAiB,EAEoC;0BADrD,WAAW,sDAAG,KAAK,EACnB,OAAqD;QAErD,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,MAAM,MAAM,GAAG,CAAC,GAAmC,EAAE,EAAE;YACnD,0BAA0B;YAC1B,MAAM,cAAc,GAAG,GAAG,CAAC,KAAK,CAAC;YACjC,MAAM,eAAe,GAAG,GAAG,CAAC,MAAM,CAAC;YACnC,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,YAAY,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC;YAC9E,MAAM,OAAO,GAA6B,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAElE,OAAO,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAE7B,kCAAkC;YAClC,8GAA8G;YAC9G,MAAM,MAAM,GAAqB,OAAO,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,cAAc,EAAE,eAAe,CAAC,CAAC,IAAK,CAAC;YAEnG,IAAI,CAAC,8BAA8B,CAAC,MAAM,EAAE,cAAc,EAAE,eAAe,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;YACnI,kCAAkC;YAClC,IAAI,SAAS,EAAE,CAAC;gBACZ,SAAS,CAAC,IAAI,CAAC,CAAC;YACpB,CAAC;QACL,CAAC,CAAC;gKAEF,QAAK,CAAC,SAAS,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,AAAE,CAAC,EAAE,KAAK,CAAC,eAAe,CAAC,CAAC;QAClF,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;;;;;;;OAaG,CACI,8BAA8B,CACjC,MAAkB,EAClB,cAAsB,EACtB,eAAuB,EACvB,SAAiB,EACjB,SAAiB,EACjB,QAAkB,EAClB,OAAiB,EACE;0BAAnB,WAAW,sDAAG,KAAK;QAEnB,IAAI,CAAC,IAAI,CAAC,qBAAqB,6JAAC,eAAY,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,qBAAqB,6JAAC,eAAY,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,qBAAqB,6JAAC,eAAY,CAAC,MAAM,CAAC,EAAE,CAAC;YACrK,kKAAM,CAAC,IAAI,CAAC,kGAAkG,CAAC,CAAC;YAChH,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,SAAS,GAAe,IAAI,CAAC,eAAe,6JAAC,eAAY,CAAC,YAAY,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAC1F,MAAM,OAAO,GAAe,IAAI,CAAC,eAAe,6JAAC,eAAY,CAAC,UAAU,CAAC,CAAC;QAC1E,MAAM,GAAG,GAAa,IAAI,CAAC,eAAe,6JAAC,eAAY,CAAC,MAAM,CAAC,CAAC;QAChE,IAAI,QAAQ,qKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;QAC9B,MAAM,MAAM,qKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;QAC9B,MAAM,EAAE,qKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;QAE1B,QAAQ,GAAG,QAAQ,sKAAI,UAAO,CAAC,IAAI,EAAE,CAAC;QACtC,OAAO,GAAG,OAAO,IAAI,sKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEvC,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,SAAS,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,CAAE,CAAC;8KACvD,UAAO,CAAC,cAAc,CAAC,SAAS,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;8KACnD,UAAO,CAAC,cAAc,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;8KAC/C,UAAO,CAAC,cAAc,CAAC,GAAG,EAAE,AAAC,KAAK,GAAG,CAAC,CAAC,EAAG,CAAC,EAAE,EAAE,CAAC,CAAC;YAEjD,iBAAiB;YACjB,MAAM,CAAC,GAAG,AAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,AAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAG,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,EAAG,cAAc,GAAG,CAAC,CAAC;YACtG,MAAM,CAAC,GAAG,AAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,AAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAG,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC,EAAG,eAAe,GAAG,CAAC,CAAC;YAExG,MAAM,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC;YACzC,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;YAC9B,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;YAClC,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;YAElC,MAAM,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;YAE/C,MAAM,CAAC,SAAS,EAAE,CAAC;YACnB,MAAM,CAAC,YAAY,CAAC,SAAS,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC,GAAG,QAAQ,CAAC,CAAC;YACpE,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAEhC,QAAQ,CAAC,OAAO,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;QACvC,CAAC;+KAED,aAAU,CAAC,cAAc,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,EAAE,OAAO,CAAC,CAAC;QAEjE,IAAI,WAAW,EAAE,CAAC;YACd,IAAI,CAAC,eAAe,6JAAC,eAAY,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;YAC3D,IAAI,CAAC,eAAe,CAAC,2KAAY,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YACvD,IAAI,CAAC,eAAe,6JAAC,eAAY,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QACnD,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,kBAAkB,4JAAC,gBAAY,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;YAC9D,IAAI,CAAC,kBAAkB,6JAAC,eAAY,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAC9D,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,oBAAoB,CAAC,OAAqB,EAAE,SAAqB,EAAA;QACrE,MAAM,OAAO,GAAG,IAAI,YAAY,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACrD,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,sCAAsC;QACtC,MAAM,oBAAoB,GACtB,IAAI,CAAC,eAAe,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC,IAAA,CAAA,IAAS,CAAC,wCAAwC,CAAC,CAAC,CAAC,SAAS,CAAC,iCAAiC,CAAC,CAAC;QAEnK,uBAAuB;QACvB,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,OAAO,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,CAAE,CAAC;YACrD,MAAM,EAAE,qKAAG,UAAO,CAAC,SAAS,CAAC,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;YAC5D,MAAM,EAAE,qKAAG,UAAO,CAAC,SAAS,CAAC,SAAS,EAAE,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAChE,MAAM,EAAE,qKAAG,UAAO,CAAC,SAAS,CAAC,SAAS,EAAE,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAEhE,MAAM,IAAI,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAC7B,MAAM,IAAI,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAE7B,MAAM,MAAM,qKAAG,UAAO,CAAC,SAAS,CAAC,4KAAO,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;YAC5D,IAAI,oBAAoB,EAAE,CAAC;gBACvB,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5B,CAAC;YAED,sCAAsC;YACtC,IAAK,IAAI,UAAU,GAAG,CAAC,EAAE,UAAU,GAAG,CAAC,EAAE,UAAU,EAAE,CAAE,CAAC;gBACpD,OAAO,CAAC,YAAY,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;gBACnC,OAAO,CAAC,YAAY,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;gBACnC,OAAO,CAAC,YAAY,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;YACvC,CAAC;QACL,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAEO,uBAAuB,GAAgC;6BAA/B,iEAA0B,KAAK;QAC3D,MAAM,KAAK,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE;;mBAAC,+BAAK,CAAC,eAAe,CAAC,IAAI,CAAC,0DAA1B,sBAA4B,cAAc,EAAE,CAAC,CAAC;;QAC1G,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAG,CAAC;QACnC,MAAM,IAAI,GAAmC,CAAA,CAAE,CAAC;QAEhD,MAAM,gBAAgB,GAAG,CAAC,IAAgB,EAAE,IAAY,EAAgB,EAAE;YACtE,MAAM,OAAO,GAAG,IAAI,YAAY,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC;YACxD,IAAI,KAAK,GAAG,CAAC,CAAC;YACd,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBAClD,IAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,EAAE,MAAM,EAAE,CAAE,CAAC;oBAC3C,OAAO,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,MAAM,CAAC,CAAC;gBAC5D,CAAC;YACL,CAAC;YACD,OAAO,OAAO,CAAC;QACnB,CAAC,CAAC;QAEF,0BAA0B;QAC1B,MAAM,gBAAgB,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAEhD,0BAA0B;QAC1B,MAAM,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAEvE,oBAAoB;QACpB,KAAK,MAAM,IAAI,IAAI,KAAK,CAAE,CAAC;YACvB,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAE,CAAC;QAC7C,CAAC;QAED,qBAAqB;QACrB,KAAK,MAAM,IAAI,IAAI,KAAK,CAAE,CAAC;YACvB,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAE,CAAC;YACjD,MAAM,IAAI,GAAG,YAAY,CAAC,OAAO,EAAE,CAAC;YAEpC,IAAI,cAAc,IAAI,IAAI,iKAAK,eAAY,CAAC,UAAU,EAAE,CAAC;gBACrD,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,IAAI,6JAAC,eAAY,CAAC,YAAY,CAAC,CAAC,CAAC;gBACpF,IAAI,CAAC,eAAe,6JAAC,eAAY,CAAC,UAAU,EAAE,OAAO,EAAE,YAAY,CAAC,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC;YAC7F,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,YAAY,CAAC,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC;YACrG,CAAC;QACL,CAAC;QAED,uBAAuB;QACvB,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,IAAK,IAAI,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE,WAAW,EAAE,CAAE,CAAC;gBACxF,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;gBAE9D,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAG,CAAC;gBACzC,MAAM,CAAC,YAAY,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC;gBAEpD,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC;gBACpC,IAAI,OAAO,EAAE,CAAC;oBACV,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;gBACrH,CAAC;gBAED,MAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;gBACtC,IAAI,QAAQ,EAAE,CAAC;oBACX,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC;gBACtD,CAAC;gBAED,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;gBAC5B,IAAI,GAAG,EAAE,CAAC;oBACN,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;gBAC5C,CAAC;gBAED,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;gBAClC,IAAI,MAAM,EAAE,CAAC;oBACT,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;gBAClD,CAAC;YACL,CAAC;YACD,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,CAAC;QAC1C,CAAC;QAED,iBAAiB;QACjB,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAClD,OAAO,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;QAC3B,CAAC;QACD,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAEzB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAEvB,mBAAmB;QACnB,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,KAAK,MAAM,WAAW,IAAI,iBAAiB,CAAE,CAAC;YAC1C,MAAM,YAAY,GAAG,WAAW,CAAC,eAAe,EAAE,CAAC;YACnD,MAAM,OAAO,+JAAG,UAAO,CAAC,SAAS,CAAC,WAAW,CAAC,aAAa,EAAE,WAAW,CAAC,UAAU,EAAE,WAAW,CAAC,UAAU,EAAE,WAAW,CAAC,UAAU,EAAE,WAAW,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;YACnK,OAAO,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;QAEvC,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG,CACI,uBAAuB,GAAA;QAC1B,OAAO,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;IAC9C,CAAC;IAED;;;;;OAKG,CACI,sBAAsB,GAAA;QACzB,OAAO,IAAI,CAAC,uBAAuB,EAAE,CAAC;IAC1C,CAAC;IAED;;;;;OAKG,CACI,SAAS,GAA6B;0BAA5B,iEAAuB,KAAK;QACzC,MAAM,UAAU,0KAAG,aAAU,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QACpD,IAAI,CAAS,CAAC;QACd,IAAI,WAAW,IAAI,IAAI,CAAC,qBAAqB,6JAAC,eAAY,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;YAC3F,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;gBAC7C,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YAChC,CAAC;YACD,IAAI,CAAC,eAAe,6JAAC,eAAY,CAAC,UAAU,EAAE,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,uBAAuB,6JAAC,eAAY,CAAC,UAAU,CAAC,CAAC,CAAC;QAC7H,CAAC;QAED,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;YACrB,IAAI,IAAI,CAAC;YACT,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,CAAE,CAAC;gBAChD,mBAAmB;gBACnB,IAAI,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBACjC,UAAU,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBACtD,UAAU,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;YACrC,CAAC;YACD,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,uBAAuB,6JAAC,eAAY,CAAC,YAAY,CAAC,EAAE,IAAI,CAAC,CAAC;QAC7G,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG,CACI,gBAAgB,GAA0B;4BAAzB,iEAAwB,CAAC;QAC7C,MAAM,UAAU,0KAAG,aAAU,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QACpD,MAAM,cAAc,GAAG,UAAU,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC;QACpJ,MAAM,SAAS,GAAG,UAAU,CAAC,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC;QACvJ,MAAM,GAAG,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC;QACzH,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC;QAE7I,IAAI,CAAC,cAAc,IAAI,CAAC,SAAS,EAAE,CAAC;qKAChC,SAAM,CAAC,IAAI,CAAC,+FAA+F,CAAC,CAAC;QACjH,CAAC,MAAM,CAAC;YACJ,UAAU,CAAC,OAAO,GAAG,cAAc,CAAC;YACpC,UAAU,CAAC,SAAS,GAAG,SAAS,CAAC;YACjC,IAAI,GAAG,EAAE,CAAC;gBACN,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC;YACzB,CAAC;YACD,IAAI,OAAO,EAAE,CAAC;gBACV,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;YACjC,CAAC;YAED,MAAM,QAAQ,GAAW,aAAa,GAAG,CAAC,CAAC,CAAC,6DAA6D;YACzG,MAAM,WAAW,GAAyB,EAAE,CAAC;YAC7C,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;gBACpC,WAAW,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;YACxB,CAAC;YACD,IAAI,CAAS,CAAC,CAAC,mCAAmC;YAClD,IAAI,CAAS,CAAC,CAAC,uCAAuC;YACtD,MAAM,aAAa,GAAY,sKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACpD,MAAM,WAAW,GAAY,sKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAClD,MAAM,OAAO,GAAY,sKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC3C,MAAM,OAAO,GAAa,EAAE,CAAC;YAC7B,MAAM,WAAW,GAAa,EAAE,CAAC;YACjC,MAAM,IAAI,GAAgC,EAAE,CAAC;YAC7C,IAAI,GAAW,CAAC;YAChB,IAAI,WAAW,GAAW,SAAS,CAAC,MAAM,CAAC;YAC3C,IAAI,KAAa,CAAC;YAClB,IAAI,GAAG,EAAE,CAAC;gBACN,KAAK,GAAG,GAAG,CAAC,MAAM,CAAC;YACvB,CAAC;YACD,IAAI,UAAkB,CAAC;YACvB,IAAI,OAAO,EAAE,CAAC;gBACV,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC;YAChC,CAAC;YAED,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,CAAE,CAAC;gBAChD,WAAW,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;gBACnC,WAAW,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBACvC,WAAW,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBACvC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;oBACzB,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;oBACnB,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;oBAC7B,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE,CAAC;wBACjD,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;wBACb,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;oBACjB,CAAC,MAAM,CAAC;wBACJ,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE,CAAC;4BACxB,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;wBACjB,CAAC;wBACD,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE,CAAC;4BACxB,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;wBACjB,CAAC;oBACL,CAAC;oBACD,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE,CAAC;wBACvD,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;wBAChB,aAAa,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC;wBACnE,aAAa,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC;wBAC3E,aAAa,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC;wBAC3E,IAAI,OAAO,EAAE,CAAC;4BACV,WAAW,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC;4BAC7D,WAAW,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC;4BACrE,WAAW,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC;wBACzE,CAAC;wBACD,IAAI,GAAG,EAAE,CAAC;4BACN,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC;4BACjD,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC;wBAC7D,CAAC;wBACD,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;wBACnB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,CAAE,CAAC;4BAChC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;4BACtC,SAAS,CAAC,WAAW,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;4BAClE,SAAS,CAAC,WAAW,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;4BACtE,SAAS,CAAC,WAAW,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;4BACtE,IAAI,OAAO,EAAE,CAAC;gCACV,OAAO,CAAC,UAAW,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC;gCAC5D,OAAO,CAAC,UAAW,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC;gCAChE,OAAO,CAAC,UAAW,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC;4BACpE,CAAC;4BACD,IAAI,GAAG,EAAE,CAAC;gCACN,GAAG,CAAC,KAAM,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;gCAC3C,GAAG,CAAC,KAAM,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;4BACnD,CAAC;wBACL,CAAC;wBACD,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;wBACnB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;wBAChB,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;wBACxB,IAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,CAAE,CAAC;4BACjC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;wBAChD,CAAC;oBACL,CAAC;gBACL,CAAC;gBACD,+DAA+D;gBAC/D,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;gBACtC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtE,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,CAAE,CAAC;oBAChC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtE,aAAa,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;oBAC5F,aAAa,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;oBACpG,aAAa,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;oBACpG,IAAI,OAAO,EAAE,CAAC;wBACV,WAAW,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;wBACtF,WAAW,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;wBAC9F,WAAW,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;oBAClG,CAAC;oBACD,IAAI,GAAG,EAAE,CAAC;wBACN,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;wBAC1E,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;oBACtF,CAAC;oBACD,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;wBACzB,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;wBACzC,SAAS,CAAC,WAAW,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;wBAClF,SAAS,CAAC,WAAW,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;wBACtF,SAAS,CAAC,WAAW,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;wBACtF,IAAI,OAAO,EAAE,CAAC;4BACV,OAAO,CAAC,UAAW,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC;4BAC5E,OAAO,CAAC,UAAW,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC;4BAChF,OAAO,CAAC,UAAW,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC;wBACpF,CAAC;wBACD,IAAI,GAAG,EAAE,CAAC;4BACN,GAAG,CAAC,KAAM,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;4BAC3D,GAAG,CAAC,KAAM,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;wBACnE,CAAC;oBACL,CAAC;gBACL,CAAC;gBACD,WAAW,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBAE3E,iBAAiB;gBACjB,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtE,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,CAAE,CAAC;oBAChC,IAAI,CAAS,CAAC;oBACd,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;wBACrB,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;wBAClF,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;oBACtF,CAAC;oBACD,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACtF,CAAC;YACL,CAAC;YAED,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;YAC7B,UAAU,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,uBAAuB,6JAAC,eAAY,CAAC,YAAY,CAAC,CAAC,CAAC;QAC1F,CAAC;IACL,CAAC;IAED;;;;OAIG,CACI,mBAAmB,GAAA;QACtB,MAAM,UAAU,0KAAG,aAAU,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QACpD,MAAM,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC;QAClC,MAAM,cAAc,GAAG,UAAU,CAAC,OAAO,CAAC;QAC1C,MAAM,gBAAgB,GAAG,UAAU,CAAC,SAAS,CAAC;QAC9C,MAAM,aAAa,GAAG,UAAU,CAAC,MAAM,CAAC;QACxC,MAAM,oBAAoB,GAAG,UAAU,CAAC,eAAe,CAAC;QACxD,MAAM,oBAAoB,GAAG,UAAU,CAAC,eAAe,CAAC;QACxD,MAAM,yBAAyB,GAAG,UAAU,CAAC,oBAAoB,CAAC;QAClE,MAAM,yBAAyB,GAAG,UAAU,CAAC,oBAAoB,CAAC;QAElE,IAAI,cAAc,KAAK,KAAK,CAAC,IAAI,gBAAgB,KAAK,KAAK,CAAC,IAAI,cAAc,KAAK,IAAI,IAAI,gBAAgB,KAAK,IAAI,EAAE,CAAC;YACnH,kKAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;QACrD,CAAC,MAAM,CAAC;YACJ,MAAM,SAAS,GAAkB,EAAE,CAAC;YACpC,MAAM,OAAO,GAAkB,EAAE,CAAC;YAClC,MAAM,GAAG,GAAkB,EAAE,CAAC;YAC9B,MAAM,MAAM,GAAkB,EAAE,CAAC;YACjC,MAAM,aAAa,GAAkB,EAAE,CAAC;YACxC,MAAM,aAAa,GAAkB,EAAE,CAAC;YACxC,MAAM,kBAAkB,GAAkB,EAAE,CAAC;YAC7C,MAAM,kBAAkB,GAAkB,EAAE,CAAC;YAC7C,IAAI,OAAO,GAAkB,EAAE,CAAC,CAAC,wDAAwD;YAEzF,IAAI,QAAQ,GAAW,CAAC,CAAC,CAAC,wCAAwC;YAClE,MAAM,eAAe,GAA8B,CAAA,CAAE,CAAC,CAAC,0BAA0B;YACjF,IAAI,GAAW,CAAC,CAAC,wCAAwC;YACzD,IAAI,KAAoB,CAAC;YAEzB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,CAAE,CAAC;gBAChD,KAAK,GAAG;oBAAC,cAAc,CAAC,CAAC,CAAC;oBAAE,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC;oBAAE,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC;iBAAC,CAAC,CAAC,sBAAsB;gBACjG,OAAO,GAAG,EAAE,CAAC;gBACb,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;oBACzB,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;oBAChB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;wBACzB,qBAAqB;wBACrB,IAAI,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,UAAU,EAAE,CAAC;4BAC5D,gBAAgB,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;wBAC3C,CAAC;wBACD,OAAO,CAAC,CAAC,CAAC,IAAI,gBAAgB,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;oBAC3D,CAAC;gBACL,CAAC;gBACD,oDAAoD;gBACpD,oEAAoE;gBACpE,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACtF,oEAAoE;oBACpE,6DAA6D;oBAC7D,mEAAmE;oBACnE,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;wBACzB,GAAG,GAAG,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;wBAClC,IAAI,GAAG,KAAK,SAAS,EAAE,CAAC;4BACpB,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC;4BACvC,GAAG,GAAG,QAAQ,EAAE,CAAC;4BACjB,+DAA+D;4BAC/D,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;gCACzB,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;4BACvD,CAAC;4BACD,IAAI,aAAa,KAAK,IAAI,IAAI,aAAa,KAAK,KAAK,CAAC,EAAE,CAAC;gCACrD,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;oCACzB,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gCACjD,CAAC;4BACL,CAAC;4BACD,IAAI,UAAU,KAAK,IAAI,IAAI,UAAU,KAAK,KAAK,CAAC,EAAE,CAAC;gCAC/C,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;oCACzB,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gCAC3C,CAAC;4BACL,CAAC;4BACD,IAAI,oBAAoB,KAAK,IAAI,IAAI,oBAAoB,KAAK,KAAK,CAAC,EAAE,CAAC;gCACnE,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;oCACzB,aAAa,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gCAC/D,CAAC;4BACL,CAAC;4BACD,IAAI,oBAAoB,KAAK,IAAI,IAAI,oBAAoB,KAAK,KAAK,CAAC,EAAE,CAAC;gCACnE,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;oCACzB,aAAa,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gCAC/D,CAAC;4BACL,CAAC;4BACD,IAAI,yBAAyB,KAAK,IAAI,IAAI,yBAAyB,KAAK,KAAK,CAAC,EAAE,CAAC;gCAC7E,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;oCACzB,kBAAkB,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gCACzE,CAAC;4BACL,CAAC;4BACD,IAAI,yBAAyB,KAAK,IAAI,IAAI,yBAAyB,KAAK,KAAK,CAAC,EAAE,CAAC;gCAC7E,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;oCACzB,kBAAkB,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gCACzE,CAAC;4BACL,CAAC;wBACL,CAAC;wBACD,yCAAyC;wBACzC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBACtB,CAAC;gBACL,CAAC;YACL,CAAC;YAED,MAAM,OAAO,GAAkB,EAAE,CAAC;mLAClC,aAAU,CAAC,cAAc,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;YAEvD,0CAA0C;YAC1C,UAAU,CAAC,SAAS,GAAG,SAAS,CAAC;YACjC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;YAC7B,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;YAC7B,IAAI,UAAU,KAAK,IAAI,IAAI,UAAU,KAAK,KAAK,CAAC,EAAE,CAAC;gBAC/C,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC;YACzB,CAAC;YACD,IAAI,aAAa,KAAK,IAAI,IAAI,aAAa,KAAK,KAAK,CAAC,EAAE,CAAC;gBACrD,UAAU,CAAC,MAAM,GAAG,MAAM,CAAC;YAC/B,CAAC;YACD,IAAI,oBAAoB,KAAK,IAAI,IAAI,oBAAoB,KAAK,KAAK,CAAC,EAAE,CAAC;gBACnE,UAAU,CAAC,eAAe,GAAG,aAAa,CAAC;YAC/C,CAAC;YACD,IAAI,oBAAoB,KAAK,IAAI,IAAI,oBAAoB,KAAK,KAAK,CAAC,EAAE,CAAC;gBACnE,UAAU,CAAC,eAAe,GAAG,aAAa,CAAC;YAC/C,CAAC;YACD,IAAI,yBAAyB,KAAK,IAAI,IAAI,yBAAyB,KAAK,KAAK,CAAC,EAAE,CAAC;gBAC7E,UAAU,CAAC,oBAAoB,GAAG,kBAAkB,CAAC;YACzD,CAAC;YACD,IAAI,oBAAoB,KAAK,IAAI,IAAI,oBAAoB,KAAK,KAAK,CAAC,EAAE,CAAC;gBACnE,UAAU,CAAC,oBAAoB,GAAG,kBAAkB,CAAC;YACzD,CAAC;YAED,UAAU,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,uBAAuB,6JAAC,eAAY,CAAC,YAAY,CAAC,CAAC,CAAC;QAC1F,CAAC;IACL,CAAC;IAED,YAAY;IACZ;;OAEG,CACH,mGAAmG;IAC5F,MAAM,CAAC,qBAAqB,CAAC,IAAY,EAAE,IAAU,EAAA;QACxD,qKAAM,cAAA,AAAW,EAAC,eAAe,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG,CACH,6DAA6D;IACtD,MAAM,CAAC,sBAAsB,CAAC,KAAY,EAAE,YAAmC,EAAE,UAAe,EAAA;QACnG,qKAAM,cAAW,AAAX,EAAY,iBAAiB,CAAC,CAAC;IACzC,CAAC;IAED;;;;;OAKG,CACI,cAAc,CAAC,IAAY,EAAA;QAC9B,MAAM,QAAQ,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAExD,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAE9B,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED;;;;OAIG,CACI,oBAAoB,GAAA;QACvB,IAAK,IAAI,aAAa,GAAG,CAAC,EAAE,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,aAAa,EAAE,CAAE,CAAC;YACjF,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YAC/C,QAAQ,CAAC,cAAc,EAAE,CAAC;QAC9B,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG,CACI,eAAe,CAAC,eAAuC,EAAA;QAC1D,MAAM,OAAO,GAAiB,IAAI,CAAC,UAAU,EAAE,CAAC;QAChD,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,6JAAC,eAAY,CAAC,YAAY,CAAC,CAAC;QAElE,IAAI,CAAC,SAAS,IAAI,CAAC,OAAO,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,eAAe,GAAc,EAAE,CAAC;QACtC,IAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,SAAS,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC,CAAE,CAAC;YACtD,eAAe,CAAC,IAAI,mKAAC,UAAO,CAAC,SAAS,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC,CAAC;QAC5D,CAAC;QACD,MAAM,KAAK,GAAa,EAAE,CAAC;gKAE3B,YAAS,CAAC,gBAAgB,CACtB,eAAe,CAAC,MAAM,EACtB,EAAE,EACF,CAAC,SAAS,EAAE,EAAE;YACV,MAAM,OAAO,GAAG,eAAe,CAAC,MAAM,GAAG,CAAC,GAAG,SAAS,CAAC;YACvD,MAAM,cAAc,GAAG,eAAe,CAAC,OAAO,CAAC,CAAC;YAChD,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,EAAE,CAAC,CAAE,CAAC;gBAC/B,MAAM,eAAe,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;gBAC3C,IAAI,cAAc,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,CAAC;oBACzC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;oBACnB,MAAM;gBACV,CAAC;YACL,CAAC;QACL,CAAC,EACD,GAAG,EAAE;YACD,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAE,CAAC;gBACtC,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;YACjD,CAAC;YAED,2BAA2B;YAC3B,MAAM,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAClD,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YACzB,IAAI,CAAC,SAAS,GAAG,iBAAiB,CAAC;YACnC,IAAI,eAAe,EAAE,CAAC;gBAClB,eAAe,CAAC,IAAI,CAAC,CAAC;YAC1B,CAAC;QACL,CAAC,CACJ,CAAC;QACF,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACa,SAAS,GAA8B;kCAA7B,iEAA2B,CAAA,CAAE;QACnD,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACrC,mBAAmB,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;QACjC,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC7C,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAE/C,2JAAI,OAAI,2JAAI,OAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YAC7B,mBAAmB,CAAC,IAAI,0JAAG,OAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAClD,CAAC;QAED,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;QAEvD,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,mBAAmB,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;QAC/E,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YACvB,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;QAC3D,CAAC;QAED,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QACrD,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAChC,mBAAmB,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,OAAO,EAAE,CAAC;QACtE,CAAC,MAAM,CAAC;YACJ,mBAAmB,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,OAAO,EAAE,CAAC;QACtE,CAAC;QAED,mBAAmB,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACtD,mBAAmB,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAC/C,mBAAmB,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAC7D,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC;QAE/C,mBAAmB,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;QAEzD,mBAAmB,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QACvD,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACjD,mBAAmB,CAAC,wBAAwB,GAAG,IAAI,CAAC,wBAAwB,CAAC;QAE7E,mBAAmB,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;QAC3D,mBAAmB,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QACzD,mBAAmB,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;QACrE,mBAAmB,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC;QACvE,mBAAmB,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAC/C,mBAAmB,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;QAE3D,SAAS;QACT,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,CAAC;QACxD,CAAC;QAED,WAAW;QACX,mBAAmB,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACnD,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;QAChC,IAAI,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YAC7B,mBAAmB,CAAC,gBAAgB,GAAG,QAAQ,CAAC,QAAQ,CAAC;YACzD,mBAAmB,CAAC,UAAU,GAAG,QAAQ,CAAC,EAAE,CAAC;YAE7C,YAAY;YACZ,mBAAmB,CAAC,SAAS,GAAG,EAAE,CAAC;YACnC,IAAK,IAAI,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAE,CAAC;gBAClE,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;gBAEzC,mBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC;oBAC/B,aAAa,EAAE,OAAO,CAAC,aAAa;oBACpC,aAAa,EAAE,OAAO,CAAC,aAAa;oBACpC,aAAa,EAAE,OAAO,CAAC,aAAa;oBACpC,UAAU,EAAE,OAAO,CAAC,UAAU;oBAC9B,UAAU,EAAE,OAAO,CAAC,UAAU;iBACjC,CAAC,CAAC;YACP,CAAC;QACL,CAAC;QAED,WAAW;QACX,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAC;gBAChC,mBAAmB,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBAC9D,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,cAAc;YACrE,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,mBAAmB,CAAC,gBAAgB,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC;YAC5E,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,cAAc;QACnF,CAAC;QAED,gBAAgB;QAChB,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,mBAAmB,CAAC,oBAAoB,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC;QAChF,CAAC;QAED,WAAW;QACX,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAClD,mBAAmB,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QACrE,CAAC;QAED,UAAU;QACV,6DAA6D;QAC7D,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,aAAa,0JAAC,0BAAuB,CAAC,kBAAkB,CAAC,EAAE,CAAC;YAC5E,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC3C,IAAI,QAAQ,EAAE,CAAC;gBACX,mBAAmB,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;gBAC5D,mBAAmB,CAAC,eAAe,GAAG,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;gBACpE,mBAAmB,CAAC,kBAAkB,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;gBACnE,mBAAmB,CAAC,eAAe,GAAG,QAAQ,CAAC,IAAI,CAAC;YACxD,CAAC;QACL,CAAC;QAED,WAAW;QACX,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QACjD,CAAC;QAED,YAAY;QACZ,mBAAmB,CAAC,SAAS,GAAG,EAAE,CAAC;QACnC,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACzD,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YACvC,IAAI,QAAQ,CAAC,cAAc,EAAE,CAAC;gBAC1B,SAAS;YACb,CAAC;YAED,MAAM,qBAAqB,GAAQ;gBAC/B,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,EAAE,EAAE,QAAQ,CAAC,EAAE;gBACf,SAAS,EAAE,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC;gBACpC,SAAS,EAAE,QAAQ,CAAC,SAAS;gBAC7B,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,eAAe,EAAE,QAAQ,CAAC,eAAe;gBACzC,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,OAAO,EAAE;gBACrC,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE;aACtC,CAAC;YAEF,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;gBAClB,QAAQ,CAAC,MAAM,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,CAAC;YAC9D,CAAC;YAED,IAAI,QAAQ,CAAC,kBAAkB,EAAE,CAAC;gBAC9B,qBAAqB,CAAC,kBAAkB,GAAG,QAAQ,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;YACrF,CAAC,MAAM,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;gBAC3B,qBAAqB,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;YACjE,CAAC;YAED,UAAU;YACV,6DAA6D;YAC7D,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,aAAa,0JAAC,0BAAuB,CAAC,kBAAkB,CAAC,EAAE,CAAC;gBAC5E,MAAM,QAAQ,GAAG,QAAQ,CAAC,kBAAkB,EAAE,CAAC;gBAC/C,IAAI,QAAQ,EAAE,CAAC;oBACX,qBAAqB,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;oBAC9D,qBAAqB,CAAC,eAAe,GAAG,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;oBACtE,qBAAqB,CAAC,kBAAkB,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;oBACrE,qBAAqB,CAAC,eAAe,GAAG,QAAQ,CAAC,IAAI,CAAC;gBAC1D,CAAC;YACL,CAAC;YAED,WAAW;YACX,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;gBACpB,qBAAqB,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC;YACvD,CAAC;YAED,iBAAiB;YACjB,IAAI,QAAQ,CAAC,aAAa,EAAE,CAAC;gBACzB,qBAAqB,CAAC,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACpF,CAAC;YAED,mBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAE1D,aAAa;0LACb,sBAAmB,CAAC,0BAA0B,CAAC,QAAQ,EAAE,qBAAqB,CAAC,CAAC;YAChF,qBAAqB,CAAC,MAAM,GAAG,QAAQ,CAAC,wBAAwB,EAAE,CAAC;QACvE,CAAC;QAED,iBAAiB;QACjB,IAAI,IAAI,CAAC,wBAAwB,CAAC,cAAc,IAAI,IAAI,CAAC,wBAAwB,CAAC,UAAU,EAAE,CAAC;YAC3F,mBAAmB,CAAC,aAAa,GAAG;gBAChC,cAAc,EAAE,IAAI,CAAC,wBAAwB,CAAC,cAAc;gBAC5D,UAAU,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,UAAU,CAAC;gBAChE,gBAAgB,EAAE,IAAI,CAAC,wBAAwB,CAAC,gBAAgB;gBAChE,aAAa,EAAE,IAAI,CAAC,yBAAyB;aAChD,CAAC;YAEF,IAAI,IAAI,CAAC,+BAA+B,EAAE,CAAC;gBACvC,MAAM,gBAAgB,GAAQ;oBAC1B,IAAI,EAAE,CAAA,CAAE;oBACR,KAAK,EAAE,CAAA,CAAE;oBACT,OAAO,EAAE,CAAA,CAAE;iBACd,CAAC;gBAEF,IAAK,MAAM,IAAI,IAAI,IAAI,CAAC,+BAA+B,CAAC,IAAI,CAAE,CAAC;oBAC3D,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,+BAA+B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;oBAC1F,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,+BAA+B,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;oBAChF,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,+BAA+B,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBACxF,CAAC;gBAED,mBAAmB,CAAC,aAAa,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;YAC1E,CAAC;QACL,CAAC;QAED,aAAa;sLACb,sBAAmB,CAAC,0BAA0B,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAC;QAC1E,mBAAmB,CAAC,MAAM,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAE7D,aAAa;QACb,mBAAmB,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAE/C,QAAQ;QACR,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACjD,mBAAmB,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;QAEzD,UAAU;QACV,mBAAmB,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QACrD,mBAAmB,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;QAC/D,mBAAmB,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QAEvD,MAAM;QACN,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAE7C,iBAAiB;QACjB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1E,CAAC;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED,cAAA,EAAgB,CACA,mCAAmC,GAAA;QAC/C,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACjB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,+BAA+B,EAAE,CAAC;QAEvC,MAAM,kBAAkB,GAAG,IAAI,CAAC,6BAA6B,CAAC,mBAAmB,CAAC;QAClF,IAAI,kBAAkB,IAAI,kBAAkB,CAAC,WAAW,EAAE,CAAC;YACvD,IAAI,kBAAkB,CAAC,WAAW,KAAK,IAAI,CAAC,gBAAgB,EAAE,EAAE,CAAC;yKAC7D,SAAM,CAAC,KAAK,CAAC,kGAAkG,CAAC,CAAC;gBACjH,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;gBAC/B,OAAO;YACX,CAAC;YAED,IAAI,kBAAkB,CAAC,wBAAwB,EAAE,CAAC;gBAC9C,OAAO;YACX,CAAC;YAED,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,kBAAkB,CAAC,cAAc,EAAE,KAAK,EAAE,CAAE,CAAC;gBACrE,MAAM,WAAW,GAAG,kBAAkB,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBAE9D,MAAM,SAAS,GAAG,WAAW,CAAC,YAAY,EAAE,CAAC;gBAC7C,IAAI,CAAC,SAAS,EAAE,CAAC;6KACb,SAAM,CAAC,KAAK,CAAC,mDAAmD,CAAC,CAAC;oBAClE,OAAO;gBACX,CAAC;gBAED,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,2KAAY,CAAC,YAAY,GAAG,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;gBAEtF,MAAM,OAAO,GAAG,WAAW,CAAC,UAAU,EAAE,CAAC;gBACzC,IAAI,OAAO,EAAE,CAAC;oBACV,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,2KAAY,CAAC,UAAU,GAAG,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;gBACtF,CAAC;gBAED,MAAM,QAAQ,GAAG,WAAW,CAAC,WAAW,EAAE,CAAC;gBAC3C,IAAI,QAAQ,EAAE,CAAC;oBACX,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,2KAAY,CAAC,WAAW,GAAG,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;gBACxF,CAAC;gBAED,MAAM,GAAG,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC;gBACjC,IAAI,GAAG,EAAE,CAAC;oBACN,IAAI,CAAC,QAAQ,CAAC,eAAe,6JAAC,eAAY,CAAC,MAAM,GAAG,GAAG,GAAG,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;gBACpF,CAAC;gBAED,MAAM,IAAI,GAAG,WAAW,CAAC,OAAO,EAAE,CAAC;gBACnC,IAAI,IAAI,EAAE,CAAC;oBACP,IAAI,CAAC,QAAQ,CAAC,eAAe,6JAAC,eAAY,CAAC,OAAO,GAAG,GAAG,GAAG,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;gBACtF,CAAC;gBAED,MAAM,MAAM,GAAG,WAAW,CAAC,SAAS,EAAE,CAAC;gBACvC,IAAI,MAAM,EAAE,CAAC;oBACT,IAAI,CAAC,QAAQ,CAAC,eAAe,6JAAC,eAAY,CAAC,SAAS,GAAG,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;gBACpF,CAAC;YACL,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,IAAI,KAAK,GAAG,CAAC,CAAC;YAEd,YAAY;YACZ,MAAO,IAAI,CAAC,QAAQ,CAAC,qBAAqB,6JAAC,eAAY,CAAC,YAAY,GAAG,KAAK,CAAC,CAAE,CAAC;gBAC5E,IAAI,CAAC,QAAQ,CAAC,kBAAkB,6JAAC,eAAY,CAAC,YAAY,GAAG,KAAK,CAAC,CAAC;gBAEpE,IAAI,IAAI,CAAC,QAAQ,CAAC,qBAAqB,CAAC,2KAAY,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC;oBACvE,IAAI,CAAC,QAAQ,CAAC,kBAAkB,6JAAC,eAAY,CAAC,UAAU,GAAG,KAAK,CAAC,CAAC;gBACtE,CAAC;gBACD,IAAI,IAAI,CAAC,QAAQ,CAAC,qBAAqB,6JAAC,eAAY,CAAC,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC;oBACxE,IAAI,CAAC,QAAQ,CAAC,kBAAkB,6JAAC,eAAY,CAAC,WAAW,GAAG,KAAK,CAAC,CAAC;gBACvE,CAAC;gBACD,IAAI,IAAI,CAAC,QAAQ,CAAC,qBAAqB,6JAAC,eAAY,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC;oBACnE,IAAI,CAAC,QAAQ,CAAC,kBAAkB,6JAAC,eAAY,CAAC,MAAM,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC;gBACxE,CAAC;gBACD,IAAI,IAAI,CAAC,QAAQ,CAAC,qBAAqB,6JAAC,eAAY,CAAC,OAAO,GAAG,KAAK,CAAC,EAAE,CAAC;oBACpE,IAAI,CAAC,QAAQ,CAAC,kBAAkB,6JAAC,eAAY,CAAC,OAAO,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC;gBACzE,CAAC;gBACD,IAAI,IAAI,CAAC,QAAQ,CAAC,qBAAqB,6JAAC,eAAY,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC;oBACtE,IAAI,CAAC,QAAQ,CAAC,kBAAkB,6JAAC,eAAY,CAAC,SAAS,GAAG,KAAK,CAAC,CAAC;gBACrE,CAAC;gBACD,KAAK,EAAE,CAAC;YACZ,CAAC;QACL,CAAC;IACL,CAAC;IAmDD;;;;;;OAMG,CACI,MAAM,CAAU,KAAK,CAAC,UAAe,EAAE,KAAY,EAAE,OAAe,EAAA;QACvE,IAAI,IAAU,CAAC;QAEf,IAAI,UAAU,CAAC,IAAI,IAAI,UAAU,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;YACrD,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QACpD,CAAC,MAAM,IAAI,UAAU,CAAC,IAAI,IAAI,UAAU,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;YAC7D,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QACrD,CAAC,MAAM,IAAI,UAAU,CAAC,IAAI,IAAI,UAAU,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;YAC/D,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QACvD,CAAC,MAAM,IAAI,UAAU,CAAC,IAAI,IAAI,UAAU,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;YAClE,IAAI,GAAG,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QAC1D,CAAC,MAAM,IAAI,UAAU,CAAC,IAAI,IAAI,UAAU,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;YAC5D,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QACpD,CAAC,MAAM,CAAC;YACJ,IAAI,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAC5C,CAAC;QACD,IAAI,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,CAAC;QACxB,IAAI,CAAC,sBAAsB,GAAG,UAAU,CAAC,QAAQ,CAAC;QAElD,2JAAI,OAAI,EAAE,CAAC;mKACP,OAAI,CAAC,SAAS,CAAC,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,CAAC,QAAQ,qKAAG,UAAO,CAAC,SAAS,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAEvD,IAAI,UAAU,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACpC,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC;QACxC,CAAC;QAED,IAAI,UAAU,CAAC,kBAAkB,EAAE,CAAC;YAChC,IAAI,CAAC,kBAAkB,qKAAG,aAAU,CAAC,SAAS,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC;QAClF,CAAC,MAAM,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC;YAC7B,IAAI,CAAC,QAAQ,qKAAG,UAAO,CAAC,SAAS,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,CAAC,OAAO,GAAG,4KAAO,CAAC,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAErD,IAAI,UAAU,CAAC,WAAW,EAAE,CAAC;YACzB,IAAI,CAAC,qBAAqB,mKAAC,SAAM,CAAC,SAAS,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC;QACzE,CAAC,MAAM,IAAI,UAAU,CAAC,WAAW,EAAE,CAAC;YAChC,IAAI,CAAC,cAAc,mKAAC,SAAM,CAAC,SAAS,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QACtC,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,SAAS,CAAC;QACtC,IAAI,CAAC,gBAAgB,GAAG,UAAU,CAAC,gBAAgB,CAAC;QACpD,IAAI,CAAC,wBAAwB,GAAG,CAAC,CAAC,UAAU,CAAC,wBAAwB,CAAC;QAEtE,IAAI,CAAC,eAAe,GAAG,UAAU,CAAC,eAAe,CAAC;QAClD,IAAI,CAAC,wBAAwB,GAAG,UAAU,CAAC,wBAAwB,CAAC;QAEpE,IAAI,UAAU,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACpC,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC;QACxC,CAAC;QAED,IAAI,UAAU,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACpC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC,QAAQ,CAAC;QAC1C,CAAC;QAED,IAAI,UAAU,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;YACtC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC,UAAU,CAAC;QAC5C,CAAC;QAED,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,cAAc,CAAC;QAEhD,IAAI,UAAU,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YACzC,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,aAAa,CAAC;QAClD,CAAC;QAED,IAAI,UAAU,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;YACtC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC,UAAU,CAAC;QAC5C,CAAC;QAED,IAAI,CAAC,eAAe,GAAG,UAAU,CAAC,eAAe,CAAC;QAClD,IAAI,CAAC,qBAAqB,GAAG,CAAC,CAAC,UAAU,CAAC,qBAAqB,CAAC;QAEhE,IAAI,UAAU,CAAC,SAAS,EAAE,CAAC;YACvB,IAAI,CAAC,SAAS,qKAAG,UAAO,CAAC,SAAS,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,UAAU,CAAC,eAAe,EAAE,CAAC;YAC7B,IAAI,CAAC,eAAe,qKAAG,UAAO,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;QACzE,CAAC;QAED,kEAAkE;QAClE,IAAI,UAAU,CAAC,+BAA+B,IAAI,IAAI,EAAE,CAAC;YACrD,IAAI,CAAC,eAAe,GAAG,UAAU,CAAC,+BAA+B,CAAC;QACtE,CAAC;QAED,IAAI,UAAU,CAAC,eAAe,KAAK,SAAS,EAAE,CAAC;YAC3C,IAAI,CAAC,eAAe,GAAG,UAAU,CAAC,eAAe,CAAC;QACtD,CAAC;QAED,IAAI,UAAU,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;YACrC,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,SAAS,CAAC;QAC1C,CAAC;QAED,IAAI,CAAC,0BAA0B,GAAG,UAAU,CAAC,cAAc,CAAC;QAE5D,oBAAoB;QACpB,IAAI,UAAU,CAAC,iBAAiB,EAAE,CAAC;YAC/B,IAAI,CAAC,YAAY,CAAC,iBAAiB,GAAG,UAAU,CAAC,iBAAiB,CAAC;QACvE,CAAC;QAED,SAAS;QACT,IAAI,UAAU,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACpC,IAAI,CAAC,gBAAgB,GAAG,UAAU,CAAC,QAAQ,CAAC;QAChD,CAAC;QAED,IAAI,UAAU,CAAC,mBAAmB,KAAK,SAAS,EAAE,CAAC;YAC/C,IAAI,CAAC,2BAA2B,GAAG,UAAU,CAAC,mBAAmB,CAAC;QACtE,CAAC;QAED,UAAU;QACV,IAAI,UAAU,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;YACnC,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;QACnD,CAAC;QAED,UAAU;QACV,IAAI,UAAU,CAAC,YAAY,KAAK,SAAS,EAAE,CAAC;YACxC,IAAI,CAAC,YAAY,GAAG,UAAU,CAAC,YAAY,CAAC;QAChD,CAAC;QAED,IAAI,UAAU,CAAC,YAAY,KAAK,SAAS,EAAE,CAAC;YACxC,IAAI,CAAC,YAAY,mKAAG,UAAM,CAAC,SAAS,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,UAAU,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YACzC,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,aAAa,CAAC;QAClD,CAAC;QAED,WAAW;QACX,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,UAAU,CAAC,WAAW,CAAC;QAC5C,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,cAAc,CAAC;QAEhD,IAAI,UAAU,CAAC,gBAAgB,EAAE,CAAC;YAC9B,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC,wBAAwB,CAAC;YACzD,IAAI,CAAC,gBAAgB,GAAG,OAAO,GAAG,UAAU,CAAC,gBAAgB,CAAC;YAC9D,IAAI,CAAC,iBAAiB,mKAAC,UAAO,CAAC,SAAS,CAAC,UAAU,CAAC,kBAAkB,CAAC,oKAAE,UAAO,CAAC,SAAS,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC,CAAC;YAE3H,IAAI,UAAU,CAAC,WAAW,EAAE,CAAC;gBACzB,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC,WAAW,CAAC;YAC9C,CAAC;YAED,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;YACrB,IAAI,UAAU,CAAC,MAAM,EAAE,CAAC;gBACpB,IAAI,CAAC,UAAU,CAAC,IAAI,6JAAC,eAAY,CAAC,MAAM,CAAC,CAAC;YAC9C,CAAC;YAED,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;gBACrB,IAAI,CAAC,UAAU,CAAC,IAAI,6JAAC,eAAY,CAAC,OAAO,CAAC,CAAC;YAC/C,CAAC;YAED,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;gBACrB,IAAI,CAAC,UAAU,CAAC,IAAI,6JAAC,eAAY,CAAC,OAAO,CAAC,CAAC;YAC/C,CAAC;YAED,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;gBACrB,IAAI,CAAC,UAAU,CAAC,IAAI,6JAAC,eAAY,CAAC,OAAO,CAAC,CAAC;YAC/C,CAAC;YAED,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;gBACrB,IAAI,CAAC,UAAU,CAAC,IAAI,6JAAC,eAAY,CAAC,OAAO,CAAC,CAAC;YAC/C,CAAC;YAED,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;gBACrB,IAAI,CAAC,UAAU,CAAC,IAAI,6JAAC,eAAY,CAAC,OAAO,CAAC,CAAC;YAC/C,CAAC;YAED,IAAI,UAAU,CAAC,SAAS,EAAE,CAAC;gBACvB,IAAI,CAAC,UAAU,CAAC,IAAI,6JAAC,eAAY,CAAC,SAAS,CAAC,CAAC;YACjD,CAAC;YAED,IAAI,UAAU,CAAC,kBAAkB,EAAE,CAAC;gBAChC,IAAI,CAAC,UAAU,CAAC,IAAI,6JAAC,eAAY,CAAC,mBAAmB,CAAC,CAAC;YAC3D,CAAC;YAED,IAAI,UAAU,CAAC,kBAAkB,EAAE,CAAC;gBAChC,IAAI,CAAC,UAAU,CAAC,IAAI,6JAAC,eAAY,CAAC,mBAAmB,CAAC,CAAC;YAC3D,CAAC;YAED,IAAI,CAAC,qBAAqB,gKAAG,WAAQ,CAAC,eAAe,CAAC;YAEtD,IAAI,yLAAgB,CAAC,mCAAmC,EAAE,CAAC;gBACvD,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC5B,CAAC;QACL,CAAC,MAAM,CAAC;yKACJ,WAAQ,CAAC,eAAe,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAC/C,CAAC;QAED,WAAW;QACX,IAAI,UAAU,CAAC,gBAAgB,EAAE,CAAC;YAC9B,IAAI,CAAC,kBAAkB,GAAG,UAAU,CAAC,gBAAgB,CAAC;QAC1D,CAAC,MAAM,IAAI,UAAU,CAAC,UAAU,EAAE,CAAC;YAC/B,IAAI,CAAC,kBAAkB,GAAG,UAAU,CAAC,UAAU,CAAC;QACpD,CAAC;QAED,gBAAgB;QAChB,IAAI,UAAU,CAAC,oBAAoB,GAAG,CAAC,CAAC,EAAE,CAAC;YACvC,IAAI,CAAC,4BAA4B,GAAG,UAAU,CAAC,oBAAoB,CAAC;QACxE,CAAC;QAED,WAAW;QACX,IAAI,UAAU,CAAC,UAAU,KAAK,SAAS,IAAI,UAAU,CAAC,UAAU,KAAK,IAAI,EAAE,CAAC;YACxE,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,mBAAmB,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YACjE,IAAI,UAAU,CAAC,kBAAkB,EAAE,CAAC;gBAChC,IAAI,CAAC,kBAAkB,GAAG,UAAU,CAAC,kBAAkB,CAAC;YAC5D,CAAC;QACL,CAAC;QAED,aAAa;QACb,IAAI,UAAU,CAAC,UAAU,EAAE,CAAC;YACxB,IAAK,IAAI,cAAc,GAAG,CAAC,EAAE,cAAc,GAAG,UAAU,CAAC,UAAU,CAAC,MAAM,EAAE,cAAc,EAAE,CAAE,CAAC;gBAC3F,MAAM,eAAe,GAAG,UAAU,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;gBAC9D,MAAM,aAAa,mKAAG,WAAA,AAAQ,EAAC,mBAAmB,CAAC,CAAC;gBACpD,IAAI,aAAa,EAAE,CAAC;oBAChB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;gBAC/D,CAAC;YACL,CAAC;2JACD,OAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,UAAU,CAAC,WAAW,EAAE,CAAC;YACzB,KAAK,CAAC,cAAc,CAAC,IAAI,EAAE,UAAU,CAAC,eAAe,EAAE,UAAU,CAAC,aAAa,EAAE,UAAU,CAAC,eAAe,EAAE,UAAU,CAAC,gBAAgB,IAAI,GAAG,CAAC,CAAC;QACrJ,CAAC;QAED,aAAa;QACb,IAAI,UAAU,CAAC,SAAS,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACvD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC;QAC9D,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC;QAChC,CAAC;QAED,UAAU;QACV,IAAI,UAAU,CAAC,eAAe,EAAE,CAAC;YAC7B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;QAChF,CAAC;QAED,SAAS;QACT,IAAI,UAAU,CAAC,UAAU,EAAE,CAAC;YACxB,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG;gBACrB,GAAG,EAAE,UAAU,CAAC,UAAU;gBAC1B,SAAS,EAAE,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI;gBACnE,SAAS,EAAE,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI;aACtE,CAAC;QACN,CAAC;QAED,YAAY;QACZ,IAAI,UAAU,CAAC,SAAS,EAAE,CAAC;YACvB,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBAC/D,MAAM,cAAc,GAAG,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACnD,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;gBAE1D,IAAI,cAAc,CAAC,EAAE,EAAE,CAAC;oBACpB,QAAQ,CAAC,EAAE,GAAG,cAAc,CAAC,EAAE,CAAC;gBACpC,CAAC;gBAED,2JAAI,OAAI,EAAE,CAAC;oBACP,IAAI,cAAc,CAAC,IAAI,EAAE,CAAC;8KACtB,QAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC;oBAClD,CAAC,MAAM,CAAC;wBACJ,8JAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC;oBAC9C,CAAC;gBACL,CAAC;gBAED,QAAQ,CAAC,QAAQ,qKAAG,UAAO,CAAC,SAAS,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;gBAE/D,IAAI,cAAc,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;oBACxC,QAAQ,CAAC,QAAQ,GAAG,cAAc,CAAC,QAAQ,CAAC;gBAChD,CAAC;gBAED,IAAI,cAAc,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;oBACxC,QAAQ,CAAC,gBAAgB,GAAG,cAAc,CAAC,QAAQ,CAAC;gBACxD,CAAC;gBAED,IAAI,cAAc,CAAC,mBAAmB,KAAK,SAAS,EAAE,CAAC;oBACnD,QAAQ,CAAC,2BAA2B,GAAG,cAAc,CAAC,mBAAmB,CAAC;gBAC9E,CAAC;gBAED,IAAI,cAAc,CAAC,SAAS,KAAK,SAAS,IAAI,cAAc,CAAC,SAAS,KAAK,IAAI,EAAE,CAAC;oBAC9E,QAAQ,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;gBAClD,CAAC;gBAED,IAAI,cAAc,CAAC,SAAS,KAAK,SAAS,IAAI,cAAc,CAAC,SAAS,KAAK,IAAI,EAAE,CAAC;oBAC9E,QAAQ,CAAC,SAAS,GAAG,cAAc,CAAC,SAAS,CAAC;gBAClD,CAAC;gBAED,IAAI,cAAc,CAAC,UAAU,KAAK,SAAS,IAAI,cAAc,CAAC,UAAU,KAAK,IAAI,EAAE,CAAC;oBAChF,QAAQ,CAAC,UAAU,GAAG,cAAc,CAAC,UAAU,CAAC;gBACpD,CAAC;gBAED,IAAI,cAAc,CAAC,kBAAkB,EAAE,CAAC;oBACpC,QAAQ,CAAC,kBAAkB,qKAAG,aAAU,CAAC,SAAS,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC;gBAC1F,CAAC,MAAM,IAAI,cAAc,CAAC,QAAQ,EAAE,CAAC;oBACjC,QAAQ,CAAC,QAAQ,GAAG,4KAAO,CAAC,SAAS,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;gBACnE,CAAC;gBAED,QAAQ,CAAC,OAAO,qKAAG,UAAO,CAAC,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;gBAE7D,IAAI,cAAc,CAAC,eAAe,IAAI,SAAS,IAAI,cAAc,CAAC,eAAe,IAAI,IAAI,EAAE,CAAC;oBACxF,QAAQ,CAAC,eAAe,GAAG,cAAc,CAAC,eAAe,CAAC;gBAC9D,CAAC;gBACD,IAAI,cAAc,CAAC,QAAQ,IAAI,SAAS,IAAI,cAAc,CAAC,QAAQ,IAAI,IAAI,EAAE,CAAC;oBAC1E,QAAQ,CAAC,UAAU,GAAG,cAAc,CAAC,QAAQ,CAAC;gBAClD,CAAC;gBACD,IAAI,cAAc,CAAC,eAAe,IAAI,SAAS,IAAI,cAAc,CAAC,eAAe,IAAI,IAAI,EAAE,CAAC;oBACxF,QAAQ,CAAC,eAAe,GAAG,cAAc,CAAC,eAAe,CAAC;gBAC9D,CAAC;gBACD,IAAI,cAAc,CAAC,wBAAwB,IAAI,SAAS,IAAI,cAAc,CAAC,wBAAwB,IAAI,IAAI,EAAE,CAAC;oBAC1G,QAAQ,CAAC,wBAAwB,GAAG,cAAc,CAAC,wBAAwB,CAAC;gBAChF,CAAC;gBACD,IAAI,cAAc,CAAC,UAAU,IAAI,SAAS,IAAI,cAAc,CAAC,wBAAwB,IAAI,IAAI,EAAE,CAAC;oBAC5F,QAAQ,CAAC,UAAU,GAAG,cAAc,CAAC,UAAU,CAAC;gBACpD,CAAC;gBAED,UAAU;gBACV,IAAI,cAAc,CAAC,eAAe,EAAE,CAAC;oBACjC,QAAQ,CAAC,eAAe,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC;gBAC5F,CAAC;gBAED,UAAU;gBACV,IAAI,cAAc,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;oBACvC,QAAQ,CAAC,YAAY,CAAC,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC;gBAC3D,CAAC;gBAED,YAAY;gBACZ,IAAI,cAAc,CAAC,UAAU,EAAE,CAAC;oBAC5B,IAAK,IAAI,cAAc,GAAG,CAAC,EAAE,cAAc,GAAG,cAAc,CAAC,UAAU,CAAC,MAAM,EAAE,cAAc,EAAE,CAAE,CAAC;wBAC/F,MAAM,eAAe,GAAG,cAAc,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;wBAClE,MAAM,aAAa,mKAAG,WAAA,AAAQ,EAAC,mBAAmB,CAAC,CAAC;wBACpD,IAAI,aAAa,EAAE,CAAC;4BAChB,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;wBACnE,CAAC;oBACL,CAAC;mKACD,OAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,cAAc,EAAE,KAAK,CAAC,CAAC;oBAE3D,IAAI,cAAc,CAAC,WAAW,EAAE,CAAC;wBAC7B,KAAK,CAAC,cAAc,CAChB,QAAQ,EACR,cAAc,CAAC,eAAe,EAC9B,cAAc,CAAC,aAAa,EAC5B,cAAc,CAAC,eAAe,EAC9B,cAAc,CAAC,gBAAgB,IAAI,GAAG,CACzC,CAAC;oBACN,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;QAED,iBAAiB;QACjB,IAAI,UAAU,CAAC,aAAa,EAAE,CAAC;YAC3B,MAAM,aAAa,GAAG,UAAU,CAAC,aAAa,CAAC;YAE/C,IAAI,CAAC,yBAAyB,GAAG,CAAC,CAAC,aAAa,CAAC,aAAa,CAAC;YAE/D,IAAI,aAAa,CAAC,UAAU,EAAE,CAAC;gBAC3B,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,IAAI,YAAY,CAAC,aAAa,CAAC,UAAU,CAAC,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;gBAE5F,IAAI,CAAC,wBAAwB,CAAC,gBAAgB,GAAG,aAAa,CAAC,gBAAgB,CAAC;gBAChF,IAAI,CAAC,wBAAwB,CAAC,cAAc,GAAG,aAAa,CAAC,cAAc,CAAC;YAChF,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,wBAAwB,CAAC,gBAAgB,GAAG,aAAa,CAAC,gBAAgB,CAAC;YACpF,CAAC;YAED,IAAI,UAAU,CAAC,aAAa,CAAC,gBAAgB,EAAE,CAAC;gBAC5C,MAAM,gBAAgB,GAAG,UAAU,CAAC,aAAa,CAAC,gBAAgB,CAAC;gBAEnE,IAAK,MAAM,IAAI,IAAI,gBAAgB,CAAC,IAAI,CAAE,CAAC;oBACvC,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,IAAI,YAAY,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;oBACvH,IAAI,CAAC,+BAA+B,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACpF,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,YAAY;IAEZ;;;OAGG,CACI,0BAA0B,GAAA;QAC7B,MAAM,gBAAgB,GAAG,IAAI,CAAC,qBAAqB,CAAC;QACpD,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,CAAC;YACrC,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,6JAAC,eAAY,CAAC,YAAY,CAAC,CAAC;YAC/D,IAAI,CAAC,MAAM,EAAE,CAAC;gBACV,OAAO,gBAAgB,CAAC,gBAAgB,CAAC;YAC7C,CAAC;YAED,gBAAgB,CAAC,gBAAgB,GAAG,IAAI,YAAY,CAAM,MAAM,CAAC,CAAC;YAElE,IAAI,CAAC,IAAI,CAAC,uBAAuB,6JAAC,eAAY,CAAC,YAAY,CAAC,EAAE,CAAC;gBAC3D,IAAI,CAAC,eAAe,6JAAC,eAAY,CAAC,YAAY,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;YAClE,CAAC;QACL,CAAC;QACD,OAAO,gBAAgB,CAAC,gBAAgB,CAAC;IAC7C,CAAC;IAED;;;OAGG,CACI,wBAAwB,GAAA;QAC3B,MAAM,gBAAgB,GAAG,IAAI,CAAC,qBAAqB,CAAC;QAEpD,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC;YACnC,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,6JAAC,eAAY,CAAC,UAAU,CAAC,CAAC;YAE7D,IAAI,CAAC,MAAM,EAAE,CAAC;gBACV,OAAO,gBAAgB,CAAC,cAAc,CAAC;YAC3C,CAAC;YAED,gBAAgB,CAAC,cAAc,GAAG,IAAI,YAAY,CAAM,MAAM,CAAC,CAAC;YAEhE,IAAI,CAAC,IAAI,CAAC,uBAAuB,6JAAC,eAAY,CAAC,UAAU,CAAC,EAAE,CAAC;gBACzD,IAAI,CAAC,eAAe,6JAAC,eAAY,CAAC,UAAU,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;YAChE,CAAC;QACL,CAAC;QACD,OAAO,gBAAgB,CAAC,cAAc,CAAC;IAC3C,CAAC;IAED;;;;OAIG,CACI,aAAa,CAAC,QAAkB,EAAA;QACnC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACjB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,wBAAwB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,UAAU,EAAE,EAAE,CAAC;YACzE,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,wBAAwB,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,UAAU,EAAE,CAAC;QAEtE,IAAI,CAAC,IAAI,CAAC,qBAAqB,6JAAC,eAAY,CAAC,YAAY,CAAC,EAAE,CAAC;YACzD,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,qBAAqB,6JAAC,eAAY,CAAC,mBAAmB,CAAC,EAAE,CAAC;YAChE,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,qBAAqB,6JAAC,eAAY,CAAC,mBAAmB,CAAC,EAAE,CAAC;YAChE,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,qBAAqB,6JAAC,eAAY,CAAC,UAAU,CAAC,CAAC;QAEvE,MAAM,gBAAgB,GAAG,IAAI,CAAC,qBAAqB,CAAC;QAEpD,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,CAAC;YACrC,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;YACzC,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAClC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC/B,CAAC;QAED,IAAI,UAAU,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC;YACjD,IAAI,CAAC,wBAAwB,EAAE,CAAC;QACpC,CAAC;QAED,8EAA8E;QAC9E,IAAI,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,2KAAY,CAAC,YAAY,CAAC,CAAC;QAEpE,IAAI,CAAC,aAAa,EAAE,CAAC;YACjB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,CAAC,CAAC,aAAa,YAAY,YAAY,CAAC,EAAE,CAAC;YAC3C,aAAa,GAAG,IAAI,YAAY,CAAC,aAAa,CAAC,CAAC;QACpD,CAAC;QAED,4EAA4E;QAC5E,IAAI,WAAW,GAAG,IAAI,CAAC,eAAe,6JAAC,eAAY,CAAC,UAAU,CAAC,CAAC;QAEhE,IAAI,UAAU,EAAE,CAAC;YACb,IAAI,CAAC,WAAW,EAAE,CAAC;gBACf,OAAO,IAAI,CAAC;YAChB,CAAC;YAED,IAAI,CAAC,CAAC,WAAW,YAAY,YAAY,CAAC,EAAE,CAAC;gBACzC,WAAW,GAAG,IAAI,YAAY,CAAC,WAAW,CAAC,CAAC;YAChD,CAAC;QACL,CAAC;QAED,MAAM,mBAAmB,GAAG,IAAI,CAAC,eAAe,CAAC,2KAAY,CAAC,mBAAmB,CAAC,CAAC;QACnF,MAAM,mBAAmB,GAAG,IAAI,CAAC,eAAe,6JAAC,eAAY,CAAC,mBAAmB,CAAC,CAAC;QAEnF,IAAI,CAAC,mBAAmB,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC/C,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;QAC/C,MAAM,wBAAwB,GAAG,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,6JAAC,eAAY,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACjH,MAAM,wBAAwB,GAAG,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,6JAAC,eAAY,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAEjH,MAAM,gBAAgB,GAAG,QAAQ,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAE7D,MAAM,WAAW,qKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;QACnC,MAAM,WAAW,GAAG,sKAAI,SAAM,EAAE,CAAC;QACjC,MAAM,UAAU,GAAG,sKAAI,SAAM,EAAE,CAAC;QAEhC,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAI,GAAW,CAAC;QAChB,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,aAAa,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,EAAE,YAAY,IAAI,CAAC,CAAE,CAAC;YAC9E,IAAI,MAAc,CAAC;YACnB,IAAK,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE,CAAE,CAAC;gBAC3B,MAAM,GAAG,mBAAmB,CAAC,YAAY,GAAG,GAAG,CAAC,CAAC;gBACjD,IAAI,MAAM,GAAG,CAAC,EAAE,CAAC;sLACb,SAAM,CAAC,2BAA2B,CAAC,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,YAAY,GAAG,GAAG,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;oBACnI,WAAW,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;gBACtC,CAAC;YACL,CAAC;YACD,IAAI,UAAU,EAAE,CAAC;gBACb,IAAK,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE,CAAE,CAAC;oBAC3B,MAAM,GAAG,wBAAyB,CAAC,YAAY,GAAG,GAAG,CAAC,CAAC;oBACvD,IAAI,MAAM,GAAG,CAAC,EAAE,CAAC;0LACb,SAAM,CAAC,2BAA2B,CAAC,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,wBAAyB,CAAC,YAAY,GAAG,GAAG,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;wBACzI,WAAW,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;oBACtC,CAAC;gBACL,CAAC;YACL,CAAC;8KAED,UAAO,CAAC,mCAAmC,CACvC,gBAAgB,CAAC,gBAAiB,CAAC,KAAK,CAAC,EACzC,gBAAgB,CAAC,gBAAiB,CAAC,KAAK,GAAG,CAAC,CAAC,EAC7C,gBAAgB,CAAC,gBAAiB,CAAC,KAAK,GAAG,CAAC,CAAC,EAC7C,WAAW,EACX,WAAW,CACd,CAAC;YACF,WAAW,CAAC,OAAO,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;YAE1C,IAAI,UAAU,EAAE,CAAC;kLACb,UAAO,CAAC,8BAA8B,CAClC,gBAAgB,CAAC,cAAe,CAAC,KAAK,CAAC,EACvC,gBAAgB,CAAC,cAAe,CAAC,KAAK,GAAG,CAAC,CAAC,EAC3C,gBAAgB,CAAC,cAAe,CAAC,KAAK,GAAG,CAAC,CAAC,EAC3C,WAAW,EACX,WAAW,CACd,CAAC;gBACF,WAAW,CAAC,OAAO,CAAC,WAAY,EAAE,KAAK,CAAC,CAAC;YAC7C,CAAC;YAED,WAAW,CAAC,KAAK,EAAE,CAAC;QACxB,CAAC;QAED,IAAI,CAAC,kBAAkB,6JAAC,eAAY,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC;QAClE,IAAI,UAAU,EAAE,CAAC;YACb,IAAI,CAAC,kBAAkB,CAAC,2KAAY,CAAC,UAAU,EAAE,WAAY,CAAC,CAAC;QACnE,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,QAAQ;IAER;;;;OAIG,CACI,MAAM,CAAC,MAAM,CAAC,MAAsB,EAAA;QACvC,IAAI,SAAS,GAAsB,IAAI,CAAC;QACxC,IAAI,SAAS,GAAsB,IAAI,CAAC;QAExC,KAAK,MAAM,IAAI,IAAI,MAAM,CAAE,CAAC;YACxB,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;YAE5C,MAAM,WAAW,GAAG,YAAY,CAAC,WAAW,CAAC;YAC7C,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,EAAE,CAAC;gBAC3B,SAAS,GAAG,WAAW,CAAC,YAAY,CAAC;gBACrC,SAAS,GAAG,WAAW,CAAC,YAAY,CAAC;YACzC,CAAC,MAAM,CAAC;gBACJ,SAAS,CAAC,eAAe,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;gBACpD,SAAS,CAAC,eAAe,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;YACxD,CAAC;QACL,CAAC;QAED,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,EAAE,CAAC;YAC3B,OAAO;gBACH,GAAG,oKAAE,UAAO,CAAC,IAAI,EAAE;gBACnB,GAAG,oKAAE,UAAO,CAAC,IAAI,EAAE;aACtB,CAAC;QACN,CAAC;QAED,OAAO;YACH,GAAG,EAAE,SAAS;YACd,GAAG,EAAE,SAAS;SACjB,CAAC;IACN,CAAC;IAED;;;;OAIG,CACI,MAAM,CAAC,MAAM,CAAC,oBAAqE,EAAA;QACtF,MAAM,YAAY,GAAG,oBAAoB,YAAY,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC;QACtH,yKAAO,UAAO,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC;IAC9D,CAAC;IAED;;;;;;;;;OASG,CACI,MAAM,CAAC,WAAW,CACrB,MAAmB,EAKU;4BAJ7B,aAAa,oDAAG,IAAI,EACpB,kBAA4B,iDAC5B,YAAmB,iDACnB,sBAAgC,iDAChC,mBAA6B;QAE7B,uKAAO,mBAAA,AAAgB,EAAC,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,aAAa,EAAE,kBAAkB,EAAE,YAAY,EAAE,sBAAsB,EAAE,mBAAmB,EAAE,KAAK,CAAC,CAAC,CAAC;IACrK,CAAC;IAED;;;;;;;;;OASG,CACI,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAChC,MAAmB,EAKU;4BAJ7B,aAAa,oDAAG,IAAI,EACpB,kBAA4B,iDAC5B,YAAmB,iDACnB,sBAAgC,iDAChC,mBAA6B;QAE7B,OAAO,sKAAM,oBAAiB,AAAjB,EACT,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,aAAa,EAAE,kBAAkB,EAAE,YAAY,EAAE,sBAAsB,EAAE,mBAAmB,EAAE,IAAI,CAAC,EACtI,0LAAA,AAAuB,EAAE,CAC5B,CAAC;IACN,CAAC;IAEO,MAAM,CAAC,CAAC,qBAAqB,CACjC,MAAmB,EAMH;4BALhB,aAAa,oDAAG,IAAI,EACpB,kBAAuC,iDACvC,YAA8B,iDAC9B,sBAA2C,iDAC3C,mBAAwC,iDACxC,OAAgB;QAEhB,wDAAwD;QACxD,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAEhC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,KAAa,CAAC;QAClB,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACtB,IAAI,aAAa,GAAG,CAAC,CAAC;YAEtB,oBAAoB;YACpB,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBAC7C,aAAa,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE,CAAC;gBAElD,IAAI,aAAa,IAAI,KAAK,EAAE,CAAC;4KACzB,UAAM,CAAC,IAAI,CAAC,4IAA4I,CAAC,CAAC;oBAC1J,OAAO,IAAI,CAAC;gBAChB,CAAC;YACL,CAAC;QACL,CAAC;QACD,IAAI,mBAAmB,EAAE,CAAC;YACtB,sBAAsB,GAAG,KAAK,CAAC;QACnC,CAAC;QACD,MAAM,aAAa,GAAoB,IAAI,KAAK,EAAY,CAAC;QAC7D,MAAM,kBAAkB,GAAkB,IAAI,KAAK,EAAU,CAAC;QAC9D,QAAQ;QACR,MAAM,WAAW,GAAkB,IAAI,KAAK,EAAU,CAAC;QACvD,MAAM,sBAAsB,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC;QAEzD,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAC7C,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;YAC3B,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;yKACpB,SAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;gBAC7C,OAAO,IAAI,CAAC;YAChB,CAAC;YAED,IAAI,sBAAsB,KAAK,IAAI,CAAC,eAAe,EAAE,CAAC;yKAClD,SAAM,CAAC,IAAI,CAAC,4DAA4D,CAAC,CAAC;gBAC1E,OAAO,IAAI,CAAC;YAChB,CAAC;YAED,IAAI,sBAAsB,EAAE,CAAC;gBACzB,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC;YAC7C,CAAC;YAED,IAAI,mBAAmB,EAAE,CAAC;gBACtB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAChB,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC/B,IAAI,QAAQ,iLAAY,gBAAa,EAAE,CAAC;wBACpC,IAAK,IAAI,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,YAAY,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAE,CAAC;4BACzE,IAAI,aAAa,CAAC,OAAO,CAAW,QAAQ,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;gCACvE,aAAa,CAAC,IAAI,CAAW,QAAQ,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC;4BAClE,CAAC;wBACL,CAAC;wBACD,IAAK,IAAI,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAE,CAAC;4BAClE,kBAAkB,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAW,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;4BACxH,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;wBAC1D,CAAC;oBACL,CAAC,MAAM,CAAC;wBACJ,IAAI,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;4BACtC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;wBACjC,CAAC;wBACD,IAAK,IAAI,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAE,CAAC;4BAClE,kBAAkB,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;4BACzD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;wBAC1D,CAAC;oBACL,CAAC;gBACL,CAAC,MAAM,CAAC;oBACJ,IAAK,IAAI,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAE,CAAC;wBAClE,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;wBAC3B,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;oBAC1D,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAEzB,MAAM,qBAAqB,GAAG,CAAC,IAAU,EAAE,EAAE;YACzC,MAAM,EAAE,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,UAAU,0KAAG,aAAU,CAAC,eAAe,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;YAClE,OAAO;gBAAE,UAAU;gBAAE,SAAS,EAAE,EAAE;YAAA,CAAE,CAAC;QACzC,CAAC,CAAC;QAEF,MAAM,EAAE,UAAU,EAAE,gBAAgB,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG,qBAAqB,CAAC,MAAM,CAAC,CAAC;QACnG,IAAI,OAAO,EAAE,CAAC;YACV,KAAK,CAAC;QACV,CAAC;QAED,MAAM,eAAe,GAAG,IAAI,KAAK,CAAiD,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACrG,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;YACrC,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,qBAAqB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1D,IAAI,OAAO,EAAE,CAAC;gBACV,KAAK,CAAC;YACV,CAAC;QACL,CAAC;QAED,MAAM,cAAc,GAAG,gBAAgB,CAAC,eAAe,CAAC,eAAe,EAAE,eAAe,EAAE,kBAAkB,EAAE,OAAO,EAAE,CAAC,aAAa,CAAC,CAAC;QACvI,IAAI,kBAAkB,GAAG,cAAc,CAAC,IAAI,EAAE,CAAC;QAC/C,MAAO,CAAC,kBAAkB,CAAC,IAAI,CAAE,CAAC;YAC9B,IAAI,OAAO,EAAE,CAAC;gBACV,KAAK,CAAC;YACV,CAAC;YACD,kBAAkB,GAAG,cAAc,CAAC,IAAI,EAAE,CAAC;QAC/C,CAAC;QACD,MAAM,UAAU,GAAG,kBAAkB,CAAC,KAAK,CAAC;QAE5C,IAAI,CAAC,YAAY,EAAE,CAAC;YAChB,YAAY,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,SAAS,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;QACxE,CAAC;QAED,MAAM,gBAAgB,GAAG,UAAU,CAAC,iBAAiB,CAAC,YAAY,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QACxF,IAAI,oBAAoB,GAAG,gBAAgB,CAAC,IAAI,EAAE,CAAC;QACnD,MAAO,CAAC,oBAAoB,CAAC,IAAI,CAAE,CAAC;YAChC,IAAI,OAAO,EAAE,CAAC;gBACV,KAAK,CAAC;YACV,CAAC;YACD,oBAAoB,GAAG,gBAAgB,CAAC,IAAI,EAAE,CAAC;QACnD,CAAC;QAED,qBAAqB;QACrB,YAAY,CAAC,eAAe,GAAG,MAAM,CAAC,eAAe,CAAC;QACtD,YAAY,CAAC,eAAe,GAAG,MAAM,CAAC,eAAe,CAAC;QAEtD,WAAW;QACX,IAAI,aAAa,EAAE,CAAC;YAChB,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBAC7C,MAAM,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,CAAC;YAC5B,CAAC;QACL,CAAC;QAED,YAAY;QACZ,IAAI,sBAAsB,IAAI,mBAAmB,EAAE,CAAC;YAChD,8BAA8B;YAC9B,YAAY,CAAC,gBAAgB,EAAE,CAAC;YAChC,KAAK,GAAG,CAAC,CAAC;YACV,IAAI,MAAM,GAAG,CAAC,CAAC;YAEf,+CAA+C;YAC/C,MAAO,KAAK,GAAG,WAAW,CAAC,MAAM,CAAE,CAAC;4KAChC,UAAO,CAAC,iBAAiB,CAAC,CAAC,EAAE,MAAM,EAAE,WAAW,CAAC,KAAK,CAAC,EAAE,YAAY,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;gBACzF,MAAM,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC;gBAC7B,KAAK,EAAE,CAAC;YACZ,CAAC;YAED,KAAK,MAAM,OAAO,IAAI,YAAY,CAAC,SAAS,CAAE,CAAC;gBAC3C,OAAO,CAAC,mBAAmB,EAAE,CAAC;YAClC,CAAC;YAED,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,mBAAmB,EAAE,CAAC;YACtB,MAAM,gBAAgB,GAAG,yKAAI,gBAAa,CAAC,MAAM,CAAC,IAAI,GAAG,SAAS,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;YACvF,gBAAgB,CAAC,YAAY,GAAG,aAAa,CAAC;YAC9C,IAAK,IAAI,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,YAAY,CAAC,SAAS,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAE,CAAC;gBAC1E,YAAY,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,aAAa,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC;YAClF,CAAC;YACD,YAAY,CAAC,QAAQ,GAAG,gBAAgB,CAAC;QAC7C,CAAC,MAAM,CAAC;YACJ,YAAY,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;QAC5C,CAAC;QAED,OAAO,YAAY,CAAC;IACxB,CAAC;IAED;;OAEG,CACI,WAAW,CAAC,QAAuB,EAAA;QACtC,QAAQ,CAAC,+BAA+B,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;QACjE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG,CACI,cAAc,CAAC,QAAuB,EAAA;QACzC,mBAAmB;QACnB,MAAM,KAAK,GAAG,QAAQ,CAAC,+BAA+B,CAAC;QACvD,IAAI,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC;YACd,IAAI,KAAK,KAAK,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACtC,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACvD,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;gBAC7B,IAAI,CAAC,+BAA+B,GAAG,KAAK,CAAC;YACjD,CAAC;YAED,QAAQ,CAAC,+BAA+B,GAAG,CAAC,CAAC,CAAC;YAC9C,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;QACzB,CAAC;IACL,CAAC;IAED,cAAA,EAAgB,CACA,iBAAiB,GAAA;QAC7B,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,IAAI,IAAI,CAAC,eAAe,qKAAK,WAAQ,CAAC,+BAA+B,CAAC;IACjH,CAAC;IAED,cAAA,EAAgB,CACT,qBAAqB,CAAC,QAAgB,EAAA;QACzC,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,IAAI,KAAK,CAAC,gBAAgB,EAAE,CAAC;YACzB,OAAO,2KAAQ,CAAC,aAAa,CAAC;QAClC,CAAC;QAED,IAAI,KAAK,CAAC,cAAc,EAAE,CAAC;YACvB,OAAO,2KAAQ,CAAC,iBAAiB,CAAC;QACtC,CAAC;;QAED,0CAAO,IAAI,CAAC,yBAAyB,6FAAI,QAAQ,CAAC;IACtD,CAAC;IAED,qBAAqB;IACrB;;;;;OAKG,CACH,gEAAgE;IACzD,eAAe,CAAC,EAAU,EAAA;QAC7B,OAAO,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;IACpC,CAAC;IAED;;;;;;;;;;;;;;OAcG,CACI,MAAM,CAAC,YAAY,CACtB,IAAY,EACZ,SAAsB,EACtB,UAAmB,EACnB,SAAkB,EAClB,MAAc,EACd,KAAa,EACb,SAAmB,EACnB,eAAwB,EACxB,QAAe,EAAA;QAEf,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;IACpE,CAAC;IAED;;;;;;;;;;OAUG,CACI,MAAM,CAAC,UAAU,CAAC,IAAY,EAAE,MAAc,EAAE,YAAoB,EAAE,KAAsB,EAAE,SAAmB,EAAE,eAAwB,EAAA;QAC9I,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;IACpE,CAAC;IAED;;;;;;;;;OASG,CACI,MAAM,CAAC,SAAS,CAAC,IAAY,EAAE,IAAY,EAAE,KAAsB,EAAE,SAAmB,EAAE,eAAwB,EAAA;QACrH,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;IACpE,CAAC;IAED;;;;;;;;;;OAUG,CACI,MAAM,CAAC,YAAY,CAAC,IAAY,EAAE,QAAgB,EAAE,QAAgB,EAAE,KAAa,EAAE,SAAmB,EAAE,eAAwB,EAAA;QACrI,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;IACpE,CAAC;IAED;;;;;;;;OAQG,CACI,MAAM,CAAC,gBAAgB,CAAC,IAAY,EAAE,QAAgB,EAAE,QAAgB,EAAE,KAAa,EAAA;QAC1F,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;IACpE,CAAC;IAED;;;;;;;;;;;;;OAaG,CACI,MAAM,CAAC,cAAc,CACxB,IAAY,EACZ,MAAc,EACd,WAAmB,EACnB,cAAsB,EACtB,YAAoB,EACpB,YAAiB,EACjB,KAAa,EACb,SAAe,EACf,eAAwB,EAAA;QAExB,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;IACpE,CAAC;IAED,iCAAiC;IACjC;;;;;;;;;;;OAWG,CACI,MAAM,CAAC,WAAW,CAAC,IAAY,EAAE,QAAgB,EAAE,SAAiB,EAAE,YAAoB,EAAE,KAAa,EAAE,SAAmB,EAAE,eAAwB,EAAA;QAC3J,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;IACpE,CAAC;IAED;;;;;;;;;;;;;;OAcG,CACI,MAAM,CAAC,eAAe,CACzB,IAAY,EACZ,MAAc,EACd,IAAY,EACZ,cAAsB,EACtB,eAAuB,EACvB,CAAS,EACT,CAAS,EACT,KAAa,EACb,SAAmB,EACnB,eAAwB,EAAA;QAExB,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;IACpE,CAAC;IAED;;;;;;;;;OASG,CACI,MAAM,CAAC,WAAW,CAAC,IAAY,EAAE,MAAiB,EAAE,KAAsB,EAAE,SAAkB,EAAE,QAA8B,EAAA;QACjI,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;IACpE,CAAC;IAED;;;;;;;;;;;;OAYG,CACI,MAAM,CAAC,iBAAiB,CAC3B,IAAY,EACZ,MAAiB,EACjB,QAAgB,EAChB,OAAe,EACf,MAAc,EACd,KAAsB,EACtB,SAAmB,EACnB,QAAoB,EAAA;QAEpB,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;IACpE,CAAC;IAED;;;;;;;;;;;;;;;;;OAiBG,CACI,MAAM,CAAC,aAAa,CAAC,IAAY,EAAE,KAAgB,EAAE,KAAY,EAAE,KAAmB,EAAE,SAAmB,EAAE,eAAwB,EAAE,eAAqB,EAAA;QAC/J,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;IACpE,CAAC;IAED;;;;;;;;;;;;;OAaG,CACI,MAAM,CAAC,cAAc,CACxB,IAAY,EACZ,KAAgB,EAChB,KAAa,EACb,KAAY,EACZ,KAAmB,EACnB,SAAmB,EACnB,eAAwB,EACxB,eAAqB,EAAA;QAErB,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;IACpE,CAAC;IAED;;;;;;;;;;;;;;;;;OAiBG,CACI,MAAM,CAAC,YAAY,CACtB,IAAY,EACZ,KAAgB,EAChB,IAAe,EACf,KAAa,EACb,QAAgB,EAChB,GAAW,EACX,KAAsB,EACtB,SAAmB,EACnB,eAAwB,EACxB,QAAe,EAAA;QAEf,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;IACpE,CAAC;IAED;;;;;;;;;;;;;;;;;;;;OAoBG,CACI,MAAM,CAAC,kBAAkB,CAC5B,IAAY,EACZ,KAAgB,EAChB,IAAe,EACf,aAAkE,EAClE,gBAAqE,EACrE,gBAAyB,EACzB,eAAwB,EACxB,GAAW,EACX,KAAY,EACZ,SAAmB,EACnB,eAAwB,EACxB,QAAe,EAAA;QAEf,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;IACpE,CAAC;IAED;;;;;;;;;;;;OAYG,CACI,MAAM,CAAC,WAAW,CAAC,IAAY,EAAE,KAAgB,EAAE,MAAc,EAAE,YAAoB,EAAE,KAAY,EAAE,SAAmB,EAAE,eAAwB,EAAA;QACvJ,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;IACpE,CAAC;IAED;;;;;;;;;OASG,CACI,MAAM,CAAC,WAAW,CAAC,IAAY,EAAE,IAAY,EAAE,KAAY,EAAE,SAAmB,EAAE,eAAwB,EAAA;QAC7G,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;IACpE,CAAC;IAED;;;;;;;;;;OAUG,CACI,MAAM,CAAC,YAAY,CAAC,IAAY,EAAE,KAAa,EAAE,MAAc,EAAE,YAAoB,EAAE,KAAa,EAAE,SAAmB,EAAA;QAC5H,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;IACpE,CAAC;IAED;;;;;;;;;;;;;OAaG,CACI,MAAM,CAAC,iBAAiB,CAC3B,IAAY,EACZ,IAAY,EACZ,IAAY,EACZ,IAAY,EACZ,IAAY,EACZ,YAAsC,EACtC,SAAmC,EACnC,KAAY,EACZ,SAAmB,EAAA;QAEnB,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;IACpE,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG,CACI,MAAM,CAAC,yBAAyB,CACnC,IAAY,EACZ,GAAW,EACX,KAAa,EACb,MAAc,EACd,YAAoB,EACpB,SAAiB,EACjB,SAAiB,EACjB,KAAY,EACZ,SAAmB,EACnB,OAAoC,EACpC,WAAoB,EAAA;QAEpB,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;IACpE,CAAC;IAED;;;;;;;;;;;;;;;;;;OAkBG,CACI,MAAM,CAAC,UAAU,CACpB,IAAY,EACZ,IAAe,EACf,MAAc,EACd,YAAoB,EACpB,cAAyD,EACzD,GAAW,EACX,KAAY,EACZ,SAAmB,EACnB,eAAwB,EACxB,QAAe,EAAA;QAEf,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;IACpE,CAAC;IAED;;;;;;;;;;;;;;;;;;;OAmBG,CACI,MAAM,CAAC,gBAAgB,CAC1B,IAAY,EACZ,OAWC,EACD,KAAY,EAAA;QAEZ,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;IACpE,CAAC;IAED;;;;;;;;;;;;;;;OAeG,CACI,MAAM,CAAC,eAAe,CACzB,IAAY,EACZ,OAAkH,EAClH,KAAY,EAAA;QAEZ,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;IACpE,CAAC;IAED;;;;;;;;;;;;OAYG,CACI,MAAM,CAAC,WAAW,CAAC,IAAY,EAAE,UAAwB,EAAE,QAAiB,EAAE,MAAe,EAAE,IAAa,EAAE,KAAa,EAAA;QAC9H,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;IACpE,CAAC;IAED;;;;;;;OAOG,CACI,MAAM,CAAC,aAAa,CAAC,IAAY,EAAE,OAA8B,EAAE,KAAY,EAAA;QAClF,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;IACpE,CAAC;IAED;;;;;;OAMG,CACI,MAAM,CAAC,gBAAgB,CAAC,IAAU,EAAA;QACrC,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;IACpE,CAAC;IA9yJD,cAAA,EAAgB,CAChB,YACI,IAAY,EACZ,QAAyB,IAAI,EAC7B,kBAAwD,IAAI,EAC5D,SAAyB,IAAI,EAC7B,kBAA4B,EAC5B,uBAAgC,IAAI,CAAA;QAEpC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAtiBvB,gBAAgB;QACR,IAAA,CAAA,qBAAqB,GAAG,IAAI,qBAAqB,EAAE,CAAC;QAsH5D,UAAU;QAEV;;;WAGG,CACI,IAAA,CAAA,cAAc,GAAG,SAAS,CAAC,mBAAmB,CAAC;QAEtD;;;;;WAKG,CACI,IAAA,CAAA,SAAS,GAAoB,EAAE,CAAC;QAgBvC,UAAU;QACV,cAAA,EAAgB,CACT,IAAA,CAAA,oBAAoB,GAAmC,IAAI,CAAC;QAEnE,cAAA,EAAgB,CACT,IAAA,CAAA,SAAS,GAAuB,IAAI,CAAC;QAsB5C,cAAA,EAAgB,CACT,IAAA,CAAA,wBAAwB,GAAG,IAAI,wBAAwB,EAAE,CAAC;QAEjE,cAAA,EAAgB,CACT,IAAA,CAAA,0BAA0B,GAAY,KAAK,CAAC;QAEnD,uEAAuE;QACvE,cAAA,EAAgB,CACT,IAAA,CAAA,+BAA+B,GAAW,IAAI,CAAC,WAAW,CAAC;QA6DlE;;;;;;WAMG,CACI,IAAA,CAAA,gBAAgB,GAAG,KAAK,CAAC;QA4S5B,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAExB,IAAI,CAAC,oBAAoB,GAAG,IAAI,oBAAoB,EAAE,CAAC;QACvD,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAErD,IAAI,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC;YACnC,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC,iCAAiC,CAAC;QACvE,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC,wCAAwC,CAAC;QAC9E,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,CAAC,UAAmB,EAAE,KAAa,EAAE,iBAA4B,EAAE,EAAE;YACtF,IAAI,UAAU,IAAI,iBAAiB,EAAE,CAAC;gBAClC,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;oBACtB,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;gBACjC,CAAC,MAAM,CAAC;oBACJ,iBAAiB,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;gBACjD,CAAC;YACL,CAAC;QACL,CAAC,CAAC;QAEF,IAAI,MAAM,GAAmB,IAAI,CAAC;QAClC,IAAI,kBAAkB,GAAG,KAAK,CAAC;QAE/B,IAAI,eAAe,IAAK,eAAwB,CAAC,oBAAoB,KAAK,SAAS,EAAE,CAAC;YAClF,MAAM,OAAO,GAAG,eAAsC,CAAC;;YAEvD,MAAM,8BAAW,MAAM,2CAAd,OAAO,WAAW,IAAI,CAAC;;YAChC,MAAM,sBAAG,OAAO,CAAC,MAAM,6DAAI,IAAI,CAAC;;YAChC,kBAAkB,0CAAW,kBAAkB,uDAA1B,OAAO,uBAAuB,KAAK,CAAC;gBAClC,OAAO;YAA9B,oBAAoB,4CAAW,oBAAoB,yFAAI,IAAI,CAAC;;YAC5D,kBAAkB,0CAAW,kBAAkB,uDAA1B,OAAO,uBAAuB,KAAK,CAAC;QAC7D,CAAC,MAAM,CAAC;YACJ,MAAM,GAAG,eAAiC,CAAC;QAC/C,CAAC;QAED,IAAI,MAAM,EAAE,CAAC;YACT,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,kBAAkB,EAAE,oBAAoB,EAAE,kBAAkB,CAAC,CAAC;QAC3F,CAAC;QAED,SAAS;QACT,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;YAClB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACzB,CAAC;QAED,IAAI,CAAC,oBAAoB,CAAC,0BAA0B,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,CAAC,eAAe,CAAC;QAElG,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,GAAG,CAAC,QAAwB,EAAE,EAAE;YAChF,iDAAiD;YACjD,QAAQ,CAAC,oBAAoB,GAAG,IAAI,CAAC;YACrC,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;gBACrB,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YACrD,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,EAAE,CAAC;oBACtD,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,GAAG,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAG,EAAE;wBAC/F,+BAA+B;wBAC/B,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;4BACrB,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,CAAC,CAAC;4BAChG,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,GAAG,IAAI,CAAC;4BAC1D,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;wBACrD,CAAC;oBACL,CAAC,CAAC,CAAC;gBACP,CAAC;YACL,CAAC;QACL,CAAC,CAAC;QAEF,IAAI,CAAC,qBAAqB,GAAG,iKAAI,aAAU,CAAC,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,CAAC,CAAC;QAElG,IAAI,MAAM,EAAE,CAAC;YACT,MAAM,CAAC,kBAAkB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QACpD,CAAC;IACL,CAAC;;AAltBD,SAAS;AAET;;GAEG,CACoB,KAAA,SAAS,0KAAG,aAAU,CAAC,SAAd,CAAwB;AAExD;;GAEG,CACoB,KAAA,QAAQ,GAAG,oLAAU,CAAC,QAAd,CAAuB;AACtD;;GAEG,CACoB,KAAA,UAAU,0KAAG,aAAU,CAAC,UAAd,CAAyB;AAC1D;;GAEG,CACoB,KAAA,WAAW,0KAAG,aAAU,CAAC,WAAd,CAA0B;AAC5D;;GAEG,CACoB,KAAA,MAAM,GAAG,CAAC,AAAJ,CAAK;AAClC;;GAEG,CACoB,KAAA,SAAS,GAAG,CAAC,AAAJ,CAAK;AACrC;;GAEG,CACoB,KAAA,OAAO,GAAG,CAAC,AAAJ,CAAK;AACnC;;GAEG,CACoB,KAAA,OAAO,GAAG,CAAC,AAAJ,CAAK;AACnC;;GAEG,CACoB,KAAA,OAAO,GAAG,CAAC,AAAJ,CAAK;AACnC;;GAEG,CACoB,KAAA,SAAS,GAAG,CAAC,AAAJ,CAAK;AACrC;;GAEG,CACoB,KAAA,WAAW,GAAG,CAAC,AAAJ,CAAK;AACvC;;GAEG,CACoB,KAAA,QAAQ,GAAG,CAAC,AAAJ,CAAK;AACpC;;GAEG,CACoB,KAAA,UAAU,GAAG,CAAC,AAAJ,CAAK;AACtC;;GAEG,CACoB,KAAA,kBAAkB,GAAG,CAAH,AAAI,CAAC;AAC9C;;GAEG,CACoB,KAAA,iBAAiB,GAAG,CAAC,AAAJ,CAAK;AAC7C;;GAEG,CACoB,KAAA,MAAM,GAAG,CAAC,AAAJ,CAAK;AAClC;;GAEG,CACoB,KAAA,IAAI,GAAG,CAAC,AAAJ,CAAK;AAChC;;GAEG,CACoB,KAAA,KAAK,GAAG,CAAC,AAAJ,CAAK;AACjC;;GAEG,CACoB,KAAA,GAAG,GAAG,CAAH,AAAI,CAAC;AAC/B;;GAEG,CACoB,KAAA,MAAM,GAAG,CAAC,AAAJ,CAAK;AAElC;;GAEG,CACW,KAAA,8BAA8B,GAAG,KAAH,AAAQ,CAAC;AA03HrD,UAAU;AACV;;GAEG,CACH,6DAA6D;AAC/C,KAAA,iBAAiB,GAAG,CAAC,UAAe,EAAE,KAAY,EAAQ,EAAE;IACtE,qKAAM,cAAW,AAAX,EAAY,YAAY,CAAC,CAAC;AACpC,CAAC,AAF8B,CAE7B;AAEF;;GAEG,CACH,6DAA6D;AAC/C,KAAA,mBAAmB,GAAG,CAAC,UAAe,EAAE,KAAY,EAAgB,EAAE;IAChF,qKAAM,cAAA,AAAW,EAAC,cAAc,CAAC,CAAC;AACtC,CAAC,AAFgC,CAE/B;AAEF;;GAEG,CACH,6DAA6D;AAC/C,KAAA,gBAAgB,GAAG,CAAC,UAAe,EAAE,KAAY,EAAQ,EAAE;IACrE,qKAAM,cAAA,AAAW,EAAC,WAAW,CAAC,CAAC;AACnC,CAAC,AAF6B,CAE5B;AAEF;;GAEG,CACH,6DAA6D;AAC/C,KAAA,sBAAsB,GAAG,CAAC,UAAe,EAAE,KAAY,EAAQ,EAAE;IAC3E,qKAAM,cAAA,AAAW,EAAC,iBAAiB,CAAC,CAAC;AACzC,CAAC,AAFmC,CAElC;AAEF;;GAEG,CACH,6DAA6D;AAC/C,KAAA,4BAA4B,GAAG,CAAC,UAAe,EAAE,KAAY,EAAQ,EAAE;IACjF,OAAM,4KAAA,AAAW,EAAC,uBAAuB,CAAC,CAAC;AAC/C,CAAC,AAFyC,CAExC;AAEF;;GAEG,CACH,6DAA6D;AAC/C,KAAA,gBAAgB,GAAG,CAAC,UAAe,EAAE,KAAY,EAAQ,EAAE;IACrE,qKAAM,cAAA,AAAW,EAAC,WAAW,CAAC,CAAC;AACnC,CAAC,AAF6B,CAE5B;gKAi7CN,gBAAA,AAAa,EAAC,cAAc,EAAE,IAAI,CAAC,CAAC", "debugId": null}}]}