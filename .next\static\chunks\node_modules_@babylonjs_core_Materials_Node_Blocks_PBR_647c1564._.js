(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/sheenBlock.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "SheenBlock": ()=>SheenBlock
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/tslib.es6.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterialBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialBlockConnectionPointTypes.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialBlockTargets.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$typeStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Misc/typeStore.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Decorators$2f$nodeDecorator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Decorators/nodeDecorator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialConnectionPointCustomObject$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterialConnectionPointCustomObject.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
class SheenBlock extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlock"] {
    /**
     * Initialize the block and prepare the context for build
     * @param state defines the state that will be used for the build
     */ initialize(state) {
        state._excludeVariableName("sheenOut");
        state._excludeVariableName("sheenMapData");
        state._excludeVariableName("vSheenColor");
        state._excludeVariableName("vSheenRoughness");
    }
    /**
     * Gets the current class name
     * @returns the class name
     */ getClassName() {
        return "SheenBlock";
    }
    /**
     * Gets the intensity input component
     */ get intensity() {
        return this._inputs[0];
    }
    /**
     * Gets the color input component
     */ get color() {
        return this._inputs[1];
    }
    /**
     * Gets the roughness input component
     */ get roughness() {
        return this._inputs[2];
    }
    /**
     * Gets the sheen object output component
     */ get sheen() {
        return this._outputs[0];
    }
    prepareDefines(defines) {
        defines.setValue("SHEEN", true);
        defines.setValue("SHEEN_USE_ROUGHNESS_FROM_MAINTEXTURE", true, true);
        defines.setValue("SHEEN_LINKWITHALBEDO", this.linkSheenWithAlbedo, true);
        defines.setValue("SHEEN_ROUGHNESS", this.roughness.isConnected, true);
        defines.setValue("SHEEN_ALBEDOSCALING", this.albedoScaling, true);
    }
    /**
     * Gets the main code of the block (fragment side)
     * @param reflectionBlock instance of a ReflectionBlock null if the code must be generated without an active reflection module
     * @param state define the build state
     * @returns the shader code
     */ getCode(reflectionBlock, state) {
        let code = "";
        const color = this.color.isConnected ? this.color.associatedVariableName : "vec3".concat(state.fSuffix, "(1.)");
        const intensity = this.intensity.isConnected ? this.intensity.associatedVariableName : "1.";
        const roughness = this.roughness.isConnected ? this.roughness.associatedVariableName : "0.";
        const texture = "vec4".concat(state.fSuffix, "(0.)");
        const isWebGPU = state.shaderLanguage === 1 /* ShaderLanguage.WGSL */ ;
        code = "#ifdef SHEEN\n            ".concat(isWebGPU ? "var sheenOut: sheenOutParams" : "sheenOutParams sheenOut", ";\n\n            ").concat(state._declareLocalVar("vSheenColor", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector4), " = vec4").concat(state.fSuffix, "(").concat(color, ", ").concat(intensity, ");\n\n            sheenOut = sheenBlock(\n                vSheenColor\n            #ifdef SHEEN_ROUGHNESS\n                , ").concat(roughness, "\n            #endif\n                , roughness\n            #ifdef SHEEN_TEXTURE\n                , ").concat(texture, "\n                ").concat(isWebGPU ? ", ".concat(texture, "Sampler") : "", "\n                , 1.0\n            #endif\n                , reflectanceF0\n            #ifdef SHEEN_LINKWITHALBEDO\n                , baseColor\n                , surfaceAlbedo\n            #endif\n            #ifdef ENVIRONMENTBRDF\n                , NdotV\n                , environmentBrdf\n            #endif\n            #if defined(REFLECTION) && defined(ENVIRONMENTBRDF)\n                , AARoughnessFactors\n                , ").concat(isWebGPU ? "uniforms." : "").concat(reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._vReflectionMicrosurfaceInfosName, "\n                , ").concat(reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._vReflectionInfosName, "\n                , ").concat(reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock.reflectionColor, "\n                , ").concat(isWebGPU ? "uniforms." : "", "vLightingIntensity\n                #ifdef ").concat(reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._define3DName, "\n                    , ").concat(reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._cubeSamplerName, "                                      \n                    ").concat(isWebGPU ? ", ".concat(reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._cubeSamplerName, "Sampler") : "", "\n                #else\n                    , ").concat(reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._2DSamplerName, "\n                    ").concat(isWebGPU ? ", ".concat(reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._2DSamplerName, "Sampler") : "", "\n                #endif\n                , reflectionOut.reflectionCoords\n                , NdotVUnclamped\n                #ifndef LODBASEDMICROSFURACE\n                    #ifdef ").concat(reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._define3DName, "\n                        , ").concat(reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._cubeSamplerName, "                        \n                        ").concat(isWebGPU ? ", ".concat(reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._cubeSamplerName, "Sampler") : "", "\n                        , ").concat(reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._cubeSamplerName, "\n                        ").concat(isWebGPU ? ", ".concat(reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._cubeSamplerName, "Sampler") : "", "\n                    #else\n                        , ").concat(reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._2DSamplerName, "\n                        ").concat(isWebGPU ? ", ".concat(reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._2DSamplerName, "Sampler") : "", "\n                        , ").concat(reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._2DSamplerName, "\n                        ").concat(isWebGPU ? ", ".concat(reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._2DSamplerName, "Sampler") : "", "\n                    #endif\n                #endif\n                #if !defined(").concat(reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._defineSkyboxName, ") && defined(RADIANCEOCCLUSION)\n                    , seo\n                #endif\n                #if !defined(").concat(reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._defineSkyboxName, ") && defined(HORIZONOCCLUSION) && defined(BUMP) && defined(").concat(reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._define3DName, ")\n                    , eho\n                #endif\n            #endif\n            );\n\n            #ifdef SHEEN_LINKWITHALBEDO\n                surfaceAlbedo = sheenOut.surfaceAlbedo;\n            #endif\n        #endif\n");
        return code;
    }
    _buildBlock(state) {
        if (state.target === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment) {
            state.sharedData.blocksWithDefines.push(this);
        }
        return this;
    }
    _dumpPropertiesCode() {
        let codeString = super._dumpPropertiesCode();
        codeString += "".concat(this._codeVariableName, ".albedoScaling = ").concat(this.albedoScaling, ";\n");
        codeString += "".concat(this._codeVariableName, ".linkSheenWithAlbedo = ").concat(this.linkSheenWithAlbedo, ";\n");
        return codeString;
    }
    serialize() {
        const serializationObject = super.serialize();
        serializationObject.albedoScaling = this.albedoScaling;
        serializationObject.linkSheenWithAlbedo = this.linkSheenWithAlbedo;
        return serializationObject;
    }
    _deserialize(serializationObject, scene, rootUrl) {
        super._deserialize(serializationObject, scene, rootUrl);
        this.albedoScaling = serializationObject.albedoScaling;
        this.linkSheenWithAlbedo = serializationObject.linkSheenWithAlbedo;
    }
    /**
     * Create a new SheenBlock
     * @param name defines the block name
     */ constructor(name){
        super(name, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        /**
         * If true, the sheen effect is layered above the base BRDF with the albedo-scaling technique.
         * It allows the strength of the sheen effect to not depend on the base color of the material,
         * making it easier to setup and tweak the effect
         */ this.albedoScaling = false;
        /**
         * Defines if the sheen is linked to the sheen color.
         */ this.linkSheenWithAlbedo = false;
        this._isUnique = true;
        this.registerInput("intensity", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float, true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerInput("color", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Color3, true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerInput("roughness", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float, true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerOutput("sheen", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Object, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialConnectionPointCustomObject$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialConnectionPointCustomObject"]("sheen", this, 1 /* NodeMaterialConnectionPointDirection.Output */ , SheenBlock, "SheenBlock"));
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Decorators$2f$nodeDecorator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["editableInPropertyPage"])("Albedo scaling", 0 /* PropertyTypeForEdition.Boolean */ , "PROPERTIES", {
        embedded: true,
        notifiers: {
            update: true
        }
    })
], SheenBlock.prototype, "albedoScaling", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Decorators$2f$nodeDecorator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["editableInPropertyPage"])("Link sheen with albedo", 0 /* PropertyTypeForEdition.Boolean */ , "PROPERTIES", {
        embedded: true,
        notifiers: {
            update: true
        }
    })
], SheenBlock.prototype, "linkSheenWithAlbedo", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$typeStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RegisterClass"])("BABYLON.SheenBlock", SheenBlock); //# sourceMappingURL=sheenBlock.js.map
}),
"[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/anisotropyBlock.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "AnisotropyBlock": ()=>AnisotropyBlock
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterialBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialBlockConnectionPointTypes.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialBlockTargets.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$typeStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Misc/typeStore.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialConnectionPointCustomObject$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterialConnectionPointCustomObject.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Fragment$2f$TBNBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Fragment/TBNBlock.js [app-client] (ecmascript)");
;
;
;
;
;
;
class AnisotropyBlock extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlock"] {
    /**
     * Initialize the block and prepare the context for build
     * @param state defines the state that will be used for the build
     */ initialize(state) {
        state._excludeVariableName("anisotropicOut");
        state._excludeVariableName("TBN");
    }
    /**
     * Gets the current class name
     * @returns the class name
     */ getClassName() {
        return "AnisotropyBlock";
    }
    /**
     * Gets the intensity input component
     */ get intensity() {
        return this._inputs[0];
    }
    /**
     * Gets the direction input component
     */ get direction() {
        return this._inputs[1];
    }
    /**
     * Gets the uv input component
     */ get uv() {
        return this._inputs[2];
    }
    /**
     * Gets the worldTangent input component
     */ get worldTangent() {
        return this._inputs[3];
    }
    /**
     * Gets the TBN input component
     */ // eslint-disable-next-line @typescript-eslint/naming-convention
    get TBN() {
        return this._inputs[4];
    }
    /**
     * Gets the roughness input component
     */ get roughness() {
        return this._inputs[5];
    }
    /**
     * Gets the anisotropy object output component
     */ get anisotropy() {
        return this._outputs[0];
    }
    _generateTBNSpace(state) {
        let code = "";
        const comments = "//".concat(this.name);
        const uv = this.uv;
        const worldPosition = this.worldPositionConnectionPoint;
        const worldNormal = this.worldNormalConnectionPoint;
        const worldTangent = this.worldTangent;
        const isWebGPU = state.shaderLanguage === 1 /* ShaderLanguage.WGSL */ ;
        if (!uv.isConnected) {
            // we must set the uv input as optional because we may not end up in this method (in case a PerturbNormal block is linked to the PBR material)
            // in which case uv is not required. But if we do come here, we do need the uv, so we have to raise an error but not with throw, else
            // it will stop the building of the node material and will lead to errors in the editor!
            state.sharedData.raiseBuildError("You must connect the 'uv' input of the ".concat(this.name, " block!"));
        }
        state._emitExtension("derivatives", "#extension GL_OES_standard_derivatives : enable");
        const tangentReplaceString = {
            search: /defined\(TANGENT\)/g,
            replace: worldTangent.isConnected ? "defined(TANGENT)" : "defined(IGNORE)"
        };
        const tbn = this.TBN;
        if (tbn.isConnected) {
            state.compilationString += "\n            #ifdef TBNBLOCK\n            ".concat(isWebGPU ? "var TBN" : "mat3 TBN", " = ").concat(tbn.associatedVariableName, ";\n            #endif\n            ");
        } else if (worldTangent.isConnected) {
            code += "".concat(state._declareLocalVar("tbnNormal", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3), " = normalize(").concat(worldNormal.associatedVariableName, ".xyz);\n");
            code += "".concat(state._declareLocalVar("tbnTangent", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3), " = normalize(").concat(worldTangent.associatedVariableName, ".xyz);\n");
            code += "".concat(state._declareLocalVar("tbnBitangent", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3), " = cross(tbnNormal, tbnTangent) * ").concat(this._tangentCorrectionFactorName, ";\n");
            code += "".concat(isWebGPU ? "var vTBN" : "mat3 vTBN", " = ").concat(isWebGPU ? "mat3x3f" : "mat3", "(tbnTangent, tbnBitangent, tbnNormal);\n");
        }
        code += "\n            #if defined(".concat(worldTangent.isConnected ? "TANGENT" : "IGNORE", ") && defined(NORMAL)\n                ").concat(isWebGPU ? "var TBN" : "mat3 TBN", " = vTBN;\n            #else\n                ").concat(isWebGPU ? "var TBN" : "mat3 TBN", " = cotangent_frame(").concat(worldNormal.associatedVariableName + ".xyz", ", ").concat("v_" + worldPosition.associatedVariableName + ".xyz", ", ").concat(uv.isConnected ? uv.associatedVariableName : "vec2(0.)", ", vec2").concat(state.fSuffix, "(1., 1.));\n            #endif\n");
        state._emitFunctionFromInclude("bumpFragmentMainFunctions", comments, {
            replaceStrings: [
                tangentReplaceString
            ]
        });
        return code;
    }
    /**
     * Gets the main code of the block (fragment side)
     * @param state current state of the node material building
     * @param generateTBNSpace if true, the code needed to create the TBN coordinate space is generated
     * @returns the shader code
     */ getCode(state) {
        let generateTBNSpace = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;
        let code = "";
        if (generateTBNSpace) {
            code += this._generateTBNSpace(state);
        }
        const isWebGPU = state.shaderLanguage === 1 /* ShaderLanguage.WGSL */ ;
        const intensity = this.intensity.isConnected ? this.intensity.associatedVariableName : "1.0";
        const direction = this.direction.isConnected ? this.direction.associatedVariableName : "vec2(1., 0.)";
        const roughness = this.roughness.isConnected ? this.roughness.associatedVariableName : "0.";
        code += "".concat(isWebGPU ? "var anisotropicOut: anisotropicOutParams" : "anisotropicOutParams anisotropicOut", ";\n            anisotropicOut = anisotropicBlock(\n                vec3(").concat(direction, ", ").concat(intensity, "),\n                ").concat(roughness, ",\n            #ifdef ANISOTROPIC_TEXTURE\n                vec3(0.),\n            #endif\n                TBN,\n                normalW,\n                viewDirectionW\n            );\n");
        return code;
    }
    prepareDefines(defines) {
        defines.setValue("ANISOTROPIC", true);
        defines.setValue("ANISOTROPIC_TEXTURE", false, true);
        defines.setValue("ANISOTROPIC_LEGACY", !this.roughness.isConnected);
    }
    bind(effect, nodeMaterial, mesh) {
        super.bind(effect, nodeMaterial, mesh);
        if (mesh) {
            effect.setFloat(this._tangentCorrectionFactorName, mesh.getWorldMatrix().determinant() < 0 ? -1 : 1);
        }
    }
    _buildBlock(state) {
        if (state.target === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment) {
            state.sharedData.blocksWithDefines.push(this);
            state.sharedData.bindableBlocks.push(this);
            this._tangentCorrectionFactorName = state._getFreeDefineName("tangentCorrectionFactor");
            state._emitUniformFromString(this._tangentCorrectionFactorName, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float);
        }
        return this;
    }
    /**
     * Create a new AnisotropyBlock
     * @param name defines the block name
     */ constructor(name){
        super(name, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this._tangentCorrectionFactorName = "";
        this._isUnique = true;
        this.registerInput("intensity", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float, true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerInput("direction", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector2, true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerInput("uv", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector2, true); // need this property and the next one in case there's no PerturbNormal block connected to the main PBR block
        this.registerInput("worldTangent", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector4, true);
        this.registerInput("TBN", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Object, true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].VertexAndFragment, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialConnectionPointCustomObject$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialConnectionPointCustomObject"]("TBN", this, 0 /* NodeMaterialConnectionPointDirection.Input */ , __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Fragment$2f$TBNBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TBNBlock"], "TBNBlock"));
        this.registerInput("roughness", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float, true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerOutput("anisotropy", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Object, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialConnectionPointCustomObject$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialConnectionPointCustomObject"]("anisotropy", this, 1 /* NodeMaterialConnectionPointDirection.Output */ , AnisotropyBlock, "AnisotropyBlock"));
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$typeStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RegisterClass"])("BABYLON.AnisotropyBlock", AnisotropyBlock); //# sourceMappingURL=anisotropyBlock.js.map
}),
"[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/reflectionBlock.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "ReflectionBlock": ()=>ReflectionBlock
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/tslib.es6.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialBlockConnectionPointTypes.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialBlockTargets.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$typeStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Misc/typeStore.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialConnectionPointCustomObject$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterialConnectionPointCustomObject.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Dual$2f$reflectionTextureBaseBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Dual/reflectionTextureBaseBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Textures$2f$texture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Textures/texture.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Decorators$2f$nodeDecorator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Decorators/nodeDecorator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$logger$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Misc/logger.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
class ReflectionBlock extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Dual$2f$reflectionTextureBaseBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ReflectionTextureBaseBlock"] {
    _onGenerateOnlyFragmentCodeChanged() {
        if (this.position.isConnected) {
            this.generateOnlyFragmentCode = !this.generateOnlyFragmentCode;
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$logger$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Logger"].Error("The position input must not be connected to be able to switch!");
            return false;
        }
        this._setTarget();
        return true;
    }
    _setTarget() {
        super._setTarget();
        this.getInputByName("position").target = this.generateOnlyFragmentCode ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Vertex;
        if (this.generateOnlyFragmentCode) {
            this.forceIrradianceInFragment = true;
        }
    }
    /**
     * Gets the current class name
     * @returns the class name
     */ getClassName() {
        return "ReflectionBlock";
    }
    /**
     * Gets the position input component
     */ get position() {
        return this._inputs[0];
    }
    /**
     * Gets the world position input component
     */ get worldPosition() {
        return this.worldPositionConnectionPoint;
    }
    /**
     * Gets the world normal input component
     */ get worldNormal() {
        return this.worldNormalConnectionPoint;
    }
    /**
     * Gets the world input component
     */ get world() {
        return this._inputs[1];
    }
    /**
     * Gets the camera (or eye) position component
     */ get cameraPosition() {
        return this.cameraPositionConnectionPoint;
    }
    /**
     * Gets the view input component
     */ get view() {
        return this.viewConnectionPoint;
    }
    /**
     * Gets the color input component
     */ get color() {
        return this._inputs[2];
    }
    /**
     * Gets the reflection object output component
     */ get reflection() {
        return this._outputs[0];
    }
    /**
     * Returns true if the block has a texture (either its own texture or the environment texture from the scene, if set)
     */ get hasTexture() {
        return !!this._getTexture();
    }
    /**
     * Gets the reflection color (either the name of the variable if the color input is connected, else a default value)
     */ get reflectionColor() {
        return this.color.isConnected ? this.color.associatedVariableName : "vec3(1., 1., 1.)";
    }
    _getTexture() {
        if (this.texture) {
            return this.texture;
        }
        return this._scene.environmentTexture;
    }
    prepareDefines(defines) {
        super.prepareDefines(defines);
        const reflectionTexture = this._getTexture();
        const reflection = reflectionTexture && reflectionTexture.getTextureMatrix;
        defines.setValue("REFLECTION", reflection, true);
        if (!reflection) {
            return;
        }
        defines.setValue(this._defineLODReflectionAlpha, reflectionTexture.lodLevelInAlpha, true);
        defines.setValue(this._defineLinearSpecularReflection, reflectionTexture.linearSpecularLOD, true);
        defines.setValue(this._defineOppositeZ, this._scene.useRightHandedSystem ? !reflectionTexture.invertZ : reflectionTexture.invertZ, true);
        defines.setValue("SPHERICAL_HARMONICS", this.useSphericalHarmonics, true);
        defines.setValue("GAMMAREFLECTION", reflectionTexture.gammaSpace, true);
        defines.setValue("RGBDREFLECTION", reflectionTexture.isRGBD, true);
        if (reflectionTexture && reflectionTexture.coordinatesMode !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Textures$2f$texture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Texture"].SKYBOX_MODE) {
            if (reflectionTexture.isCube) {
                defines.setValue("USESPHERICALFROMREFLECTIONMAP", true);
                defines.setValue("USEIRRADIANCEMAP", false);
                if (this.forceIrradianceInFragment || this._scene.getEngine().getCaps().maxVaryingVectors <= 8) {
                    defines.setValue("USESPHERICALINVERTEX", false);
                } else {
                    defines.setValue("USESPHERICALINVERTEX", true);
                }
            }
        }
    }
    bind(effect, nodeMaterial, mesh, subMesh) {
        super.bind(effect, nodeMaterial, mesh);
        const reflectionTexture = this._getTexture();
        if (!reflectionTexture || !subMesh) {
            return;
        }
        if (reflectionTexture.isCube) {
            effect.setTexture(this._cubeSamplerName, reflectionTexture);
        } else {
            effect.setTexture(this._2DSamplerName, reflectionTexture);
        }
        const width = reflectionTexture.getSize().width;
        effect.setFloat3(this._vReflectionMicrosurfaceInfosName, width, reflectionTexture.lodGenerationScale, reflectionTexture.lodGenerationOffset);
        effect.setFloat2(this._vReflectionFilteringInfoName, width, Math.log2(width));
        const defines = subMesh.materialDefines;
        const polynomials = reflectionTexture.sphericalPolynomial;
        if (defines.USESPHERICALFROMREFLECTIONMAP && polynomials) {
            if (defines.SPHERICAL_HARMONICS) {
                const preScaledHarmonics = polynomials.preScaledHarmonics;
                effect.setVector3("vSphericalL00", preScaledHarmonics.l00);
                effect.setVector3("vSphericalL1_1", preScaledHarmonics.l1_1);
                effect.setVector3("vSphericalL10", preScaledHarmonics.l10);
                effect.setVector3("vSphericalL11", preScaledHarmonics.l11);
                effect.setVector3("vSphericalL2_2", preScaledHarmonics.l2_2);
                effect.setVector3("vSphericalL2_1", preScaledHarmonics.l2_1);
                effect.setVector3("vSphericalL20", preScaledHarmonics.l20);
                effect.setVector3("vSphericalL21", preScaledHarmonics.l21);
                effect.setVector3("vSphericalL22", preScaledHarmonics.l22);
            } else {
                effect.setFloat3("vSphericalX", polynomials.x.x, polynomials.x.y, polynomials.x.z);
                effect.setFloat3("vSphericalY", polynomials.y.x, polynomials.y.y, polynomials.y.z);
                effect.setFloat3("vSphericalZ", polynomials.z.x, polynomials.z.y, polynomials.z.z);
                effect.setFloat3("vSphericalXX_ZZ", polynomials.xx.x - polynomials.zz.x, polynomials.xx.y - polynomials.zz.y, polynomials.xx.z - polynomials.zz.z);
                effect.setFloat3("vSphericalYY_ZZ", polynomials.yy.x - polynomials.zz.x, polynomials.yy.y - polynomials.zz.y, polynomials.yy.z - polynomials.zz.z);
                effect.setFloat3("vSphericalZZ", polynomials.zz.x, polynomials.zz.y, polynomials.zz.z);
                effect.setFloat3("vSphericalXY", polynomials.xy.x, polynomials.xy.y, polynomials.xy.z);
                effect.setFloat3("vSphericalYZ", polynomials.yz.x, polynomials.yz.y, polynomials.yz.z);
                effect.setFloat3("vSphericalZX", polynomials.zx.x, polynomials.zx.y, polynomials.zx.z);
            }
        }
    }
    /**
     * Gets the code to inject in the vertex shader
     * @param state current state of the node material building
     * @returns the shader code
     */ handleVertexSide(state) {
        let code = super.handleVertexSide(state);
        const isWebGPU = state.shaderLanguage === 1 /* ShaderLanguage.WGSL */ ;
        state._emitFunctionFromInclude("harmonicsFunctions", "//".concat(this.name), {
            replaceStrings: [
                {
                    search: /uniform vec3 vSphericalL00;[\s\S]*?uniform vec3 vSphericalL22;/g,
                    replace: ""
                },
                {
                    search: /uniform vec3 vSphericalX;[\s\S]*?uniform vec3 vSphericalZX;/g,
                    replace: ""
                }
            ]
        });
        const reflectionVectorName = state._getFreeVariableName("reflectionVector");
        this._vEnvironmentIrradianceName = state._getFreeVariableName("vEnvironmentIrradiance");
        state._emitVaryingFromString(this._vEnvironmentIrradianceName, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3, "defined(USESPHERICALFROMREFLECTIONMAP) && defined(USESPHERICALINVERTEX)");
        state._emitUniformFromString("vSphericalL00", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3, "SPHERICAL_HARMONICS");
        state._emitUniformFromString("vSphericalL1_1", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3, "SPHERICAL_HARMONICS");
        state._emitUniformFromString("vSphericalL10", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3, "SPHERICAL_HARMONICS");
        state._emitUniformFromString("vSphericalL11", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3, "SPHERICAL_HARMONICS");
        state._emitUniformFromString("vSphericalL2_2", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3, "SPHERICAL_HARMONICS");
        state._emitUniformFromString("vSphericalL2_1", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3, "SPHERICAL_HARMONICS");
        state._emitUniformFromString("vSphericalL20", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3, "SPHERICAL_HARMONICS");
        state._emitUniformFromString("vSphericalL21", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3, "SPHERICAL_HARMONICS");
        state._emitUniformFromString("vSphericalL22", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3, "SPHERICAL_HARMONICS");
        state._emitUniformFromString("vSphericalX", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3, "SPHERICAL_HARMONICS", true);
        state._emitUniformFromString("vSphericalY", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3, "SPHERICAL_HARMONICS", true);
        state._emitUniformFromString("vSphericalZ", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3, "SPHERICAL_HARMONICS", true);
        state._emitUniformFromString("vSphericalXX_ZZ", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3, "SPHERICAL_HARMONICS", true);
        state._emitUniformFromString("vSphericalYY_ZZ", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3, "SPHERICAL_HARMONICS", true);
        state._emitUniformFromString("vSphericalZZ", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3, "SPHERICAL_HARMONICS", true);
        state._emitUniformFromString("vSphericalXY", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3, "SPHERICAL_HARMONICS", true);
        state._emitUniformFromString("vSphericalYZ", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3, "SPHERICAL_HARMONICS", true);
        state._emitUniformFromString("vSphericalZX", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3, "SPHERICAL_HARMONICS", true);
        code += "#if defined(USESPHERICALFROMREFLECTIONMAP) && defined(USESPHERICALINVERTEX)\n                ".concat(state._declareLocalVar(reflectionVectorName, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3), " = (").concat((isWebGPU ? "uniforms." : "") + this._reflectionMatrixName, " * vec4").concat(state.fSuffix, "(normalize(").concat(this.worldNormal.associatedVariableName, ").xyz, 0)).xyz;\n                #ifdef ").concat(this._defineOppositeZ, "\n                    ").concat(reflectionVectorName, ".z *= -1.0;\n                #endif\n                ").concat(isWebGPU ? "vertexOutputs." : "").concat(this._vEnvironmentIrradianceName, " = computeEnvironmentIrradiance(").concat(reflectionVectorName, ");\n            #endif\n");
        return code;
    }
    /**
     * Gets the main code of the block (fragment side)
     * @param state current state of the node material building
     * @param normalVarName name of the existing variable corresponding to the normal
     * @returns the shader code
     */ getCode(state, normalVarName) {
        let code = "";
        this.handleFragmentSideInits(state);
        const isWebGPU = state.shaderLanguage === 1 /* ShaderLanguage.WGSL */ ;
        state._emitFunctionFromInclude("harmonicsFunctions", "//".concat(this.name), {
            replaceStrings: [
                {
                    search: /uniform vec3 vSphericalL00;[\s\S]*?uniform vec3 vSphericalL22;/g,
                    replace: ""
                },
                {
                    search: /uniform vec3 vSphericalX;[\s\S]*?uniform vec3 vSphericalZX;/g,
                    replace: ""
                }
            ]
        });
        if (!isWebGPU) {
            state._emitFunction("sampleReflection", "\n                #ifdef ".concat(this._define3DName, "\n                    #define sampleReflection(s, c) textureCube(s, c)\n                #else\n                    #define sampleReflection(s, c) texture2D(s, c)\n                #endif\n"), "//".concat(this.name));
            state._emitFunction("sampleReflectionLod", "\n                #ifdef ".concat(this._define3DName, "\n                    #define sampleReflectionLod(s, c, l) textureCubeLodEXT(s, c, l)\n                #else\n                    #define sampleReflectionLod(s, c, l) texture2DLodEXT(s, c, l)\n                #endif\n"), "//".concat(this.name));
        }
        const computeReflectionCoordsFunc = isWebGPU ? "\n            fn computeReflectionCoordsPBR(worldPos: vec4f, worldNormal: vec3f) -> vec3f {\n                ".concat(this.handleFragmentSideCodeReflectionCoords(state, "worldNormal", "worldPos", true, true), "\n                return ").concat(this._reflectionVectorName, ";\n            }\n") : "\n            vec3 computeReflectionCoordsPBR(vec4 worldPos, vec3 worldNormal) {\n                ".concat(this.handleFragmentSideCodeReflectionCoords(state, "worldNormal", "worldPos", true, true), "\n                return ").concat(this._reflectionVectorName, ";\n            }\n");
        state._emitFunction("computeReflectionCoordsPBR", computeReflectionCoordsFunc, "//".concat(this.name));
        this._vReflectionMicrosurfaceInfosName = state._getFreeVariableName("vReflectionMicrosurfaceInfos");
        state._emitUniformFromString(this._vReflectionMicrosurfaceInfosName, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3);
        this._vReflectionInfosName = state._getFreeVariableName("vReflectionInfos");
        this._vReflectionFilteringInfoName = state._getFreeVariableName("vReflectionFilteringInfo");
        state._emitUniformFromString(this._vReflectionFilteringInfoName, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector2);
        code += "#ifdef REFLECTION\n            ".concat(state._declareLocalVar(this._vReflectionInfosName, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector2), " = vec2").concat(state.fSuffix, "(1., 0.);\n\n            ").concat(isWebGPU ? "var reflectionOut: reflectionOutParams" : "reflectionOutParams reflectionOut", ";\n\n            reflectionOut = reflectionBlock(\n                ").concat(this.generateOnlyFragmentCode ? this._worldPositionNameInFragmentOnlyMode : (isWebGPU ? "input." : "") + "v_" + this.worldPosition.associatedVariableName, ".xyz\n                , ").concat(normalVarName, "\n                , alphaG\n                , ").concat((isWebGPU ? "uniforms." : "") + this._vReflectionMicrosurfaceInfosName, "\n                , ").concat(this._vReflectionInfosName, "\n                , ").concat(this.reflectionColor, "\n            #ifdef ANISOTROPIC\n                ,anisotropicOut\n            #endif\n            #if defined(").concat(this._defineLODReflectionAlpha, ") && !defined(").concat(this._defineSkyboxName, ")\n                ,NdotVUnclamped\n            #endif\n            #ifdef ").concat(this._defineLinearSpecularReflection, "\n                , roughness\n            #endif\n            #ifdef ").concat(this._define3DName, "\n                , ").concat(this._cubeSamplerName, "\n                ").concat(isWebGPU ? ", ".concat(this._cubeSamplerName, "Sampler") : "", "\n            #else\n                , ").concat(this._2DSamplerName, "\n                ").concat(isWebGPU ? ", ".concat(this._2DSamplerName, "Sampler") : "", "\n            #endif\n            #if defined(NORMAL) && defined(USESPHERICALINVERTEX)\n                , ").concat(isWebGPU ? "input." : "").concat(this._vEnvironmentIrradianceName, "\n            #endif\n            #if (defined(USESPHERICALFROMREFLECTIONMAP) && (!defined(NORMAL) || !defined(USESPHERICALINVERTEX))) || (defined(USEIRRADIANCEMAP) && defined(REFLECTIONMAP_3D))\n                    , ").concat(this._reflectionMatrixName, "\n            #endif\n            #ifdef USEIRRADIANCEMAP\n                , irradianceSampler         // ** not handled **\n                ").concat(isWebGPU ? ", irradianceSamplerSampler" : "", "\n                #ifdef USE_IRRADIANCE_DOMINANT_DIRECTION\n                , vReflectionDominantDirection\n                #endif\n            #endif\n            #ifndef LODBASEDMICROSFURACE\n                #ifdef ").concat(this._define3DName, "\n                    , ").concat(this._cubeSamplerName, "\n                    ").concat(isWebGPU ? ", ".concat(this._cubeSamplerName, "Sampler") : "", "\n                    , ").concat(this._cubeSamplerName, "\n                    ").concat(isWebGPU ? ", ".concat(this._cubeSamplerName, "Sampler") : "", "\n                #else\n                    , ").concat(this._2DSamplerName, "\n                    ").concat(isWebGPU ? ", ".concat(this._2DSamplerName, "Sampler") : "", "\n                    , ").concat(this._2DSamplerName, "                    \n                    ").concat(isWebGPU ? ", ".concat(this._2DSamplerName, "Sampler") : "", "\n                #endif\n            #endif\n            #ifdef REALTIME_FILTERING\n                , ").concat(this._vReflectionFilteringInfoName, "\n                #ifdef IBL_CDF_FILTERING\n                    , icdfSampler         // ** not handled **\n                    ").concat(isWebGPU ? ", icdfSamplerSampler" : "", "\n                #endif\n            #endif\n            , viewDirectionW\n            , diffuseRoughness\n            , surfaceAlbedo\n            );\n        #endif\n");
        return code;
    }
    _buildBlock(state) {
        this._scene = state.sharedData.scene;
        if (state.target !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment) {
            this._defineLODReflectionAlpha = state._getFreeDefineName("LODINREFLECTIONALPHA");
            this._defineLinearSpecularReflection = state._getFreeDefineName("LINEARSPECULARREFLECTION");
        }
        return this;
    }
    _dumpPropertiesCode() {
        let codeString = super._dumpPropertiesCode();
        if (this.texture) {
            codeString += "".concat(this._codeVariableName, ".texture.gammaSpace = ").concat(this.texture.gammaSpace, ";\n");
        }
        codeString += "".concat(this._codeVariableName, ".useSphericalHarmonics = ").concat(this.useSphericalHarmonics, ";\n");
        codeString += "".concat(this._codeVariableName, ".forceIrradianceInFragment = ").concat(this.forceIrradianceInFragment, ";\n");
        return codeString;
    }
    serialize() {
        var _this_texture;
        const serializationObject = super.serialize();
        serializationObject.useSphericalHarmonics = this.useSphericalHarmonics;
        serializationObject.forceIrradianceInFragment = this.forceIrradianceInFragment;
        var _this_texture_gammaSpace;
        serializationObject.gammaSpace = (_this_texture_gammaSpace = (_this_texture = this.texture) === null || _this_texture === void 0 ? void 0 : _this_texture.gammaSpace) !== null && _this_texture_gammaSpace !== void 0 ? _this_texture_gammaSpace : true;
        return serializationObject;
    }
    _deserialize(serializationObject, scene, rootUrl) {
        super._deserialize(serializationObject, scene, rootUrl);
        this.useSphericalHarmonics = serializationObject.useSphericalHarmonics;
        this.forceIrradianceInFragment = serializationObject.forceIrradianceInFragment;
        if (this.texture) {
            this.texture.gammaSpace = serializationObject.gammaSpace;
        }
    }
    /**
     * Create a new ReflectionBlock
     * @param name defines the block name
     */ constructor(name){
        super(name);
        /**
         * Defines if the material uses spherical harmonics vs spherical polynomials for the
         * diffuse part of the IBL.
         */ this.useSphericalHarmonics = true;
        /**
         * Force the shader to compute irradiance in the fragment shader in order to take bump in account.
         */ this.forceIrradianceInFragment = false;
        this._isUnique = true;
        this.registerInput("position", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].AutoDetect, false, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Vertex);
        this.registerInput("world", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Matrix, false, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Vertex);
        this.registerInput("color", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Color3, true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerOutput("reflection", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Object, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialConnectionPointCustomObject$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialConnectionPointCustomObject"]("reflection", this, 1 /* NodeMaterialConnectionPointDirection.Output */ , ReflectionBlock, "ReflectionBlock"));
        this.position.addExcludedConnectionPointFromAllowedTypes(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Color3 | __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3 | __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector4);
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Decorators$2f$nodeDecorator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["editableInPropertyPage"])("Spherical Harmonics", 0 /* PropertyTypeForEdition.Boolean */ , "ADVANCED", {
        embedded: true,
        notifiers: {
            update: true
        }
    })
], ReflectionBlock.prototype, "useSphericalHarmonics", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Decorators$2f$nodeDecorator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["editableInPropertyPage"])("Force irradiance in fragment", 0 /* PropertyTypeForEdition.Boolean */ , "ADVANCED", {
        embedded: true,
        notifiers: {
            update: true
        }
    })
], ReflectionBlock.prototype, "forceIrradianceInFragment", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$typeStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RegisterClass"])("BABYLON.ReflectionBlock", ReflectionBlock); //# sourceMappingURL=reflectionBlock.js.map
}),
"[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/clearCoatBlock.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "ClearCoatBlock": ()=>ClearCoatBlock
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/tslib.es6.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterialBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialBlockConnectionPointTypes.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialBlockTargets.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$typeStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Misc/typeStore.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Input$2f$inputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Input/inputBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialConnectionPointCustomObject$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterialConnectionPointCustomObject.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$PBR$2f$pbrClearCoatConfiguration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/PBR/pbrClearCoatConfiguration.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Decorators$2f$nodeDecorator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Decorators/nodeDecorator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Fragment$2f$TBNBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Fragment/TBNBlock.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
class ClearCoatBlock extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlock"] {
    /**
     * Initialize the block and prepare the context for build
     * @param state defines the state that will be used for the build
     */ initialize(state) {
        state._excludeVariableName("clearcoatOut");
        state._excludeVariableName("vClearCoatParams");
        state._excludeVariableName("vClearCoatTintParams");
        state._excludeVariableName("vClearCoatRefractionParams");
        state._excludeVariableName("vClearCoatTangentSpaceParams");
        state._excludeVariableName("vGeometricNormaClearCoatW");
    }
    /**
     * Gets the current class name
     * @returns the class name
     */ getClassName() {
        return "ClearCoatBlock";
    }
    /**
     * Gets the intensity input component
     */ get intensity() {
        return this._inputs[0];
    }
    /**
     * Gets the roughness input component
     */ get roughness() {
        return this._inputs[1];
    }
    /**
     * Gets the ior input component
     */ get indexOfRefraction() {
        return this._inputs[2];
    }
    /**
     * Gets the bump texture input component
     */ get normalMapColor() {
        return this._inputs[3];
    }
    /**
     * Gets the uv input component
     */ get uv() {
        return this._inputs[4];
    }
    /**
     * Gets the tint color input component
     */ get tintColor() {
        return this._inputs[5];
    }
    /**
     * Gets the tint "at distance" input component
     */ get tintAtDistance() {
        return this._inputs[6];
    }
    /**
     * Gets the tint thickness input component
     */ get tintThickness() {
        return this._inputs[7];
    }
    /**
     * Gets the world tangent input component
     */ get worldTangent() {
        return this._inputs[8];
    }
    /**
     * Gets the world normal input component
     */ get worldNormal() {
        return this._inputs[9];
    }
    /**
     * Gets the TBN input component
     */ // eslint-disable-next-line @typescript-eslint/naming-convention
    get TBN() {
        return this._inputs[10];
    }
    /**
     * Gets the clear coat object output component
     */ get clearcoat() {
        return this._outputs[0];
    }
    autoConfigure() {
        if (!this.intensity.isConnected) {
            const intensityInput = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Input$2f$inputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InputBlock"]("ClearCoat intensity", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float);
            intensityInput.value = 1;
            intensityInput.output.connectTo(this.intensity);
        }
    }
    prepareDefines(defines) {
        defines.setValue("CLEARCOAT", true);
        defines.setValue("CLEARCOAT_TEXTURE", false, true);
        defines.setValue("CLEARCOAT_USE_ROUGHNESS_FROM_MAINTEXTURE", true, true);
        defines.setValue("CLEARCOAT_TINT", this.tintColor.isConnected || this.tintThickness.isConnected || this.tintAtDistance.isConnected, true);
        defines.setValue("CLEARCOAT_BUMP", this.normalMapColor.isConnected, true);
        defines.setValue("CLEARCOAT_DEFAULTIOR", this.indexOfRefraction.isConnected ? this.indexOfRefraction.connectInputBlock.value === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$PBR$2f$pbrClearCoatConfiguration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PBRClearCoatConfiguration"]._DefaultIndexOfRefraction : true, true);
        defines.setValue("CLEARCOAT_REMAP_F0", this.remapF0OnInterfaceChange, true);
    }
    bind(effect, nodeMaterial, mesh) {
        var _this_indexOfRefraction_connectInputBlock;
        super.bind(effect, nodeMaterial, mesh);
        var _this_indexOfRefraction_connectInputBlock_value;
        // Clear Coat Refraction params
        const indexOfRefraction = (_this_indexOfRefraction_connectInputBlock_value = (_this_indexOfRefraction_connectInputBlock = this.indexOfRefraction.connectInputBlock) === null || _this_indexOfRefraction_connectInputBlock === void 0 ? void 0 : _this_indexOfRefraction_connectInputBlock.value) !== null && _this_indexOfRefraction_connectInputBlock_value !== void 0 ? _this_indexOfRefraction_connectInputBlock_value : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$PBR$2f$pbrClearCoatConfiguration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PBRClearCoatConfiguration"]._DefaultIndexOfRefraction;
        const a = 1 - indexOfRefraction;
        const b = 1 + indexOfRefraction;
        const f0 = Math.pow(-a / b, 2); // Schlicks approx: (ior1 - ior2) / (ior1 + ior2) where ior2 for air is close to vacuum = 1.
        const eta = 1 / indexOfRefraction;
        effect.setFloat4("vClearCoatRefractionParams", f0, eta, a, b);
        // Clear Coat tangent space params
        const mainPBRBlock = this.clearcoat.hasEndpoints ? this.clearcoat.endpoints[0].ownerBlock : null;
        const perturbedNormalBlock = (mainPBRBlock === null || mainPBRBlock === void 0 ? void 0 : mainPBRBlock.perturbedNormal.isConnected) ? mainPBRBlock.perturbedNormal.connectedPoint.ownerBlock : null;
        if (this._scene._mirroredCameraPosition) {
            effect.setFloat2("vClearCoatTangentSpaceParams", (perturbedNormalBlock === null || perturbedNormalBlock === void 0 ? void 0 : perturbedNormalBlock.invertX) ? 1.0 : -1.0, (perturbedNormalBlock === null || perturbedNormalBlock === void 0 ? void 0 : perturbedNormalBlock.invertY) ? 1.0 : -1.0);
        } else {
            effect.setFloat2("vClearCoatTangentSpaceParams", (perturbedNormalBlock === null || perturbedNormalBlock === void 0 ? void 0 : perturbedNormalBlock.invertX) ? -1.0 : 1.0, (perturbedNormalBlock === null || perturbedNormalBlock === void 0 ? void 0 : perturbedNormalBlock.invertY) ? -1.0 : 1.0);
        }
        if (mesh) {
            effect.setFloat(this._tangentCorrectionFactorName, mesh.getWorldMatrix().determinant() < 0 ? -1 : 1);
        }
    }
    _generateTBNSpace(state, worldPositionVarName, worldNormalVarName) {
        let code = "";
        const comments = "//".concat(this.name);
        const worldTangent = this.worldTangent;
        const isWebGPU = state.shaderLanguage === 1 /* ShaderLanguage.WGSL */ ;
        if (!isWebGPU) {
            state._emitExtension("derivatives", "#extension GL_OES_standard_derivatives : enable");
        }
        const tangentReplaceString = {
            search: /defined\(TANGENT\)/g,
            replace: worldTangent.isConnected ? "defined(TANGENT)" : "defined(IGNORE)"
        };
        const tbn = this.TBN;
        if (tbn.isConnected) {
            state.compilationString += "\n            #ifdef TBNBLOCK\n                ".concat(isWebGPU ? "var TBN" : "mat3 TBN", " = ").concat(tbn.associatedVariableName, ";\n            #endif\n            ");
        } else if (worldTangent.isConnected) {
            code += "".concat(state._declareLocalVar("tbnNormal", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3), " = normalize(").concat(worldNormalVarName, ".xyz);\n");
            code += "".concat(state._declareLocalVar("tbnTangent", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3), " = normalize(").concat(worldTangent.associatedVariableName, ".xyz);\n");
            code += "".concat(state._declareLocalVar("tbnBitangent", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3), " = cross(tbnNormal, tbnTangent) * ").concat(this._tangentCorrectionFactorName, ";\n");
            code += "".concat(isWebGPU ? "var vTBN" : "mat3 vTBN", " = ").concat(isWebGPU ? "mat3x3f" : "mat3", "(tbnTangent, tbnBitangent, tbnNormal);\n");
        }
        state._emitFunctionFromInclude("bumpFragmentMainFunctions", comments, {
            replaceStrings: [
                tangentReplaceString
            ]
        });
        return code;
    }
    /** @internal */ static _GetInitializationCode(state, ccBlock) {
        let code = "";
        const intensity = (ccBlock === null || ccBlock === void 0 ? void 0 : ccBlock.intensity.isConnected) ? ccBlock.intensity.associatedVariableName : "1.";
        const roughness = (ccBlock === null || ccBlock === void 0 ? void 0 : ccBlock.roughness.isConnected) ? ccBlock.roughness.associatedVariableName : "0.";
        const tintColor = (ccBlock === null || ccBlock === void 0 ? void 0 : ccBlock.tintColor.isConnected) ? ccBlock.tintColor.associatedVariableName : "vec3".concat(state.fSuffix, "(1.)");
        const tintThickness = (ccBlock === null || ccBlock === void 0 ? void 0 : ccBlock.tintThickness.isConnected) ? ccBlock.tintThickness.associatedVariableName : "1.";
        code += "\n            #ifdef CLEARCOAT\n                ".concat(state._declareLocalVar("vClearCoatParams", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector2), " = vec2").concat(state.fSuffix, "(").concat(intensity, ", ").concat(roughness, ");\n                ").concat(state._declareLocalVar("vClearCoatTintParams", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector4), " = vec4").concat(state.fSuffix, "(").concat(tintColor, ", ").concat(tintThickness, ");\n            #endif\n");
        return code;
    }
    /**
     * Gets the main code of the block (fragment side)
     * @param state current state of the node material building
     * @param ccBlock instance of a ClearCoatBlock or null if the code must be generated without an active clear coat module
     * @param reflectionBlock instance of a ReflectionBlock null if the code must be generated without an active reflection module
     * @param worldPosVarName name of the variable holding the world position
     * @param generateTBNSpace if true, the code needed to create the TBN coordinate space is generated
     * @param vTBNAvailable indicate that the vTBN variable is already existing because it has already been generated by another block (PerturbNormal or Anisotropy)
     * @param worldNormalVarName name of the variable holding the world normal
     * @returns the shader code
     */ static GetCode(state, ccBlock, reflectionBlock, worldPosVarName, generateTBNSpace, vTBNAvailable, worldNormalVarName) {
        let code = "";
        const normalMapColor = (ccBlock === null || ccBlock === void 0 ? void 0 : ccBlock.normalMapColor.isConnected) ? ccBlock.normalMapColor.associatedVariableName : "vec3".concat(state.fSuffix, "(0.)");
        const uv = (ccBlock === null || ccBlock === void 0 ? void 0 : ccBlock.uv.isConnected) ? ccBlock.uv.associatedVariableName : "vec2".concat(state.fSuffix, "(0.)");
        const tintAtDistance = (ccBlock === null || ccBlock === void 0 ? void 0 : ccBlock.tintAtDistance.isConnected) ? ccBlock.tintAtDistance.associatedVariableName : "1.";
        const tintTexture = "vec4".concat(state.fSuffix, "(0.)");
        if (ccBlock) {
            state._emitUniformFromString("vClearCoatRefractionParams", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector4);
            state._emitUniformFromString("vClearCoatTangentSpaceParams", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector2);
            const normalShading = ccBlock.worldNormal;
            code += "".concat(state._declareLocalVar("vGeometricNormaClearCoatW", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3), " = ").concat(normalShading.isConnected ? "normalize(" + normalShading.associatedVariableName + ".xyz)" : "geometricNormalW", ";\n");
        } else {
            code += "".concat(state._declareLocalVar("vGeometricNormaClearCoatW", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3), " = geometricNormalW;\n");
        }
        if (generateTBNSpace && ccBlock) {
            code += ccBlock._generateTBNSpace(state, worldPosVarName, worldNormalVarName);
            vTBNAvailable = ccBlock.worldTangent.isConnected;
        }
        const isWebGPU = state.shaderLanguage === 1 /* ShaderLanguage.WGSL */ ;
        code += "".concat(isWebGPU ? "var clearcoatOut: clearcoatOutParams" : "clearcoatOutParams clearcoatOut", ";\n\n        #ifdef CLEARCOAT\n            clearcoatOut = clearcoatBlock(\n                ").concat(worldPosVarName, ".xyz\n                , vGeometricNormaClearCoatW\n                , viewDirectionW\n                , vClearCoatParams\n                , specularEnvironmentR0\n            #ifdef CLEARCOAT_TEXTURE\n                , vec2").concat(state.fSuffix, "(0.)\n            #endif\n            #ifdef CLEARCOAT_TINT\n                , vClearCoatTintParams\n                , ").concat(tintAtDistance, "\n                , ").concat(isWebGPU ? "uniforms." : "", "vClearCoatRefractionParams\n                #ifdef CLEARCOAT_TINT_TEXTURE\n                    , ").concat(tintTexture, "\n                #endif\n            #endif\n            #ifdef CLEARCOAT_BUMP\n                , vec2").concat(state.fSuffix, "(0., 1.)\n                , vec4").concat(state.fSuffix, "(").concat(normalMapColor, ", 0.)\n                , ").concat(uv, "\n                #if defined(").concat(vTBNAvailable ? "TANGENT" : "IGNORE", ") && defined(NORMAL)\n                    , vTBN\n                #else\n                    , ").concat(isWebGPU ? "uniforms." : "", "vClearCoatTangentSpaceParams\n                #endif\n                #ifdef OBJECTSPACE_NORMALMAP\n                    , normalMatrix\n                #endif\n            #endif\n            #if defined(FORCENORMALFORWARD) && defined(NORMAL)\n                , faceNormal\n            #endif\n            #ifdef REFLECTION\n                , ").concat(isWebGPU ? "uniforms." : "").concat(reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._vReflectionMicrosurfaceInfosName, "\n                , ").concat(reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._vReflectionInfosName, "\n                , ").concat(reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock.reflectionColor, "\n                , ").concat(isWebGPU ? "uniforms." : "", "vLightingIntensity\n                #ifdef ").concat(reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._define3DName, "\n                    , ").concat(reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._cubeSamplerName, "       \n                    ").concat(isWebGPU ? ", ".concat(reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._cubeSamplerName, "Sampler") : "", "\n                #else\n                    , ").concat(reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._2DSamplerName, "       \n                    ").concat(isWebGPU ? ", ".concat(reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._2DSamplerName, "Sampler") : "", "\n                #endif\n                #ifndef LODBASEDMICROSFURACE\n                    #ifdef ").concat(reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._define3DName, "\n                        , ").concat(reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._cubeSamplerName, "       \n                        ").concat(isWebGPU ? ", ".concat(reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._cubeSamplerName, "Sampler") : "", "\n                        , ").concat(reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._cubeSamplerName, "\n                        ").concat(isWebGPU ? ", ".concat(reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._cubeSamplerName, "Sampler") : "", "\n                    #else\n                        , ").concat(reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._2DSamplerName, "\n                        ").concat(isWebGPU ? ", ".concat(reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._2DSamplerName, "Sampler") : "", "\n                        , ").concat(reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._2DSamplerName, "\n                        ").concat(isWebGPU ? ", ".concat(reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._2DSamplerName, "Sampler") : "", "                        \n                    #endif\n                #endif\n            #endif\n            #if defined(CLEARCOAT_BUMP) || defined(TWOSIDEDLIGHTING)\n                , (").concat(state._generateTernary("1.", "-1.", isWebGPU ? "fragmentInputs.frontFacing" : "gl_FrontFacing"), ")\n            #endif\n            );\n        #else\n            clearcoatOut.specularEnvironmentR0 = specularEnvironmentR0;\n        #endif\n");
        return code;
    }
    _buildBlock(state) {
        this._scene = state.sharedData.scene;
        if (state.target === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment) {
            state.sharedData.bindableBlocks.push(this);
            state.sharedData.blocksWithDefines.push(this);
            this._tangentCorrectionFactorName = state._getFreeDefineName("tangentCorrectionFactor");
            state._emitUniformFromString(this._tangentCorrectionFactorName, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float);
        }
        return this;
    }
    _dumpPropertiesCode() {
        let codeString = super._dumpPropertiesCode();
        codeString += "".concat(this._codeVariableName, ".remapF0OnInterfaceChange = ").concat(this.remapF0OnInterfaceChange, ";\n");
        return codeString;
    }
    serialize() {
        const serializationObject = super.serialize();
        serializationObject.remapF0OnInterfaceChange = this.remapF0OnInterfaceChange;
        return serializationObject;
    }
    _deserialize(serializationObject, scene, rootUrl) {
        super._deserialize(serializationObject, scene, rootUrl);
        var _serializationObject_remapF0OnInterfaceChange;
        this.remapF0OnInterfaceChange = (_serializationObject_remapF0OnInterfaceChange = serializationObject.remapF0OnInterfaceChange) !== null && _serializationObject_remapF0OnInterfaceChange !== void 0 ? _serializationObject_remapF0OnInterfaceChange : true;
    }
    /**
     * Create a new ClearCoatBlock
     * @param name defines the block name
     */ constructor(name){
        super(name, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this._tangentCorrectionFactorName = "";
        /**
         * Defines if the F0 value should be remapped to account for the interface change in the material.
         */ this.remapF0OnInterfaceChange = true;
        this._isUnique = true;
        this.registerInput("intensity", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float, false, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerInput("roughness", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float, true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerInput("indexOfRefraction", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float, true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerInput("normalMapColor", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Color3, true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerInput("uv", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector2, true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerInput("tintColor", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Color3, true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerInput("tintAtDistance", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float, true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerInput("tintThickness", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float, true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerInput("worldTangent", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector4, true);
        this.registerInput("worldNormal", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].AutoDetect, true);
        this.worldNormal.addExcludedConnectionPointFromAllowedTypes(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Color4 | __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector4 | __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3);
        this.registerInput("TBN", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Object, true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].VertexAndFragment, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialConnectionPointCustomObject$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialConnectionPointCustomObject"]("TBN", this, 0 /* NodeMaterialConnectionPointDirection.Input */ , __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Fragment$2f$TBNBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TBNBlock"], "TBNBlock"));
        this.registerOutput("clearcoat", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Object, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialConnectionPointCustomObject$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialConnectionPointCustomObject"]("clearcoat", this, 1 /* NodeMaterialConnectionPointDirection.Output */ , ClearCoatBlock, "ClearCoatBlock"));
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Decorators$2f$nodeDecorator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["editableInPropertyPage"])("Remap F0 on interface change", 0 /* PropertyTypeForEdition.Boolean */ , "ADVANCED", {
        embedded: true
    })
], ClearCoatBlock.prototype, "remapF0OnInterfaceChange", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$typeStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RegisterClass"])("BABYLON.ClearCoatBlock", ClearCoatBlock); //# sourceMappingURL=clearCoatBlock.js.map
}),
"[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/iridescenceBlock.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "IridescenceBlock": ()=>IridescenceBlock
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterialBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialBlockConnectionPointTypes.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialBlockTargets.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$typeStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Misc/typeStore.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Input$2f$inputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Input/inputBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialConnectionPointCustomObject$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterialConnectionPointCustomObject.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$PBR$2f$pbrIridescenceConfiguration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/PBR/pbrIridescenceConfiguration.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
class IridescenceBlock extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlock"] {
    /**
     * Initialize the block and prepare the context for build
     * @param state defines the state that will be used for the build
     */ initialize(state) {
        state._excludeVariableName("iridescenceOut");
        state._excludeVariableName("vIridescenceParams");
    }
    /**
     * Gets the current class name
     * @returns the class name
     */ getClassName() {
        return "IridescenceBlock";
    }
    /**
     * Gets the intensity input component
     */ get intensity() {
        return this._inputs[0];
    }
    /**
     * Gets the indexOfRefraction input component
     */ get indexOfRefraction() {
        return this._inputs[1];
    }
    /**
     * Gets the thickness input component
     */ get thickness() {
        return this._inputs[2];
    }
    /**
     * Gets the iridescence object output component
     */ get iridescence() {
        return this._outputs[0];
    }
    autoConfigure() {
        if (!this.intensity.isConnected) {
            const intensityInput = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Input$2f$inputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InputBlock"]("Iridescence intensity", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float);
            intensityInput.value = 1;
            intensityInput.output.connectTo(this.intensity);
            const indexOfRefractionInput = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Input$2f$inputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InputBlock"]("Iridescence ior", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float);
            indexOfRefractionInput.value = 1.3;
            indexOfRefractionInput.output.connectTo(this.indexOfRefraction);
            const thicknessInput = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Input$2f$inputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InputBlock"]("Iridescence thickness", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float);
            thicknessInput.value = 400;
            thicknessInput.output.connectTo(this.thickness);
        }
    }
    prepareDefines(defines) {
        defines.setValue("IRIDESCENCE", true, true);
        defines.setValue("IRIDESCENCE_TEXTURE", false, true);
        defines.setValue("IRIDESCENCE_THICKNESS_TEXTURE", false, true);
    }
    /**
     * Gets the main code of the block (fragment side)
     * @param iridescenceBlock instance of a IridescenceBlock or null if the code must be generated without an active iridescence module
     * @param state defines the build state
     * @returns the shader code
     */ static GetCode(iridescenceBlock, state) {
        let code = "";
        const intensityName = (iridescenceBlock === null || iridescenceBlock === void 0 ? void 0 : iridescenceBlock.intensity.isConnected) ? iridescenceBlock.intensity.associatedVariableName : "1.";
        const indexOfRefraction = (iridescenceBlock === null || iridescenceBlock === void 0 ? void 0 : iridescenceBlock.indexOfRefraction.isConnected) ? iridescenceBlock.indexOfRefraction.associatedVariableName : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$PBR$2f$pbrIridescenceConfiguration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PBRIridescenceConfiguration"]._DefaultIndexOfRefraction;
        const thickness = (iridescenceBlock === null || iridescenceBlock === void 0 ? void 0 : iridescenceBlock.thickness.isConnected) ? iridescenceBlock.thickness.associatedVariableName : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$PBR$2f$pbrIridescenceConfiguration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PBRIridescenceConfiguration"]._DefaultMaximumThickness;
        const isWebGPU = state.shaderLanguage === 1 /* ShaderLanguage.WGSL */ ;
        code += "".concat(isWebGPU ? "var iridescenceOut: iridescenceOutParams" : "iridescenceOutParams iridescenceOut", ";\n\n        #ifdef IRIDESCENCE\n            iridescenceOut = iridescenceBlock(\n                vec4(").concat(intensityName, ", ").concat(indexOfRefraction, ", 1., ").concat(thickness, ")\n                , NdotV\n                , specularEnvironmentR0\n                #ifdef CLEARCOAT\n                    , NdotVUnclamped\n                    , vClearCoatParams\n                #endif                \n            );\n\n            ").concat(isWebGPU ? "let" : "float", " iridescenceIntensity = iridescenceOut.iridescenceIntensity;\n            specularEnvironmentR0 = iridescenceOut.specularEnvironmentR0;\n        #endif\n");
        return code;
    }
    _buildBlock(state) {
        if (state.target === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment) {
            state.sharedData.bindableBlocks.push(this);
            state.sharedData.blocksWithDefines.push(this);
        }
        return this;
    }
    serialize() {
        const serializationObject = super.serialize();
        return serializationObject;
    }
    _deserialize(serializationObject, scene, rootUrl) {
        super._deserialize(serializationObject, scene, rootUrl);
    }
    /**
     * Create a new IridescenceBlock
     * @param name defines the block name
     */ constructor(name){
        super(name, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this._isUnique = true;
        this.registerInput("intensity", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float, true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerInput("indexOfRefraction", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float, true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerInput("thickness", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float, true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerOutput("iridescence", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Object, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialConnectionPointCustomObject$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialConnectionPointCustomObject"]("iridescence", this, 1 /* NodeMaterialConnectionPointDirection.Output */ , IridescenceBlock, "IridescenceBlock"));
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$typeStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RegisterClass"])("BABYLON.IridescenceBlock", IridescenceBlock); //# sourceMappingURL=iridescenceBlock.js.map
}),
"[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/refractionBlock.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "RefractionBlock": ()=>RefractionBlock
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/tslib.es6.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialBlockConnectionPointTypes.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialBlockTargets.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$typeStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Misc/typeStore.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Input$2f$inputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Input/inputBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialConnectionPointCustomObject$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterialConnectionPointCustomObject.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Decorators$2f$nodeDecorator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Decorators/nodeDecorator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterialBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Textures$2f$cubeTexture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Textures/cubeTexture.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Textures$2f$texture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Textures/texture.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialSystemValues$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialSystemValues.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
class RefractionBlock extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlock"] {
    /**
     * Initialize the block and prepare the context for build
     * @param state defines the state that will be used for the build
     */ initialize(state) {
        state._excludeVariableName("vRefractionPosition");
        state._excludeVariableName("vRefractionSize");
    }
    /**
     * Gets the current class name
     * @returns the class name
     */ getClassName() {
        return "RefractionBlock";
    }
    /**
     * Gets the intensity input component
     */ get intensity() {
        return this._inputs[0];
    }
    /**
     * Gets the tint at distance input component
     */ get tintAtDistance() {
        return this._inputs[1];
    }
    /**
     * Gets the volume index of refraction input component
     */ get volumeIndexOfRefraction() {
        return this._inputs[2];
    }
    /**
     * Gets the view input component
     */ get view() {
        return this.viewConnectionPoint;
    }
    /**
     * Gets the refraction object output component
     */ get refraction() {
        return this._outputs[0];
    }
    /**
     * Returns true if the block has a texture
     */ get hasTexture() {
        return !!this._getTexture();
    }
    _getTexture() {
        if (this.texture) {
            return this.texture;
        }
        return this._scene.environmentTexture;
    }
    autoConfigure(material) {
        let additionalFilteringInfo = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : ()=>true;
        if (!this.intensity.isConnected) {
            const intensityInput = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Input$2f$inputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InputBlock"]("Refraction intensity", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float);
            intensityInput.value = 1;
            intensityInput.output.connectTo(this.intensity);
        }
        if (this.view && !this.view.isConnected) {
            let viewInput = material.getInputBlockByPredicate((b)=>b.systemValue === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialSystemValues$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialSystemValues"].View && additionalFilteringInfo(b));
            if (!viewInput) {
                viewInput = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Input$2f$inputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InputBlock"]("view");
                viewInput.setAsSystemValue(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialSystemValues$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialSystemValues"].View);
            }
            viewInput.output.connectTo(this.view);
        }
    }
    prepareDefines(defines) {
        const refractionTexture = this._getTexture();
        const refraction = refractionTexture && refractionTexture.getTextureMatrix;
        defines.setValue("SS_REFRACTION", refraction, true);
        if (!refraction) {
            return;
        }
        defines.setValue(this._define3DName, refractionTexture.isCube, true);
        defines.setValue(this._defineLODRefractionAlpha, refractionTexture.lodLevelInAlpha, true);
        defines.setValue(this._defineLinearSpecularRefraction, refractionTexture.linearSpecularLOD, true);
        defines.setValue(this._defineOppositeZ, this._scene.useRightHandedSystem && refractionTexture.isCube ? !refractionTexture.invertZ : refractionTexture.invertZ, true);
        defines.setValue("SS_LINKREFRACTIONTOTRANSPARENCY", this.linkRefractionWithTransparency, true);
        defines.setValue("SS_GAMMAREFRACTION", refractionTexture.gammaSpace, true);
        defines.setValue("SS_RGBDREFRACTION", refractionTexture.isRGBD, true);
        defines.setValue("SS_USE_LOCAL_REFRACTIONMAP_CUBIC", refractionTexture.boundingBoxSize ? true : false, true);
        defines.setValue("SS_USE_THICKNESS_AS_DEPTH", this.useThicknessAsDepth, true);
    }
    isReady() {
        const texture = this._getTexture();
        if (texture && !texture.isReadyOrNotBlocking()) {
            return false;
        }
        return true;
    }
    bind(effect, nodeMaterial, mesh) {
        var _this_volumeIndexOfRefraction_connectInputBlock, _this_indexOfRefractionConnectionPoint_connectInputBlock;
        super.bind(effect, nodeMaterial, mesh);
        const refractionTexture = this._getTexture();
        if (!refractionTexture) {
            return;
        }
        if (refractionTexture.isCube) {
            effect.setTexture(this._cubeSamplerName, refractionTexture);
        } else {
            effect.setTexture(this._2DSamplerName, refractionTexture);
        }
        effect.setMatrix(this._refractionMatrixName, refractionTexture.getRefractionTextureMatrix());
        let depth = 1.0;
        if (!refractionTexture.isCube) {
            if (refractionTexture.depth) {
                depth = refractionTexture.depth;
            }
        }
        var _this_volumeIndexOfRefraction_connectInputBlock_value, _ref;
        const indexOfRefraction = (_ref = (_this_volumeIndexOfRefraction_connectInputBlock_value = (_this_volumeIndexOfRefraction_connectInputBlock = this.volumeIndexOfRefraction.connectInputBlock) === null || _this_volumeIndexOfRefraction_connectInputBlock === void 0 ? void 0 : _this_volumeIndexOfRefraction_connectInputBlock.value) !== null && _this_volumeIndexOfRefraction_connectInputBlock_value !== void 0 ? _this_volumeIndexOfRefraction_connectInputBlock_value : (_this_indexOfRefractionConnectionPoint_connectInputBlock = this.indexOfRefractionConnectionPoint.connectInputBlock) === null || _this_indexOfRefractionConnectionPoint_connectInputBlock === void 0 ? void 0 : _this_indexOfRefractionConnectionPoint_connectInputBlock.value) !== null && _ref !== void 0 ? _ref : 1.5;
        effect.setFloat4(this._vRefractionInfosName, refractionTexture.level, 1 / indexOfRefraction, depth, this.invertRefractionY ? -1 : 1);
        effect.setFloat4(this._vRefractionMicrosurfaceInfosName, refractionTexture.getSize().width, refractionTexture.lodGenerationScale, refractionTexture.lodGenerationOffset, 1 / indexOfRefraction);
        const width = refractionTexture.getSize().width;
        effect.setFloat2(this._vRefractionFilteringInfoName, width, Math.log2(width));
        if (refractionTexture.boundingBoxSize) {
            const cubeTexture = refractionTexture;
            effect.setVector3("vRefractionPosition", cubeTexture.boundingBoxPosition);
            effect.setVector3("vRefractionSize", cubeTexture.boundingBoxSize);
        }
    }
    /**
     * Gets the main code of the block (fragment side)
     * @param state current state of the node material building
     * @returns the shader code
     */ getCode(state) {
        const code = "";
        state.sharedData.blockingBlocks.push(this);
        state.sharedData.textureBlocks.push(this);
        // Samplers
        this._cubeSamplerName = state._getFreeVariableName(this.name + "CubeSampler");
        state.samplers.push(this._cubeSamplerName);
        this._2DSamplerName = state._getFreeVariableName(this.name + "2DSampler");
        state.samplers.push(this._2DSamplerName);
        this._define3DName = state._getFreeDefineName("SS_REFRACTIONMAP_3D");
        const refractionTexture = this._getTexture();
        if (refractionTexture) {
            state._samplerDeclaration += "#ifdef ".concat(this._define3DName, "\n");
            state._emitCubeSampler(this._cubeSamplerName, undefined, true);
            state._samplerDeclaration += "#else\n";
            state._emit2DSampler(this._2DSamplerName, undefined, true);
            state._samplerDeclaration += "#endif\n";
        }
        // Fragment
        state.sharedData.blocksWithDefines.push(this);
        state.sharedData.bindableBlocks.push(this);
        this._defineLODRefractionAlpha = state._getFreeDefineName("SS_LODINREFRACTIONALPHA");
        this._defineLinearSpecularRefraction = state._getFreeDefineName("SS_LINEARSPECULARREFRACTION");
        this._defineOppositeZ = state._getFreeDefineName("SS_REFRACTIONMAP_OPPOSITEZ");
        this._refractionMatrixName = state._getFreeVariableName("refractionMatrix");
        state._emitUniformFromString(this._refractionMatrixName, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Matrix);
        if (state.shaderLanguage !== 1 /* ShaderLanguage.WGSL */ ) {
            state._emitFunction("sampleRefraction", "\n                #ifdef ".concat(this._define3DName, "\n                    #define sampleRefraction(s, c) textureCube(s, c)\n                #else\n                    #define sampleRefraction(s, c) texture2D(s, c)\n                #endif\n"), "//".concat(this.name));
            state._emitFunction("sampleRefractionLod", "\n                #ifdef ".concat(this._define3DName, "\n                    #define sampleRefractionLod(s, c, l) textureCubeLodEXT(s, c, l)\n                #else\n                    #define sampleRefractionLod(s, c, l) texture2DLodEXT(s, c, l)\n                #endif\n"), "//".concat(this.name));
        }
        this._vRefractionMicrosurfaceInfosName = state._getFreeVariableName("vRefractionMicrosurfaceInfos");
        state._emitUniformFromString(this._vRefractionMicrosurfaceInfosName, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector4);
        this._vRefractionInfosName = state._getFreeVariableName("vRefractionInfos");
        state._emitUniformFromString(this._vRefractionInfosName, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector4);
        this._vRefractionFilteringInfoName = state._getFreeVariableName("vRefractionFilteringInfo");
        state._emitUniformFromString(this._vRefractionFilteringInfoName, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector2);
        state._emitUniformFromString("vRefractionPosition", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3);
        state._emitUniformFromString("vRefractionSize", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3);
        return code;
    }
    _buildBlock(state) {
        this._scene = state.sharedData.scene;
        return this;
    }
    _dumpPropertiesCode() {
        let codeString = super._dumpPropertiesCode();
        if (this.texture) {
            if (this.texture.isCube) {
                codeString = "".concat(this._codeVariableName, '.texture = new BABYLON.CubeTexture("').concat(this.texture.name, '");\n');
            } else {
                codeString = "".concat(this._codeVariableName, '.texture = new BABYLON.Texture("').concat(this.texture.name, '");\n');
            }
            codeString += "".concat(this._codeVariableName, ".texture.coordinatesMode = ").concat(this.texture.coordinatesMode, ";\n");
        }
        codeString += "".concat(this._codeVariableName, ".linkRefractionWithTransparency = ").concat(this.linkRefractionWithTransparency, ";\n");
        codeString += "".concat(this._codeVariableName, ".invertRefractionY = ").concat(this.invertRefractionY, ";\n");
        codeString += "".concat(this._codeVariableName, ".useThicknessAsDepth = ").concat(this.useThicknessAsDepth, ";\n");
        return codeString;
    }
    serialize() {
        const serializationObject = super.serialize();
        if (this.texture && !this.texture.isRenderTarget) {
            serializationObject.texture = this.texture.serialize();
        }
        serializationObject.linkRefractionWithTransparency = this.linkRefractionWithTransparency;
        serializationObject.invertRefractionY = this.invertRefractionY;
        serializationObject.useThicknessAsDepth = this.useThicknessAsDepth;
        return serializationObject;
    }
    _deserialize(serializationObject, scene, rootUrl) {
        super._deserialize(serializationObject, scene, rootUrl);
        if (serializationObject.texture) {
            rootUrl = serializationObject.texture.url.indexOf("data:") === 0 ? "" : rootUrl;
            if (serializationObject.texture.isCube) {
                this.texture = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Textures$2f$cubeTexture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CubeTexture"].Parse(serializationObject.texture, scene, rootUrl);
            } else {
                this.texture = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Textures$2f$texture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Texture"].Parse(serializationObject.texture, scene, rootUrl);
            }
        }
        this.linkRefractionWithTransparency = serializationObject.linkRefractionWithTransparency;
        this.invertRefractionY = serializationObject.invertRefractionY;
        this.useThicknessAsDepth = !!serializationObject.useThicknessAsDepth;
    }
    /**
     * Create a new RefractionBlock
     * @param name defines the block name
     */ constructor(name){
        super(name, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        /**
         * This parameters will make the material used its opacity to control how much it is refracting against not.
         * Materials half opaque for instance using refraction could benefit from this control.
         */ this.linkRefractionWithTransparency = false;
        /**
         * Controls if refraction needs to be inverted on Y. This could be useful for procedural texture.
         */ this.invertRefractionY = false;
        /**
         * Controls if refraction needs to be inverted on Y. This could be useful for procedural texture.
         */ this.useThicknessAsDepth = false;
        this._isUnique = true;
        this.registerInput("intensity", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float, false, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerInput("tintAtDistance", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float, true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerInput("volumeIndexOfRefraction", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float, true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerOutput("refraction", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Object, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialConnectionPointCustomObject$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialConnectionPointCustomObject"]("refraction", this, 1 /* NodeMaterialConnectionPointDirection.Output */ , RefractionBlock, "RefractionBlock"));
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Decorators$2f$nodeDecorator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["editableInPropertyPage"])("Link refraction to transparency", 0 /* PropertyTypeForEdition.Boolean */ , "ADVANCED", {
        embedded: true,
        notifiers: {
            update: true
        }
    })
], RefractionBlock.prototype, "linkRefractionWithTransparency", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Decorators$2f$nodeDecorator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["editableInPropertyPage"])("Invert refraction Y", 0 /* PropertyTypeForEdition.Boolean */ , "ADVANCED", {
        embedded: true,
        notifiers: {
            update: true
        }
    })
], RefractionBlock.prototype, "invertRefractionY", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Decorators$2f$nodeDecorator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["editableInPropertyPage"])("Use thickness as depth", 0 /* PropertyTypeForEdition.Boolean */ , "ADVANCED", {
        embedded: true,
        notifiers: {
            update: true
        }
    })
], RefractionBlock.prototype, "useThicknessAsDepth", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$typeStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RegisterClass"])("BABYLON.RefractionBlock", RefractionBlock); //# sourceMappingURL=refractionBlock.js.map
}),
"[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/subSurfaceBlock.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "SubSurfaceBlock": ()=>SubSurfaceBlock
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/tslib.es6.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterialBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialBlockConnectionPointTypes.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialBlockTargets.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$typeStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Misc/typeStore.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Input$2f$inputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Input/inputBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialConnectionPointCustomObject$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterialConnectionPointCustomObject.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$PBR$2f$refractionBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/refractionBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Decorators$2f$nodeDecorator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Decorators/nodeDecorator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$PBR$2f$pbrSubSurfaceConfiguration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/PBR/pbrSubSurfaceConfiguration.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
class SubSurfaceBlock extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlock"] {
    /**
     * Initialize the block and prepare the context for build
     * @param state defines the state that will be used for the build
     */ initialize(state) {
        state._excludeVariableName("subSurfaceOut");
        state._excludeVariableName("vThicknessParam");
        state._excludeVariableName("vTintColor");
        state._excludeVariableName("vTranslucencyColor");
        state._excludeVariableName("vSubSurfaceIntensity");
        state._excludeVariableName("dispersion");
    }
    /**
     * Gets the current class name
     * @returns the class name
     */ getClassName() {
        return "SubSurfaceBlock";
    }
    /**
     * Gets the thickness component
     */ get thickness() {
        return this._inputs[0];
    }
    /**
     * Gets the tint color input component
     */ get tintColor() {
        return this._inputs[1];
    }
    /**
     * Gets the translucency intensity input component
     */ get translucencyIntensity() {
        return this._inputs[2];
    }
    /**
     * Gets the translucency diffusion distance input component
     */ get translucencyDiffusionDist() {
        return this._inputs[3];
    }
    /**
     * Gets the refraction object parameters
     */ get refraction() {
        return this._inputs[4];
    }
    /**
     * Gets the dispersion input component
     */ get dispersion() {
        return this._inputs[5];
    }
    /**
     * Gets the sub surface object output component
     */ get subsurface() {
        return this._outputs[0];
    }
    autoConfigure() {
        if (!this.thickness.isConnected) {
            const thicknessInput = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Input$2f$inputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InputBlock"]("SubSurface thickness", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float);
            thicknessInput.value = 0;
            thicknessInput.output.connectTo(this.thickness);
        }
    }
    prepareDefines(defines) {
        const translucencyEnabled = this.translucencyDiffusionDist.isConnected || this.translucencyIntensity.isConnected;
        defines.setValue("SUBSURFACE", translucencyEnabled || this.refraction.isConnected, true);
        defines.setValue("SS_TRANSLUCENCY", translucencyEnabled, true);
        defines.setValue("SS_THICKNESSANDMASK_TEXTURE", false, true);
        defines.setValue("SS_REFRACTIONINTENSITY_TEXTURE", false, true);
        defines.setValue("SS_TRANSLUCENCYINTENSITY_TEXTURE", false, true);
        defines.setValue("SS_USE_GLTF_TEXTURES", false, true);
        defines.setValue("SS_DISPERSION", this.dispersion.isConnected, true);
        defines.setValue("SS_APPLY_ALBEDO_AFTER_SUBSURFACE", this.applyAlbedoAfterSubSurface, true);
    }
    /**
     * Gets the main code of the block (fragment side)
     * @param state current state of the node material building
     * @param ssBlock instance of a SubSurfaceBlock or null if the code must be generated without an active sub surface module
     * @param reflectionBlock instance of a ReflectionBlock null if the code must be generated without an active reflection module
     * @param worldPosVarName name of the variable holding the world position
     * @returns the shader code
     */ static GetCode(state, ssBlock, reflectionBlock, worldPosVarName) {
        var _ssBlock_refraction_connectedPoint;
        let code = "";
        const thickness = (ssBlock === null || ssBlock === void 0 ? void 0 : ssBlock.thickness.isConnected) ? ssBlock.thickness.associatedVariableName : "0.";
        const tintColor = (ssBlock === null || ssBlock === void 0 ? void 0 : ssBlock.tintColor.isConnected) ? ssBlock.tintColor.associatedVariableName : "vec3(1.)";
        const translucencyIntensity = (ssBlock === null || ssBlock === void 0 ? void 0 : ssBlock.translucencyIntensity.isConnected) ? ssBlock === null || ssBlock === void 0 ? void 0 : ssBlock.translucencyIntensity.associatedVariableName : "1.";
        const translucencyDiffusionDistance = (ssBlock === null || ssBlock === void 0 ? void 0 : ssBlock.translucencyDiffusionDist.isConnected) ? ssBlock === null || ssBlock === void 0 ? void 0 : ssBlock.translucencyDiffusionDist.associatedVariableName : "vec3(1.)";
        const refractionBlock = (ssBlock === null || ssBlock === void 0 ? void 0 : ssBlock.refraction.isConnected) ? ssBlock === null || ssBlock === void 0 ? void 0 : (_ssBlock_refraction_connectedPoint = ssBlock.refraction.connectedPoint) === null || _ssBlock_refraction_connectedPoint === void 0 ? void 0 : _ssBlock_refraction_connectedPoint.ownerBlock : null;
        const refractionTintAtDistance = (refractionBlock === null || refractionBlock === void 0 ? void 0 : refractionBlock.tintAtDistance.isConnected) ? refractionBlock.tintAtDistance.associatedVariableName : "1.";
        const refractionIntensity = (refractionBlock === null || refractionBlock === void 0 ? void 0 : refractionBlock.intensity.isConnected) ? refractionBlock.intensity.associatedVariableName : "1.";
        const refractionView = (refractionBlock === null || refractionBlock === void 0 ? void 0 : refractionBlock.view.isConnected) ? refractionBlock.view.associatedVariableName : "";
        const dispersion = (ssBlock === null || ssBlock === void 0 ? void 0 : ssBlock.dispersion.isConnected) ? ssBlock === null || ssBlock === void 0 ? void 0 : ssBlock.dispersion.associatedVariableName : "0.0";
        const isWebGPU = state.shaderLanguage === 1 /* ShaderLanguage.WGSL */ ;
        var _refractionBlock_getCode;
        code += (_refractionBlock_getCode = refractionBlock === null || refractionBlock === void 0 ? void 0 : refractionBlock.getCode(state)) !== null && _refractionBlock_getCode !== void 0 ? _refractionBlock_getCode : "";
        code += "".concat(isWebGPU ? "var subSurfaceOut: subSurfaceOutParams" : "subSurfaceOutParams subSurfaceOut", ";\n\n        #ifdef SUBSURFACE\n            ").concat(state._declareLocalVar("vThicknessParam", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector2), " = vec2").concat(state.fSuffix, "(0., ").concat(thickness, ");\n            ").concat(state._declareLocalVar("vTintColor", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector4), " = vec4").concat(state.fSuffix, "(").concat(tintColor, ", ").concat(refractionTintAtDistance, ");\n            ").concat(state._declareLocalVar("vSubSurfaceIntensity", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3), " = vec3(").concat(refractionIntensity, ", ").concat(translucencyIntensity, ", 0.);\n            ").concat(state._declareLocalVar("dispersion", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float), " = ").concat(dispersion, ";\n            subSurfaceOut = subSurfaceBlock(\n                vSubSurfaceIntensity\n                , vThicknessParam\n                , vTintColor\n                , normalW\n            #ifdef LEGACY_SPECULAR_ENERGY_CONSERVATION\n        ");
        code += isWebGPU ? ", vec3f(max(colorSpecularEnvironmentReflectance.r, max(colorSpecularEnvironmentReflectance.g, colorSpecularEnvironmentReflectance.b)))/n" : ", vec3(max(colorSpecularEnvironmentReflectance.r, max(colorSpecularEnvironmentReflectance.g, colorSpecularEnvironmentReflectance.b)))/n";
        var _refractionBlock__vRefractionInfosName, _refractionBlock__refractionMatrixName, _refractionBlock__vRefractionMicrosurfaceInfosName, _refractionBlock__defineLODRefractionAlpha, _refractionBlock__defineLinearSpecularRefraction, _refractionBlock__define3DName, _refractionBlock__cubeSamplerName, _refractionBlock__2DSamplerName, _refractionBlock__define3DName1, _refractionBlock__cubeSamplerName1, _refractionBlock__cubeSamplerName2, _refractionBlock__2DSamplerName1, _refractionBlock__2DSamplerName2, _refractionBlock__vRefractionFilteringInfoName;
        code += "#else\n                , baseSpecularEnvironmentReflectance\n            #endif\n            #ifdef SS_THICKNESSANDMASK_TEXTURE\n                , vec4".concat(state.fSuffix, "(0.)\n            #endif\n            #ifdef REFLECTION\n                #ifdef SS_TRANSLUCENCY\n                    , ").concat((isWebGPU ? "uniforms." : "") + (reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._reflectionMatrixName), "\n                    #ifdef USESPHERICALFROMREFLECTIONMAP\n                        #if !defined(NORMAL) || !defined(USESPHERICALINVERTEX)\n                            , reflectionOut.irradianceVector\n                        #endif\n                        #if defined(REALTIME_FILTERING)\n                            , ").concat(reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._cubeSamplerName, "\n                            ").concat(isWebGPU ? ", ".concat(reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._cubeSamplerName, "Sampler") : "", "\n                            , ").concat(reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._vReflectionFilteringInfoName, "\n                        #endif\n                        #endif\n                    #ifdef USEIRRADIANCEMAP\n                        , irradianceSampler\n                        ").concat(isWebGPU ? ", irradianceSamplerSampler" : "", "\n                    #endif\n                #endif\n            #endif\n            #if defined(SS_REFRACTION) || defined(SS_TRANSLUCENCY)\n                , surfaceAlbedo\n            #endif\n            #ifdef SS_REFRACTION\n                , ").concat(worldPosVarName, ".xyz\n                , viewDirectionW\n                , ").concat(refractionView, "\n                , ").concat((isWebGPU ? "uniforms." : "") + ((_refractionBlock__vRefractionInfosName = refractionBlock === null || refractionBlock === void 0 ? void 0 : refractionBlock._vRefractionInfosName) !== null && _refractionBlock__vRefractionInfosName !== void 0 ? _refractionBlock__vRefractionInfosName : ""), "\n                , ").concat((isWebGPU ? "uniforms." : "") + ((_refractionBlock__refractionMatrixName = refractionBlock === null || refractionBlock === void 0 ? void 0 : refractionBlock._refractionMatrixName) !== null && _refractionBlock__refractionMatrixName !== void 0 ? _refractionBlock__refractionMatrixName : ""), "\n                , ").concat((isWebGPU ? "uniforms." : "") + ((_refractionBlock__vRefractionMicrosurfaceInfosName = refractionBlock === null || refractionBlock === void 0 ? void 0 : refractionBlock._vRefractionMicrosurfaceInfosName) !== null && _refractionBlock__vRefractionMicrosurfaceInfosName !== void 0 ? _refractionBlock__vRefractionMicrosurfaceInfosName : ""), "\n                , ").concat(isWebGPU ? "uniforms." : "", "vLightingIntensity\n                #ifdef SS_LINKREFRACTIONTOTRANSPARENCY\n                    , alpha\n                #endif\n                #ifdef ").concat((_refractionBlock__defineLODRefractionAlpha = refractionBlock === null || refractionBlock === void 0 ? void 0 : refractionBlock._defineLODRefractionAlpha) !== null && _refractionBlock__defineLODRefractionAlpha !== void 0 ? _refractionBlock__defineLODRefractionAlpha : "IGNORE", "\n                    , NdotVUnclamped\n                #endif\n                #ifdef ").concat((_refractionBlock__defineLinearSpecularRefraction = refractionBlock === null || refractionBlock === void 0 ? void 0 : refractionBlock._defineLinearSpecularRefraction) !== null && _refractionBlock__defineLinearSpecularRefraction !== void 0 ? _refractionBlock__defineLinearSpecularRefraction : "IGNORE", "\n                    , roughness\n                #endif\n                , alphaG\n                #ifdef ").concat((_refractionBlock__define3DName = refractionBlock === null || refractionBlock === void 0 ? void 0 : refractionBlock._define3DName) !== null && _refractionBlock__define3DName !== void 0 ? _refractionBlock__define3DName : "IGNORE", "\n                    , ").concat((_refractionBlock__cubeSamplerName = refractionBlock === null || refractionBlock === void 0 ? void 0 : refractionBlock._cubeSamplerName) !== null && _refractionBlock__cubeSamplerName !== void 0 ? _refractionBlock__cubeSamplerName : "", "\n                    ").concat(isWebGPU ? ", ".concat(refractionBlock === null || refractionBlock === void 0 ? void 0 : refractionBlock._cubeSamplerName, "Sampler") : "", "\n                #else\n                    , ").concat((_refractionBlock__2DSamplerName = refractionBlock === null || refractionBlock === void 0 ? void 0 : refractionBlock._2DSamplerName) !== null && _refractionBlock__2DSamplerName !== void 0 ? _refractionBlock__2DSamplerName : "", "\n                    ").concat(isWebGPU ? ", ".concat(refractionBlock === null || refractionBlock === void 0 ? void 0 : refractionBlock._2DSamplerName, "Sampler") : "", "\n                #endif\n                #ifndef LODBASEDMICROSFURACE\n                    #ifdef ").concat((_refractionBlock__define3DName1 = refractionBlock === null || refractionBlock === void 0 ? void 0 : refractionBlock._define3DName) !== null && _refractionBlock__define3DName1 !== void 0 ? _refractionBlock__define3DName1 : "IGNORE", "\n                        , ").concat((_refractionBlock__cubeSamplerName1 = refractionBlock === null || refractionBlock === void 0 ? void 0 : refractionBlock._cubeSamplerName) !== null && _refractionBlock__cubeSamplerName1 !== void 0 ? _refractionBlock__cubeSamplerName1 : "", "                        \n                        ").concat(isWebGPU ? ", ".concat(refractionBlock === null || refractionBlock === void 0 ? void 0 : refractionBlock._cubeSamplerName, "Sampler") : "", "\n                        , ").concat((_refractionBlock__cubeSamplerName2 = refractionBlock === null || refractionBlock === void 0 ? void 0 : refractionBlock._cubeSamplerName) !== null && _refractionBlock__cubeSamplerName2 !== void 0 ? _refractionBlock__cubeSamplerName2 : "", "                        \n                        ").concat(isWebGPU ? ", ".concat(refractionBlock === null || refractionBlock === void 0 ? void 0 : refractionBlock._cubeSamplerName, "Sampler") : "", "\n                    #else\n                        , ").concat((_refractionBlock__2DSamplerName1 = refractionBlock === null || refractionBlock === void 0 ? void 0 : refractionBlock._2DSamplerName) !== null && _refractionBlock__2DSamplerName1 !== void 0 ? _refractionBlock__2DSamplerName1 : "", "\n                        ").concat(isWebGPU ? ", ".concat(refractionBlock === null || refractionBlock === void 0 ? void 0 : refractionBlock._2DSamplerName, "Sampler") : "", "\n                        , ").concat((_refractionBlock__2DSamplerName2 = refractionBlock === null || refractionBlock === void 0 ? void 0 : refractionBlock._2DSamplerName) !== null && _refractionBlock__2DSamplerName2 !== void 0 ? _refractionBlock__2DSamplerName2 : "", "\n                        ").concat(isWebGPU ? ", ".concat(refractionBlock === null || refractionBlock === void 0 ? void 0 : refractionBlock._2DSamplerName, "Sampler") : "", "\n                    #endif\n                #endif\n                #ifdef ANISOTROPIC\n                    , anisotropicOut\n                #endif\n                #ifdef REALTIME_FILTERING\n                    , ").concat((_refractionBlock__vRefractionFilteringInfoName = refractionBlock === null || refractionBlock === void 0 ? void 0 : refractionBlock._vRefractionFilteringInfoName) !== null && _refractionBlock__vRefractionFilteringInfoName !== void 0 ? _refractionBlock__vRefractionFilteringInfoName : "", "\n                #endif\n                #ifdef SS_USE_LOCAL_REFRACTIONMAP_CUBIC\n                    , vRefractionPosition\n                    , vRefractionSize\n                #endif\n                #ifdef SS_DISPERSION\n                    , dispersion\n                #endif\n            #endif\n            #ifdef SS_TRANSLUCENCY\n                , ").concat(translucencyDiffusionDistance, "\n                , vTintColor\n                #ifdef SS_TRANSLUCENCYCOLOR_TEXTURE\n                    , vec4").concat(state.fSuffix, "(0.)\n                #endif\n            #endif                \n            );\n\n            #ifdef SS_REFRACTION\n                surfaceAlbedo = subSurfaceOut.surfaceAlbedo;\n                #ifdef SS_LINKREFRACTIONTOTRANSPARENCY\n                    alpha = subSurfaceOut.alpha;\n                #endif\n            #endif\n        #else\n            subSurfaceOut.specularEnvironmentReflectance = colorSpecularEnvironmentReflectance;\n        #endif\n");
        return code;
    }
    _buildBlock(state) {
        if (state.target === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment) {
            state.sharedData.blocksWithDefines.push(this);
        }
        return this;
    }
    /**
     * Create a new SubSurfaceBlock
     * @param name defines the block name
     */ constructor(name){
        super(name, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        /**
         * Set it to true if your rendering in 8.0+ is different from that in 7 when you use sub-surface properties (transmission, refraction, etc.)
         */ this.applyAlbedoAfterSubSurface = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$PBR$2f$pbrSubSurfaceConfiguration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PBRSubSurfaceConfiguration"].DEFAULT_APPLY_ALBEDO_AFTERSUBSURFACE;
        this._isUnique = true;
        this.registerInput("thickness", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float, false, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerInput("tintColor", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Color3, true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerInput("translucencyIntensity", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float, true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerInput("translucencyDiffusionDist", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Color3, true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerInput("refraction", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Object, true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialConnectionPointCustomObject$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialConnectionPointCustomObject"]("refraction", this, 0 /* NodeMaterialConnectionPointDirection.Input */ , __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$PBR$2f$refractionBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RefractionBlock"], "RefractionBlock"));
        this.registerInput("dispersion", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float, true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerOutput("subsurface", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Object, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialConnectionPointCustomObject$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialConnectionPointCustomObject"]("subsurface", this, 1 /* NodeMaterialConnectionPointDirection.Output */ , SubSurfaceBlock, "SubSurfaceBlock"));
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Decorators$2f$nodeDecorator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["editableInPropertyPage"])("Apply albedo after sub-surface", 0 /* PropertyTypeForEdition.Boolean */ , "ADVANCED")
], SubSurfaceBlock.prototype, "applyAlbedoAfterSubSurface", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$typeStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RegisterClass"])("BABYLON.SubSurfaceBlock", SubSurfaceBlock); //# sourceMappingURL=subSurfaceBlock.js.map
}),
"[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/pbrMetallicRoughnessBlock.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "PBRMetallicRoughnessBlock": ()=>PBRMetallicRoughnessBlock
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/tslib.es6.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterialBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialBlockConnectionPointTypes.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialBlockTargets.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialSystemValues$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialSystemValues.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Input$2f$inputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Input/inputBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$typeStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Misc/typeStore.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$PBR$2f$pbrBaseMaterial$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/PBR/pbrBaseMaterial.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Decorators$2f$nodeDecorator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Decorators/nodeDecorator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialConnectionPointCustomObject$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterialConnectionPointCustomObject.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$PBR$2f$sheenBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/sheenBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$brdfTextureTools$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Misc/brdfTextureTools.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$materialFlags$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/materialFlags.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$PBR$2f$anisotropyBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/anisotropyBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$PBR$2f$reflectionBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/reflectionBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$PBR$2f$clearCoatBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/clearCoatBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$PBR$2f$iridescenceBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/iridescenceBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$PBR$2f$subSurfaceBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/subSurfaceBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Maths/math.color.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$logger$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Misc/logger.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$materialHelper$2e$functions$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/materialHelper.functions.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const MapOutputToVariable = {
    ambientClr: [
        "finalAmbient",
        ""
    ],
    diffuseDir: [
        "finalDiffuse",
        ""
    ],
    specularDir: [
        "finalSpecularScaled",
        "!defined(UNLIT) && defined(SPECULARTERM)"
    ],
    clearcoatDir: [
        "finalClearCoatScaled",
        "!defined(UNLIT) && defined(CLEARCOAT)"
    ],
    sheenDir: [
        "finalSheenScaled",
        "!defined(UNLIT) && defined(SHEEN)"
    ],
    diffuseInd: [
        "finalIrradiance",
        "!defined(UNLIT) && defined(REFLECTION)"
    ],
    specularInd: [
        "finalRadianceScaled",
        "!defined(UNLIT) && defined(REFLECTION)"
    ],
    clearcoatInd: [
        "clearcoatOut.finalClearCoatRadianceScaled",
        "!defined(UNLIT) && defined(REFLECTION) && defined(CLEARCOAT)"
    ],
    sheenInd: [
        "sheenOut.finalSheenRadianceScaled",
        "!defined(UNLIT) && defined(REFLECTION) && defined(SHEEN) && defined(ENVIRONMENTBRDF)"
    ],
    refraction: [
        "subSurfaceOut.finalRefraction",
        "!defined(UNLIT) && defined(SS_REFRACTION)"
    ],
    lighting: [
        "finalColor.rgb",
        ""
    ],
    shadow: [
        "aggShadow",
        ""
    ],
    alpha: [
        "alpha",
        ""
    ]
};
class PBRMetallicRoughnessBlock extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlock"] {
    static _OnGenerateOnlyFragmentCodeChanged(block, _propertyName) {
        const that = block;
        if (that.worldPosition.isConnected || that.worldNormal.isConnected) {
            that.generateOnlyFragmentCode = !that.generateOnlyFragmentCode;
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$logger$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Logger"].Error("The worldPosition and worldNormal inputs must not be connected to be able to switch!");
            return false;
        }
        that._setTarget();
        return true;
    }
    _setTarget() {
        this._setInitialTarget(this.generateOnlyFragmentCode ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].VertexAndFragment);
        this.getInputByName("worldPosition").target = this.generateOnlyFragmentCode ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Vertex;
        this.getInputByName("worldNormal").target = this.generateOnlyFragmentCode ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Vertex;
    }
    /**
     * Initialize the block and prepare the context for build
     * @param state defines the state that will be used for the build
     */ initialize(state) {
        state._excludeVariableName("vLightingIntensity");
        state._excludeVariableName("geometricNormalW");
        state._excludeVariableName("normalW");
        state._excludeVariableName("faceNormal");
        state._excludeVariableName("albedoOpacityOut");
        state._excludeVariableName("surfaceAlbedo");
        state._excludeVariableName("alpha");
        state._excludeVariableName("aoOut");
        state._excludeVariableName("baseColor");
        state._excludeVariableName("reflectivityOut");
        state._excludeVariableName("microSurface");
        state._excludeVariableName("roughness");
        state._excludeVariableName("vReflectivityColor");
        state._excludeVariableName("NdotVUnclamped");
        state._excludeVariableName("NdotV");
        state._excludeVariableName("alphaG");
        state._excludeVariableName("AARoughnessFactors");
        state._excludeVariableName("environmentBrdf");
        state._excludeVariableName("ambientMonochrome");
        state._excludeVariableName("seo");
        state._excludeVariableName("eho");
        state._excludeVariableName("environmentRadiance");
        state._excludeVariableName("irradianceVector");
        state._excludeVariableName("environmentIrradiance");
        state._excludeVariableName("diffuseBase");
        state._excludeVariableName("specularBase");
        state._excludeVariableName("preInfo");
        state._excludeVariableName("info");
        state._excludeVariableName("shadow");
        state._excludeVariableName("finalDiffuse");
        state._excludeVariableName("finalAmbient");
        state._excludeVariableName("ambientOcclusionForDirectDiffuse");
        state._excludeVariableName("finalColor");
        state._excludeVariableName("vClipSpacePosition");
        state._excludeVariableName("vDebugMode");
        // eslint-disable-next-line @typescript-eslint/no-floating-promises
        this._initShaderSourceAsync(state.shaderLanguage);
    }
    async _initShaderSourceAsync(shaderLanguage) {
        this._codeIsReady = false;
        if (shaderLanguage === 1 /* ShaderLanguage.WGSL */ ) {
            await Promise.all([
                __turbopack_context__.r("[project]/node_modules/@babylonjs/core/ShadersWGSL/pbr.vertex.js [app-client] (ecmascript, async loader)")(__turbopack_context__.i),
                __turbopack_context__.r("[project]/node_modules/@babylonjs/core/ShadersWGSL/pbr.fragment.js [app-client] (ecmascript, async loader)")(__turbopack_context__.i)
            ]);
        } else {
            await Promise.all([
                __turbopack_context__.r("[project]/node_modules/@babylonjs/core/Shaders/pbr.vertex.js [app-client] (ecmascript, async loader)")(__turbopack_context__.i),
                __turbopack_context__.r("[project]/node_modules/@babylonjs/core/Shaders/pbr.fragment.js [app-client] (ecmascript, async loader)")(__turbopack_context__.i)
            ]);
        }
        this._codeIsReady = true;
        this.onCodeIsReadyObservable.notifyObservers(this);
    }
    /**
     * Gets the current class name
     * @returns the class name
     */ getClassName() {
        return "PBRMetallicRoughnessBlock";
    }
    /**
     * Gets the world position input component
     */ get worldPosition() {
        return this._inputs[0];
    }
    /**
     * Gets the world normal input component
     */ get worldNormal() {
        return this._inputs[1];
    }
    /**
     * Gets the view matrix parameter
     */ get view() {
        return this._inputs[2];
    }
    /**
     * Gets the camera position input component
     */ get cameraPosition() {
        return this._inputs[3];
    }
    /**
     * Gets the perturbed normal input component
     */ get perturbedNormal() {
        return this._inputs[4];
    }
    /**
     * Gets the base color input component
     */ get baseColor() {
        return this._inputs[5];
    }
    /**
     * Gets the metallic input component
     */ get metallic() {
        return this._inputs[6];
    }
    /**
     * Gets the roughness input component
     */ get roughness() {
        return this._inputs[7];
    }
    /**
     * Gets the ambient occlusion input component
     */ get ambientOcc() {
        return this._inputs[8];
    }
    /**
     * Gets the opacity input component
     */ get opacity() {
        return this._inputs[9];
    }
    /**
     * Gets the index of refraction input component
     */ get indexOfRefraction() {
        return this._inputs[10];
    }
    /**
     * Gets the ambient color input component
     */ get ambientColor() {
        return this._inputs[11];
    }
    /**
     * Gets the reflection object parameters
     */ get reflection() {
        return this._inputs[12];
    }
    /**
     * Gets the clear coat object parameters
     */ get clearcoat() {
        return this._inputs[13];
    }
    /**
     * Gets the sheen object parameters
     */ get sheen() {
        return this._inputs[14];
    }
    /**
     * Gets the sub surface object parameters
     */ get subsurface() {
        return this._inputs[15];
    }
    /**
     * Gets the anisotropy object parameters
     */ get anisotropy() {
        return this._inputs[16];
    }
    /**
     * Gets the iridescence object parameters
     */ get iridescence() {
        return this._inputs[17];
    }
    /**
     * Gets the ambient output component
     */ get ambientClr() {
        return this._outputs[0];
    }
    /**
     * Gets the diffuse output component
     */ get diffuseDir() {
        return this._outputs[1];
    }
    /**
     * Gets the specular output component
     */ get specularDir() {
        return this._outputs[2];
    }
    /**
     * Gets the clear coat output component
     */ get clearcoatDir() {
        return this._outputs[3];
    }
    /**
     * Gets the sheen output component
     */ get sheenDir() {
        return this._outputs[4];
    }
    /**
     * Gets the indirect diffuse output component
     */ get diffuseInd() {
        return this._outputs[5];
    }
    /**
     * Gets the indirect specular output component
     */ get specularInd() {
        return this._outputs[6];
    }
    /**
     * Gets the indirect clear coat output component
     */ get clearcoatInd() {
        return this._outputs[7];
    }
    /**
     * Gets the indirect sheen output component
     */ get sheenInd() {
        return this._outputs[8];
    }
    /**
     * Gets the refraction output component
     */ get refraction() {
        return this._outputs[9];
    }
    /**
     * Gets the global lighting output component
     */ get lighting() {
        return this._outputs[10];
    }
    /**
     * Gets the shadow output component
     */ get shadow() {
        return this._outputs[11];
    }
    /**
     * Gets the alpha output component
     */ get alpha() {
        return this._outputs[12];
    }
    autoConfigure(material) {
        let additionalFilteringInfo = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : ()=>true;
        if (!this.cameraPosition.isConnected) {
            let cameraPositionInput = material.getInputBlockByPredicate((b)=>b.systemValue === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialSystemValues$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialSystemValues"].CameraPosition && additionalFilteringInfo(b));
            if (!cameraPositionInput) {
                cameraPositionInput = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Input$2f$inputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InputBlock"]("cameraPosition");
                cameraPositionInput.setAsSystemValue(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialSystemValues$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialSystemValues"].CameraPosition);
            }
            cameraPositionInput.output.connectTo(this.cameraPosition);
        }
        if (!this.view.isConnected) {
            let viewInput = material.getInputBlockByPredicate((b)=>b.systemValue === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialSystemValues$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialSystemValues"].View && additionalFilteringInfo(b));
            if (!viewInput) {
                viewInput = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Input$2f$inputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InputBlock"]("view");
                viewInput.setAsSystemValue(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialSystemValues$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialSystemValues"].View);
            }
            viewInput.output.connectTo(this.view);
        }
    }
    prepareDefines(defines, nodeMaterial, mesh) {
        if (!mesh) {
            return;
        }
        // General
        defines.setValue("PBR", true);
        defines.setValue("METALLICWORKFLOW", true);
        defines.setValue("DEBUGMODE", this.debugMode, true);
        defines.setValue("DEBUGMODE_FORCERETURN", true);
        defines.setValue("NORMALXYSCALE", true);
        defines.setValue("BUMP", this.perturbedNormal.isConnected, true);
        defines.setValue("LODBASEDMICROSFURACE", this._scene.getEngine().getCaps().textureLOD);
        // Albedo & Opacity
        defines.setValue("ALBEDO", false, true);
        defines.setValue("OPACITY", this.opacity.isConnected, true);
        // Ambient occlusion
        defines.setValue("AMBIENT", true, true);
        defines.setValue("AMBIENTINGRAYSCALE", false, true);
        // Reflectivity
        defines.setValue("REFLECTIVITY", false, true);
        defines.setValue("AOSTOREINMETALMAPRED", false, true);
        defines.setValue("METALLNESSSTOREINMETALMAPBLUE", false, true);
        defines.setValue("ROUGHNESSSTOREINMETALMAPALPHA", false, true);
        defines.setValue("ROUGHNESSSTOREINMETALMAPGREEN", false, true);
        // Lighting & colors
        if (this.lightFalloff === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$PBR$2f$pbrBaseMaterial$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PBRBaseMaterial"].LIGHTFALLOFF_STANDARD) {
            defines.setValue("USEPHYSICALLIGHTFALLOFF", false);
            defines.setValue("USEGLTFLIGHTFALLOFF", false);
        } else if (this.lightFalloff === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$PBR$2f$pbrBaseMaterial$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PBRBaseMaterial"].LIGHTFALLOFF_GLTF) {
            defines.setValue("USEPHYSICALLIGHTFALLOFF", false);
            defines.setValue("USEGLTFLIGHTFALLOFF", true);
        } else {
            defines.setValue("USEPHYSICALLIGHTFALLOFF", true);
            defines.setValue("USEGLTFLIGHTFALLOFF", false);
        }
        // Transparency
        const alphaTestCutOffString = this.alphaTestCutoff.toString();
        defines.setValue("ALPHABLEND", this.useAlphaBlending, true);
        defines.setValue("ALPHAFROMALBEDO", false, true);
        defines.setValue("ALPHATEST", this.useAlphaTest, true);
        defines.setValue("ALPHATESTVALUE", alphaTestCutOffString.indexOf(".") < 0 ? alphaTestCutOffString + "." : alphaTestCutOffString, true);
        defines.setValue("OPACITYRGB", false, true);
        // Rendering
        defines.setValue("RADIANCEOVERALPHA", this.useRadianceOverAlpha, true);
        defines.setValue("SPECULAROVERALPHA", this.useSpecularOverAlpha, true);
        defines.setValue("SPECULARAA", this._scene.getEngine().getCaps().standardDerivatives && this.enableSpecularAntiAliasing, true);
        defines.setValue("REALTIME_FILTERING", this.realTimeFiltering, true);
        const scene = mesh.getScene();
        const engine = scene.getEngine();
        if (engine._features.needTypeSuffixInShaderConstants) {
            defines.setValue("NUM_SAMPLES", this.realTimeFilteringQuality + "u", true);
        } else {
            defines.setValue("NUM_SAMPLES", "" + this.realTimeFilteringQuality, true);
        }
        defines.setValue("BASE_DIFFUSE_MODEL", this.baseDiffuseModel, true);
        // Advanced
        defines.setValue("BRDF_V_HEIGHT_CORRELATED", true);
        defines.setValue("LEGACY_SPECULAR_ENERGY_CONSERVATION", true);
        defines.setValue("MS_BRDF_ENERGY_CONSERVATION", this.useEnergyConservation, true);
        defines.setValue("RADIANCEOCCLUSION", this.useRadianceOcclusion, true);
        defines.setValue("HORIZONOCCLUSION", this.useHorizonOcclusion, true);
        defines.setValue("UNLIT", this.unlit, true);
        defines.setValue("FORCENORMALFORWARD", this.forceNormalForward, true);
        if (this._environmentBRDFTexture && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$materialFlags$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MaterialFlags"].ReflectionTextureEnabled) {
            defines.setValue("ENVIRONMENTBRDF", true);
            defines.setValue("ENVIRONMENTBRDF_RGBD", this._environmentBRDFTexture.isRGBD, true);
        } else {
            defines.setValue("ENVIRONMENTBRDF", false);
            defines.setValue("ENVIRONMENTBRDF_RGBD", false);
        }
        if (defines._areImageProcessingDirty && nodeMaterial.imageProcessingConfiguration) {
            nodeMaterial.imageProcessingConfiguration.prepareDefines(defines);
        }
        if (!defines._areLightsDirty) {
            return;
        }
        if (!this.light) {
            // Lights
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$materialHelper$2e$functions$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PrepareDefinesForLights"])(scene, mesh, defines, true, nodeMaterial.maxSimultaneousLights);
            defines._needNormals = true;
            // Multiview
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$materialHelper$2e$functions$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PrepareDefinesForMultiview"])(scene, defines);
        } else {
            const state = {
                needNormals: false,
                needRebuild: false,
                lightmapMode: false,
                shadowEnabled: false,
                specularEnabled: false
            };
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$materialHelper$2e$functions$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PrepareDefinesForLight"])(scene, mesh, this.light, this._lightId, defines, true, state);
            if (state.needRebuild) {
                defines.rebuild();
            }
        }
    }
    updateUniformsAndSamples(state, nodeMaterial, defines, uniformBuffers) {
        for(let lightIndex = 0; lightIndex < nodeMaterial.maxSimultaneousLights; lightIndex++){
            if (!defines["LIGHT" + lightIndex]) {
                break;
            }
            const onlyUpdateBuffersList = state.uniforms.indexOf("vLightData" + lightIndex) >= 0;
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$materialHelper$2e$functions$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PrepareUniformsAndSamplersForLight"])(lightIndex, state.uniforms, state.samplers, defines["PROJECTEDLIGHTTEXTURE" + lightIndex], uniformBuffers, onlyUpdateBuffersList, defines["IESLIGHTTEXTURE" + lightIndex]);
        }
    }
    isReady(mesh, nodeMaterial, defines) {
        if (this._environmentBRDFTexture && !this._environmentBRDFTexture.isReady()) {
            return false;
        }
        if (defines._areImageProcessingDirty && nodeMaterial.imageProcessingConfiguration) {
            if (!nodeMaterial.imageProcessingConfiguration.isReady()) {
                return false;
            }
        }
        return true;
    }
    bind(effect, nodeMaterial, mesh) {
        if (!mesh) {
            return;
        }
        const scene = mesh.getScene();
        if (!this.light) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$materialHelper$2e$functions$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BindLights"])(scene, mesh, effect, true, nodeMaterial.maxSimultaneousLights);
        } else {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$materialHelper$2e$functions$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BindLight"])(this.light, this._lightId, scene, effect, true);
        }
        effect.setTexture(this._environmentBrdfSamplerName, this._environmentBRDFTexture);
        effect.setFloat2("vDebugMode", this.debugLimit, this.debugFactor);
        const ambientScene = this._scene.ambientColor;
        if (ambientScene) {
            effect.setColor3("ambientFromScene", ambientScene);
        }
        const invertNormal = scene.useRightHandedSystem === (scene._mirroredCameraPosition != null);
        effect.setFloat(this._invertNormalName, invertNormal ? -1 : 1);
        effect.setFloat4("vLightingIntensity", this.directIntensity, 1, this.environmentIntensity * this._scene.environmentIntensity, this.specularIntensity);
        // reflectivity bindings
        const metallicF90 = this._metallicF0Factor;
        effect.setColor4(this._vMetallicReflectanceFactorsName, this._metallicReflectanceColor, metallicF90);
        if (nodeMaterial.imageProcessingConfiguration) {
            nodeMaterial.imageProcessingConfiguration.bind(effect);
        }
    }
    _injectVertexCode(state) {
        var _this_reflection_connectedPoint;
        const worldPos = this.worldPosition;
        const worldNormal = this.worldNormal;
        const comments = "//".concat(this.name);
        const isWebGPU = state.shaderLanguage === 1 /* ShaderLanguage.WGSL */ ;
        // Declaration
        if (!this.light) {
            // Emit for all lights
            state._emitFunctionFromInclude(state.supportUniformBuffers ? "lightVxUboDeclaration" : "lightVxFragmentDeclaration", comments, {
                repeatKey: "maxSimultaneousLights"
            });
            this._lightId = 0;
            state.sharedData.dynamicUniformBlocks.push(this);
        } else {
            this._lightId = (state.counters["lightCounter"] !== undefined ? state.counters["lightCounter"] : -1) + 1;
            state.counters["lightCounter"] = this._lightId;
            state._emitFunctionFromInclude(state.supportUniformBuffers ? "lightVxUboDeclaration" : "lightVxFragmentDeclaration", comments, {
                replaceStrings: [
                    {
                        search: /{X}/g,
                        replace: this._lightId.toString()
                    }
                ]
            }, this._lightId.toString());
        }
        // Inject code in vertex
        const worldPosVaryingName = "v_" + worldPos.associatedVariableName;
        if (state._emitVaryingFromString(worldPosVaryingName, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector4)) {
            state.compilationString += (isWebGPU ? "vertexOutputs." : "") + "".concat(worldPosVaryingName, " = ").concat(worldPos.associatedVariableName, ";\n");
        }
        const worldNormalVaryingName = "v_" + worldNormal.associatedVariableName;
        if (state._emitVaryingFromString(worldNormalVaryingName, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector4)) {
            state.compilationString += (isWebGPU ? "vertexOutputs." : "") + "".concat(worldNormalVaryingName, " = ").concat(worldNormal.associatedVariableName, ";\n");
        }
        const reflectionBlock = this.reflection.isConnected ? (_this_reflection_connectedPoint = this.reflection.connectedPoint) === null || _this_reflection_connectedPoint === void 0 ? void 0 : _this_reflection_connectedPoint.ownerBlock : null;
        if (reflectionBlock) {
            reflectionBlock.viewConnectionPoint = this.view;
        }
        var _reflectionBlock_handleVertexSide;
        state.compilationString += (_reflectionBlock_handleVertexSide = reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock.handleVertexSide(state)) !== null && _reflectionBlock_handleVertexSide !== void 0 ? _reflectionBlock_handleVertexSide : "";
        if (state._emitVaryingFromString("vClipSpacePosition", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector4, "defined(IGNORE) || DEBUGMODE > 0")) {
            state._injectAtEnd += "#if DEBUGMODE > 0\n";
            state._injectAtEnd += (isWebGPU ? "vertexOutputs." : "") + "vClipSpacePosition = ".concat(isWebGPU ? "vertexOutputs.position" : "gl_Position", ";\n");
            state._injectAtEnd += "#endif\n";
        }
        if (this.light) {
            state.compilationString += state._emitCodeFromInclude("shadowsVertex", comments, {
                replaceStrings: [
                    {
                        search: /{X}/g,
                        replace: this._lightId.toString()
                    },
                    {
                        search: /worldPos/g,
                        replace: worldPos.associatedVariableName
                    }
                ]
            });
        } else {
            state.compilationString += "".concat(state._declareLocalVar("worldPos", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector4), " = ").concat(worldPos.associatedVariableName, ";\n");
            if (this.view.isConnected) {
                state.compilationString += "".concat(state._declareLocalVar("view", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Matrix), " = ").concat(this.view.associatedVariableName, ";\n");
            }
            state.compilationString += state._emitCodeFromInclude("shadowsVertex", comments, {
                repeatKey: "maxSimultaneousLights"
            });
        }
    }
    _getAlbedoOpacityCode(state) {
        const isWebGPU = state.shaderLanguage === 1 /* ShaderLanguage.WGSL */ ;
        let code = isWebGPU ? "var albedoOpacityOut: albedoOpacityOutParams;\n" : "albedoOpacityOutParams albedoOpacityOut;\n";
        const albedoColor = this.baseColor.isConnected ? this.baseColor.associatedVariableName : "vec3(1.)";
        const opacity = this.opacity.isConnected ? this.opacity.associatedVariableName : "1.";
        code += "albedoOpacityOut = albedoOpacityBlock(\n                vec4".concat(state.fSuffix, "(").concat(albedoColor, ", 1.)\n            #ifdef ALBEDO\n                ,vec4").concat(state.fSuffix, "(1.)\n                ,vec2").concat(state.fSuffix, "(1., 1.)\n            #endif\n                ,1. /* Base Weight */\n            #ifdef OPACITY\n                ,vec4").concat(state.fSuffix, "(").concat(opacity, ")\n                ,vec2").concat(state.fSuffix, "(1., 1.)\n            #endif\n            );\n\n            ").concat(state._declareLocalVar("surfaceAlbedo", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3), " = albedoOpacityOut.surfaceAlbedo;\n            ").concat(state._declareLocalVar("alpha", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float), " = albedoOpacityOut.alpha;\n");
        return code;
    }
    _getAmbientOcclusionCode(state) {
        const isWebGPU = state.shaderLanguage === 1 /* ShaderLanguage.WGSL */ ;
        let code = isWebGPU ? "var aoOut: ambientOcclusionOutParams;\n" : "ambientOcclusionOutParams aoOut;\n";
        const ao = this.ambientOcc.isConnected ? this.ambientOcc.associatedVariableName : "1.";
        code += "aoOut = ambientOcclusionBlock(\n            #ifdef AMBIENT\n                vec3".concat(state.fSuffix, "(").concat(ao, "),\n                vec4").concat(state.fSuffix, "(0., 1.0, 1.0, 0.)\n            #endif\n            );\n");
        return code;
    }
    _getReflectivityCode(state) {
        var _this_indexOfRefraction_connectInputBlock;
        const isWebGPU = state.shaderLanguage === 1 /* ShaderLanguage.WGSL */ ;
        let code = isWebGPU ? "var reflectivityOut: reflectivityOutParams;\n" : "reflectivityOutParams reflectivityOut;\n";
        const aoIntensity = "1.";
        this._vMetallicReflectanceFactorsName = state._getFreeVariableName("vMetallicReflectanceFactors");
        state._emitUniformFromString(this._vMetallicReflectanceFactorsName, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector4);
        this._baseDiffuseRoughnessName = state._getFreeVariableName("baseDiffuseRoughness");
        state._emitUniformFromString(this._baseDiffuseRoughnessName, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float);
        const outsideIOR = 1; // consider air as clear coat and other layers would remap in the shader.
        var _this_indexOfRefraction_connectInputBlock_value;
        const ior = (_this_indexOfRefraction_connectInputBlock_value = (_this_indexOfRefraction_connectInputBlock = this.indexOfRefraction.connectInputBlock) === null || _this_indexOfRefraction_connectInputBlock === void 0 ? void 0 : _this_indexOfRefraction_connectInputBlock.value) !== null && _this_indexOfRefraction_connectInputBlock_value !== void 0 ? _this_indexOfRefraction_connectInputBlock_value : 1.5;
        // Based of the schlick fresnel approximation model
        // for dielectrics.
        const f0 = Math.pow((ior - outsideIOR) / (ior + outsideIOR), 2);
        code += "".concat(state._declareLocalVar("baseColor", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3), " = surfaceAlbedo;\n            ").concat(isWebGPU ? "let" : "vec4".concat(state.fSuffix), " vReflectivityColor = vec4").concat(state.fSuffix, "(").concat(this.metallic.associatedVariableName, ", ").concat(this.roughness.associatedVariableName, ", ").concat(this.indexOfRefraction.associatedVariableName || "1.5", ", ").concat(f0, ");\n            reflectivityOut = reflectivityBlock(\n                vReflectivityColor\n            #ifdef METALLICWORKFLOW\n                , surfaceAlbedo\n                , ").concat((isWebGPU ? "uniforms." : "") + this._vMetallicReflectanceFactorsName, "\n            #endif\n                , ").concat((isWebGPU ? "uniforms." : "") + this._baseDiffuseRoughnessName, "\n            #ifdef BASE_DIFFUSE_ROUGHNESS\n                , 0.\n                , vec2").concat(state.fSuffix, "(0., 0.)\n            #endif\n            #ifdef REFLECTIVITY\n                , vec3").concat(state.fSuffix, "(0., 0., ").concat(aoIntensity, ")\n                , vec4").concat(state.fSuffix, "(1.)\n            #endif\n            #if defined(METALLICWORKFLOW) && defined(REFLECTIVITY)  && defined(AOSTOREINMETALMAPRED)\n                , aoOut.ambientOcclusionColor\n            #endif\n            #ifdef MICROSURFACEMAP\n                , microSurfaceTexel <== not handled!\n            #endif\n            );\n\n            ").concat(state._declareLocalVar("microSurface", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float), " = reflectivityOut.microSurface;\n            ").concat(state._declareLocalVar("roughness", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float), " = reflectivityOut.roughness;\n            ").concat(state._declareLocalVar("diffuseRoughness", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float), " = reflectivityOut.diffuseRoughness;\n\n            #ifdef METALLICWORKFLOW\n                surfaceAlbedo = reflectivityOut.surfaceAlbedo;\n            #endif\n            #if defined(METALLICWORKFLOW) && defined(REFLECTIVITY) && defined(AOSTOREINMETALMAPRED)\n                aoOut.ambientOcclusionColor = reflectivityOut.ambientOcclusionColor;\n            #endif\n");
        return code;
    }
    _buildBlock(state) {
        var _this_reflection_connectedPoint, _this_anisotropy_connectedPoint, _this_sheen_connectedPoint, _this_clearcoat_connectedPoint, _this_iridescence_connectedPoint, _worldTangent, _this_perturbedNormal_connectedPoint, _this_anisotropy_connectedPoint1, _this_subsurface_connectedPoint, _refraction_connectedPoint, _this_subsurface_connectedPoint1;
        super._buildBlock(state);
        this._scene = state.sharedData.scene;
        const isWebGPU = state.shaderLanguage === 1 /* ShaderLanguage.WGSL */ ;
        if (!this._environmentBRDFTexture) {
            this._environmentBRDFTexture = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$brdfTextureTools$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GetEnvironmentBRDFTexture"])(this._scene);
        }
        const reflectionBlock = this.reflection.isConnected ? (_this_reflection_connectedPoint = this.reflection.connectedPoint) === null || _this_reflection_connectedPoint === void 0 ? void 0 : _this_reflection_connectedPoint.ownerBlock : null;
        if (reflectionBlock) {
            // Need those variables to be setup when calling _injectVertexCode
            reflectionBlock.worldPositionConnectionPoint = this.worldPosition;
            reflectionBlock.cameraPositionConnectionPoint = this.cameraPosition;
            reflectionBlock.worldNormalConnectionPoint = this.worldNormal;
            reflectionBlock.viewConnectionPoint = this.view;
        }
        if (state.target !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment) {
            // Vertex
            this._injectVertexCode(state);
            return this;
        }
        // Fragment
        state.sharedData.forcedBindableBlocks.push(this);
        state.sharedData.blocksWithDefines.push(this);
        state.sharedData.blockingBlocks.push(this);
        if (this.generateOnlyFragmentCode) {
            state.sharedData.dynamicUniformBlocks.push(this);
        }
        const comments = "//".concat(this.name);
        const normalShading = this.perturbedNormal;
        let worldPosVarName = this.worldPosition.associatedVariableName;
        let worldNormalVarName = this.worldNormal.associatedVariableName;
        if (this.generateOnlyFragmentCode) {
            worldPosVarName = state._getFreeVariableName("globalWorldPos");
            state._emitFunction("pbr_globalworldpos", isWebGPU ? "var<private> ".concat(worldPosVarName, ":vec3").concat(state.fSuffix, ";\n") : "vec3".concat(state.fSuffix, " ").concat(worldPosVarName, ";\n"), comments);
            state.compilationString += "".concat(worldPosVarName, " = ").concat(this.worldPosition.associatedVariableName, ".xyz;\n");
            worldNormalVarName = state._getFreeVariableName("globalWorldNormal");
            state._emitFunction("pbr_globalworldnorm", isWebGPU ? "var<private> ".concat(worldNormalVarName, ":vec4").concat(state.fSuffix, ";\n") : "vec4".concat(state.fSuffix, " ").concat(worldNormalVarName, ";\n"), comments);
            state.compilationString += "".concat(worldNormalVarName, " = ").concat(this.worldNormal.associatedVariableName, ";\n");
            state.compilationString += state._emitCodeFromInclude("shadowsVertex", comments, {
                repeatKey: "maxSimultaneousLights",
                substitutionVars: this.generateOnlyFragmentCode ? "worldPos,".concat(this.worldPosition.associatedVariableName) : undefined
            });
            state.compilationString += "#if DEBUGMODE > 0\n";
            state.compilationString += "".concat(state._declareLocalVar("vClipSpacePosition", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector4), " = vec4").concat(state.fSuffix, "((vec2").concat(state.fSuffix, "(").concat(isWebGPU ? "fragmentInputs.position" : "gl_FragCoord.xy", ") / vec2").concat(state.fSuffix, "(1.0)) * 2.0 - 1.0, 0.0, 1.0);\n");
            state.compilationString += "#endif\n";
        } else {
            worldPosVarName = (isWebGPU ? "input." : "") + "v_" + worldPosVarName;
            worldNormalVarName = (isWebGPU ? "input." : "") + "v_" + worldNormalVarName;
        }
        this._environmentBrdfSamplerName = state._getFreeVariableName("environmentBrdfSampler");
        state._emit2DSampler(this._environmentBrdfSamplerName);
        state.sharedData.hints.needAlphaBlending = state.sharedData.hints.needAlphaBlending || this.useAlphaBlending;
        state.sharedData.hints.needAlphaTesting = state.sharedData.hints.needAlphaTesting || this.useAlphaTest;
        state._emitExtension("lod", "#extension GL_EXT_shader_texture_lod : enable", "defined(LODBASEDMICROSFURACE)");
        state._emitExtension("derivatives", "#extension GL_OES_standard_derivatives : enable");
        state._emitUniformFromString("vDebugMode", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector2, "defined(IGNORE) || DEBUGMODE > 0");
        state._emitUniformFromString("ambientFromScene", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3);
        // Image processing uniforms
        state.uniforms.push("exposureLinear");
        state.uniforms.push("contrast");
        state.uniforms.push("vInverseScreenSize");
        state.uniforms.push("vignetteSettings1");
        state.uniforms.push("vignetteSettings2");
        state.uniforms.push("vCameraColorCurveNegative");
        state.uniforms.push("vCameraColorCurveNeutral");
        state.uniforms.push("vCameraColorCurvePositive");
        state.uniforms.push("txColorTransform");
        state.uniforms.push("colorTransformSettings");
        state.uniforms.push("ditherIntensity");
        //
        // Includes
        //
        if (!this.light) {
            // Emit for all lights
            state._emitFunctionFromInclude(state.supportUniformBuffers ? "lightUboDeclaration" : "lightFragmentDeclaration", comments, {
                repeatKey: "maxSimultaneousLights",
                substitutionVars: this.generateOnlyFragmentCode ? "varying," : undefined
            });
        } else {
            state._emitFunctionFromInclude(state.supportUniformBuffers ? "lightUboDeclaration" : "lightFragmentDeclaration", comments, {
                replaceStrings: [
                    {
                        search: /{X}/g,
                        replace: this._lightId.toString()
                    }
                ]
            }, this._lightId.toString());
        }
        state._emitFunctionFromInclude("helperFunctions", comments);
        state._emitFunctionFromInclude("importanceSampling", comments);
        state._emitFunctionFromInclude("pbrHelperFunctions", comments);
        state._emitFunctionFromInclude("imageProcessingDeclaration", comments);
        state._emitFunctionFromInclude("imageProcessingFunctions", comments);
        state._emitFunctionFromInclude("shadowsFragmentFunctions", comments);
        state._emitFunctionFromInclude("pbrDirectLightingSetupFunctions", comments);
        state._emitFunctionFromInclude("pbrDirectLightingFalloffFunctions", comments);
        var _reflectionBlock__defineSkyboxName;
        state._emitFunctionFromInclude("pbrBRDFFunctions", comments, {
            replaceStrings: [
                {
                    search: /REFLECTIONMAP_SKYBOX/g,
                    replace: (_reflectionBlock__defineSkyboxName = reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._defineSkyboxName) !== null && _reflectionBlock__defineSkyboxName !== void 0 ? _reflectionBlock__defineSkyboxName : "REFLECTIONMAP_SKYBOX"
                }
            ]
        });
        state._emitFunctionFromInclude("hdrFilteringFunctions", comments);
        state._emitFunctionFromInclude("pbrDirectLightingFunctions", comments);
        state._emitFunctionFromInclude("pbrIBLFunctions", comments);
        state._emitFunctionFromInclude("pbrBlockAlbedoOpacity", comments);
        state._emitFunctionFromInclude("pbrBlockReflectivity", comments);
        state._emitFunctionFromInclude("pbrBlockAmbientOcclusion", comments);
        state._emitFunctionFromInclude("pbrBlockAlphaFresnel", comments);
        state._emitFunctionFromInclude("pbrBlockAnisotropic", comments);
        //
        // code
        //
        state._emitUniformFromString("vLightingIntensity", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector4);
        if (reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock.generateOnlyFragmentCode) {
            state.compilationString += reflectionBlock.handleVertexSide(state);
        }
        // _____________________________ Geometry Information ____________________________
        this._vNormalWName = state._getFreeVariableName("vNormalW");
        state.compilationString += "".concat(state._declareLocalVar(this._vNormalWName, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector4), " = normalize(").concat(worldNormalVarName, ");\n");
        if (state._registerTempVariable("viewDirectionW")) {
            state.compilationString += "".concat(state._declareLocalVar("viewDirectionW", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3), " = normalize(").concat(this.cameraPosition.associatedVariableName, " - ").concat(worldPosVarName, ".xyz);\n");
        }
        state.compilationString += "".concat(state._declareLocalVar("geometricNormalW", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3), " = ").concat(this._vNormalWName, ".xyz;\n");
        state.compilationString += "".concat(state._declareLocalVar("normalW", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3), " = ").concat(normalShading.isConnected ? "normalize(" + normalShading.associatedVariableName + ".xyz)" : "geometricNormalW", ";\n");
        this._invertNormalName = state._getFreeVariableName("invertNormal");
        state._emitUniformFromString(this._invertNormalName, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float);
        state.compilationString += state._emitCodeFromInclude("pbrBlockNormalFinal", comments, {
            replaceStrings: [
                {
                    search: /vPositionW/g,
                    replace: worldPosVarName + ".xyz"
                },
                {
                    search: /vEyePosition.w/g,
                    replace: this._invertNormalName
                }
            ]
        });
        // _____________________________ Albedo & Opacity ______________________________
        state.compilationString += this._getAlbedoOpacityCode(state);
        state.compilationString += state._emitCodeFromInclude("depthPrePass", comments);
        // _____________________________ AO  _______________________________
        state.compilationString += this._getAmbientOcclusionCode(state);
        state.compilationString += state._emitCodeFromInclude("pbrBlockLightmapInit", comments);
        // _____________________________ UNLIT  _______________________________
        state.compilationString += "#ifdef UNLIT\n                ".concat(state._declareLocalVar("diffuseBase", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3), " = vec3").concat(state.fSuffix, "(1., 1., 1.);\n            #else\n");
        // _____________________________ Reflectivity _______________________________
        state.compilationString += this._getReflectivityCode(state);
        var _reflectionBlock__defineSkyboxName1, _reflectionBlock__define3DName;
        // _____________________________ Geometry info _________________________________
        state.compilationString += state._emitCodeFromInclude("pbrBlockGeometryInfo", comments, {
            replaceStrings: [
                {
                    search: /REFLECTIONMAP_SKYBOX/g,
                    replace: (_reflectionBlock__defineSkyboxName1 = reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._defineSkyboxName) !== null && _reflectionBlock__defineSkyboxName1 !== void 0 ? _reflectionBlock__defineSkyboxName1 : "REFLECTIONMAP_SKYBOX"
                },
                {
                    search: /REFLECTIONMAP_3D/g,
                    replace: (_reflectionBlock__define3DName = reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._define3DName) !== null && _reflectionBlock__define3DName !== void 0 ? _reflectionBlock__define3DName : "REFLECTIONMAP_3D"
                }
            ]
        });
        // _____________________________ Anisotropy _______________________________________
        const anisotropyBlock = this.anisotropy.isConnected ? (_this_anisotropy_connectedPoint = this.anisotropy.connectedPoint) === null || _this_anisotropy_connectedPoint === void 0 ? void 0 : _this_anisotropy_connectedPoint.ownerBlock : null;
        if (anisotropyBlock) {
            anisotropyBlock.worldPositionConnectionPoint = this.worldPosition;
            anisotropyBlock.worldNormalConnectionPoint = this.worldNormal;
            state.compilationString += anisotropyBlock.getCode(state, !this.perturbedNormal.isConnected);
        }
        // _____________________________ Reflection _______________________________________
        if (reflectionBlock && reflectionBlock.hasTexture) {
            state.compilationString += reflectionBlock.getCode(state, anisotropyBlock ? "anisotropicOut.anisotropicNormal" : "normalW");
        }
        var _reflectionBlock__define3DName1, _reflectionBlock__defineOppositeZ, _reflectionBlock__defineProjectionName, _reflectionBlock__defineSkyboxName2, _reflectionBlock__defineLODReflectionAlpha, _reflectionBlock__defineLinearSpecularReflection, _reflectionBlock__vReflectionFilteringInfoName;
        state._emitFunctionFromInclude("pbrBlockReflection", comments, {
            replaceStrings: [
                {
                    search: /computeReflectionCoords/g,
                    replace: "computeReflectionCoordsPBR"
                },
                {
                    search: /REFLECTIONMAP_3D/g,
                    replace: (_reflectionBlock__define3DName1 = reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._define3DName) !== null && _reflectionBlock__define3DName1 !== void 0 ? _reflectionBlock__define3DName1 : "REFLECTIONMAP_3D"
                },
                {
                    search: /REFLECTIONMAP_OPPOSITEZ/g,
                    replace: (_reflectionBlock__defineOppositeZ = reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._defineOppositeZ) !== null && _reflectionBlock__defineOppositeZ !== void 0 ? _reflectionBlock__defineOppositeZ : "REFLECTIONMAP_OPPOSITEZ"
                },
                {
                    search: /REFLECTIONMAP_PROJECTION/g,
                    replace: (_reflectionBlock__defineProjectionName = reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._defineProjectionName) !== null && _reflectionBlock__defineProjectionName !== void 0 ? _reflectionBlock__defineProjectionName : "REFLECTIONMAP_PROJECTION"
                },
                {
                    search: /REFLECTIONMAP_SKYBOX/g,
                    replace: (_reflectionBlock__defineSkyboxName2 = reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._defineSkyboxName) !== null && _reflectionBlock__defineSkyboxName2 !== void 0 ? _reflectionBlock__defineSkyboxName2 : "REFLECTIONMAP_SKYBOX"
                },
                {
                    search: /LODINREFLECTIONALPHA/g,
                    replace: (_reflectionBlock__defineLODReflectionAlpha = reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._defineLODReflectionAlpha) !== null && _reflectionBlock__defineLODReflectionAlpha !== void 0 ? _reflectionBlock__defineLODReflectionAlpha : "LODINREFLECTIONALPHA"
                },
                {
                    search: /LINEARSPECULARREFLECTION/g,
                    replace: (_reflectionBlock__defineLinearSpecularReflection = reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._defineLinearSpecularReflection) !== null && _reflectionBlock__defineLinearSpecularReflection !== void 0 ? _reflectionBlock__defineLinearSpecularReflection : "LINEARSPECULARREFLECTION"
                },
                {
                    search: /vReflectionFilteringInfo/g,
                    replace: (_reflectionBlock__vReflectionFilteringInfoName = reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._vReflectionFilteringInfoName) !== null && _reflectionBlock__vReflectionFilteringInfoName !== void 0 ? _reflectionBlock__vReflectionFilteringInfoName : "vReflectionFilteringInfo"
                }
            ]
        });
        // ___________________ Compute Reflectance aka R0 F0 info _________________________
        state.compilationString += state._emitCodeFromInclude("pbrBlockReflectance0", comments, {
            replaceStrings: [
                {
                    search: /metallicReflectanceFactors/g,
                    replace: (isWebGPU ? "uniforms." : "") + this._vMetallicReflectanceFactorsName
                }
            ]
        });
        // ________________________________ Sheen ______________________________
        const sheenBlock = this.sheen.isConnected ? (_this_sheen_connectedPoint = this.sheen.connectedPoint) === null || _this_sheen_connectedPoint === void 0 ? void 0 : _this_sheen_connectedPoint.ownerBlock : null;
        if (sheenBlock) {
            state.compilationString += sheenBlock.getCode(reflectionBlock, state);
        }
        var _reflectionBlock__define3DName2, _reflectionBlock__defineSkyboxName3, _reflectionBlock__defineLODReflectionAlpha1, _reflectionBlock__defineLinearSpecularReflection1;
        state._emitFunctionFromInclude("pbrBlockSheen", comments, {
            replaceStrings: [
                {
                    search: /REFLECTIONMAP_3D/g,
                    replace: (_reflectionBlock__define3DName2 = reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._define3DName) !== null && _reflectionBlock__define3DName2 !== void 0 ? _reflectionBlock__define3DName2 : "REFLECTIONMAP_3D"
                },
                {
                    search: /REFLECTIONMAP_SKYBOX/g,
                    replace: (_reflectionBlock__defineSkyboxName3 = reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._defineSkyboxName) !== null && _reflectionBlock__defineSkyboxName3 !== void 0 ? _reflectionBlock__defineSkyboxName3 : "REFLECTIONMAP_SKYBOX"
                },
                {
                    search: /LODINREFLECTIONALPHA/g,
                    replace: (_reflectionBlock__defineLODReflectionAlpha1 = reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._defineLODReflectionAlpha) !== null && _reflectionBlock__defineLODReflectionAlpha1 !== void 0 ? _reflectionBlock__defineLODReflectionAlpha1 : "LODINREFLECTIONALPHA"
                },
                {
                    search: /LINEARSPECULARREFLECTION/g,
                    replace: (_reflectionBlock__defineLinearSpecularReflection1 = reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._defineLinearSpecularReflection) !== null && _reflectionBlock__defineLinearSpecularReflection1 !== void 0 ? _reflectionBlock__defineLinearSpecularReflection1 : "LINEARSPECULARREFLECTION"
                }
            ]
        });
        // ____________________ Clear Coat Initialization Code _____________________
        const clearcoatBlock = this.clearcoat.isConnected ? (_this_clearcoat_connectedPoint = this.clearcoat.connectedPoint) === null || _this_clearcoat_connectedPoint === void 0 ? void 0 : _this_clearcoat_connectedPoint.ownerBlock : null;
        state.compilationString += __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$PBR$2f$clearCoatBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ClearCoatBlock"]._GetInitializationCode(state, clearcoatBlock);
        // _____________________________ Iridescence _______________________________
        const iridescenceBlock = this.iridescence.isConnected ? (_this_iridescence_connectedPoint = this.iridescence.connectedPoint) === null || _this_iridescence_connectedPoint === void 0 ? void 0 : _this_iridescence_connectedPoint.ownerBlock : null;
        state.compilationString += __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$PBR$2f$iridescenceBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["IridescenceBlock"].GetCode(iridescenceBlock, state);
        state._emitFunctionFromInclude("pbrBlockIridescence", comments, {
            replaceStrings: []
        });
        // _____________________________ Clear Coat ____________________________
        const generateTBNSpace = !this.perturbedNormal.isConnected && !this.anisotropy.isConnected;
        const isTangentConnectedToPerturbNormal = this.perturbedNormal.isConnected && ((_worldTangent = ((_this_perturbedNormal_connectedPoint = this.perturbedNormal.connectedPoint) === null || _this_perturbedNormal_connectedPoint === void 0 ? void 0 : _this_perturbedNormal_connectedPoint.ownerBlock).worldTangent) === null || _worldTangent === void 0 ? void 0 : _worldTangent.isConnected);
        const isTangentConnectedToAnisotropy = this.anisotropy.isConnected && ((_this_anisotropy_connectedPoint1 = this.anisotropy.connectedPoint) === null || _this_anisotropy_connectedPoint1 === void 0 ? void 0 : _this_anisotropy_connectedPoint1.ownerBlock).worldTangent.isConnected;
        let vTBNAvailable = isTangentConnectedToPerturbNormal || !this.perturbedNormal.isConnected && isTangentConnectedToAnisotropy;
        state.compilationString += __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$PBR$2f$clearCoatBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ClearCoatBlock"].GetCode(state, clearcoatBlock, reflectionBlock, worldPosVarName, generateTBNSpace, vTBNAvailable, worldNormalVarName);
        if (generateTBNSpace) {
            var _clearcoatBlock_worldTangent_isConnected;
            vTBNAvailable = (_clearcoatBlock_worldTangent_isConnected = clearcoatBlock === null || clearcoatBlock === void 0 ? void 0 : clearcoatBlock.worldTangent.isConnected) !== null && _clearcoatBlock_worldTangent_isConnected !== void 0 ? _clearcoatBlock_worldTangent_isConnected : false;
        }
        var _reflectionBlock__define3DName3, _reflectionBlock__defineOppositeZ1, _reflectionBlock__defineProjectionName1, _reflectionBlock__defineSkyboxName4, _reflectionBlock__defineLODReflectionAlpha2, _reflectionBlock__defineLinearSpecularReflection2;
        state._emitFunctionFromInclude("pbrBlockClearcoat", comments, {
            replaceStrings: [
                {
                    search: /computeReflectionCoords/g,
                    replace: "computeReflectionCoordsPBR"
                },
                {
                    search: /REFLECTIONMAP_3D/g,
                    replace: (_reflectionBlock__define3DName3 = reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._define3DName) !== null && _reflectionBlock__define3DName3 !== void 0 ? _reflectionBlock__define3DName3 : "REFLECTIONMAP_3D"
                },
                {
                    search: /REFLECTIONMAP_OPPOSITEZ/g,
                    replace: (_reflectionBlock__defineOppositeZ1 = reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._defineOppositeZ) !== null && _reflectionBlock__defineOppositeZ1 !== void 0 ? _reflectionBlock__defineOppositeZ1 : "REFLECTIONMAP_OPPOSITEZ"
                },
                {
                    search: /REFLECTIONMAP_PROJECTION/g,
                    replace: (_reflectionBlock__defineProjectionName1 = reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._defineProjectionName) !== null && _reflectionBlock__defineProjectionName1 !== void 0 ? _reflectionBlock__defineProjectionName1 : "REFLECTIONMAP_PROJECTION"
                },
                {
                    search: /REFLECTIONMAP_SKYBOX/g,
                    replace: (_reflectionBlock__defineSkyboxName4 = reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._defineSkyboxName) !== null && _reflectionBlock__defineSkyboxName4 !== void 0 ? _reflectionBlock__defineSkyboxName4 : "REFLECTIONMAP_SKYBOX"
                },
                {
                    search: /LODINREFLECTIONALPHA/g,
                    replace: (_reflectionBlock__defineLODReflectionAlpha2 = reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._defineLODReflectionAlpha) !== null && _reflectionBlock__defineLODReflectionAlpha2 !== void 0 ? _reflectionBlock__defineLODReflectionAlpha2 : "LODINREFLECTIONALPHA"
                },
                {
                    search: /LINEARSPECULARREFLECTION/g,
                    replace: (_reflectionBlock__defineLinearSpecularReflection2 = reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._defineLinearSpecularReflection) !== null && _reflectionBlock__defineLinearSpecularReflection2 !== void 0 ? _reflectionBlock__defineLinearSpecularReflection2 : "LINEARSPECULARREFLECTION"
                },
                {
                    search: /defined\(TANGENT\)/g,
                    replace: vTBNAvailable ? "defined(TANGENT)" : "defined(IGNORE)"
                }
            ]
        });
        var _reflectionBlock__defineSkyboxName5, _reflectionBlock__define3DName4;
        // _________________________ Specular Environment Reflectance __________________________
        state.compilationString += state._emitCodeFromInclude("pbrBlockReflectance", comments, {
            replaceStrings: [
                {
                    search: /REFLECTIONMAP_SKYBOX/g,
                    replace: (_reflectionBlock__defineSkyboxName5 = reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._defineSkyboxName) !== null && _reflectionBlock__defineSkyboxName5 !== void 0 ? _reflectionBlock__defineSkyboxName5 : "REFLECTIONMAP_SKYBOX"
                },
                {
                    search: /REFLECTIONMAP_3D/g,
                    replace: (_reflectionBlock__define3DName4 = reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._define3DName) !== null && _reflectionBlock__define3DName4 !== void 0 ? _reflectionBlock__define3DName4 : "REFLECTIONMAP_3D"
                },
                {
                    search: /uniforms\.vReflectivityColor/g,
                    replace: "vReflectivityColor"
                }
            ]
        });
        // ___________________________________ SubSurface ______________________________________
        const subsurfaceBlock = this.subsurface.isConnected ? (_this_subsurface_connectedPoint = this.subsurface.connectedPoint) === null || _this_subsurface_connectedPoint === void 0 ? void 0 : _this_subsurface_connectedPoint.ownerBlock : null;
        const refractionBlock = this.subsurface.isConnected ? (_refraction_connectedPoint = ((_this_subsurface_connectedPoint1 = this.subsurface.connectedPoint) === null || _this_subsurface_connectedPoint1 === void 0 ? void 0 : _this_subsurface_connectedPoint1.ownerBlock).refraction.connectedPoint) === null || _refraction_connectedPoint === void 0 ? void 0 : _refraction_connectedPoint.ownerBlock : null;
        if (refractionBlock) {
            refractionBlock.viewConnectionPoint = this.view;
            refractionBlock.indexOfRefractionConnectionPoint = this.indexOfRefraction;
        }
        state.compilationString += __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$PBR$2f$subSurfaceBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubSurfaceBlock"].GetCode(state, subsurfaceBlock, reflectionBlock, worldPosVarName);
        var _reflectionBlock__define3DName5, _reflectionBlock__defineOppositeZ2, _reflectionBlock__defineProjectionName2, _refractionBlock__define3DName, _refractionBlock__defineLODRefractionAlpha, _refractionBlock__defineLinearSpecularRefraction, _refractionBlock__defineOppositeZ;
        state._emitFunctionFromInclude("pbrBlockSubSurface", comments, {
            replaceStrings: [
                {
                    search: /REFLECTIONMAP_3D/g,
                    replace: (_reflectionBlock__define3DName5 = reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._define3DName) !== null && _reflectionBlock__define3DName5 !== void 0 ? _reflectionBlock__define3DName5 : "REFLECTIONMAP_3D"
                },
                {
                    search: /REFLECTIONMAP_OPPOSITEZ/g,
                    replace: (_reflectionBlock__defineOppositeZ2 = reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._defineOppositeZ) !== null && _reflectionBlock__defineOppositeZ2 !== void 0 ? _reflectionBlock__defineOppositeZ2 : "REFLECTIONMAP_OPPOSITEZ"
                },
                {
                    search: /REFLECTIONMAP_PROJECTION/g,
                    replace: (_reflectionBlock__defineProjectionName2 = reflectionBlock === null || reflectionBlock === void 0 ? void 0 : reflectionBlock._defineProjectionName) !== null && _reflectionBlock__defineProjectionName2 !== void 0 ? _reflectionBlock__defineProjectionName2 : "REFLECTIONMAP_PROJECTION"
                },
                {
                    search: /SS_REFRACTIONMAP_3D/g,
                    replace: (_refractionBlock__define3DName = refractionBlock === null || refractionBlock === void 0 ? void 0 : refractionBlock._define3DName) !== null && _refractionBlock__define3DName !== void 0 ? _refractionBlock__define3DName : "SS_REFRACTIONMAP_3D"
                },
                {
                    search: /SS_LODINREFRACTIONALPHA/g,
                    replace: (_refractionBlock__defineLODRefractionAlpha = refractionBlock === null || refractionBlock === void 0 ? void 0 : refractionBlock._defineLODRefractionAlpha) !== null && _refractionBlock__defineLODRefractionAlpha !== void 0 ? _refractionBlock__defineLODRefractionAlpha : "SS_LODINREFRACTIONALPHA"
                },
                {
                    search: /SS_LINEARSPECULARREFRACTION/g,
                    replace: (_refractionBlock__defineLinearSpecularRefraction = refractionBlock === null || refractionBlock === void 0 ? void 0 : refractionBlock._defineLinearSpecularRefraction) !== null && _refractionBlock__defineLinearSpecularRefraction !== void 0 ? _refractionBlock__defineLinearSpecularRefraction : "SS_LINEARSPECULARREFRACTION"
                },
                {
                    search: /SS_REFRACTIONMAP_OPPOSITEZ/g,
                    replace: (_refractionBlock__defineOppositeZ = refractionBlock === null || refractionBlock === void 0 ? void 0 : refractionBlock._defineOppositeZ) !== null && _refractionBlock__defineOppositeZ !== void 0 ? _refractionBlock__defineOppositeZ : "SS_REFRACTIONMAP_OPPOSITEZ"
                }
            ]
        });
        // _____________________________ Direct Lighting Info __________________________________
        state.compilationString += state._emitCodeFromInclude("pbrBlockDirectLighting", comments);
        if (this.light) {
            state.compilationString += state._emitCodeFromInclude("lightFragment", comments, {
                replaceStrings: [
                    {
                        search: /{X}/g,
                        replace: this._lightId.toString()
                    },
                    {
                        search: new RegExp("".concat(isWebGPU ? "fragmentInputs." : "", "vPositionW"), "g"),
                        replace: worldPosVarName + ".xyz"
                    },
                    {
                        search: /uniforms\.vReflectivityColor/g,
                        replace: "vReflectivityColor"
                    }
                ]
            });
        } else {
            state.compilationString += state._emitCodeFromInclude("lightFragment", comments, {
                repeatKey: "maxSimultaneousLights",
                substitutionVars: "".concat(isWebGPU ? "fragmentInputs." : "", "vPositionW,").concat(worldPosVarName, ".xyz,uniforms.vReflectivityColor,vReflectivityColor")
            });
        }
        // _____________________________ Compute Final Lit Components ________________________
        state.compilationString += state._emitCodeFromInclude("pbrBlockFinalLitComponents", comments);
        // _____________________________ UNLIT (2) ________________________
        state.compilationString += "#endif\n"; // UNLIT
        // _____________________________ Compute Final Unlit Components ________________________
        const aoColor = this.ambientColor.isConnected ? this.ambientColor.associatedVariableName : "vec3".concat(state.fSuffix, "(0., 0., 0.)");
        let aoDirectLightIntensity = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$PBR$2f$pbrBaseMaterial$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PBRBaseMaterial"].DEFAULT_AO_ON_ANALYTICAL_LIGHTS.toString();
        if (aoDirectLightIntensity.indexOf(".") === -1) {
            aoDirectLightIntensity += ".";
        }
        let replaceStrings = [
            {
                search: /vec3 finalEmissive[\s\S]*?finalEmissive\*=vLightingIntensity\.y;/g,
                replace: ""
            },
            {
                search: new RegExp("".concat(isWebGPU ? "uniforms." : "", "vAmbientColor"), "g"),
                replace: aoColor + " * ".concat(isWebGPU ? "uniforms." : "", "ambientFromScene")
            },
            {
                search: new RegExp("".concat(isWebGPU ? "uniforms." : "", "vAmbientInfos.w"), "g"),
                replace: aoDirectLightIntensity
            }
        ];
        if (isWebGPU) {
            replaceStrings[0] = {
                search: /var finalEmissive[\s\S]*?finalEmissive\*=uniforms.vLightingIntensity\.y;/g,
                replace: ""
            };
        }
        state.compilationString += state._emitCodeFromInclude("pbrBlockFinalUnlitComponents", comments, {
            replaceStrings: replaceStrings
        });
        // _____________________________ Output Final Color Composition ________________________
        state.compilationString += state._emitCodeFromInclude("pbrBlockFinalColorComposition", comments, {
            replaceStrings: [
                {
                    search: /finalEmissive/g,
                    replace: "vec3".concat(state.fSuffix, "(0.)")
                }
            ]
        });
        // _____________________________ Apply image processing ________________________
        if (isWebGPU) {
            replaceStrings = [
                {
                    search: /mesh.visibility/g,
                    replace: "1."
                }
            ];
        } else {
            replaceStrings = [
                {
                    search: /visibility/g,
                    replace: "1."
                }
            ];
        }
        state.compilationString += state._emitCodeFromInclude("pbrBlockImageProcessing", comments, {
            replaceStrings: replaceStrings
        });
        // _____________________________ Generate debug code ________________________
        const colorOutput = isWebGPU ? "fragmentOutputs.color" : "gl_FragColor";
        replaceStrings = [
            {
                search: new RegExp("".concat(isWebGPU ? "fragmentInputs." : "", "vNormalW"), "g"),
                replace: this._vNormalWName
            },
            {
                search: new RegExp("".concat(isWebGPU ? "fragmentInputs." : "", "vPositionW"), "g"),
                replace: worldPosVarName
            },
            {
                search: /uniforms\.vReflectivityColor/g,
                replace: "vReflectivityColor"
            },
            {
                search: /albedoTexture\.rgb;/g,
                replace: "vec3".concat(state.fSuffix, "(1.);\n").concat(colorOutput, ".rgb = toGammaSpace(").concat(colorOutput, ".rgb);\n")
            }
        ];
        state.compilationString += state._emitCodeFromInclude("pbrDebug", comments, {
            replaceStrings: replaceStrings
        });
        // _____________________________ Generate end points ________________________
        for (const output of this._outputs){
            if (output.hasEndpoints) {
                const remap = MapOutputToVariable[output.name];
                if (remap) {
                    const [varName, conditions] = remap;
                    if (conditions) {
                        state.compilationString += "#if ".concat(conditions, "\n");
                    }
                    state.compilationString += "".concat(state._declareOutput(output), " = ").concat(varName, ";\n");
                    if (conditions) {
                        state.compilationString += "#else\n";
                        state.compilationString += "".concat(state._declareOutput(output), " = vec3").concat(state.fSuffix, "(0.);\n");
                        state.compilationString += "#endif\n";
                    }
                } else {
                    state.sharedData.raiseBuildError("There's no remapping for the ".concat(output.name, " end point! No code generated"));
                }
            }
        }
        return this;
    }
    _dumpPropertiesCode() {
        let codeString = super._dumpPropertiesCode();
        codeString += "".concat(this._codeVariableName, ".lightFalloff = ").concat(this.lightFalloff, ";\n");
        codeString += "".concat(this._codeVariableName, ".useAlphaTest = ").concat(this.useAlphaTest, ";\n");
        codeString += "".concat(this._codeVariableName, ".alphaTestCutoff = ").concat(this.alphaTestCutoff, ";\n");
        codeString += "".concat(this._codeVariableName, ".useAlphaBlending = ").concat(this.useAlphaBlending, ";\n");
        codeString += "".concat(this._codeVariableName, ".useRadianceOverAlpha = ").concat(this.useRadianceOverAlpha, ";\n");
        codeString += "".concat(this._codeVariableName, ".useSpecularOverAlpha = ").concat(this.useSpecularOverAlpha, ";\n");
        codeString += "".concat(this._codeVariableName, ".enableSpecularAntiAliasing = ").concat(this.enableSpecularAntiAliasing, ";\n");
        codeString += "".concat(this._codeVariableName, ".realTimeFiltering = ").concat(this.realTimeFiltering, ";\n");
        codeString += "".concat(this._codeVariableName, ".realTimeFilteringQuality = ").concat(this.realTimeFilteringQuality, ";\n");
        codeString += "".concat(this._codeVariableName, ".useEnergyConservation = ").concat(this.useEnergyConservation, ";\n");
        codeString += "".concat(this._codeVariableName, ".useRadianceOcclusion = ").concat(this.useRadianceOcclusion, ";\n");
        codeString += "".concat(this._codeVariableName, ".useHorizonOcclusion = ").concat(this.useHorizonOcclusion, ";\n");
        codeString += "".concat(this._codeVariableName, ".unlit = ").concat(this.unlit, ";\n");
        codeString += "".concat(this._codeVariableName, ".forceNormalForward = ").concat(this.forceNormalForward, ";\n");
        codeString += "".concat(this._codeVariableName, ".debugMode = ").concat(this.debugMode, ";\n");
        codeString += "".concat(this._codeVariableName, ".debugLimit = ").concat(this.debugLimit, ";\n");
        codeString += "".concat(this._codeVariableName, ".debugFactor = ").concat(this.debugFactor, ";\n");
        return codeString;
    }
    serialize() {
        const serializationObject = super.serialize();
        if (this.light) {
            serializationObject.lightId = this.light.id;
        }
        serializationObject.lightFalloff = this.lightFalloff;
        serializationObject.useAlphaTest = this.useAlphaTest;
        serializationObject.alphaTestCutoff = this.alphaTestCutoff;
        serializationObject.useAlphaBlending = this.useAlphaBlending;
        serializationObject.useRadianceOverAlpha = this.useRadianceOverAlpha;
        serializationObject.useSpecularOverAlpha = this.useSpecularOverAlpha;
        serializationObject.enableSpecularAntiAliasing = this.enableSpecularAntiAliasing;
        serializationObject.realTimeFiltering = this.realTimeFiltering;
        serializationObject.realTimeFilteringQuality = this.realTimeFilteringQuality;
        serializationObject.useEnergyConservation = this.useEnergyConservation;
        serializationObject.useRadianceOcclusion = this.useRadianceOcclusion;
        serializationObject.useHorizonOcclusion = this.useHorizonOcclusion;
        serializationObject.unlit = this.unlit;
        serializationObject.forceNormalForward = this.forceNormalForward;
        serializationObject.debugMode = this.debugMode;
        serializationObject.debugLimit = this.debugLimit;
        serializationObject.debugFactor = this.debugFactor;
        serializationObject.generateOnlyFragmentCode = this.generateOnlyFragmentCode;
        return serializationObject;
    }
    _deserialize(serializationObject, scene, rootUrl) {
        super._deserialize(serializationObject, scene, rootUrl);
        if (serializationObject.lightId) {
            this.light = scene.getLightById(serializationObject.lightId);
        }
        var _serializationObject_lightFalloff;
        this.lightFalloff = (_serializationObject_lightFalloff = serializationObject.lightFalloff) !== null && _serializationObject_lightFalloff !== void 0 ? _serializationObject_lightFalloff : 0;
        this.useAlphaTest = serializationObject.useAlphaTest;
        this.alphaTestCutoff = serializationObject.alphaTestCutoff;
        this.useAlphaBlending = serializationObject.useAlphaBlending;
        this.useRadianceOverAlpha = serializationObject.useRadianceOverAlpha;
        this.useSpecularOverAlpha = serializationObject.useSpecularOverAlpha;
        this.enableSpecularAntiAliasing = serializationObject.enableSpecularAntiAliasing;
        this.realTimeFiltering = !!serializationObject.realTimeFiltering;
        var _serializationObject_realTimeFilteringQuality;
        this.realTimeFilteringQuality = (_serializationObject_realTimeFilteringQuality = serializationObject.realTimeFilteringQuality) !== null && _serializationObject_realTimeFilteringQuality !== void 0 ? _serializationObject_realTimeFilteringQuality : 8;
        this.useEnergyConservation = serializationObject.useEnergyConservation;
        this.useRadianceOcclusion = serializationObject.useRadianceOcclusion;
        this.useHorizonOcclusion = serializationObject.useHorizonOcclusion;
        this.unlit = serializationObject.unlit;
        this.forceNormalForward = !!serializationObject.forceNormalForward;
        this.debugMode = serializationObject.debugMode;
        this.debugLimit = serializationObject.debugLimit;
        this.debugFactor = serializationObject.debugFactor;
        this.generateOnlyFragmentCode = !!serializationObject.generateOnlyFragmentCode;
        this._setTarget();
    }
    /**
     * Create a new ReflectionBlock
     * @param name defines the block name
     */ constructor(name){
        super(name, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].VertexAndFragment);
        this._environmentBRDFTexture = null;
        this._metallicReflectanceColor = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Color3"].White();
        this._metallicF0Factor = 1;
        /**
         * Intensity of the direct lights e.g. the four lights available in your scene.
         * This impacts both the direct diffuse and specular highlights.
         */ this.directIntensity = 1.0;
        /**
         * Intensity of the environment e.g. how much the environment will light the object
         * either through harmonics for rough material or through the reflection for shiny ones.
         */ this.environmentIntensity = 1.0;
        /**
         * This is a special control allowing the reduction of the specular highlights coming from the
         * four lights of the scene. Those highlights may not be needed in full environment lighting.
         */ this.specularIntensity = 1.0;
        /**
         * Defines the  falloff type used in this material.
         * It by default is Physical.
         */ this.lightFalloff = 0;
        /**
         * Specifies that alpha test should be used
         */ this.useAlphaTest = false;
        /**
         * Defines the alpha limits in alpha test mode.
         */ this.alphaTestCutoff = 0.5;
        /**
         * Specifies that alpha blending should be used
         */ this.useAlphaBlending = false;
        /**
         * Specifies that the material will keeps the reflection highlights over a transparent surface (only the most luminous ones).
         * A car glass is a good example of that. When the street lights reflects on it you can not see what is behind.
         */ this.useRadianceOverAlpha = true;
        /**
         * Specifies that the material will keeps the specular highlights over a transparent surface (only the most luminous ones).
         * A car glass is a good example of that. When sun reflects on it you can not see what is behind.
         */ this.useSpecularOverAlpha = true;
        /**
         * Enables specular anti aliasing in the PBR shader.
         * It will both interacts on the Geometry for analytical and IBL lighting.
         * It also prefilter the roughness map based on the bump values.
         */ this.enableSpecularAntiAliasing = false;
        /**
         * Enables realtime filtering on the texture.
         */ this.realTimeFiltering = false;
        /**
         * Quality switch for realtime filtering
         */ this.realTimeFilteringQuality = 8;
        /**
         * Base Diffuse Model
         */ this.baseDiffuseModel = 0;
        /**
         * Defines if the material uses energy conservation.
         */ this.useEnergyConservation = true;
        /**
         * This parameters will enable/disable radiance occlusion by preventing the radiance to lit
         * too much the area relying on ambient texture to define their ambient occlusion.
         */ this.useRadianceOcclusion = true;
        /**
         * This parameters will enable/disable Horizon occlusion to prevent normal maps to look shiny when the normal
         * makes the reflect vector face the model (under horizon).
         */ this.useHorizonOcclusion = true;
        /**
         * If set to true, no lighting calculations will be applied.
         */ this.unlit = false;
        /**
         * Force normal to face away from face.
         */ this.forceNormalForward = false;
        /** Indicates that no code should be generated in the vertex shader. Can be useful in some specific circumstances (like when doing ray marching for eg) */ this.generateOnlyFragmentCode = false;
        /**
         * Defines the material debug mode.
         * It helps seeing only some components of the material while troubleshooting.
         */ this.debugMode = 0;
        /**
         * Specify from where on screen the debug mode should start.
         * The value goes from -1 (full screen) to 1 (not visible)
         * It helps with side by side comparison against the final render
         * This defaults to 0
         */ this.debugLimit = 0;
        /**
         * As the default viewing range might not be enough (if the ambient is really small for instance)
         * You can use the factor to better multiply the final value.
         */ this.debugFactor = 1;
        this._isUnique = true;
        this.registerInput("worldPosition", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector4, false, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Vertex);
        this.registerInput("worldNormal", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector4, false, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Vertex);
        this.registerInput("view", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Matrix, false);
        this.registerInput("cameraPosition", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3, false, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerInput("perturbedNormal", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector4, true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerInput("baseColor", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Color3, true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerInput("metallic", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float, false, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerInput("roughness", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float, false, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerInput("ambientOcc", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float, true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerInput("opacity", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float, true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerInput("indexOfRefraction", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float, true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerInput("ambientColor", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Color3, true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerInput("reflection", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Object, true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialConnectionPointCustomObject$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialConnectionPointCustomObject"]("reflection", this, 0 /* NodeMaterialConnectionPointDirection.Input */ , __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$PBR$2f$reflectionBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ReflectionBlock"], "ReflectionBlock"));
        this.registerInput("clearcoat", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Object, true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialConnectionPointCustomObject$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialConnectionPointCustomObject"]("clearcoat", this, 0 /* NodeMaterialConnectionPointDirection.Input */ , __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$PBR$2f$clearCoatBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ClearCoatBlock"], "ClearCoatBlock"));
        this.registerInput("sheen", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Object, true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialConnectionPointCustomObject$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialConnectionPointCustomObject"]("sheen", this, 0 /* NodeMaterialConnectionPointDirection.Input */ , __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$PBR$2f$sheenBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SheenBlock"], "SheenBlock"));
        this.registerInput("subsurface", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Object, true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialConnectionPointCustomObject$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialConnectionPointCustomObject"]("subsurface", this, 0 /* NodeMaterialConnectionPointDirection.Input */ , __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$PBR$2f$subSurfaceBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubSurfaceBlock"], "SubSurfaceBlock"));
        this.registerInput("anisotropy", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Object, true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialConnectionPointCustomObject$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialConnectionPointCustomObject"]("anisotropy", this, 0 /* NodeMaterialConnectionPointDirection.Input */ , __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$PBR$2f$anisotropyBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AnisotropyBlock"], "AnisotropyBlock"));
        this.registerInput("iridescence", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Object, true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialConnectionPointCustomObject$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialConnectionPointCustomObject"]("iridescence", this, 0 /* NodeMaterialConnectionPointDirection.Input */ , __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$PBR$2f$iridescenceBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["IridescenceBlock"], "IridescenceBlock"));
        this.registerOutput("ambientClr", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Color3, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerOutput("diffuseDir", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Color3, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerOutput("specularDir", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Color3, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerOutput("clearcoatDir", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Color3, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerOutput("sheenDir", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Color3, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerOutput("diffuseInd", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Color3, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerOutput("specularInd", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Color3, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerOutput("clearcoatInd", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Color3, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerOutput("sheenInd", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Color3, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerOutput("refraction", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Color3, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerOutput("lighting", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Color3, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerOutput("shadow", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
        this.registerOutput("alpha", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment);
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Decorators$2f$nodeDecorator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["editableInPropertyPage"])("Direct lights", 1 /* PropertyTypeForEdition.Float */ , "INTENSITY", {
        min: 0,
        max: 1,
        notifiers: {
            update: true
        }
    })
], PBRMetallicRoughnessBlock.prototype, "directIntensity", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Decorators$2f$nodeDecorator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["editableInPropertyPage"])("Environment lights", 1 /* PropertyTypeForEdition.Float */ , "INTENSITY", {
        min: 0,
        max: 1,
        notifiers: {
            update: true
        }
    })
], PBRMetallicRoughnessBlock.prototype, "environmentIntensity", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Decorators$2f$nodeDecorator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["editableInPropertyPage"])("Specular highlights", 1 /* PropertyTypeForEdition.Float */ , "INTENSITY", {
        min: 0,
        max: 1,
        notifiers: {
            update: true
        }
    })
], PBRMetallicRoughnessBlock.prototype, "specularIntensity", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Decorators$2f$nodeDecorator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["editableInPropertyPage"])("Light falloff", 4 /* PropertyTypeForEdition.List */ , "LIGHTING & COLORS", {
        notifiers: {
            update: true
        },
        options: [
            {
                label: "Physical",
                value: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$PBR$2f$pbrBaseMaterial$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PBRBaseMaterial"].LIGHTFALLOFF_PHYSICAL
            },
            {
                label: "GLTF",
                value: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$PBR$2f$pbrBaseMaterial$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PBRBaseMaterial"].LIGHTFALLOFF_GLTF
            },
            {
                label: "Standard",
                value: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$PBR$2f$pbrBaseMaterial$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PBRBaseMaterial"].LIGHTFALLOFF_STANDARD
            }
        ]
    })
], PBRMetallicRoughnessBlock.prototype, "lightFalloff", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Decorators$2f$nodeDecorator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["editableInPropertyPage"])("Alpha Testing", 0 /* PropertyTypeForEdition.Boolean */ , "OPACITY")
], PBRMetallicRoughnessBlock.prototype, "useAlphaTest", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Decorators$2f$nodeDecorator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["editableInPropertyPage"])("Alpha CutOff", 1 /* PropertyTypeForEdition.Float */ , "OPACITY", {
        min: 0,
        max: 1,
        notifiers: {
            update: true
        }
    })
], PBRMetallicRoughnessBlock.prototype, "alphaTestCutoff", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Decorators$2f$nodeDecorator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["editableInPropertyPage"])("Alpha blending", 0 /* PropertyTypeForEdition.Boolean */ , "OPACITY")
], PBRMetallicRoughnessBlock.prototype, "useAlphaBlending", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Decorators$2f$nodeDecorator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["editableInPropertyPage"])("Radiance over alpha", 0 /* PropertyTypeForEdition.Boolean */ , "RENDERING", {
        notifiers: {
            update: true
        }
    })
], PBRMetallicRoughnessBlock.prototype, "useRadianceOverAlpha", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Decorators$2f$nodeDecorator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["editableInPropertyPage"])("Specular over alpha", 0 /* PropertyTypeForEdition.Boolean */ , "RENDERING", {
        notifiers: {
            update: true
        }
    })
], PBRMetallicRoughnessBlock.prototype, "useSpecularOverAlpha", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Decorators$2f$nodeDecorator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["editableInPropertyPage"])("Specular anti-aliasing", 0 /* PropertyTypeForEdition.Boolean */ , "RENDERING", {
        notifiers: {
            update: true
        }
    })
], PBRMetallicRoughnessBlock.prototype, "enableSpecularAntiAliasing", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Decorators$2f$nodeDecorator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["editableInPropertyPage"])("Realtime filtering", 0 /* PropertyTypeForEdition.Boolean */ , "RENDERING", {
        notifiers: {
            update: true
        }
    })
], PBRMetallicRoughnessBlock.prototype, "realTimeFiltering", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Decorators$2f$nodeDecorator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["editableInPropertyPage"])("Realtime filtering quality", 4 /* PropertyTypeForEdition.List */ , "RENDERING", {
        notifiers: {
            update: true
        },
        options: [
            {
                label: "Low",
                value: 8
            },
            {
                label: "Medium",
                value: 16
            },
            {
                label: "High",
                value: 64
            }
        ]
    })
], PBRMetallicRoughnessBlock.prototype, "realTimeFilteringQuality", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Decorators$2f$nodeDecorator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["editableInPropertyPage"])("Diffuse Model", 4 /* PropertyTypeForEdition.List */ , "RENDERING", {
        notifiers: {
            update: true
        },
        options: [
            {
                label: "Lambert",
                value: 2
            },
            {
                label: "Burley",
                value: 1
            },
            {
                label: "Oren-Nayar",
                value: 0
            },
            {
                label: "Legacy",
                value: 3
            }
        ]
    })
], PBRMetallicRoughnessBlock.prototype, "baseDiffuseModel", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Decorators$2f$nodeDecorator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["editableInPropertyPage"])("Energy Conservation", 0 /* PropertyTypeForEdition.Boolean */ , "ADVANCED", {
        notifiers: {
            update: true
        }
    })
], PBRMetallicRoughnessBlock.prototype, "useEnergyConservation", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Decorators$2f$nodeDecorator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["editableInPropertyPage"])("Radiance occlusion", 0 /* PropertyTypeForEdition.Boolean */ , "ADVANCED", {
        notifiers: {
            update: true
        }
    })
], PBRMetallicRoughnessBlock.prototype, "useRadianceOcclusion", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Decorators$2f$nodeDecorator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["editableInPropertyPage"])("Horizon occlusion", 0 /* PropertyTypeForEdition.Boolean */ , "ADVANCED", {
        notifiers: {
            update: true
        }
    })
], PBRMetallicRoughnessBlock.prototype, "useHorizonOcclusion", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Decorators$2f$nodeDecorator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["editableInPropertyPage"])("Unlit", 0 /* PropertyTypeForEdition.Boolean */ , "ADVANCED", {
        notifiers: {
            update: true
        }
    })
], PBRMetallicRoughnessBlock.prototype, "unlit", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Decorators$2f$nodeDecorator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["editableInPropertyPage"])("Force normal forward", 0 /* PropertyTypeForEdition.Boolean */ , "ADVANCED", {
        notifiers: {
            update: true
        }
    })
], PBRMetallicRoughnessBlock.prototype, "forceNormalForward", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Decorators$2f$nodeDecorator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["editableInPropertyPage"])("Generate only fragment code", 0 /* PropertyTypeForEdition.Boolean */ , "ADVANCED", {
        notifiers: {
            rebuild: true,
            update: true,
            onValidation: PBRMetallicRoughnessBlock._OnGenerateOnlyFragmentCodeChanged
        }
    })
], PBRMetallicRoughnessBlock.prototype, "generateOnlyFragmentCode", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Decorators$2f$nodeDecorator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["editableInPropertyPage"])("Debug mode", 4 /* PropertyTypeForEdition.List */ , "DEBUG", {
        notifiers: {
            update: true
        },
        options: [
            {
                label: "None",
                value: 0
            },
            // Geometry
            {
                label: "Normalized position",
                value: 1
            },
            {
                label: "Normals",
                value: 2
            },
            {
                label: "Tangents",
                value: 3
            },
            {
                label: "Bitangents",
                value: 4
            },
            {
                label: "Bump Normals",
                value: 5
            },
            //{ label: "UV1", value: 6 },
            //{ label: "UV2", value: 7 },
            {
                label: "ClearCoat Normals",
                value: 8
            },
            {
                label: "ClearCoat Tangents",
                value: 9
            },
            {
                label: "ClearCoat Bitangents",
                value: 10
            },
            {
                label: "Anisotropic Normals",
                value: 11
            },
            {
                label: "Anisotropic Tangents",
                value: 12
            },
            {
                label: "Anisotropic Bitangents",
                value: 13
            },
            // Maps
            //{ label: "Emissive Map", value: 23 },
            //{ label: "Light Map", value: 24 },
            // Env
            {
                label: "Env Refraction",
                value: 40
            },
            {
                label: "Env Reflection",
                value: 41
            },
            {
                label: "Env Clear Coat",
                value: 42
            },
            // Lighting
            {
                label: "Direct Diffuse",
                value: 50
            },
            {
                label: "Direct Specular",
                value: 51
            },
            {
                label: "Direct Clear Coat",
                value: 52
            },
            {
                label: "Direct Sheen",
                value: 53
            },
            {
                label: "Env Irradiance",
                value: 54
            },
            // Lighting Params
            {
                label: "Surface Albedo",
                value: 60
            },
            {
                label: "Reflectance 0",
                value: 61
            },
            {
                label: "Metallic",
                value: 62
            },
            {
                label: "Metallic F0",
                value: 71
            },
            {
                label: "Roughness",
                value: 63
            },
            {
                label: "AlphaG",
                value: 64
            },
            {
                label: "NdotV",
                value: 65
            },
            {
                label: "ClearCoat Color",
                value: 66
            },
            {
                label: "ClearCoat Roughness",
                value: 67
            },
            {
                label: "ClearCoat NdotV",
                value: 68
            },
            {
                label: "Transmittance",
                value: 69
            },
            {
                label: "Refraction Transmittance",
                value: 70
            },
            // Misc
            {
                label: "SEO",
                value: 80
            },
            {
                label: "EHO",
                value: 81
            },
            {
                label: "Energy Factor",
                value: 82
            },
            {
                label: "Specular Reflectance",
                value: 83
            },
            {
                label: "Clear Coat Reflectance",
                value: 84
            },
            {
                label: "Sheen Reflectance",
                value: 85
            },
            {
                label: "Luminance Over Alpha",
                value: 86
            },
            {
                label: "Alpha",
                value: 87
            },
            {
                label: "Albedo color",
                value: 88
            },
            {
                label: "Ambient occlusion color",
                value: 89
            }
        ]
    })
], PBRMetallicRoughnessBlock.prototype, "debugMode", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Decorators$2f$nodeDecorator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["editableInPropertyPage"])("Split position", 1 /* PropertyTypeForEdition.Float */ , "DEBUG", {
        min: -1,
        max: 1,
        notifiers: {
            update: true
        }
    })
], PBRMetallicRoughnessBlock.prototype, "debugLimit", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Decorators$2f$nodeDecorator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["editableInPropertyPage"])("Output factor", 1 /* PropertyTypeForEdition.Float */ , "DEBUG", {
        min: 0,
        max: 5,
        notifiers: {
            update: true
        }
    })
], PBRMetallicRoughnessBlock.prototype, "debugFactor", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$typeStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RegisterClass"])("BABYLON.PBRMetallicRoughnessBlock", PBRMetallicRoughnessBlock); //# sourceMappingURL=pbrMetallicRoughnessBlock.js.map
}),
"[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/index.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$PBR$2f$pbrMetallicRoughnessBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/pbrMetallicRoughnessBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$PBR$2f$sheenBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/sheenBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$PBR$2f$anisotropyBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/anisotropyBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$PBR$2f$reflectionBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/reflectionBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$PBR$2f$clearCoatBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/clearCoatBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$PBR$2f$refractionBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/refractionBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$PBR$2f$subSurfaceBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/subSurfaceBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$PBR$2f$iridescenceBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/iridescenceBlock.js [app-client] (ecmascript)"); //# sourceMappingURL=index.js.map
;
;
;
;
;
;
;
;
}),
"[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/index.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$PBR$2f$pbrMetallicRoughnessBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/pbrMetallicRoughnessBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$PBR$2f$sheenBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/sheenBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$PBR$2f$anisotropyBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/anisotropyBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$PBR$2f$reflectionBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/reflectionBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$PBR$2f$clearCoatBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/clearCoatBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$PBR$2f$refractionBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/refractionBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$PBR$2f$subSurfaceBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/subSurfaceBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$PBR$2f$iridescenceBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/iridescenceBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$PBR$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/index.js [app-client] (ecmascript) <locals>");
}),
}]);

//# sourceMappingURL=node_modules_%40babylonjs_core_Materials_Node_Blocks_PBR_647c1564._.js.map