{"version": 3, "file": "buffer.align.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Buffers/buffer.align.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,UAAU,CAAC;AAChD,OAAO,EAAE,iBAAiB,EAAE,MAAM,eAAe,CAAC;AAElD,MAAM,cAAc,GAAG,CAAC,GAAG,EAAE;IACzB,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;IAChC,MAAM,IAAI,GAAG,IAAI,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IAE3C,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACxC,CAAC,CAAC,EAAE,CAAC;AA+BL,MAAM,CAAC,cAAc,CAAC,YAAY,CAAC,SAAS,EAAE,qBAAqB,EAAE;IACjE,GAAG,EAAE;QACD,OAAO,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC;IACtF,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;AAEH,MAAM,CAAC,cAAc,CAAC,YAAY,CAAC,SAAS,EAAE,qBAAqB,EAAE;IACjE,GAAG,EAAE;QACD,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC;IACrD,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;AAEH,MAAM,CAAC,cAAc,CAAC,YAAY,CAAC,SAAS,EAAE,iBAAiB,EAAE;IAC7D,GAAG,EAAE;QACD,OAAO,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;IAChG,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;AAEH,YAAY,CAAC,SAAS,CAAC,QAAQ,GAAG;IAC9B,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,CAAC;IACzB,IAAI,CAAC,cAAc,EAAE,QAAQ,EAAE,CAAC;AACpC,CAAC,CAAC;AAEF,YAAY,CAAC,SAAS,CAAC,OAAO,GAAG;IAC7B,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAED,IAAI,CAAC,cAAc,EAAE,OAAO,EAAE,CAAC;IAC/B,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;IAEhC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC5B,CAAC,CAAC;AAEF,YAAY,CAAC,SAAS,CAAC,gBAAgB,GAAG;IACtC,OAAO,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,OAAO,CAAC;AAC/C,CAAC,CAAC;AAEF,YAAY,CAAC,SAAS,CAAC,YAAY,GAAG;IAClC,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;IAEpC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,8CAA8C,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;QAC7I,OAAO;IACX,CAAC;IAED,MAAM,cAAc,GAAG,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACpD,MAAM,iBAAiB,GAAG,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACrD,MAAM,WAAW,GAAG,iBAAiB,GAAG,cAAc,CAAC;IACvD,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC;IAC7C,MAAM,eAAe,GAAG,aAAa,GAAG,iBAAiB,CAAC;IAC1D,MAAM,WAAW,GAAG,eAAe,GAAG,cAAc,CAAC;IAErD,IAAI,UAAoB,CAAC;IAEzB,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;QACtB,MAAM,iBAAiB,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC;QACjD,UAAU,GAAG,IAAI,QAAQ,CAAC,iBAAiB,CAAC,MAAM,EAAE,iBAAiB,CAAC,UAAU,EAAE,iBAAiB,CAAC,UAAU,CAAC,CAAC;IACpH,CAAC;SAAM,IAAI,IAAI,YAAY,WAAW,EAAE,CAAC;QACrC,UAAU,GAAG,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;IACxD,CAAC;SAAM,CAAC;QACJ,UAAU,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;IAC7E,CAAC;IAED,IAAI,WAAwG,CAAC;IAE7G,IAAI,IAAI,CAAC,IAAI,KAAK,YAAY,CAAC,IAAI,EAAE,CAAC;QAClC,WAAW,GAAG,IAAI,SAAS,CAAC,WAAW,CAAC,CAAC;IAC7C,CAAC;SAAM,IAAI,IAAI,CAAC,IAAI,KAAK,YAAY,CAAC,aAAa,EAAE,CAAC;QAClD,WAAW,GAAG,IAAI,UAAU,CAAC,WAAW,CAAC,CAAC;IAC9C,CAAC;SAAM,IAAI,IAAI,CAAC,IAAI,KAAK,YAAY,CAAC,KAAK,EAAE,CAAC;QAC1C,WAAW,GAAG,IAAI,UAAU,CAAC,WAAW,CAAC,CAAC;IAC9C,CAAC;SAAM,IAAI,IAAI,CAAC,IAAI,KAAK,YAAY,CAAC,cAAc,EAAE,CAAC;QACnD,WAAW,GAAG,IAAI,WAAW,CAAC,WAAW,CAAC,CAAC;IAC/C,CAAC;SAAM,IAAI,IAAI,CAAC,IAAI,KAAK,YAAY,CAAC,GAAG,EAAE,CAAC;QACxC,WAAW,GAAG,IAAI,UAAU,CAAC,WAAW,CAAC,CAAC;IAC9C,CAAC;SAAM,IAAI,IAAI,CAAC,IAAI,KAAK,YAAY,CAAC,YAAY,EAAE,CAAC;QACjD,WAAW,GAAG,IAAI,WAAW,CAAC,WAAW,CAAC,CAAC;IAC/C,CAAC;SAAM,CAAC;QACJ,WAAW,GAAG,IAAI,YAAY,CAAC,WAAW,CAAC,CAAC;IAChD,CAAC;IAED,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;IAErC,IAAI,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC;IAEnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,EAAE,EAAE,CAAC,EAAE,CAAC;QACrC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,EAAE,EAAE,CAAC,EAAE,CAAC;YACrC,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;gBAChB,KAAK,YAAY,CAAC,IAAI;oBAClB,WAAW,CAAC,CAAC,GAAG,WAAW,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;oBACxE,MAAM;gBACV,KAAK,YAAY,CAAC,aAAa;oBAC3B,WAAW,CAAC,CAAC,GAAG,WAAW,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,QAAQ,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;oBACzE,MAAM;gBACV,KAAK,YAAY,CAAC,KAAK;oBACnB,WAAW,CAAC,CAAC,GAAG,WAAW,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,QAAQ,CAAC,YAAY,GAAG,CAAC,GAAG,CAAC,EAAE,cAAc,CAAC,CAAC;oBAC7F,MAAM;gBACV,KAAK,YAAY,CAAC,cAAc;oBAC5B,WAAW,CAAC,CAAC,GAAG,WAAW,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,SAAS,CAAC,YAAY,GAAG,CAAC,GAAG,CAAC,EAAE,cAAc,CAAC,CAAC;oBAC9F,MAAM;gBACV,KAAK,YAAY,CAAC,GAAG;oBACjB,WAAW,CAAC,CAAC,GAAG,WAAW,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,QAAQ,CAAC,YAAY,GAAG,CAAC,GAAG,CAAC,EAAE,cAAc,CAAC,CAAC;oBAC7F,MAAM;gBACV,KAAK,YAAY,CAAC,YAAY;oBAC1B,WAAW,CAAC,CAAC,GAAG,WAAW,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,SAAS,CAAC,YAAY,GAAG,CAAC,GAAG,CAAC,EAAE,cAAc,CAAC,CAAC;oBAC9F,MAAM;gBACV,KAAK,YAAY,CAAC,KAAK;oBACnB,WAAW,CAAC,CAAC,GAAG,WAAW,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,UAAU,CAAC,YAAY,GAAG,CAAC,GAAG,CAAC,EAAE,cAAc,CAAC,CAAC;oBAC/F,MAAM;YACd,CAAC;QACL,CAAC;QACD,YAAY,IAAI,IAAI,CAAC,UAAU,CAAC;IACpC,CAAC;IAED,IAAI,CAAC,cAAc,EAAE,OAAO,EAAE,CAAC;IAC/B,IAAI,CAAC,cAAc,GAAG,IAAI,MAAM,CAC5B,IAAI,CAAC,MAAM,EACX,WAAW,EACX,KAAK,EACL,iBAAiB,EACjB,KAAK,EACL,IAAI,CAAC,cAAc,EAAE,EACrB,IAAI,EACJ,IAAI,CAAC,eAAe,EACpB,CAAC,IAAI,CAAC,MAAM,IAAI,cAAc,CAAC,GAAG,UAAU,CAC/C,CAAC;AACN,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"core/types\";\r\nimport type { <PERSON><PERSON><PERSON>er } from \"./dataBuffer\";\r\nimport { <PERSON><PERSON><PERSON>, VertexBuffer } from \"./buffer\";\r\nimport { GetTypeByteLength } from \"./bufferUtils\";\r\n\r\nconst IsLittleEndian = (() => {\r\n    const array = new Uint8Array(4);\r\n    const view = new Uint32Array(array.buffer);\r\n\r\n    return !!((view[0] = 1) & array[0]);\r\n})();\r\n\r\ndeclare module \"./buffer\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface VertexBuffer {\r\n        /**\r\n         * Gets the effective byte stride, that is the byte stride of the buffer that is actually sent to the GPU.\r\n         * It could be different from VertexBuffer.byteStride if a new buffer must be created under the hood because of the forceVertexBufferStrideAndOffsetMultiple4Bytes engine flag.\r\n         */\r\n        effectiveByteStride: number;\r\n\r\n        /**\r\n         * Gets the effective byte offset, that is the byte offset of the buffer that is actually sent to the GPU.\r\n         * It could be different from VertexBuffer.byteOffset if a new buffer must be created under the hood because of the forceVertexBufferStrideAndOffsetMultiple4Bytes engine flag.\r\n         */\r\n        effectiveByteOffset: number;\r\n\r\n        /**\r\n         * Gets the effective buffer, that is the buffer that is actually sent to the GPU.\r\n         * It could be different from VertexBuffer.getBuffer() if a new buffer must be created under the hood because of the forceVertexBufferStrideAndOffsetMultiple4Bytes engine flag.\r\n         */\r\n        effectiveBuffer: Nullable<DataBuffer>;\r\n\r\n        /** @internal */\r\n        _alignBuffer(): void;\r\n\r\n        /** @internal */\r\n        _alignedBuffer?: Buffer;\r\n    }\r\n}\r\n\r\nObject.defineProperty(VertexBuffer.prototype, \"effectiveByteStride\", {\r\n    get: function (this: VertexBuffer) {\r\n        return (this._alignedBuffer && this._alignedBuffer.byteStride) || this.byteStride;\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n\r\nObject.defineProperty(VertexBuffer.prototype, \"effectiveByteOffset\", {\r\n    get: function (this: VertexBuffer) {\r\n        return this._alignedBuffer ? 0 : this.byteOffset;\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n\r\nObject.defineProperty(VertexBuffer.prototype, \"effectiveBuffer\", {\r\n    get: function (this: VertexBuffer) {\r\n        return (this._alignedBuffer && this._alignedBuffer.getBuffer()) || this._buffer.getBuffer();\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n\r\nVertexBuffer.prototype._rebuild = function (): void {\r\n    this._buffer?._rebuild();\r\n    this._alignedBuffer?._rebuild();\r\n};\r\n\r\nVertexBuffer.prototype.dispose = function (): void {\r\n    if (this._ownsBuffer) {\r\n        this._buffer.dispose();\r\n    }\r\n\r\n    this._alignedBuffer?.dispose();\r\n    this._alignedBuffer = undefined;\r\n\r\n    this._isDisposed = true;\r\n};\r\n\r\nVertexBuffer.prototype.getWrapperBuffer = function (): Buffer {\r\n    return this._alignedBuffer || this._buffer;\r\n};\r\n\r\nVertexBuffer.prototype._alignBuffer = function (): void {\r\n    const data = this._buffer.getData();\r\n\r\n    if (!this.engine._features.forceVertexBufferStrideAndOffsetMultiple4Bytes || (this.byteStride % 4 === 0 && this.byteOffset % 4 === 0) || !data) {\r\n        return;\r\n    }\r\n\r\n    const typeByteLength = GetTypeByteLength(this.type);\r\n    const alignedByteStride = (this.byteStride + 3) & ~3;\r\n    const alignedSize = alignedByteStride / typeByteLength;\r\n    const totalVertices = this._maxVerticesCount;\r\n    const totalByteLength = totalVertices * alignedByteStride;\r\n    const totalLength = totalByteLength / typeByteLength;\r\n\r\n    let sourceData: DataView;\r\n\r\n    if (Array.isArray(data)) {\r\n        const sourceDataAsFloat = new Float32Array(data);\r\n        sourceData = new DataView(sourceDataAsFloat.buffer, sourceDataAsFloat.byteOffset, sourceDataAsFloat.byteLength);\r\n    } else if (data instanceof ArrayBuffer) {\r\n        sourceData = new DataView(data, 0, data.byteLength);\r\n    } else {\r\n        sourceData = new DataView(data.buffer, data.byteOffset, data.byteLength);\r\n    }\r\n\r\n    let alignedData: Int8Array | Uint8Array | Int16Array | Uint16Array | Int32Array | Uint32Array | Float32Array;\r\n\r\n    if (this.type === VertexBuffer.BYTE) {\r\n        alignedData = new Int8Array(totalLength);\r\n    } else if (this.type === VertexBuffer.UNSIGNED_BYTE) {\r\n        alignedData = new Uint8Array(totalLength);\r\n    } else if (this.type === VertexBuffer.SHORT) {\r\n        alignedData = new Int16Array(totalLength);\r\n    } else if (this.type === VertexBuffer.UNSIGNED_SHORT) {\r\n        alignedData = new Uint16Array(totalLength);\r\n    } else if (this.type === VertexBuffer.INT) {\r\n        alignedData = new Int32Array(totalLength);\r\n    } else if (this.type === VertexBuffer.UNSIGNED_INT) {\r\n        alignedData = new Uint32Array(totalLength);\r\n    } else {\r\n        alignedData = new Float32Array(totalLength);\r\n    }\r\n\r\n    const numComponents = this.getSize();\r\n\r\n    let sourceOffset = this.byteOffset;\r\n\r\n    for (let i = 0; i < totalVertices; ++i) {\r\n        for (let j = 0; j < numComponents; ++j) {\r\n            switch (this.type) {\r\n                case VertexBuffer.BYTE:\r\n                    alignedData[i * alignedSize + j] = sourceData.getInt8(sourceOffset + j);\r\n                    break;\r\n                case VertexBuffer.UNSIGNED_BYTE:\r\n                    alignedData[i * alignedSize + j] = sourceData.getUint8(sourceOffset + j);\r\n                    break;\r\n                case VertexBuffer.SHORT:\r\n                    alignedData[i * alignedSize + j] = sourceData.getInt16(sourceOffset + j * 2, IsLittleEndian);\r\n                    break;\r\n                case VertexBuffer.UNSIGNED_SHORT:\r\n                    alignedData[i * alignedSize + j] = sourceData.getUint16(sourceOffset + j * 2, IsLittleEndian);\r\n                    break;\r\n                case VertexBuffer.INT:\r\n                    alignedData[i * alignedSize + j] = sourceData.getInt32(sourceOffset + j * 4, IsLittleEndian);\r\n                    break;\r\n                case VertexBuffer.UNSIGNED_INT:\r\n                    alignedData[i * alignedSize + j] = sourceData.getUint32(sourceOffset + j * 4, IsLittleEndian);\r\n                    break;\r\n                case VertexBuffer.FLOAT:\r\n                    alignedData[i * alignedSize + j] = sourceData.getFloat32(sourceOffset + j * 4, IsLittleEndian);\r\n                    break;\r\n            }\r\n        }\r\n        sourceOffset += this.byteStride;\r\n    }\r\n\r\n    this._alignedBuffer?.dispose();\r\n    this._alignedBuffer = new Buffer(\r\n        this.engine,\r\n        alignedData,\r\n        false,\r\n        alignedByteStride,\r\n        false,\r\n        this.getIsInstanced(),\r\n        true,\r\n        this.instanceDivisor,\r\n        (this._label ?? \"VertexBuffer\") + \"_aligned\"\r\n    );\r\n};\r\n"]}