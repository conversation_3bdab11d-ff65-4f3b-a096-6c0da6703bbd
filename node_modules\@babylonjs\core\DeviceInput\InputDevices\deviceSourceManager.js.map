{"version": 3, "file": "deviceSourceManager.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/DeviceInput/InputDevices/deviceSourceManager.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAG3C,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AAGnD,OAAO,EAAE,2BAA2B,EAAE,MAAM,gCAAgC,CAAC;AAK7E;;GAEG;AACH,MAAM,OAAO,mBAAmB;IAkB5B,mBAAmB;IACnB;;;;;OAKG;IACI,eAAe,CAAuB,UAAa,EAAE,UAAmB;QAC3E,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;YAC3B,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,KAAK,SAAS,EAAE,CAAC;gBAC9C,OAAO,IAAI,CAAC;YAChB,CAAC;YAED,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,KAAK,SAAS,EAAE,CAAC;YACpF,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,UAAU,CAAoB,CAAC;IACpE,CAAC;IACD;;;;OAIG;IACI,gBAAgB,CAAuB,UAAa;QACvD,2EAA2E;QAC3E,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAC7B,OAAO,EAAE,CAAC;QACd,CAAC;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE;YAC/C,OAAO,CAAC,CAAC,MAAM,CAAC;QACpB,CAAC,CAA2B,CAAC;IACjC,CAAC;IAED;;;OAGG;IACH,YAAY,MAAsB;QAC9B,MAAM,mBAAmB,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;QAC/D,IAAI,CAAC,QAAQ,GAAG,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;QAC/C,IAAI,CAAC,YAAY,GAAG,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;QACnD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QAEtB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,CAAC;YACrC,IAAI,CAAC,OAAO,CAAC,oBAAoB,GAAG,IAAI,2BAA2B,CAAC,MAAM,CAAC,CAAC;QAChF,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,SAAS,EAAE,CAAC;QAE9C,cAAc;QACd,IAAI,CAAC,2BAA2B,GAAG,IAAI,UAAU,CAAC,CAAC,QAAQ,EAAE,EAAE;YAC3D,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClC,IAAI,OAAO,EAAE,CAAC;oBACV,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;wBAC3B,IAAI,MAAM,EAAE,CAAC;4BACT,IAAI,CAAC,2BAA2B,CAAC,cAAc,CAAC,QAAQ,EAAE,MAA0B,CAAC,CAAC;wBAC1F,CAAC;oBACL,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,8BAA8B,GAAG,IAAI,UAAU,EAAE,CAAC;QAEvD,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAExD,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC1D,IAAI,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,OAAO;QACV,2BAA2B;QAC3B,IAAI,CAAC,2BAA2B,CAAC,KAAK,EAAE,CAAC;QACzC,IAAI,CAAC,8BAA8B,CAAC,KAAK,EAAE,CAAC;QAE5C,IAAI,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,CAAC;YACpC,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAC1D,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC;gBACpD,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,OAAO,EAAE,CAAC;gBAC5C,OAAO,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC;YAC7C,CAAC;QACL,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;IACrE,CAAC;IAED,mBAAmB;IACnB;;;OAGG;IACI,UAAU,CAAC,YAA8B;QAC5C,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;YAC1C,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;QAChD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;YACnE,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,UAAU,CAAC,GAAG,YAAY,CAAC;YAC/E,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,CAAC,2BAA2B,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;IACnE,CAAC;IAED;;;;OAIG;IACI,aAAa,CAAC,UAAsB,EAAE,UAAkB;QAC3D,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,2DAA2D;QACzH,IAAI,CAAC,8BAA8B,CAAC,eAAe,CAAC,YAAgC,CAAC,CAAC;QACtF,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC;YAC1C,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,CAAC;QACjD,CAAC;QACD,sHAAsH;QACtH,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;IACzC,CAAC;IAED;;;;;OAKG;IACI,eAAe,CAAuB,UAAa,EAAE,UAAkB,EAAE,SAAmB;QAC/F,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,EAAE,wBAAwB,CAAC,eAAe,CAAC,SAAyD,CAAC,CAAC;IACjJ,CAAC;IAED,oBAAoB;IACZ,mBAAmB,CAAC,IAAgB;QACxC,QAAQ,IAAI,EAAE,CAAC;YACX,KAAK,UAAU,CAAC,QAAQ,CAAC;YACzB,KAAK,UAAU,CAAC,KAAK;gBACjB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAC5B,MAAM;YACV,KAAK,UAAU,CAAC,KAAK,CAAC;YACtB,KAAK,UAAU,CAAC,SAAS,CAAC;YAC1B,KAAK,UAAU,CAAC,SAAS,CAAC;YAC1B,KAAK,UAAU,CAAC,IAAI,CAAC;YACrB,KAAK,UAAU,CAAC,MAAM,CAAC;YACvB,KAAK,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC;gBACtB,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;gBAC/B,gDAAgD;gBAChD,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBACpC,IAAI,OAAO,EAAE,CAAC;oBACV,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;wBACtC,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;4BACb,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;4BAC5B,MAAM;wBACV,CAAC;oBACL,CAAC;gBACL,CAAC;gBACD,MAAM;YACV,CAAC;QACL,CAAC;IACL,CAAC;CACJ", "sourcesContent": ["import { DeviceType } from \"./deviceEnums\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport type { DeviceSource } from \"./deviceSource\";\r\nimport type { IObservableManager, DeviceSourceType } from \"../internalDeviceSourceManager\";\r\nimport { InternalDeviceSourceManager } from \"../internalDeviceSourceManager\";\r\nimport type { IDisposable } from \"../../scene\";\r\nimport type { AbstractEngine } from \"../../Engines/abstractEngine\";\r\nimport type { IKeyboardEvent, IPointerEvent, IUIEvent, IWheelEvent } from \"../../Events/deviceInputEvents\";\r\n\r\n/**\r\n * Class to keep track of devices\r\n */\r\nexport class DeviceSourceManager implements IDisposable, IObservableManager {\r\n    // Public Members\r\n    /**\r\n     * Observable to be triggered when after a device is connected, any new observers added will be triggered against already connected devices\r\n     */\r\n    public readonly onDeviceConnectedObservable: Observable<DeviceSourceType>;\r\n\r\n    /**\r\n     * Observable to be triggered when after a device is disconnected\r\n     */\r\n    public readonly onDeviceDisconnectedObservable: Observable<DeviceSourceType>;\r\n\r\n    // Private Members\r\n    private _engine: AbstractEngine;\r\n    private _onDisposeObserver: Nullable<Observer<AbstractEngine>>;\r\n    private readonly _devices: Array<Array<DeviceSource<DeviceType>>>;\r\n    private readonly _firstDevice: Array<number>;\r\n\r\n    // Public Functions\r\n    /**\r\n     * Gets a DeviceSource, given a type and slot\r\n     * @param deviceType - Type of Device\r\n     * @param deviceSlot - Slot or ID of device\r\n     * @returns DeviceSource\r\n     */\r\n    public getDeviceSource<T extends DeviceType>(deviceType: T, deviceSlot?: number): Nullable<DeviceSource<T>> {\r\n        if (deviceSlot === undefined) {\r\n            if (this._firstDevice[deviceType] === undefined) {\r\n                return null;\r\n            }\r\n\r\n            deviceSlot = this._firstDevice[deviceType];\r\n        }\r\n\r\n        if (!this._devices[deviceType] || this._devices[deviceType][deviceSlot] === undefined) {\r\n            return null;\r\n        }\r\n\r\n        return this._devices[deviceType][deviceSlot] as DeviceSource<T>;\r\n    }\r\n    /**\r\n     * Gets an array of DeviceSource objects for a given device type\r\n     * @param deviceType - Type of Device\r\n     * @returns All available DeviceSources of a given type\r\n     */\r\n    public getDeviceSources<T extends DeviceType>(deviceType: T): ReadonlyArray<DeviceSource<T>> {\r\n        // If device type hasn't had any devices connected yet, return empty array.\r\n        if (!this._devices[deviceType]) {\r\n            return [];\r\n        }\r\n        return this._devices[deviceType].filter((source) => {\r\n            return !!source;\r\n        }) as Array<DeviceSource<T>>;\r\n    }\r\n\r\n    /**\r\n     * Default constructor\r\n     * @param engine - Used to get canvas (if applicable)\r\n     */\r\n    constructor(engine: AbstractEngine) {\r\n        const numberOfDeviceTypes = Object.keys(DeviceType).length / 2;\r\n        this._devices = new Array(numberOfDeviceTypes);\r\n        this._firstDevice = new Array(numberOfDeviceTypes);\r\n        this._engine = engine;\r\n\r\n        if (!this._engine._deviceSourceManager) {\r\n            this._engine._deviceSourceManager = new InternalDeviceSourceManager(engine);\r\n        }\r\n        this._engine._deviceSourceManager._refCount++;\r\n\r\n        // Observables\r\n        this.onDeviceConnectedObservable = new Observable((observer) => {\r\n            for (const devices of this._devices) {\r\n                if (devices) {\r\n                    for (const device of devices) {\r\n                        if (device) {\r\n                            this.onDeviceConnectedObservable.notifyObserver(observer, device as DeviceSourceType);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        });\r\n        this.onDeviceDisconnectedObservable = new Observable();\r\n\r\n        this._engine._deviceSourceManager.registerManager(this);\r\n\r\n        this._onDisposeObserver = engine.onDisposeObservable.add(() => {\r\n            this.dispose();\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Dispose of DeviceSourceManager\r\n     */\r\n    public dispose(): void {\r\n        // Null out observable refs\r\n        this.onDeviceConnectedObservable.clear();\r\n        this.onDeviceDisconnectedObservable.clear();\r\n\r\n        if (this._engine._deviceSourceManager) {\r\n            this._engine._deviceSourceManager.unregisterManager(this);\r\n            if (--this._engine._deviceSourceManager._refCount < 1) {\r\n                this._engine._deviceSourceManager.dispose();\r\n                delete this._engine._deviceSourceManager;\r\n            }\r\n        }\r\n        this._engine.onDisposeObservable.remove(this._onDisposeObserver);\r\n    }\r\n\r\n    // Hidden Functions\r\n    /**\r\n     * @param deviceSource - Source to add\r\n     * @internal\r\n     */\r\n    public _addDevice(deviceSource: DeviceSourceType): void {\r\n        if (!this._devices[deviceSource.deviceType]) {\r\n            this._devices[deviceSource.deviceType] = [];\r\n        }\r\n\r\n        if (!this._devices[deviceSource.deviceType][deviceSource.deviceSlot]) {\r\n            this._devices[deviceSource.deviceType][deviceSource.deviceSlot] = deviceSource;\r\n            this._updateFirstDevices(deviceSource.deviceType);\r\n        }\r\n\r\n        this.onDeviceConnectedObservable.notifyObservers(deviceSource);\r\n    }\r\n\r\n    /**\r\n     * @param deviceType - DeviceType\r\n     * @param deviceSlot - DeviceSlot\r\n     * @internal\r\n     */\r\n    public _removeDevice(deviceType: DeviceType, deviceSlot: number): void {\r\n        const deviceSource = this._devices[deviceType]?.[deviceSlot]; // Grab local reference to use before removing from devices\r\n        this.onDeviceDisconnectedObservable.notifyObservers(deviceSource as DeviceSourceType);\r\n        if (this._devices[deviceType]?.[deviceSlot]) {\r\n            delete this._devices[deviceType][deviceSlot];\r\n        }\r\n        // Even if we don't delete a device, we should still check for the first device as things may have gotten out of sync.\r\n        this._updateFirstDevices(deviceType);\r\n    }\r\n\r\n    /**\r\n     * @param deviceType - DeviceType\r\n     * @param deviceSlot - DeviceSlot\r\n     * @param eventData - Event\r\n     * @internal\r\n     */\r\n    public _onInputChanged<T extends DeviceType>(deviceType: T, deviceSlot: number, eventData: IUIEvent): void {\r\n        this._devices[deviceType]?.[deviceSlot]?.onInputChangedObservable.notifyObservers(eventData as IKeyboardEvent | IWheelEvent | IPointerEvent);\r\n    }\r\n\r\n    // Private Functions\r\n    private _updateFirstDevices(type: DeviceType): void {\r\n        switch (type) {\r\n            case DeviceType.Keyboard:\r\n            case DeviceType.Mouse:\r\n                this._firstDevice[type] = 0;\r\n                break;\r\n            case DeviceType.Touch:\r\n            case DeviceType.DualSense:\r\n            case DeviceType.DualShock:\r\n            case DeviceType.Xbox:\r\n            case DeviceType.Switch:\r\n            case DeviceType.Generic: {\r\n                delete this._firstDevice[type];\r\n                // eslint-disable-next-line no-case-declarations\r\n                const devices = this._devices[type];\r\n                if (devices) {\r\n                    for (let i = 0; i < devices.length; i++) {\r\n                        if (devices[i]) {\r\n                            this._firstDevice[type] = i;\r\n                            break;\r\n                        }\r\n                    }\r\n                }\r\n                break;\r\n            }\r\n        }\r\n    }\r\n}\r\n"]}