{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/sheenBlock.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Blocks/PBR/sheenBlock.ts"], "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialConnectionPointDirection } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport { editableInPropertyPage, PropertyTypeForEdition } from \"../../../../Decorators/nodeDecorator\";\r\nimport { NodeMaterialConnectionPointCustomObject } from \"../../nodeMaterialConnectionPointCustomObject\";\r\nimport type { NodeMaterialDefines } from \"../../nodeMaterial\";\r\nimport type { ReflectionBlock } from \"./reflectionBlock\";\r\nimport type { Scene } from \"../../../../scene\";\r\nimport type { Nullable } from \"../../../../types\";\r\nimport { ShaderLanguage } from \"core/Materials/shaderLanguage\";\r\n\r\n/**\r\n * Block used to implement the sheen module of the PBR material\r\n */\r\nexport class SheenBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Create a new SheenBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this._isUnique = true;\r\n\r\n        this.registerInput(\"intensity\", NodeMaterialBlockConnectionPointTypes.Float, true, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"color\", NodeMaterialBlockConnectionPointTypes.Color3, true, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"roughness\", NodeMaterialBlockConnectionPointTypes.Float, true, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this.registerOutput(\r\n            \"sheen\",\r\n            NodeMaterialBlockConnectionPointTypes.Object,\r\n            NodeMaterialBlockTargets.Fragment,\r\n            new NodeMaterialConnectionPointCustomObject(\"sheen\", this, NodeMaterialConnectionPointDirection.Output, SheenBlock, \"SheenBlock\")\r\n        );\r\n    }\r\n\r\n    /**\r\n     * If true, the sheen effect is layered above the base BRDF with the albedo-scaling technique.\r\n     * It allows the strength of the sheen effect to not depend on the base color of the material,\r\n     * making it easier to setup and tweak the effect\r\n     */\r\n    @editableInPropertyPage(\"Albedo scaling\", PropertyTypeForEdition.Boolean, \"PROPERTIES\", { embedded: true, notifiers: { update: true } })\r\n    public albedoScaling: boolean = false;\r\n\r\n    /**\r\n     * Defines if the sheen is linked to the sheen color.\r\n     */\r\n    @editableInPropertyPage(\"Link sheen with albedo\", PropertyTypeForEdition.Boolean, \"PROPERTIES\", { embedded: true, notifiers: { update: true } })\r\n    public linkSheenWithAlbedo: boolean = false;\r\n\r\n    /**\r\n     * Initialize the block and prepare the context for build\r\n     * @param state defines the state that will be used for the build\r\n     */\r\n    public override initialize(state: NodeMaterialBuildState) {\r\n        state._excludeVariableName(\"sheenOut\");\r\n        state._excludeVariableName(\"sheenMapData\");\r\n        state._excludeVariableName(\"vSheenColor\");\r\n        state._excludeVariableName(\"vSheenRoughness\");\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public override getClassName() {\r\n        return \"SheenBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the intensity input component\r\n     */\r\n    public get intensity(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the color input component\r\n     */\r\n    public get color(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the roughness input component\r\n     */\r\n    public get roughness(): NodeMaterialConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the sheen object output component\r\n     */\r\n    public get sheen(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    public override prepareDefines(defines: NodeMaterialDefines) {\r\n        defines.setValue(\"SHEEN\", true);\r\n        defines.setValue(\"SHEEN_USE_ROUGHNESS_FROM_MAINTEXTURE\", true, true);\r\n        defines.setValue(\"SHEEN_LINKWITHALBEDO\", this.linkSheenWithAlbedo, true);\r\n        defines.setValue(\"SHEEN_ROUGHNESS\", this.roughness.isConnected, true);\r\n        defines.setValue(\"SHEEN_ALBEDOSCALING\", this.albedoScaling, true);\r\n    }\r\n\r\n    /**\r\n     * Gets the main code of the block (fragment side)\r\n     * @param reflectionBlock instance of a ReflectionBlock null if the code must be generated without an active reflection module\r\n     * @param state define the build state\r\n     * @returns the shader code\r\n     */\r\n    public getCode(reflectionBlock: Nullable<ReflectionBlock>, state: NodeMaterialBuildState): string {\r\n        let code = \"\";\r\n\r\n        const color = this.color.isConnected ? this.color.associatedVariableName : `vec3${state.fSuffix}(1.)`;\r\n        const intensity = this.intensity.isConnected ? this.intensity.associatedVariableName : \"1.\";\r\n        const roughness = this.roughness.isConnected ? this.roughness.associatedVariableName : \"0.\";\r\n        const texture = `vec4${state.fSuffix}(0.)`;\r\n        const isWebGPU = state.shaderLanguage === ShaderLanguage.WGSL;\r\n\r\n        code = `#ifdef SHEEN\r\n            ${isWebGPU ? \"var sheenOut: sheenOutParams\" : \"sheenOutParams sheenOut\"};\r\n\r\n            ${state._declareLocalVar(\"vSheenColor\", NodeMaterialBlockConnectionPointTypes.Vector4)} = vec4${state.fSuffix}(${color}, ${intensity});\r\n\r\n            sheenOut = sheenBlock(\r\n                vSheenColor\r\n            #ifdef SHEEN_ROUGHNESS\r\n                , ${roughness}\r\n            #endif\r\n                , roughness\r\n            #ifdef SHEEN_TEXTURE\r\n                , ${texture}\r\n                ${isWebGPU ? `, ${texture}Sampler` : \"\"}\r\n                , 1.0\r\n            #endif\r\n                , reflectanceF0\r\n            #ifdef SHEEN_LINKWITHALBEDO\r\n                , baseColor\r\n                , surfaceAlbedo\r\n            #endif\r\n            #ifdef ENVIRONMENTBRDF\r\n                , NdotV\r\n                , environmentBrdf\r\n            #endif\r\n            #if defined(REFLECTION) && defined(ENVIRONMENTBRDF)\r\n                , AARoughnessFactors\r\n                , ${isWebGPU ? \"uniforms.\" : \"\"}${reflectionBlock?._vReflectionMicrosurfaceInfosName}\r\n                , ${reflectionBlock?._vReflectionInfosName}\r\n                , ${reflectionBlock?.reflectionColor}\r\n                , ${isWebGPU ? \"uniforms.\" : \"\"}vLightingIntensity\r\n                #ifdef ${reflectionBlock?._define3DName}\r\n                    , ${reflectionBlock?._cubeSamplerName}                                      \r\n                    ${isWebGPU ? `, ${reflectionBlock?._cubeSamplerName}Sampler` : \"\"}\r\n                #else\r\n                    , ${reflectionBlock?._2DSamplerName}\r\n                    ${isWebGPU ? `, ${reflectionBlock?._2DSamplerName}Sampler` : \"\"}\r\n                #endif\r\n                , reflectionOut.reflectionCoords\r\n                , NdotVUnclamped\r\n                #ifndef LODBASEDMICROSFURACE\r\n                    #ifdef ${reflectionBlock?._define3DName}\r\n                        , ${reflectionBlock?._cubeSamplerName}                        \r\n                        ${isWebGPU ? `, ${reflectionBlock?._cubeSamplerName}Sampler` : \"\"}\r\n                        , ${reflectionBlock?._cubeSamplerName}\r\n                        ${isWebGPU ? `, ${reflectionBlock?._cubeSamplerName}Sampler` : \"\"}\r\n                    #else\r\n                        , ${reflectionBlock?._2DSamplerName}\r\n                        ${isWebGPU ? `, ${reflectionBlock?._2DSamplerName}Sampler` : \"\"}\r\n                        , ${reflectionBlock?._2DSamplerName}\r\n                        ${isWebGPU ? `, ${reflectionBlock?._2DSamplerName}Sampler` : \"\"}\r\n                    #endif\r\n                #endif\r\n                #if !defined(${reflectionBlock?._defineSkyboxName}) && defined(RADIANCEOCCLUSION)\r\n                    , seo\r\n                #endif\r\n                #if !defined(${reflectionBlock?._defineSkyboxName}) && defined(HORIZONOCCLUSION) && defined(BUMP) && defined(${reflectionBlock?._define3DName})\r\n                    , eho\r\n                #endif\r\n            #endif\r\n            );\r\n\r\n            #ifdef SHEEN_LINKWITHALBEDO\r\n                surfaceAlbedo = sheenOut.surfaceAlbedo;\r\n            #endif\r\n        #endif\\n`;\r\n\r\n        return code;\r\n    }\r\n\r\n    protected override _buildBlock(state: NodeMaterialBuildState) {\r\n        if (state.target === NodeMaterialBlockTargets.Fragment) {\r\n            state.sharedData.blocksWithDefines.push(this);\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    protected override _dumpPropertiesCode() {\r\n        let codeString = super._dumpPropertiesCode();\r\n\r\n        codeString += `${this._codeVariableName}.albedoScaling = ${this.albedoScaling};\\n`;\r\n        codeString += `${this._codeVariableName}.linkSheenWithAlbedo = ${this.linkSheenWithAlbedo};\\n`;\r\n\r\n        return codeString;\r\n    }\r\n\r\n    public override serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        serializationObject.albedoScaling = this.albedoScaling;\r\n        serializationObject.linkSheenWithAlbedo = this.linkSheenWithAlbedo;\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public override _deserialize(serializationObject: any, scene: Scene, rootUrl: string) {\r\n        super._deserialize(serializationObject, scene, rootUrl);\r\n\r\n        this.albedoScaling = serializationObject.albedoScaling;\r\n        this.linkSheenWithAlbedo = serializationObject.linkSheenWithAlbedo;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.SheenBlock\", SheenBlock);\r\n"], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAI1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAChF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,sBAAsB,EAA0B,MAAM,sCAAsC,CAAC;AACtG,OAAO,EAAE,uCAAuC,EAAE,MAAM,+CAA+C,CAAC;;;;;;;;AAUlG,MAAO,UAAW,uLAAQ,oBAAiB;IAC7C;;;OAGG,CACH,YAAmB,IAAY,CAAA;QAC3B,KAAK,CAAC,IAAI,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAgBnD;;;;WAIG,CAEI,IAAA,CAAA,aAAa,GAAY,KAAK,CAAC;QAEtC;;WAEG,CAEI,IAAA,CAAA,mBAAmB,GAAY,KAAK,CAAC;QA1BxC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,IAAI,CAAC,aAAa,CAAC,WAAW,6MAAE,wCAAqC,CAAC,KAAK,EAAE,IAAI,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QACtH,IAAI,CAAC,aAAa,CAAC,OAAO,6MAAE,wCAAqC,CAAC,MAAM,EAAE,IAAI,+LAAE,4BAAwB,CAAC,QAAQ,CAAC,CAAC;QACnH,IAAI,CAAC,aAAa,CAAC,WAAW,6MAAE,wCAAqC,CAAC,KAAK,EAAE,IAAI,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAEtH,IAAI,CAAC,cAAc,CACf,OAAO,EACP,mPAAqC,CAAC,MAAM,gMAC5C,2BAAwB,CAAC,QAAQ,EACjC,wMAAI,0CAAuC,CAAC,OAAO,EAAE,IAAI,EAAA,EAAA,+CAAA,KAA+C,UAAU,EAAE,YAAY,CAAC,CACpI,CAAC;IACN,CAAC;IAgBD;;;OAGG,CACa,UAAU,CAAC,KAA6B,EAAA;QACpD,KAAK,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;QACvC,KAAK,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAC3C,KAAK,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;QAC1C,KAAK,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;IAClD,CAAC;IAED;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,YAAY,CAAC;IACxB,CAAC;IAED;;OAEG,CACH,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,KAAK,GAAA;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,KAAK,GAAA;QACZ,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAEe,cAAc,CAAC,OAA4B,EAAA;QACvD,OAAO,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAChC,OAAO,CAAC,QAAQ,CAAC,sCAAsC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QACrE,OAAO,CAAC,QAAQ,CAAC,sBAAsB,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;QACzE,OAAO,CAAC,QAAQ,CAAC,iBAAiB,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QACtE,OAAO,CAAC,QAAQ,CAAC,qBAAqB,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;IACtE,CAAC;IAED;;;;;OAKG,CACI,OAAO,CAAC,eAA0C,EAAE,KAA6B,EAAA;QACpF,IAAI,IAAI,GAAG,EAAE,CAAC;QAEd,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAA,IAAA,EAAO,KAAK,CAAC,OAAO,CAAA,IAAA,CAAM,CAAC;QACtG,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC;QAC5F,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC;QAC5F,MAAM,OAAO,GAAG,CAAA,IAAA,EAAO,KAAK,CAAC,OAAO,CAAA,IAAA,CAAM,CAAC;QAC3C,MAAM,QAAQ,GAAG,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,CAAC;QAE9D,IAAI,GAAG,CAAA;cACD,QAAQ,CAAC,CAAC,CAAC,8BAA8B,CAAC,CAAC,CAAC,yBAAyB,CAAA;;cAErE,KAAK,CAAC,gBAAgB,CAAC,aAAa,4MAAE,yCAAqC,CAAC,OAAO,CAAC,CAAA,OAAA,EAAU,KAAK,CAAC,OAAO,CAAA,CAAA,EAAI,KAAK,CAAA,EAAA,EAAK,SAAS,CAAA;;;;;oBAK5H,SAAS,CAAA;;;;oBAIT,OAAO,CAAA;kBACT,QAAQ,CAAC,CAAC,CAAC,CAAA,EAAA,EAAK,OAAO,CAAA,OAAA,CAAS,CAAC,CAAC,CAAC,EAAE,CAAA;;;;;;;;;;;;;;oBAcnC,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,GAAG,eAAe,EAAE,iCAAiC,CAAA;oBAChF,eAAe,EAAE,qBAAqB,CAAA;oBACtC,eAAe,EAAE,eAAe,CAAA;oBAChC,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAA;yBACtB,eAAe,EAAE,aAAa,CAAA;wBAC/B,eAAe,EAAE,gBAAgB,CAAA;sBACnC,QAAQ,CAAC,CAAC,CAAC,CAAA,EAAA,EAAK,eAAe,EAAE,gBAAgB,CAAA,OAAA,CAAS,CAAC,CAAC,CAAC,EAAE,CAAA;;wBAE7D,eAAe,EAAE,cAAc,CAAA;sBACjC,QAAQ,CAAC,CAAC,CAAC,CAAA,EAAA,EAAK,eAAe,EAAE,cAAc,CAAA,OAAA,CAAS,CAAC,CAAC,CAAC,EAAE,CAAA;;;;;6BAKtD,eAAe,EAAE,aAAa,CAAA;4BAC/B,eAAe,EAAE,gBAAgB,CAAA;0BACnC,QAAQ,CAAC,CAAC,CAAC,CAAA,EAAA,EAAK,eAAe,EAAE,gBAAgB,CAAA,OAAA,CAAS,CAAC,CAAC,CAAC,EAAE,CAAA;4BAC7D,eAAe,EAAE,gBAAgB,CAAA;0BACnC,QAAQ,CAAC,CAAC,CAAC,CAAA,EAAA,EAAK,eAAe,EAAE,gBAAgB,CAAA,OAAA,CAAS,CAAC,CAAC,CAAC,EAAE,CAAA;;4BAE7D,eAAe,EAAE,cAAc,CAAA;0BACjC,QAAQ,CAAC,CAAC,CAAC,CAAA,EAAA,EAAK,eAAe,EAAE,cAAc,CAAA,OAAA,CAAS,CAAC,CAAC,CAAC,EAAE,CAAA;4BAC3D,eAAe,EAAE,cAAc,CAAA;0BACjC,QAAQ,CAAC,CAAC,CAAC,CAAA,EAAA,EAAK,eAAe,EAAE,cAAc,CAAA,OAAA,CAAS,CAAC,CAAC,CAAC,EAAE,CAAA;;;+BAGxD,eAAe,EAAE,iBAAiB,CAAA;;;+BAGlC,eAAe,EAAE,iBAAiB,CAAA,2DAAA,EAA8D,eAAe,EAAE,aAAa,CAAA;;;;;;;;;iBAS5I,CAAC;QAEV,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,WAAW,CAAC,KAA6B,EAAA;QACxD,IAAI,KAAK,CAAC,MAAM,KAAK,yNAAwB,CAAC,QAAQ,EAAE,CAAC;YACrD,KAAK,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClD,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,mBAAmB,GAAA;QAClC,IAAI,UAAU,GAAG,KAAK,CAAC,mBAAmB,EAAE,CAAC;QAE7C,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,iBAAA,EAAoB,IAAI,CAAC,aAAa,CAAA,GAAA,CAAK,CAAC;QACnF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,uBAAA,EAA0B,IAAI,CAAC,mBAAmB,CAAA,GAAA,CAAK,CAAC;QAE/F,OAAO,UAAU,CAAC;IACtB,CAAC;IAEe,SAAS,GAAA;QACrB,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,mBAAmB,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QACvD,mBAAmB,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;QAEnE,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEe,YAAY,CAAC,mBAAwB,EAAE,KAAY,EAAE,OAAe,EAAA;QAChF,KAAK,CAAC,YAAY,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAExD,IAAI,CAAC,aAAa,GAAG,mBAAmB,CAAC,aAAa,CAAC;QACvD,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC,mBAAmB,CAAC;IACvE,CAAC;CACJ;CApLU,oKAAA,EAAA;2KADN,yBAAA,AAAsB,EAAC,gBAAgB,EAAA,EAAA,kCAAA,KAAkC,YAAY,EAAE;QAAE,QAAQ,EAAE,IAAI;QAAE,SAAS,EAAE;YAAE,MAAM,EAAE,IAAI;QAAA,CAAE;IAAA,CAAE,CAAC;iDAClG;wJAM/B,aAAA,EAAA;2KADN,yBAAA,AAAsB,EAAC,wBAAwB,EAAA,EAAA,kCAAA,KAAkC,YAAY,EAAE;QAAE,QAAQ,EAAE,IAAI;QAAE,SAAS,EAAE;YAAE,MAAM,EAAE,IAAI;QAAA,CAAE;IAAA,CAAE,CAAC;uDACpG;6JAgLhD,gBAAA,AAAa,EAAC,oBAAoB,EAAE,UAAU,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 209, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/anisotropyBlock.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Blocks/PBR/anisotropyBlock.ts"], "sourcesContent": ["import type { NodeMaterial, NodeMaterialDefines } from \"../../nodeMaterial\";\r\nimport { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialConnectionPointDirection } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport { NodeMaterialConnectionPointCustomObject } from \"../../nodeMaterialConnectionPointCustomObject\";\r\nimport { TBNBlock } from \"../Fragment/TBNBlock\";\r\nimport type { Mesh } from \"../../../../Meshes/mesh\";\r\nimport type { Effect } from \"../../../effect\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport { ShaderLanguage } from \"core/Materials/shaderLanguage\";\r\n\r\n/**\r\n * Block used to implement the anisotropy module of the PBR material\r\n */\r\nexport class AnisotropyBlock extends NodeMaterialBlock {\r\n    private _tangentCorrectionFactorName = \"\";\r\n\r\n    /**\r\n     * The two properties below are set by the main PBR block prior to calling methods of this class.\r\n     * This is to avoid having to add them as inputs here whereas they are already inputs of the main block, so already known.\r\n     * It's less burden on the user side in the editor part.\r\n     */\r\n\r\n    /** @internal */\r\n    public worldPositionConnectionPoint: NodeMaterialConnectionPoint;\r\n    /** @internal */\r\n    public worldNormalConnectionPoint: NodeMaterialConnectionPoint;\r\n\r\n    /**\r\n     * Create a new AnisotropyBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this._isUnique = true;\r\n\r\n        this.registerInput(\"intensity\", NodeMaterialBlockConnectionPointTypes.Float, true, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"direction\", NodeMaterialBlockConnectionPointTypes.Vector2, true, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"uv\", NodeMaterialBlockConnectionPointTypes.Vector2, true); // need this property and the next one in case there's no PerturbNormal block connected to the main PBR block\r\n        this.registerInput(\"worldTangent\", NodeMaterialBlockConnectionPointTypes.Vector4, true);\r\n        this.registerInput(\r\n            \"TBN\",\r\n            NodeMaterialBlockConnectionPointTypes.Object,\r\n            true,\r\n            NodeMaterialBlockTargets.VertexAndFragment,\r\n            new NodeMaterialConnectionPointCustomObject(\"TBN\", this, NodeMaterialConnectionPointDirection.Input, TBNBlock, \"TBNBlock\")\r\n        );\r\n        this.registerInput(\"roughness\", NodeMaterialBlockConnectionPointTypes.Float, true, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this.registerOutput(\r\n            \"anisotropy\",\r\n            NodeMaterialBlockConnectionPointTypes.Object,\r\n            NodeMaterialBlockTargets.Fragment,\r\n            new NodeMaterialConnectionPointCustomObject(\"anisotropy\", this, NodeMaterialConnectionPointDirection.Output, AnisotropyBlock, \"AnisotropyBlock\")\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Initialize the block and prepare the context for build\r\n     * @param state defines the state that will be used for the build\r\n     */\r\n    public override initialize(state: NodeMaterialBuildState) {\r\n        state._excludeVariableName(\"anisotropicOut\");\r\n        state._excludeVariableName(\"TBN\");\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public override getClassName() {\r\n        return \"AnisotropyBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the intensity input component\r\n     */\r\n    public get intensity(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the direction input component\r\n     */\r\n    public get direction(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the uv input component\r\n     */\r\n    public get uv(): NodeMaterialConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the worldTangent input component\r\n     */\r\n    public get worldTangent(): NodeMaterialConnectionPoint {\r\n        return this._inputs[3];\r\n    }\r\n\r\n    /**\r\n     * Gets the TBN input component\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public get TBN(): NodeMaterialConnectionPoint {\r\n        return this._inputs[4];\r\n    }\r\n\r\n    /**\r\n     * Gets the roughness input component\r\n     */\r\n    public get roughness(): NodeMaterialConnectionPoint {\r\n        return this._inputs[5];\r\n    }\r\n\r\n    /**\r\n     * Gets the anisotropy object output component\r\n     */\r\n    public get anisotropy(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    private _generateTBNSpace(state: NodeMaterialBuildState) {\r\n        let code = \"\";\r\n\r\n        const comments = `//${this.name}`;\r\n        const uv = this.uv;\r\n        const worldPosition = this.worldPositionConnectionPoint;\r\n        const worldNormal = this.worldNormalConnectionPoint;\r\n        const worldTangent = this.worldTangent;\r\n        const isWebGPU = state.shaderLanguage === ShaderLanguage.WGSL;\r\n\r\n        if (!uv.isConnected) {\r\n            // we must set the uv input as optional because we may not end up in this method (in case a PerturbNormal block is linked to the PBR material)\r\n            // in which case uv is not required. But if we do come here, we do need the uv, so we have to raise an error but not with throw, else\r\n            // it will stop the building of the node material and will lead to errors in the editor!\r\n            state.sharedData.raiseBuildError(`You must connect the 'uv' input of the ${this.name} block!`);\r\n        }\r\n\r\n        state._emitExtension(\"derivatives\", \"#extension GL_OES_standard_derivatives : enable\");\r\n\r\n        const tangentReplaceString = { search: /defined\\(TANGENT\\)/g, replace: worldTangent.isConnected ? \"defined(TANGENT)\" : \"defined(IGNORE)\" };\r\n\r\n        const tbn = this.TBN;\r\n        if (tbn.isConnected) {\r\n            state.compilationString += `\r\n            #ifdef TBNBLOCK\r\n            ${isWebGPU ? \"var TBN\" : \"mat3 TBN\"} = ${tbn.associatedVariableName};\r\n            #endif\r\n            `;\r\n        } else if (worldTangent.isConnected) {\r\n            code += `${state._declareLocalVar(\"tbnNormal\", NodeMaterialBlockConnectionPointTypes.Vector3)} = normalize(${worldNormal.associatedVariableName}.xyz);\\n`;\r\n            code += `${state._declareLocalVar(\"tbnTangent\", NodeMaterialBlockConnectionPointTypes.Vector3)} = normalize(${worldTangent.associatedVariableName}.xyz);\\n`;\r\n            code += `${state._declareLocalVar(\"tbnBitangent\", NodeMaterialBlockConnectionPointTypes.Vector3)} = cross(tbnNormal, tbnTangent) * ${this._tangentCorrectionFactorName};\\n`;\r\n            code += `${isWebGPU ? \"var vTBN\" : \"mat3 vTBN\"} = ${isWebGPU ? \"mat3x3f\" : \"mat3\"}(tbnTangent, tbnBitangent, tbnNormal);\\n`;\r\n        }\r\n\r\n        code += `\r\n            #if defined(${worldTangent.isConnected ? \"TANGENT\" : \"IGNORE\"}) && defined(NORMAL)\r\n                ${isWebGPU ? \"var TBN\" : \"mat3 TBN\"} = vTBN;\r\n            #else\r\n                ${isWebGPU ? \"var TBN\" : \"mat3 TBN\"} = cotangent_frame(${worldNormal.associatedVariableName + \".xyz\"}, ${\"v_\" + worldPosition.associatedVariableName + \".xyz\"}, ${\r\n                    uv.isConnected ? uv.associatedVariableName : \"vec2(0.)\"\r\n                }, vec2${state.fSuffix}(1., 1.));\r\n            #endif\\n`;\r\n\r\n        state._emitFunctionFromInclude(\"bumpFragmentMainFunctions\", comments, {\r\n            replaceStrings: [tangentReplaceString],\r\n        });\r\n\r\n        return code;\r\n    }\r\n\r\n    /**\r\n     * Gets the main code of the block (fragment side)\r\n     * @param state current state of the node material building\r\n     * @param generateTBNSpace if true, the code needed to create the TBN coordinate space is generated\r\n     * @returns the shader code\r\n     */\r\n    public getCode(state: NodeMaterialBuildState, generateTBNSpace = false): string {\r\n        let code = \"\";\r\n\r\n        if (generateTBNSpace) {\r\n            code += this._generateTBNSpace(state);\r\n        }\r\n        const isWebGPU = state.shaderLanguage === ShaderLanguage.WGSL;\r\n\r\n        const intensity = this.intensity.isConnected ? this.intensity.associatedVariableName : \"1.0\";\r\n        const direction = this.direction.isConnected ? this.direction.associatedVariableName : \"vec2(1., 0.)\";\r\n        const roughness = this.roughness.isConnected ? this.roughness.associatedVariableName : \"0.\";\r\n\r\n        code += `${isWebGPU ? \"var anisotropicOut: anisotropicOutParams\" : \"anisotropicOutParams anisotropicOut\"};\r\n            anisotropicOut = anisotropicBlock(\r\n                vec3(${direction}, ${intensity}),\r\n                ${roughness},\r\n            #ifdef ANISOTROPIC_TEXTURE\r\n                vec3(0.),\r\n            #endif\r\n                TBN,\r\n                normalW,\r\n                viewDirectionW\r\n            );\\n`;\r\n\r\n        return code;\r\n    }\r\n\r\n    public override prepareDefines(defines: NodeMaterialDefines) {\r\n        defines.setValue(\"ANISOTROPIC\", true);\r\n        defines.setValue(\"ANISOTROPIC_TEXTURE\", false, true);\r\n        defines.setValue(\"ANISOTROPIC_LEGACY\", !this.roughness.isConnected);\r\n    }\r\n\r\n    public override bind(effect: Effect, nodeMaterial: NodeMaterial, mesh?: Mesh) {\r\n        super.bind(effect, nodeMaterial, mesh);\r\n\r\n        if (mesh) {\r\n            effect.setFloat(this._tangentCorrectionFactorName, mesh.getWorldMatrix().determinant() < 0 ? -1 : 1);\r\n        }\r\n    }\r\n\r\n    protected override _buildBlock(state: NodeMaterialBuildState) {\r\n        if (state.target === NodeMaterialBlockTargets.Fragment) {\r\n            state.sharedData.blocksWithDefines.push(this);\r\n            state.sharedData.bindableBlocks.push(this);\r\n\r\n            this._tangentCorrectionFactorName = state._getFreeDefineName(\"tangentCorrectionFactor\");\r\n            state._emitUniformFromString(this._tangentCorrectionFactorName, NodeMaterialBlockConnectionPointTypes.Float);\r\n        }\r\n\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.AnisotropyBlock\", AnisotropyBlock);\r\n"], "names": [], "mappings": ";;;AACA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAG1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAChF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,uCAAuC,EAAE,MAAM,+CAA+C,CAAC;AACxG,OAAO,EAAE,QAAQ,EAAE,MAAM,sBAAsB,CAAC;;;;;;;AAS1C,MAAO,eAAgB,uLAAQ,oBAAiB;IAclD;;;OAGG,CACH,YAAmB,IAAY,CAAA;QAC3B,KAAK,CAAC,IAAI,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAlB3C,IAAA,CAAA,4BAA4B,GAAG,EAAE,CAAC;QAoBtC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,mPAAqC,CAAC,KAAK,EAAE,IAAI,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QACtH,IAAI,CAAC,aAAa,CAAC,WAAW,6MAAE,wCAAqC,CAAC,OAAO,EAAE,IAAI,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QACxH,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,mPAAqC,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,6GAA6G;QAC5L,IAAI,CAAC,aAAa,CAAC,cAAc,6MAAE,wCAAqC,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACxF,IAAI,CAAC,aAAa,CACd,KAAK,6MACL,wCAAqC,CAAC,MAAM,EAC5C,IAAI,gMACJ,2BAAwB,CAAC,iBAAiB,EAC1C,wMAAI,0CAAuC,CAAC,KAAK,EAAE,IAAI,EAAA,EAAA,8CAAA,gMAA8C,WAAQ,EAAE,UAAU,CAAC,CAC7H,CAAC;QACF,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,mPAAqC,CAAC,KAAK,EAAE,IAAI,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAEtH,IAAI,CAAC,cAAc,CACf,YAAY,6MACZ,wCAAqC,CAAC,MAAM,gMAC5C,2BAAwB,CAAC,QAAQ,EACjC,IAAI,8OAAuC,CAAC,YAAY,EAAE,IAAI,EAAA,EAAA,+CAAA,KAA+C,eAAe,EAAE,iBAAiB,CAAC,CACnJ,CAAC;IACN,CAAC;IAED;;;OAGG,CACa,UAAU,CAAC,KAA6B,EAAA;QACpD,KAAK,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;QAC7C,KAAK,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;IACtC,CAAC;IAED;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IAED;;OAEG,CACH,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,EAAE,GAAA;QACT,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,YAAY,GAAA;QACnB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,gEAAgE;IAChE,IAAW,GAAG,GAAA;QACV,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAEO,iBAAiB,CAAC,KAA6B,EAAA;QACnD,IAAI,IAAI,GAAG,EAAE,CAAC;QAEd,MAAM,QAAQ,GAAG,CAAA,EAAA,EAAK,IAAI,CAAC,IAAI,EAAE,CAAC;QAClC,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;QACnB,MAAM,aAAa,GAAG,IAAI,CAAC,4BAA4B,CAAC;QACxD,MAAM,WAAW,GAAG,IAAI,CAAC,0BAA0B,CAAC;QACpD,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QACvC,MAAM,QAAQ,GAAG,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,CAAC;QAE9D,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC;YAClB,8IAA8I;YAC9I,qIAAqI;YACrI,wFAAwF;YACxF,KAAK,CAAC,UAAU,CAAC,eAAe,CAAC,CAAA,uCAAA,EAA0C,IAAI,CAAC,IAAI,CAAA,OAAA,CAAS,CAAC,CAAC;QACnG,CAAC;QAED,KAAK,CAAC,cAAc,CAAC,aAAa,EAAE,iDAAiD,CAAC,CAAC;QAEvF,MAAM,oBAAoB,GAAG;YAAE,MAAM,EAAE,qBAAqB;YAAE,OAAO,EAAE,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,iBAAiB;QAAA,CAAE,CAAC;QAE3I,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;QACrB,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC;YAClB,KAAK,CAAC,iBAAiB,IAAI,CAAA;;cAEzB,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAA,GAAA,EAAM,GAAG,CAAC,sBAAsB,CAAA;;aAElE,CAAC;QACN,CAAC,MAAM,IAAI,YAAY,CAAC,WAAW,EAAE,CAAC;YAClC,IAAI,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,WAAW,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA,aAAA,EAAgB,WAAW,CAAC,sBAAsB,CAAA,QAAA,CAAU,CAAC;YAC1J,IAAI,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,YAAY,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA,aAAA,EAAgB,YAAY,CAAC,sBAAsB,CAAA,QAAA,CAAU,CAAC;YAC5J,IAAI,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,cAAc,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA,kCAAA,EAAqC,IAAI,CAAC,4BAA4B,CAAA,GAAA,CAAK,CAAC;YAC5K,IAAI,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,WAAW,CAAA,GAAA,EAAM,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAA,wCAAA,CAA0C,CAAC;QAChI,CAAC;QAED,IAAI,IAAI,CAAA;0BACU,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAA;kBACvD,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAA;;kBAEjC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAA,mBAAA,EAAsB,WAAW,CAAC,sBAAsB,GAAG,MAAM,CAAA,EAAA,EAAK,IAAI,GAAG,aAAa,CAAC,sBAAsB,GAAG,MAAM,CAAA,EAAA,EACzJ,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,sBAAsB,CAAC,CAAC,CAAC,UACjD,CAAA,MAAA,EAAS,KAAK,CAAC,OAAO,CAAA;qBACjB,CAAC;QAEd,KAAK,CAAC,wBAAwB,CAAC,2BAA2B,EAAE,QAAQ,EAAE;YAClE,cAAc,EAAE;gBAAC,oBAAoB;aAAC;SACzC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG,CACI,OAAO,CAAC,KAA6B,EAAE,gBAAgB,GAAG,KAAK,EAAA;QAClE,IAAI,IAAI,GAAG,EAAE,CAAC;QAEd,IAAI,gBAAgB,EAAE,CAAC;YACnB,IAAI,IAAI,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAC1C,CAAC;QACD,MAAM,QAAQ,GAAG,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,CAAC;QAE9D,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAAC,KAAK,CAAC;QAC7F,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAAC,cAAc,CAAC;QACtG,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC;QAE5F,IAAI,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,0CAA0C,CAAC,CAAC,CAAC,qCAAqC,CAAA;;uBAEzF,SAAS,CAAA,EAAA,EAAK,SAAS,CAAA;kBAC5B,SAAS,CAAA;;;;;;;iBAOV,CAAC;QAEV,OAAO,IAAI,CAAC;IAChB,CAAC;IAEe,cAAc,CAAC,OAA4B,EAAA;QACvD,OAAO,CAAC,QAAQ,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;QACtC,OAAO,CAAC,QAAQ,CAAC,qBAAqB,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QACrD,OAAO,CAAC,QAAQ,CAAC,oBAAoB,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;IACxE,CAAC;IAEe,IAAI,CAAC,MAAc,EAAE,YAA0B,EAAE,IAAW,EAAA;QACxE,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;QAEvC,IAAI,IAAI,EAAE,CAAC;YACP,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,4BAA4B,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzG,CAAC;IACL,CAAC;IAEkB,WAAW,CAAC,KAA6B,EAAA;QACxD,IAAI,KAAK,CAAC,MAAM,mMAAK,2BAAwB,CAAC,QAAQ,EAAE,CAAC;YACrD,KAAK,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE3C,IAAI,CAAC,4BAA4B,GAAG,KAAK,CAAC,kBAAkB,CAAC,yBAAyB,CAAC,CAAC;YACxF,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,4BAA4B,6MAAE,wCAAqC,CAAC,KAAK,CAAC,CAAC;QACjH,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;6JAED,gBAAA,AAAa,EAAC,yBAAyB,EAAE,eAAe,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 387, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/reflectionBlock.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Blocks/PBR/reflectionBlock.ts"], "sourcesContent": ["import { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialConnectionPointDirection } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterial, NodeMaterialDefines } from \"../../nodeMaterial\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport { NodeMaterialConnectionPointCustomObject } from \"../../nodeMaterialConnectionPointCustomObject\";\r\nimport { ReflectionTextureBaseBlock } from \"../Dual/reflectionTextureBaseBlock\";\r\nimport type { Nullable } from \"../../../../types\";\r\nimport { Texture } from \"../../../Textures/texture\";\r\nimport type { BaseTexture } from \"../../../Textures/baseTexture\";\r\nimport type { Mesh } from \"../../../../Meshes/mesh\";\r\nimport type { SubMesh } from \"../../../../Meshes/subMesh\";\r\nimport type { Effect } from \"../../../effect\";\r\nimport { editableInPropertyPage, PropertyTypeForEdition } from \"../../../../Decorators/nodeDecorator\";\r\nimport type { Scene } from \"../../../../scene\";\r\nimport { Logger } from \"core/Misc/logger\";\r\nimport { ShaderLanguage } from \"core/Materials/shaderLanguage\";\r\n\r\n/**\r\n * Block used to implement the reflection module of the PBR material\r\n */\r\nexport class ReflectionBlock extends ReflectionTextureBaseBlock {\r\n    /** @internal */\r\n    public _defineLODReflectionAlpha: string;\r\n    /** @internal */\r\n    public _defineLinearSpecularReflection: string;\r\n    private _vEnvironmentIrradianceName: string;\r\n    /** @internal */\r\n    public _vReflectionMicrosurfaceInfosName: string;\r\n    /** @internal */\r\n    public _vReflectionInfosName: string;\r\n    /** @internal */\r\n    public _vReflectionFilteringInfoName: string;\r\n    private _scene: Scene;\r\n\r\n    /**\r\n     * The properties below are set by the main PBR block prior to calling methods of this class.\r\n     * This is to avoid having to add them as inputs here whereas they are already inputs of the main block, so already known.\r\n     * It's less burden on the user side in the editor part.\r\n     */\r\n\r\n    /** @internal */\r\n    public worldPositionConnectionPoint: NodeMaterialConnectionPoint;\r\n    /** @internal */\r\n    public worldNormalConnectionPoint: NodeMaterialConnectionPoint;\r\n    /** @internal */\r\n    public cameraPositionConnectionPoint: NodeMaterialConnectionPoint;\r\n    /** @internal */\r\n    public viewConnectionPoint: NodeMaterialConnectionPoint;\r\n\r\n    /**\r\n     * Defines if the material uses spherical harmonics vs spherical polynomials for the\r\n     * diffuse part of the IBL.\r\n     */\r\n    @editableInPropertyPage(\"Spherical Harmonics\", PropertyTypeForEdition.Boolean, \"ADVANCED\", { embedded: true, notifiers: { update: true } })\r\n    public useSphericalHarmonics: boolean = true;\r\n\r\n    /**\r\n     * Force the shader to compute irradiance in the fragment shader in order to take bump in account.\r\n     */\r\n    @editableInPropertyPage(\"Force irradiance in fragment\", PropertyTypeForEdition.Boolean, \"ADVANCED\", { embedded: true, notifiers: { update: true } })\r\n    public forceIrradianceInFragment: boolean = false;\r\n\r\n    protected override _onGenerateOnlyFragmentCodeChanged(): boolean {\r\n        if (this.position.isConnected) {\r\n            this.generateOnlyFragmentCode = !this.generateOnlyFragmentCode;\r\n            Logger.Error(\"The position input must not be connected to be able to switch!\");\r\n            return false;\r\n        }\r\n\r\n        this._setTarget();\r\n\r\n        return true;\r\n    }\r\n\r\n    protected override _setTarget(): void {\r\n        super._setTarget();\r\n        this.getInputByName(\"position\")!.target = this.generateOnlyFragmentCode ? NodeMaterialBlockTargets.Fragment : NodeMaterialBlockTargets.Vertex;\r\n        if (this.generateOnlyFragmentCode) {\r\n            this.forceIrradianceInFragment = true;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Create a new ReflectionBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name);\r\n\r\n        this._isUnique = true;\r\n\r\n        this.registerInput(\"position\", NodeMaterialBlockConnectionPointTypes.AutoDetect, false, NodeMaterialBlockTargets.Vertex);\r\n        this.registerInput(\"world\", NodeMaterialBlockConnectionPointTypes.Matrix, false, NodeMaterialBlockTargets.Vertex);\r\n        this.registerInput(\"color\", NodeMaterialBlockConnectionPointTypes.Color3, true, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this.registerOutput(\r\n            \"reflection\",\r\n            NodeMaterialBlockConnectionPointTypes.Object,\r\n            NodeMaterialBlockTargets.Fragment,\r\n            new NodeMaterialConnectionPointCustomObject(\"reflection\", this, NodeMaterialConnectionPointDirection.Output, ReflectionBlock, \"ReflectionBlock\")\r\n        );\r\n\r\n        this.position.addExcludedConnectionPointFromAllowedTypes(\r\n            NodeMaterialBlockConnectionPointTypes.Color3 | NodeMaterialBlockConnectionPointTypes.Vector3 | NodeMaterialBlockConnectionPointTypes.Vector4\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public override getClassName() {\r\n        return \"ReflectionBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the position input component\r\n     */\r\n    public get position(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the world position input component\r\n     */\r\n    public get worldPosition(): NodeMaterialConnectionPoint {\r\n        return this.worldPositionConnectionPoint;\r\n    }\r\n\r\n    /**\r\n     * Gets the world normal input component\r\n     */\r\n    public get worldNormal(): NodeMaterialConnectionPoint {\r\n        return this.worldNormalConnectionPoint;\r\n    }\r\n\r\n    /**\r\n     * Gets the world input component\r\n     */\r\n    public get world(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the camera (or eye) position component\r\n     */\r\n    public get cameraPosition(): NodeMaterialConnectionPoint {\r\n        return this.cameraPositionConnectionPoint;\r\n    }\r\n\r\n    /**\r\n     * Gets the view input component\r\n     */\r\n    public get view(): NodeMaterialConnectionPoint {\r\n        return this.viewConnectionPoint;\r\n    }\r\n\r\n    /**\r\n     * Gets the color input component\r\n     */\r\n    public get color(): NodeMaterialConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the reflection object output component\r\n     */\r\n    public get reflection(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Returns true if the block has a texture (either its own texture or the environment texture from the scene, if set)\r\n     */\r\n    public get hasTexture(): boolean {\r\n        return !!this._getTexture();\r\n    }\r\n\r\n    /**\r\n     * Gets the reflection color (either the name of the variable if the color input is connected, else a default value)\r\n     */\r\n    public get reflectionColor(): string {\r\n        return this.color.isConnected ? this.color.associatedVariableName : \"vec3(1., 1., 1.)\";\r\n    }\r\n\r\n    protected override _getTexture(): Nullable<BaseTexture> {\r\n        if (this.texture) {\r\n            return this.texture;\r\n        }\r\n\r\n        return this._scene.environmentTexture;\r\n    }\r\n\r\n    public override prepareDefines(defines: NodeMaterialDefines) {\r\n        super.prepareDefines(defines);\r\n\r\n        const reflectionTexture = this._getTexture();\r\n        const reflection = reflectionTexture && reflectionTexture.getTextureMatrix;\r\n\r\n        defines.setValue(\"REFLECTION\", reflection, true);\r\n\r\n        if (!reflection) {\r\n            return;\r\n        }\r\n\r\n        defines.setValue(this._defineLODReflectionAlpha, reflectionTexture.lodLevelInAlpha, true);\r\n        defines.setValue(this._defineLinearSpecularReflection, reflectionTexture.linearSpecularLOD, true);\r\n        defines.setValue(this._defineOppositeZ, this._scene.useRightHandedSystem ? !reflectionTexture.invertZ : reflectionTexture.invertZ, true);\r\n\r\n        defines.setValue(\"SPHERICAL_HARMONICS\", this.useSphericalHarmonics, true);\r\n        defines.setValue(\"GAMMAREFLECTION\", reflectionTexture.gammaSpace, true);\r\n        defines.setValue(\"RGBDREFLECTION\", reflectionTexture.isRGBD, true);\r\n\r\n        if (reflectionTexture && reflectionTexture.coordinatesMode !== Texture.SKYBOX_MODE) {\r\n            if (reflectionTexture.isCube) {\r\n                defines.setValue(\"USESPHERICALFROMREFLECTIONMAP\", true);\r\n                defines.setValue(\"USEIRRADIANCEMAP\", false);\r\n                if (this.forceIrradianceInFragment || this._scene.getEngine().getCaps().maxVaryingVectors <= 8) {\r\n                    defines.setValue(\"USESPHERICALINVERTEX\", false);\r\n                } else {\r\n                    defines.setValue(\"USESPHERICALINVERTEX\", true);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    public override bind(effect: Effect, nodeMaterial: NodeMaterial, mesh?: Mesh, subMesh?: SubMesh) {\r\n        super.bind(effect, nodeMaterial, mesh);\r\n\r\n        const reflectionTexture = this._getTexture();\r\n\r\n        if (!reflectionTexture || !subMesh) {\r\n            return;\r\n        }\r\n\r\n        if (reflectionTexture.isCube) {\r\n            effect.setTexture(this._cubeSamplerName, reflectionTexture);\r\n        } else {\r\n            effect.setTexture(this._2DSamplerName, reflectionTexture);\r\n        }\r\n\r\n        const width = reflectionTexture.getSize().width;\r\n\r\n        effect.setFloat3(this._vReflectionMicrosurfaceInfosName, width, reflectionTexture.lodGenerationScale, reflectionTexture.lodGenerationOffset);\r\n        effect.setFloat2(this._vReflectionFilteringInfoName, width, Math.log2(width));\r\n\r\n        const defines = subMesh.materialDefines as NodeMaterialDefines;\r\n\r\n        const polynomials = reflectionTexture.sphericalPolynomial;\r\n        if (defines.USESPHERICALFROMREFLECTIONMAP && polynomials) {\r\n            if (defines.SPHERICAL_HARMONICS) {\r\n                const preScaledHarmonics = polynomials.preScaledHarmonics;\r\n                effect.setVector3(\"vSphericalL00\", preScaledHarmonics.l00);\r\n                effect.setVector3(\"vSphericalL1_1\", preScaledHarmonics.l1_1);\r\n                effect.setVector3(\"vSphericalL10\", preScaledHarmonics.l10);\r\n                effect.setVector3(\"vSphericalL11\", preScaledHarmonics.l11);\r\n                effect.setVector3(\"vSphericalL2_2\", preScaledHarmonics.l2_2);\r\n                effect.setVector3(\"vSphericalL2_1\", preScaledHarmonics.l2_1);\r\n                effect.setVector3(\"vSphericalL20\", preScaledHarmonics.l20);\r\n                effect.setVector3(\"vSphericalL21\", preScaledHarmonics.l21);\r\n                effect.setVector3(\"vSphericalL22\", preScaledHarmonics.l22);\r\n            } else {\r\n                effect.setFloat3(\"vSphericalX\", polynomials.x.x, polynomials.x.y, polynomials.x.z);\r\n                effect.setFloat3(\"vSphericalY\", polynomials.y.x, polynomials.y.y, polynomials.y.z);\r\n                effect.setFloat3(\"vSphericalZ\", polynomials.z.x, polynomials.z.y, polynomials.z.z);\r\n                effect.setFloat3(\"vSphericalXX_ZZ\", polynomials.xx.x - polynomials.zz.x, polynomials.xx.y - polynomials.zz.y, polynomials.xx.z - polynomials.zz.z);\r\n                effect.setFloat3(\"vSphericalYY_ZZ\", polynomials.yy.x - polynomials.zz.x, polynomials.yy.y - polynomials.zz.y, polynomials.yy.z - polynomials.zz.z);\r\n                effect.setFloat3(\"vSphericalZZ\", polynomials.zz.x, polynomials.zz.y, polynomials.zz.z);\r\n                effect.setFloat3(\"vSphericalXY\", polynomials.xy.x, polynomials.xy.y, polynomials.xy.z);\r\n                effect.setFloat3(\"vSphericalYZ\", polynomials.yz.x, polynomials.yz.y, polynomials.yz.z);\r\n                effect.setFloat3(\"vSphericalZX\", polynomials.zx.x, polynomials.zx.y, polynomials.zx.z);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the code to inject in the vertex shader\r\n     * @param state current state of the node material building\r\n     * @returns the shader code\r\n     */\r\n    public override handleVertexSide(state: NodeMaterialBuildState): string {\r\n        let code = super.handleVertexSide(state);\r\n        const isWebGPU = state.shaderLanguage === ShaderLanguage.WGSL;\r\n\r\n        state._emitFunctionFromInclude(\"harmonicsFunctions\", `//${this.name}`, {\r\n            replaceStrings: [\r\n                { search: /uniform vec3 vSphericalL00;[\\s\\S]*?uniform vec3 vSphericalL22;/g, replace: \"\" },\r\n                { search: /uniform vec3 vSphericalX;[\\s\\S]*?uniform vec3 vSphericalZX;/g, replace: \"\" },\r\n            ],\r\n        });\r\n\r\n        const reflectionVectorName = state._getFreeVariableName(\"reflectionVector\");\r\n\r\n        this._vEnvironmentIrradianceName = state._getFreeVariableName(\"vEnvironmentIrradiance\");\r\n\r\n        state._emitVaryingFromString(\r\n            this._vEnvironmentIrradianceName,\r\n            NodeMaterialBlockConnectionPointTypes.Vector3,\r\n            \"defined(USESPHERICALFROMREFLECTIONMAP) && defined(USESPHERICALINVERTEX)\"\r\n        );\r\n\r\n        state._emitUniformFromString(\"vSphericalL00\", NodeMaterialBlockConnectionPointTypes.Vector3, \"SPHERICAL_HARMONICS\");\r\n        state._emitUniformFromString(\"vSphericalL1_1\", NodeMaterialBlockConnectionPointTypes.Vector3, \"SPHERICAL_HARMONICS\");\r\n        state._emitUniformFromString(\"vSphericalL10\", NodeMaterialBlockConnectionPointTypes.Vector3, \"SPHERICAL_HARMONICS\");\r\n        state._emitUniformFromString(\"vSphericalL11\", NodeMaterialBlockConnectionPointTypes.Vector3, \"SPHERICAL_HARMONICS\");\r\n        state._emitUniformFromString(\"vSphericalL2_2\", NodeMaterialBlockConnectionPointTypes.Vector3, \"SPHERICAL_HARMONICS\");\r\n        state._emitUniformFromString(\"vSphericalL2_1\", NodeMaterialBlockConnectionPointTypes.Vector3, \"SPHERICAL_HARMONICS\");\r\n        state._emitUniformFromString(\"vSphericalL20\", NodeMaterialBlockConnectionPointTypes.Vector3, \"SPHERICAL_HARMONICS\");\r\n        state._emitUniformFromString(\"vSphericalL21\", NodeMaterialBlockConnectionPointTypes.Vector3, \"SPHERICAL_HARMONICS\");\r\n        state._emitUniformFromString(\"vSphericalL22\", NodeMaterialBlockConnectionPointTypes.Vector3, \"SPHERICAL_HARMONICS\");\r\n\r\n        state._emitUniformFromString(\"vSphericalX\", NodeMaterialBlockConnectionPointTypes.Vector3, \"SPHERICAL_HARMONICS\", true);\r\n        state._emitUniformFromString(\"vSphericalY\", NodeMaterialBlockConnectionPointTypes.Vector3, \"SPHERICAL_HARMONICS\", true);\r\n        state._emitUniformFromString(\"vSphericalZ\", NodeMaterialBlockConnectionPointTypes.Vector3, \"SPHERICAL_HARMONICS\", true);\r\n        state._emitUniformFromString(\"vSphericalXX_ZZ\", NodeMaterialBlockConnectionPointTypes.Vector3, \"SPHERICAL_HARMONICS\", true);\r\n        state._emitUniformFromString(\"vSphericalYY_ZZ\", NodeMaterialBlockConnectionPointTypes.Vector3, \"SPHERICAL_HARMONICS\", true);\r\n        state._emitUniformFromString(\"vSphericalZZ\", NodeMaterialBlockConnectionPointTypes.Vector3, \"SPHERICAL_HARMONICS\", true);\r\n        state._emitUniformFromString(\"vSphericalXY\", NodeMaterialBlockConnectionPointTypes.Vector3, \"SPHERICAL_HARMONICS\", true);\r\n        state._emitUniformFromString(\"vSphericalYZ\", NodeMaterialBlockConnectionPointTypes.Vector3, \"SPHERICAL_HARMONICS\", true);\r\n        state._emitUniformFromString(\"vSphericalZX\", NodeMaterialBlockConnectionPointTypes.Vector3, \"SPHERICAL_HARMONICS\", true);\r\n\r\n        code += `#if defined(USESPHERICALFROMREFLECTIONMAP) && defined(USESPHERICALINVERTEX)\r\n                ${state._declareLocalVar(reflectionVectorName, NodeMaterialBlockConnectionPointTypes.Vector3)} = (${(isWebGPU ? \"uniforms.\" : \"\") + this._reflectionMatrixName} * vec4${state.fSuffix}(normalize(${this.worldNormal.associatedVariableName}).xyz, 0)).xyz;\r\n                #ifdef ${this._defineOppositeZ}\r\n                    ${reflectionVectorName}.z *= -1.0;\r\n                #endif\r\n                ${isWebGPU ? \"vertexOutputs.\" : \"\"}${this._vEnvironmentIrradianceName} = computeEnvironmentIrradiance(${reflectionVectorName});\r\n            #endif\\n`;\r\n\r\n        return code;\r\n    }\r\n\r\n    /**\r\n     * Gets the main code of the block (fragment side)\r\n     * @param state current state of the node material building\r\n     * @param normalVarName name of the existing variable corresponding to the normal\r\n     * @returns the shader code\r\n     */\r\n    public getCode(state: NodeMaterialBuildState, normalVarName: string): string {\r\n        let code = \"\";\r\n\r\n        this.handleFragmentSideInits(state);\r\n        const isWebGPU = state.shaderLanguage === ShaderLanguage.WGSL;\r\n\r\n        state._emitFunctionFromInclude(\"harmonicsFunctions\", `//${this.name}`, {\r\n            replaceStrings: [\r\n                { search: /uniform vec3 vSphericalL00;[\\s\\S]*?uniform vec3 vSphericalL22;/g, replace: \"\" },\r\n                { search: /uniform vec3 vSphericalX;[\\s\\S]*?uniform vec3 vSphericalZX;/g, replace: \"\" },\r\n            ],\r\n        });\r\n\r\n        if (!isWebGPU) {\r\n            state._emitFunction(\r\n                \"sampleReflection\",\r\n                `\r\n                #ifdef ${this._define3DName}\r\n                    #define sampleReflection(s, c) textureCube(s, c)\r\n                #else\r\n                    #define sampleReflection(s, c) texture2D(s, c)\r\n                #endif\\n`,\r\n                `//${this.name}`\r\n            );\r\n\r\n            state._emitFunction(\r\n                \"sampleReflectionLod\",\r\n                `\r\n                #ifdef ${this._define3DName}\r\n                    #define sampleReflectionLod(s, c, l) textureCubeLodEXT(s, c, l)\r\n                #else\r\n                    #define sampleReflectionLod(s, c, l) texture2DLodEXT(s, c, l)\r\n                #endif\\n`,\r\n                `//${this.name}`\r\n            );\r\n        }\r\n\r\n        const computeReflectionCoordsFunc = isWebGPU\r\n            ? `\r\n            fn computeReflectionCoordsPBR(worldPos: vec4f, worldNormal: vec3f) -> vec3f {\r\n                ${this.handleFragmentSideCodeReflectionCoords(state, \"worldNormal\", \"worldPos\", true, true)}\r\n                return ${this._reflectionVectorName};\r\n            }\\n`\r\n            : `\r\n            vec3 computeReflectionCoordsPBR(vec4 worldPos, vec3 worldNormal) {\r\n                ${this.handleFragmentSideCodeReflectionCoords(state, \"worldNormal\", \"worldPos\", true, true)}\r\n                return ${this._reflectionVectorName};\r\n            }\\n`;\r\n\r\n        state._emitFunction(\"computeReflectionCoordsPBR\", computeReflectionCoordsFunc, `//${this.name}`);\r\n\r\n        this._vReflectionMicrosurfaceInfosName = state._getFreeVariableName(\"vReflectionMicrosurfaceInfos\");\r\n\r\n        state._emitUniformFromString(this._vReflectionMicrosurfaceInfosName, NodeMaterialBlockConnectionPointTypes.Vector3);\r\n\r\n        this._vReflectionInfosName = state._getFreeVariableName(\"vReflectionInfos\");\r\n\r\n        this._vReflectionFilteringInfoName = state._getFreeVariableName(\"vReflectionFilteringInfo\");\r\n\r\n        state._emitUniformFromString(this._vReflectionFilteringInfoName, NodeMaterialBlockConnectionPointTypes.Vector2);\r\n\r\n        code += `#ifdef REFLECTION\r\n            ${state._declareLocalVar(this._vReflectionInfosName, NodeMaterialBlockConnectionPointTypes.Vector2)} = vec2${state.fSuffix}(1., 0.);\r\n\r\n            ${isWebGPU ? \"var reflectionOut: reflectionOutParams\" : \"reflectionOutParams reflectionOut\"};\r\n\r\n            reflectionOut = reflectionBlock(\r\n                ${this.generateOnlyFragmentCode ? this._worldPositionNameInFragmentOnlyMode : (isWebGPU ? \"input.\" : \"\") + \"v_\" + this.worldPosition.associatedVariableName}.xyz\r\n                , ${normalVarName}\r\n                , alphaG\r\n                , ${(isWebGPU ? \"uniforms.\" : \"\") + this._vReflectionMicrosurfaceInfosName}\r\n                , ${this._vReflectionInfosName}\r\n                , ${this.reflectionColor}\r\n            #ifdef ANISOTROPIC\r\n                ,anisotropicOut\r\n            #endif\r\n            #if defined(${this._defineLODReflectionAlpha}) && !defined(${this._defineSkyboxName})\r\n                ,NdotVUnclamped\r\n            #endif\r\n            #ifdef ${this._defineLinearSpecularReflection}\r\n                , roughness\r\n            #endif\r\n            #ifdef ${this._define3DName}\r\n                , ${this._cubeSamplerName}\r\n                ${isWebGPU ? `, ${this._cubeSamplerName}Sampler` : \"\"}\r\n            #else\r\n                , ${this._2DSamplerName}\r\n                ${isWebGPU ? `, ${this._2DSamplerName}Sampler` : \"\"}\r\n            #endif\r\n            #if defined(NORMAL) && defined(USESPHERICALINVERTEX)\r\n                , ${isWebGPU ? \"input.\" : \"\"}${this._vEnvironmentIrradianceName}\r\n            #endif\r\n            #if (defined(USESPHERICALFROMREFLECTIONMAP) && (!defined(NORMAL) || !defined(USESPHERICALINVERTEX))) || (defined(USEIRRADIANCEMAP) && defined(REFLECTIONMAP_3D))\r\n                    , ${this._reflectionMatrixName}\r\n            #endif\r\n            #ifdef USEIRRADIANCEMAP\r\n                , irradianceSampler         // ** not handled **\r\n                ${isWebGPU ? `, irradianceSamplerSampler` : \"\"}\r\n                #ifdef USE_IRRADIANCE_DOMINANT_DIRECTION\r\n                , vReflectionDominantDirection\r\n                #endif\r\n            #endif\r\n            #ifndef LODBASEDMICROSFURACE\r\n                #ifdef ${this._define3DName}\r\n                    , ${this._cubeSamplerName}\r\n                    ${isWebGPU ? `, ${this._cubeSamplerName}Sampler` : \"\"}\r\n                    , ${this._cubeSamplerName}\r\n                    ${isWebGPU ? `, ${this._cubeSamplerName}Sampler` : \"\"}\r\n                #else\r\n                    , ${this._2DSamplerName}\r\n                    ${isWebGPU ? `, ${this._2DSamplerName}Sampler` : \"\"}\r\n                    , ${this._2DSamplerName}                    \r\n                    ${isWebGPU ? `, ${this._2DSamplerName}Sampler` : \"\"}\r\n                #endif\r\n            #endif\r\n            #ifdef REALTIME_FILTERING\r\n                , ${this._vReflectionFilteringInfoName}\r\n                #ifdef IBL_CDF_FILTERING\r\n                    , icdfSampler         // ** not handled **\r\n                    ${isWebGPU ? `, icdfSamplerSampler` : \"\"}\r\n                #endif\r\n            #endif\r\n            , viewDirectionW\r\n            , diffuseRoughness\r\n            , surfaceAlbedo\r\n            );\r\n        #endif\\n`;\r\n\r\n        return code;\r\n    }\r\n\r\n    protected override _buildBlock(state: NodeMaterialBuildState) {\r\n        this._scene = state.sharedData.scene;\r\n\r\n        if (state.target !== NodeMaterialBlockTargets.Fragment) {\r\n            this._defineLODReflectionAlpha = state._getFreeDefineName(\"LODINREFLECTIONALPHA\");\r\n            this._defineLinearSpecularReflection = state._getFreeDefineName(\"LINEARSPECULARREFLECTION\");\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    protected override _dumpPropertiesCode() {\r\n        let codeString = super._dumpPropertiesCode();\r\n\r\n        if (this.texture) {\r\n            codeString += `${this._codeVariableName}.texture.gammaSpace = ${this.texture.gammaSpace};\\n`;\r\n        }\r\n        codeString += `${this._codeVariableName}.useSphericalHarmonics = ${this.useSphericalHarmonics};\\n`;\r\n        codeString += `${this._codeVariableName}.forceIrradianceInFragment = ${this.forceIrradianceInFragment};\\n`;\r\n\r\n        return codeString;\r\n    }\r\n\r\n    public override serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        serializationObject.useSphericalHarmonics = this.useSphericalHarmonics;\r\n        serializationObject.forceIrradianceInFragment = this.forceIrradianceInFragment;\r\n        serializationObject.gammaSpace = this.texture?.gammaSpace ?? true;\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public override _deserialize(serializationObject: any, scene: Scene, rootUrl: string) {\r\n        super._deserialize(serializationObject, scene, rootUrl);\r\n\r\n        this.useSphericalHarmonics = serializationObject.useSphericalHarmonics;\r\n        this.forceIrradianceInFragment = serializationObject.forceIrradianceInFragment;\r\n        if (this.texture) {\r\n            this.texture.gammaSpace = serializationObject.gammaSpace;\r\n        }\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.ReflectionBlock\", ReflectionBlock);\r\n"], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAI1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAEhF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,uCAAuC,EAAE,MAAM,+CAA+C,CAAC;AACxG,OAAO,EAAE,0BAA0B,EAAE,MAAM,oCAAoC,CAAC;AAEhF,OAAO,EAAE,OAAO,EAAE,MAAM,2BAA2B,CAAC;AAKpD,OAAO,EAAE,sBAAsB,EAA0B,MAAM,sCAAsC,CAAC;AAEtG,OAAO,EAAE,MAAM,EAAE,mCAAyB;;;;;;;;;;AAMpC,MAAO,eAAgB,kNAAQ,6BAA0B;IA0CxC,kCAAkC,GAAA;QACjD,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;YAC5B,IAAI,CAAC,wBAAwB,GAAG,CAAC,IAAI,CAAC,wBAAwB,CAAC;kKAC/D,SAAM,CAAC,KAAK,CAAC,gEAAgE,CAAC,CAAC;YAC/E,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,UAAU,EAAE,CAAC;QAElB,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,UAAU,GAAA;QACzB,KAAK,CAAC,UAAU,EAAE,CAAC;QACnB,IAAI,CAAC,cAAc,CAAC,UAAU,CAAE,CAAC,MAAM,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,+LAAC,2BAAwB,CAAC,QAAQ,CAAC,CAAC,+LAAC,2BAAwB,CAAC,MAAM,CAAC;QAC9I,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAChC,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;QAC1C,CAAC;IACL,CAAC;IAED;;;OAGG,CACH,YAAmB,IAAY,CAAA;QAC3B,KAAK,CAAC,IAAI,CAAC,CAAC;QAtChB;;;WAGG,CAEI,IAAA,CAAA,qBAAqB,GAAY,IAAI,CAAC;QAE7C;;WAEG,CAEI,IAAA,CAAA,yBAAyB,GAAY,KAAK,CAAC;QA6B9C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,IAAI,CAAC,aAAa,CAAC,UAAU,6MAAE,wCAAqC,CAAC,UAAU,EAAE,KAAK,gMAAE,2BAAwB,CAAC,MAAM,CAAC,CAAC;QACzH,IAAI,CAAC,aAAa,CAAC,OAAO,6MAAE,wCAAqC,CAAC,MAAM,EAAE,KAAK,+LAAE,4BAAwB,CAAC,MAAM,CAAC,CAAC;QAClH,IAAI,CAAC,aAAa,CAAC,OAAO,6MAAE,wCAAqC,CAAC,MAAM,EAAE,IAAI,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAEnH,IAAI,CAAC,cAAc,CACf,YAAY,6MACZ,wCAAqC,CAAC,MAAM,gMAC5C,2BAAwB,CAAC,QAAQ,EACjC,uMAAI,2CAAuC,CAAC,YAAY,EAAE,IAAI,EAAA,EAAA,+CAAA,KAA+C,eAAe,EAAE,iBAAiB,CAAC,CACnJ,CAAC;QAEF,IAAI,CAAC,QAAQ,CAAC,0CAA0C,CACpD,mPAAqC,CAAC,MAAM,8MAAG,wCAAqC,CAAC,OAAO,8MAAG,wCAAqC,CAAC,OAAO,CAC/I,CAAC;IACN,CAAC;IAED;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IAED;;OAEG,CACH,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,4BAA4B,CAAC;IAC7C,CAAC;IAED;;OAEG,CACH,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,0BAA0B,CAAC;IAC3C,CAAC;IAED;;OAEG,CACH,IAAW,KAAK,GAAA;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,cAAc,GAAA;QACrB,OAAO,IAAI,CAAC,6BAA6B,CAAC;IAC9C,CAAC;IAED;;OAEG,CACH,IAAW,IAAI,GAAA;QACX,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAED;;OAEG,CACH,IAAW,KAAK,GAAA;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;IAChC,CAAC;IAED;;OAEG,CACH,IAAW,eAAe,GAAA;QACtB,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC,kBAAkB,CAAC;IAC3F,CAAC;IAEkB,WAAW,GAAA;QAC1B,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,OAAO,IAAI,CAAC,OAAO,CAAC;QACxB,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC;IAC1C,CAAC;IAEe,cAAc,CAAC,OAA4B,EAAA;QACvD,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QAE9B,MAAM,iBAAiB,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAC7C,MAAM,UAAU,GAAG,iBAAiB,IAAI,iBAAiB,CAAC,gBAAgB,CAAC;QAE3E,OAAO,CAAC,QAAQ,CAAC,YAAY,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;QAEjD,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,OAAO;QACX,CAAC;QAED,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,yBAAyB,EAAE,iBAAiB,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;QAC1F,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,+BAA+B,EAAE,iBAAiB,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;QAClG,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAEzI,OAAO,CAAC,QAAQ,CAAC,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC;QAC1E,OAAO,CAAC,QAAQ,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QACxE,OAAO,CAAC,QAAQ,CAAC,gBAAgB,EAAE,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAEnE,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,eAAe,6KAAK,UAAO,CAAC,WAAW,EAAE,CAAC;YACjF,IAAI,iBAAiB,CAAC,MAAM,EAAE,CAAC;gBAC3B,OAAO,CAAC,QAAQ,CAAC,+BAA+B,EAAE,IAAI,CAAC,CAAC;gBACxD,OAAO,CAAC,QAAQ,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;gBAC5C,IAAI,IAAI,CAAC,yBAAyB,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,CAAC,iBAAiB,IAAI,CAAC,EAAE,CAAC;oBAC7F,OAAO,CAAC,QAAQ,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;gBACpD,CAAC,MAAM,CAAC;oBACJ,OAAO,CAAC,QAAQ,CAAC,sBAAsB,EAAE,IAAI,CAAC,CAAC;gBACnD,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAEe,IAAI,CAAC,MAAc,EAAE,YAA0B,EAAE,IAAW,EAAE,OAAiB,EAAA;QAC3F,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;QAEvC,MAAM,iBAAiB,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAE7C,IAAI,CAAC,iBAAiB,IAAI,CAAC,OAAO,EAAE,CAAC;YACjC,OAAO;QACX,CAAC;QAED,IAAI,iBAAiB,CAAC,MAAM,EAAE,CAAC;YAC3B,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,EAAE,iBAAiB,CAAC,CAAC;QAChE,CAAC,MAAM,CAAC;YACJ,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;QAC9D,CAAC;QAED,MAAM,KAAK,GAAG,iBAAiB,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC;QAEhD,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,iCAAiC,EAAE,KAAK,EAAE,iBAAiB,CAAC,kBAAkB,EAAE,iBAAiB,CAAC,mBAAmB,CAAC,CAAC;QAC7I,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,6BAA6B,EAAE,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;QAE9E,MAAM,OAAO,GAAG,OAAO,CAAC,eAAsC,CAAC;QAE/D,MAAM,WAAW,GAAG,iBAAiB,CAAC,mBAAmB,CAAC;QAC1D,IAAI,OAAO,CAAC,6BAA6B,IAAI,WAAW,EAAE,CAAC;YACvD,IAAI,OAAO,CAAC,mBAAmB,EAAE,CAAC;gBAC9B,MAAM,kBAAkB,GAAG,WAAW,CAAC,kBAAkB,CAAC;gBAC1D,MAAM,CAAC,UAAU,CAAC,eAAe,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC;gBAC3D,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,IAAI,CAAC,CAAC;gBAC7D,MAAM,CAAC,UAAU,CAAC,eAAe,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC;gBAC3D,MAAM,CAAC,UAAU,CAAC,eAAe,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC;gBAC3D,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,IAAI,CAAC,CAAC;gBAC7D,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,IAAI,CAAC,CAAC;gBAC7D,MAAM,CAAC,UAAU,CAAC,eAAe,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC;gBAC3D,MAAM,CAAC,UAAU,CAAC,eAAe,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC;gBAC3D,MAAM,CAAC,UAAU,CAAC,eAAe,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC;YAC/D,CAAC,MAAM,CAAC;gBACJ,MAAM,CAAC,SAAS,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnF,MAAM,CAAC,SAAS,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnF,MAAM,CAAC,SAAS,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnF,MAAM,CAAC,SAAS,CAAC,iBAAiB,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACnJ,MAAM,CAAC,SAAS,CAAC,iBAAiB,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACnJ,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACvF,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACvF,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACvF,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAC3F,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;OAIG,CACa,gBAAgB,CAAC,KAA6B,EAAA;QAC1D,IAAI,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QACzC,MAAM,QAAQ,GAAG,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,CAAC;QAE9D,KAAK,CAAC,wBAAwB,CAAC,oBAAoB,EAAE,CAAA,EAAA,EAAK,IAAI,CAAC,IAAI,EAAE,EAAE;YACnE,cAAc,EAAE;gBACZ;oBAAE,MAAM,EAAE,iEAAiE;oBAAE,OAAO,EAAE,EAAE;gBAAA,CAAE;gBAC1F;oBAAE,MAAM,EAAE,8DAA8D;oBAAE,OAAO,EAAE,EAAE;gBAAA,CAAE;aAC1F;SACJ,CAAC,CAAC;QAEH,MAAM,oBAAoB,GAAG,KAAK,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,CAAC;QAE5E,IAAI,CAAC,2BAA2B,GAAG,KAAK,CAAC,oBAAoB,CAAC,wBAAwB,CAAC,CAAC;QAExF,KAAK,CAAC,sBAAsB,CACxB,IAAI,CAAC,2BAA2B,6MAChC,wCAAqC,CAAC,OAAO,EAC7C,yEAAyE,CAC5E,CAAC;QAEF,KAAK,CAAC,sBAAsB,CAAC,eAAe,6MAAE,wCAAqC,CAAC,OAAO,EAAE,qBAAqB,CAAC,CAAC;QACpH,KAAK,CAAC,sBAAsB,CAAC,gBAAgB,6MAAE,wCAAqC,CAAC,OAAO,EAAE,qBAAqB,CAAC,CAAC;QACrH,KAAK,CAAC,sBAAsB,CAAC,eAAe,6MAAE,wCAAqC,CAAC,OAAO,EAAE,qBAAqB,CAAC,CAAC;QACpH,KAAK,CAAC,sBAAsB,CAAC,eAAe,6MAAE,wCAAqC,CAAC,OAAO,EAAE,qBAAqB,CAAC,CAAC;QACpH,KAAK,CAAC,sBAAsB,CAAC,gBAAgB,6MAAE,wCAAqC,CAAC,OAAO,EAAE,qBAAqB,CAAC,CAAC;QACrH,KAAK,CAAC,sBAAsB,CAAC,gBAAgB,6MAAE,wCAAqC,CAAC,OAAO,EAAE,qBAAqB,CAAC,CAAC;QACrH,KAAK,CAAC,sBAAsB,CAAC,eAAe,6MAAE,wCAAqC,CAAC,OAAO,EAAE,qBAAqB,CAAC,CAAC;QACpH,KAAK,CAAC,sBAAsB,CAAC,eAAe,6MAAE,wCAAqC,CAAC,OAAO,EAAE,qBAAqB,CAAC,CAAC;QACpH,KAAK,CAAC,sBAAsB,CAAC,eAAe,6MAAE,wCAAqC,CAAC,OAAO,EAAE,qBAAqB,CAAC,CAAC;QAEpH,KAAK,CAAC,sBAAsB,CAAC,aAAa,6MAAE,wCAAqC,CAAC,OAAO,EAAE,qBAAqB,EAAE,IAAI,CAAC,CAAC;QACxH,KAAK,CAAC,sBAAsB,CAAC,aAAa,6MAAE,wCAAqC,CAAC,OAAO,EAAE,qBAAqB,EAAE,IAAI,CAAC,CAAC;QACxH,KAAK,CAAC,sBAAsB,CAAC,aAAa,6MAAE,wCAAqC,CAAC,OAAO,EAAE,qBAAqB,EAAE,IAAI,CAAC,CAAC;QACxH,KAAK,CAAC,sBAAsB,CAAC,iBAAiB,6MAAE,wCAAqC,CAAC,OAAO,EAAE,qBAAqB,EAAE,IAAI,CAAC,CAAC;QAC5H,KAAK,CAAC,sBAAsB,CAAC,iBAAiB,6MAAE,wCAAqC,CAAC,OAAO,EAAE,qBAAqB,EAAE,IAAI,CAAC,CAAC;QAC5H,KAAK,CAAC,sBAAsB,CAAC,cAAc,6MAAE,wCAAqC,CAAC,OAAO,EAAE,qBAAqB,EAAE,IAAI,CAAC,CAAC;QACzH,KAAK,CAAC,sBAAsB,CAAC,cAAc,6MAAE,wCAAqC,CAAC,OAAO,EAAE,qBAAqB,EAAE,IAAI,CAAC,CAAC;QACzH,KAAK,CAAC,sBAAsB,CAAC,cAAc,6MAAE,wCAAqC,CAAC,OAAO,EAAE,qBAAqB,EAAE,IAAI,CAAC,CAAC;QACzH,KAAK,CAAC,sBAAsB,CAAC,cAAc,6MAAE,wCAAqC,CAAC,OAAO,EAAE,qBAAqB,EAAE,IAAI,CAAC,CAAC;QAEzH,IAAI,IAAI,CAAA;kBACE,KAAK,CAAC,gBAAgB,CAAC,oBAAoB,4MAAE,yCAAqC,CAAC,OAAO,CAAC,CAAA,IAAA,EAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAA,OAAA,EAAU,KAAK,CAAC,OAAO,CAAA,WAAA,EAAc,IAAI,CAAC,WAAW,CAAC,sBAAsB,CAAA;yBACjO,IAAI,CAAC,gBAAgB,CAAA;sBACxB,oBAAoB,CAAA;;kBAExB,QAAQ,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,2BAA2B,CAAA,gCAAA,EAAmC,oBAAoB,CAAA;qBACvH,CAAC;QAEd,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG,CACI,OAAO,CAAC,KAA6B,EAAE,aAAqB,EAAA;QAC/D,IAAI,IAAI,GAAG,EAAE,CAAC;QAEd,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC;QACpC,MAAM,QAAQ,GAAG,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,CAAC;QAE9D,KAAK,CAAC,wBAAwB,CAAC,oBAAoB,EAAE,CAAA,EAAA,EAAK,IAAI,CAAC,IAAI,EAAE,EAAE;YACnE,cAAc,EAAE;gBACZ;oBAAE,MAAM,EAAE,iEAAiE;oBAAE,OAAO,EAAE,EAAE;gBAAA,CAAE;gBAC1F;oBAAE,MAAM,EAAE,8DAA8D;oBAAE,OAAO,EAAE,EAAE;gBAAA,CAAE;aAC1F;SACJ,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;YACZ,KAAK,CAAC,aAAa,CACf,kBAAkB,EAClB,CAAA;yBACS,IAAI,CAAC,aAAa,CAAA;;;;yBAIlB,EACT,CAAA,EAAA,EAAK,IAAI,CAAC,IAAI,EAAE,CACnB,CAAC;YAEF,KAAK,CAAC,aAAa,CACf,qBAAqB,EACrB,CAAA;yBACS,IAAI,CAAC,aAAa,CAAA;;;;yBAIlB,EACT,CAAA,EAAA,EAAK,IAAI,CAAC,IAAI,EAAE,CACnB,CAAC;QACN,CAAC;QAED,MAAM,2BAA2B,GAAG,QAAQ,GACtC,CAAA;;kBAEI,IAAI,CAAC,sCAAsC,CAAC,KAAK,EAAE,aAAa,EAAE,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;yBAClF,IAAI,CAAC,qBAAqB,CAAA;gBACnC,GACF,CAAA;;kBAEI,IAAI,CAAC,sCAAsC,CAAC,KAAK,EAAE,aAAa,EAAE,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;yBAClF,IAAI,CAAC,qBAAqB,CAAA;gBACnC,CAAC;QAET,KAAK,CAAC,aAAa,CAAC,4BAA4B,EAAE,2BAA2B,EAAE,CAAA,EAAA,EAAK,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QAEjG,IAAI,CAAC,iCAAiC,GAAG,KAAK,CAAC,oBAAoB,CAAC,8BAA8B,CAAC,CAAC;QAEpG,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,iCAAiC,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAC;QAEpH,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,CAAC;QAE5E,IAAI,CAAC,6BAA6B,GAAG,KAAK,CAAC,oBAAoB,CAAC,0BAA0B,CAAC,CAAC;QAE5F,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,6BAA6B,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAC;QAEhH,IAAI,IAAI,CAAA;cACF,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,qBAAqB,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA,OAAA,EAAU,KAAK,CAAC,OAAO,CAAA;;cAExH,QAAQ,CAAC,CAAC,CAAC,wCAAwC,CAAC,CAAC,CAAC,mCAAmC,CAAA;;;kBAGrF,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAA;oBACvJ,aAAa,CAAA;;oBAEb,CAAC,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,iCAAiC,CAAA;oBACtE,IAAI,CAAC,qBAAqB,CAAA;oBAC1B,IAAI,CAAC,eAAe,CAAA;;;;0BAId,IAAI,CAAC,yBAAyB,CAAA,cAAA,EAAiB,IAAI,CAAC,iBAAiB,CAAA;;;qBAG1E,IAAI,CAAC,+BAA+B,CAAA;;;qBAGpC,IAAI,CAAC,aAAa,CAAA;oBACnB,IAAI,CAAC,gBAAgB,CAAA;kBACvB,QAAQ,CAAC,CAAC,CAAC,CAAA,EAAA,EAAK,IAAI,CAAC,gBAAgB,CAAA,OAAA,CAAS,CAAC,CAAC,CAAC,EAAE,CAAA;;oBAEjD,IAAI,CAAC,cAAc,CAAA;kBACrB,QAAQ,CAAC,CAAC,CAAC,CAAA,EAAA,EAAK,IAAI,CAAC,cAAc,CAAA,OAAA,CAAS,CAAC,CAAC,CAAC,EAAE,CAAA;;;oBAG/C,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,2BAA2B,CAAA;;;wBAGvD,IAAI,CAAC,qBAAqB,CAAA;;;;kBAIhC,QAAQ,CAAC,CAAC,CAAC,CAAA,0BAAA,CAA4B,CAAC,CAAC,CAAC,EAAE,CAAA;;;;;;yBAMrC,IAAI,CAAC,aAAa,CAAA;wBACnB,IAAI,CAAC,gBAAgB,CAAA;sBACvB,QAAQ,CAAC,CAAC,CAAC,CAAA,EAAA,EAAK,IAAI,CAAC,gBAAgB,CAAA,OAAA,CAAS,CAAC,CAAC,CAAC,EAAE,CAAA;wBACjD,IAAI,CAAC,gBAAgB,CAAA;sBACvB,QAAQ,CAAC,CAAC,CAAC,CAAA,EAAA,EAAK,IAAI,CAAC,gBAAgB,CAAA,OAAA,CAAS,CAAC,CAAC,CAAC,EAAE,CAAA;;wBAEjD,IAAI,CAAC,cAAc,CAAA;sBACrB,QAAQ,CAAC,CAAC,CAAC,CAAA,EAAA,EAAK,IAAI,CAAC,cAAc,CAAA,OAAA,CAAS,CAAC,CAAC,CAAC,EAAE,CAAA;wBAC/C,IAAI,CAAC,cAAc,CAAA;sBACrB,QAAQ,CAAC,CAAC,CAAC,CAAA,EAAA,EAAK,IAAI,CAAC,cAAc,CAAA,OAAA,CAAS,CAAC,CAAC,CAAC,EAAE,CAAA;;;;oBAInD,IAAI,CAAC,6BAA6B,CAAA;;;sBAGhC,QAAQ,CAAC,CAAC,CAAC,CAAA,oBAAA,CAAsB,CAAC,CAAC,CAAC,EAAE,CAAA;;;;;;;iBAO3C,CAAC;QAEV,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,WAAW,CAAC,KAA6B,EAAA;QACxD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC;QAErC,IAAI,KAAK,CAAC,MAAM,mMAAK,2BAAwB,CAAC,QAAQ,EAAE,CAAC;YACrD,IAAI,CAAC,yBAAyB,GAAG,KAAK,CAAC,kBAAkB,CAAC,sBAAsB,CAAC,CAAC;YAClF,IAAI,CAAC,+BAA+B,GAAG,KAAK,CAAC,kBAAkB,CAAC,0BAA0B,CAAC,CAAC;QAChG,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,mBAAmB,GAAA;QAClC,IAAI,UAAU,GAAG,KAAK,CAAC,mBAAmB,EAAE,CAAC;QAE7C,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,sBAAA,EAAyB,IAAI,CAAC,OAAO,CAAC,UAAU,CAAA,GAAA,CAAK,CAAC;QACjG,CAAC;QACD,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,yBAAA,EAA4B,IAAI,CAAC,qBAAqB,CAAA,GAAA,CAAK,CAAC;QACnG,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,6BAAA,EAAgC,IAAI,CAAC,yBAAyB,CAAA,GAAA,CAAK,CAAC;QAE3G,OAAO,UAAU,CAAC;IACtB,CAAC;IAEe,SAAS,GAAA;QACrB,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,mBAAmB,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC;QACvE,mBAAmB,CAAC,yBAAyB,GAAG,IAAI,CAAC,yBAAyB,CAAC;QAC/E,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,EAAE,UAAU,IAAI,IAAI,CAAC;QAElE,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEe,YAAY,CAAC,mBAAwB,EAAE,KAAY,EAAE,OAAe,EAAA;QAChF,KAAK,CAAC,YAAY,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAExD,IAAI,CAAC,qBAAqB,GAAG,mBAAmB,CAAC,qBAAqB,CAAC;QACvE,IAAI,CAAC,yBAAyB,GAAG,mBAAmB,CAAC,yBAAyB,CAAC;QAC/E,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,mBAAmB,CAAC,UAAU,CAAC;QAC7D,CAAC;IACL,CAAC;CACJ;wJAzcU,aAAA,EAAA;IADN,gMAAA,AAAsB,EAAC,qBAAqB,EAAA,EAAA,kCAAA,KAAkC,UAAU,EAAE;QAAE,QAAQ,EAAE,IAAI;QAAE,SAAS,EAAE;YAAE,MAAM,EAAE,IAAI;QAAA,CAAE;IAAA,CAAE,CAAC;8DAC9F;wJAMtC,aAAA,EAAA;KADN,+LAAA,AAAsB,EAAC,8BAA8B,EAAA,EAAA,kCAAA,KAAkC,UAAU,EAAE;QAAE,QAAQ,EAAE,IAAI;QAAE,SAAS,EAAE;YAAE,MAAM,EAAE,IAAI;QAAA,CAAE;IAAA,CAAE,CAAC;kEAClG;6JAqctD,gBAAA,AAAa,EAAC,yBAAyB,EAAE,eAAe,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 794, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/clearCoatBlock.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Blocks/PBR/clearCoatBlock.ts"], "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialConnectionPointDirection } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport { InputBlock } from \"../Input/inputBlock\";\r\nimport { NodeMaterialConnectionPointCustomObject } from \"../../nodeMaterialConnectionPointCustomObject\";\r\nimport type { NodeMaterial, NodeMaterialDefines } from \"../../nodeMaterial\";\r\nimport type { ReflectionBlock } from \"./reflectionBlock\";\r\nimport type { Scene } from \"../../../../scene\";\r\nimport type { Nullable } from \"../../../../types\";\r\nimport type { Mesh } from \"../../../../Meshes/mesh\";\r\nimport type { Effect } from \"../../../effect\";\r\nimport type { PBRMetallicRoughnessBlock } from \"./pbrMetallicRoughnessBlock\";\r\nimport type { PerturbNormalBlock } from \"../Fragment/perturbNormalBlock\";\r\nimport { PBRClearCoatConfiguration } from \"../../../PBR/pbrClearCoatConfiguration\";\r\nimport { editableInPropertyPage, PropertyTypeForEdition } from \"../../../../Decorators/nodeDecorator\";\r\nimport { TBNBlock } from \"../Fragment/TBNBlock\";\r\nimport { ShaderLanguage } from \"core/Materials/shaderLanguage\";\r\n\r\n/**\r\n * Block used to implement the clear coat module of the PBR material\r\n */\r\nexport class ClearCoatBlock extends NodeMaterialBlock {\r\n    private _scene: Scene;\r\n    private _tangentCorrectionFactorName = \"\";\r\n\r\n    /**\r\n     * Create a new ClearCoatBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this._isUnique = true;\r\n\r\n        this.registerInput(\"intensity\", NodeMaterialBlockConnectionPointTypes.Float, false, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"roughness\", NodeMaterialBlockConnectionPointTypes.Float, true, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"indexOfRefraction\", NodeMaterialBlockConnectionPointTypes.Float, true, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"normalMapColor\", NodeMaterialBlockConnectionPointTypes.Color3, true, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"uv\", NodeMaterialBlockConnectionPointTypes.Vector2, true, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"tintColor\", NodeMaterialBlockConnectionPointTypes.Color3, true, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"tintAtDistance\", NodeMaterialBlockConnectionPointTypes.Float, true, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"tintThickness\", NodeMaterialBlockConnectionPointTypes.Float, true, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"worldTangent\", NodeMaterialBlockConnectionPointTypes.Vector4, true);\r\n        this.registerInput(\"worldNormal\", NodeMaterialBlockConnectionPointTypes.AutoDetect, true);\r\n        this.worldNormal.addExcludedConnectionPointFromAllowedTypes(\r\n            NodeMaterialBlockConnectionPointTypes.Color4 | NodeMaterialBlockConnectionPointTypes.Vector4 | NodeMaterialBlockConnectionPointTypes.Vector3\r\n        );\r\n        this.registerInput(\r\n            \"TBN\",\r\n            NodeMaterialBlockConnectionPointTypes.Object,\r\n            true,\r\n            NodeMaterialBlockTargets.VertexAndFragment,\r\n            new NodeMaterialConnectionPointCustomObject(\"TBN\", this, NodeMaterialConnectionPointDirection.Input, TBNBlock, \"TBNBlock\")\r\n        );\r\n\r\n        this.registerOutput(\r\n            \"clearcoat\",\r\n            NodeMaterialBlockConnectionPointTypes.Object,\r\n            NodeMaterialBlockTargets.Fragment,\r\n            new NodeMaterialConnectionPointCustomObject(\"clearcoat\", this, NodeMaterialConnectionPointDirection.Output, ClearCoatBlock, \"ClearCoatBlock\")\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Defines if the F0 value should be remapped to account for the interface change in the material.\r\n     */\r\n    @editableInPropertyPage(\"Remap F0 on interface change\", PropertyTypeForEdition.Boolean, \"ADVANCED\", { embedded: true })\r\n    public remapF0OnInterfaceChange: boolean = true;\r\n\r\n    /**\r\n     * Initialize the block and prepare the context for build\r\n     * @param state defines the state that will be used for the build\r\n     */\r\n    public override initialize(state: NodeMaterialBuildState) {\r\n        state._excludeVariableName(\"clearcoatOut\");\r\n        state._excludeVariableName(\"vClearCoatParams\");\r\n        state._excludeVariableName(\"vClearCoatTintParams\");\r\n        state._excludeVariableName(\"vClearCoatRefractionParams\");\r\n        state._excludeVariableName(\"vClearCoatTangentSpaceParams\");\r\n        state._excludeVariableName(\"vGeometricNormaClearCoatW\");\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public override getClassName() {\r\n        return \"ClearCoatBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the intensity input component\r\n     */\r\n    public get intensity(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the roughness input component\r\n     */\r\n    public get roughness(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the ior input component\r\n     */\r\n    public get indexOfRefraction(): NodeMaterialConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the bump texture input component\r\n     */\r\n    public get normalMapColor(): NodeMaterialConnectionPoint {\r\n        return this._inputs[3];\r\n    }\r\n\r\n    /**\r\n     * Gets the uv input component\r\n     */\r\n    public get uv(): NodeMaterialConnectionPoint {\r\n        return this._inputs[4];\r\n    }\r\n\r\n    /**\r\n     * Gets the tint color input component\r\n     */\r\n    public get tintColor(): NodeMaterialConnectionPoint {\r\n        return this._inputs[5];\r\n    }\r\n\r\n    /**\r\n     * Gets the tint \"at distance\" input component\r\n     */\r\n    public get tintAtDistance(): NodeMaterialConnectionPoint {\r\n        return this._inputs[6];\r\n    }\r\n\r\n    /**\r\n     * Gets the tint thickness input component\r\n     */\r\n    public get tintThickness(): NodeMaterialConnectionPoint {\r\n        return this._inputs[7];\r\n    }\r\n\r\n    /**\r\n     * Gets the world tangent input component\r\n     */\r\n    public get worldTangent(): NodeMaterialConnectionPoint {\r\n        return this._inputs[8];\r\n    }\r\n\r\n    /**\r\n     * Gets the world normal input component\r\n     */\r\n    public get worldNormal(): NodeMaterialConnectionPoint {\r\n        return this._inputs[9];\r\n    }\r\n\r\n    /**\r\n     * Gets the TBN input component\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public get TBN(): NodeMaterialConnectionPoint {\r\n        return this._inputs[10];\r\n    }\r\n\r\n    /**\r\n     * Gets the clear coat object output component\r\n     */\r\n    public get clearcoat(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    public override autoConfigure() {\r\n        if (!this.intensity.isConnected) {\r\n            const intensityInput = new InputBlock(\"ClearCoat intensity\", NodeMaterialBlockTargets.Fragment, NodeMaterialBlockConnectionPointTypes.Float);\r\n            intensityInput.value = 1;\r\n            intensityInput.output.connectTo(this.intensity);\r\n        }\r\n    }\r\n\r\n    public override prepareDefines(defines: NodeMaterialDefines) {\r\n        defines.setValue(\"CLEARCOAT\", true);\r\n        defines.setValue(\"CLEARCOAT_TEXTURE\", false, true);\r\n        defines.setValue(\"CLEARCOAT_USE_ROUGHNESS_FROM_MAINTEXTURE\", true, true);\r\n        defines.setValue(\"CLEARCOAT_TINT\", this.tintColor.isConnected || this.tintThickness.isConnected || this.tintAtDistance.isConnected, true);\r\n        defines.setValue(\"CLEARCOAT_BUMP\", this.normalMapColor.isConnected, true);\r\n        defines.setValue(\r\n            \"CLEARCOAT_DEFAULTIOR\",\r\n            this.indexOfRefraction.isConnected ? this.indexOfRefraction.connectInputBlock!.value === PBRClearCoatConfiguration._DefaultIndexOfRefraction : true,\r\n            true\r\n        );\r\n        defines.setValue(\"CLEARCOAT_REMAP_F0\", this.remapF0OnInterfaceChange, true);\r\n    }\r\n\r\n    public override bind(effect: Effect, nodeMaterial: NodeMaterial, mesh?: Mesh) {\r\n        super.bind(effect, nodeMaterial, mesh);\r\n\r\n        // Clear Coat Refraction params\r\n        const indexOfRefraction = this.indexOfRefraction.connectInputBlock?.value ?? PBRClearCoatConfiguration._DefaultIndexOfRefraction;\r\n\r\n        const a = 1 - indexOfRefraction;\r\n        const b = 1 + indexOfRefraction;\r\n        const f0 = Math.pow(-a / b, 2); // Schlicks approx: (ior1 - ior2) / (ior1 + ior2) where ior2 for air is close to vacuum = 1.\r\n        const eta = 1 / indexOfRefraction;\r\n\r\n        effect.setFloat4(\"vClearCoatRefractionParams\", f0, eta, a, b);\r\n\r\n        // Clear Coat tangent space params\r\n        const mainPBRBlock = this.clearcoat.hasEndpoints ? (this.clearcoat.endpoints[0].ownerBlock as PBRMetallicRoughnessBlock) : null;\r\n        const perturbedNormalBlock = mainPBRBlock?.perturbedNormal.isConnected ? (mainPBRBlock.perturbedNormal.connectedPoint!.ownerBlock as PerturbNormalBlock) : null;\r\n\r\n        if (this._scene._mirroredCameraPosition) {\r\n            effect.setFloat2(\"vClearCoatTangentSpaceParams\", perturbedNormalBlock?.invertX ? 1.0 : -1.0, perturbedNormalBlock?.invertY ? 1.0 : -1.0);\r\n        } else {\r\n            effect.setFloat2(\"vClearCoatTangentSpaceParams\", perturbedNormalBlock?.invertX ? -1.0 : 1.0, perturbedNormalBlock?.invertY ? -1.0 : 1.0);\r\n        }\r\n\r\n        if (mesh) {\r\n            effect.setFloat(this._tangentCorrectionFactorName, mesh.getWorldMatrix().determinant() < 0 ? -1 : 1);\r\n        }\r\n    }\r\n\r\n    private _generateTBNSpace(state: NodeMaterialBuildState, worldPositionVarName: string, worldNormalVarName: string) {\r\n        let code = \"\";\r\n\r\n        const comments = `//${this.name}`;\r\n        const worldTangent = this.worldTangent;\r\n        const isWebGPU = state.shaderLanguage === ShaderLanguage.WGSL;\r\n\r\n        if (!isWebGPU) {\r\n            state._emitExtension(\"derivatives\", \"#extension GL_OES_standard_derivatives : enable\");\r\n        }\r\n\r\n        const tangentReplaceString = { search: /defined\\(TANGENT\\)/g, replace: worldTangent.isConnected ? \"defined(TANGENT)\" : \"defined(IGNORE)\" };\r\n\r\n        const tbn = this.TBN;\r\n        if (tbn.isConnected) {\r\n            state.compilationString += `\r\n            #ifdef TBNBLOCK\r\n                ${isWebGPU ? \"var TBN\" : \"mat3 TBN\"} = ${tbn.associatedVariableName};\r\n            #endif\r\n            `;\r\n        } else if (worldTangent.isConnected) {\r\n            code += `${state._declareLocalVar(\"tbnNormal\", NodeMaterialBlockConnectionPointTypes.Vector3)} = normalize(${worldNormalVarName}.xyz);\\n`;\r\n            code += `${state._declareLocalVar(\"tbnTangent\", NodeMaterialBlockConnectionPointTypes.Vector3)} = normalize(${worldTangent.associatedVariableName}.xyz);\\n`;\r\n            code += `${state._declareLocalVar(\"tbnBitangent\", NodeMaterialBlockConnectionPointTypes.Vector3)} = cross(tbnNormal, tbnTangent) * ${this._tangentCorrectionFactorName};\\n`;\r\n            code += `${isWebGPU ? \"var vTBN\" : \"mat3 vTBN\"} = ${isWebGPU ? \"mat3x3f\" : \"mat3\"}(tbnTangent, tbnBitangent, tbnNormal);\\n`;\r\n        }\r\n\r\n        state._emitFunctionFromInclude(\"bumpFragmentMainFunctions\", comments, {\r\n            replaceStrings: [tangentReplaceString],\r\n        });\r\n\r\n        return code;\r\n    }\r\n\r\n    /** @internal */\r\n    public static _GetInitializationCode(state: NodeMaterialBuildState, ccBlock: Nullable<ClearCoatBlock>): string {\r\n        let code = \"\";\r\n\r\n        const intensity = ccBlock?.intensity.isConnected ? ccBlock.intensity.associatedVariableName : \"1.\";\r\n        const roughness = ccBlock?.roughness.isConnected ? ccBlock.roughness.associatedVariableName : \"0.\";\r\n\r\n        const tintColor = ccBlock?.tintColor.isConnected ? ccBlock.tintColor.associatedVariableName : `vec3${state.fSuffix}(1.)`;\r\n        const tintThickness = ccBlock?.tintThickness.isConnected ? ccBlock.tintThickness.associatedVariableName : \"1.\";\r\n\r\n        code += `\r\n            #ifdef CLEARCOAT\r\n                ${state._declareLocalVar(\"vClearCoatParams\", NodeMaterialBlockConnectionPointTypes.Vector2)} = vec2${state.fSuffix}(${intensity}, ${roughness});\r\n                ${state._declareLocalVar(\"vClearCoatTintParams\", NodeMaterialBlockConnectionPointTypes.Vector4)} = vec4${state.fSuffix}(${tintColor}, ${tintThickness});\r\n            #endif\\n`;\r\n\r\n        return code;\r\n    }\r\n\r\n    /**\r\n     * Gets the main code of the block (fragment side)\r\n     * @param state current state of the node material building\r\n     * @param ccBlock instance of a ClearCoatBlock or null if the code must be generated without an active clear coat module\r\n     * @param reflectionBlock instance of a ReflectionBlock null if the code must be generated without an active reflection module\r\n     * @param worldPosVarName name of the variable holding the world position\r\n     * @param generateTBNSpace if true, the code needed to create the TBN coordinate space is generated\r\n     * @param vTBNAvailable indicate that the vTBN variable is already existing because it has already been generated by another block (PerturbNormal or Anisotropy)\r\n     * @param worldNormalVarName name of the variable holding the world normal\r\n     * @returns the shader code\r\n     */\r\n    public static GetCode(\r\n        state: NodeMaterialBuildState,\r\n        ccBlock: Nullable<ClearCoatBlock>,\r\n        reflectionBlock: Nullable<ReflectionBlock>,\r\n        worldPosVarName: string,\r\n        generateTBNSpace: boolean,\r\n        vTBNAvailable: boolean,\r\n        worldNormalVarName: string\r\n    ): string {\r\n        let code = \"\";\r\n\r\n        const normalMapColor = ccBlock?.normalMapColor.isConnected ? ccBlock.normalMapColor.associatedVariableName : `vec3${state.fSuffix}(0.)`;\r\n        const uv = ccBlock?.uv.isConnected ? ccBlock.uv.associatedVariableName : `vec2${state.fSuffix}(0.)`;\r\n\r\n        const tintAtDistance = ccBlock?.tintAtDistance.isConnected ? ccBlock.tintAtDistance.associatedVariableName : \"1.\";\r\n        const tintTexture = `vec4${state.fSuffix}(0.)`;\r\n\r\n        if (ccBlock) {\r\n            state._emitUniformFromString(\"vClearCoatRefractionParams\", NodeMaterialBlockConnectionPointTypes.Vector4);\r\n            state._emitUniformFromString(\"vClearCoatTangentSpaceParams\", NodeMaterialBlockConnectionPointTypes.Vector2);\r\n\r\n            const normalShading = ccBlock.worldNormal;\r\n            code += `${state._declareLocalVar(\"vGeometricNormaClearCoatW\", NodeMaterialBlockConnectionPointTypes.Vector3)} = ${normalShading.isConnected ? \"normalize(\" + normalShading.associatedVariableName + \".xyz)\" : \"geometricNormalW\"};\\n`;\r\n        } else {\r\n            code += `${state._declareLocalVar(\"vGeometricNormaClearCoatW\", NodeMaterialBlockConnectionPointTypes.Vector3)} = geometricNormalW;\\n`;\r\n        }\r\n\r\n        if (generateTBNSpace && ccBlock) {\r\n            code += ccBlock._generateTBNSpace(state, worldPosVarName, worldNormalVarName);\r\n            vTBNAvailable = ccBlock.worldTangent.isConnected;\r\n        }\r\n\r\n        const isWebGPU = state.shaderLanguage === ShaderLanguage.WGSL;\r\n        code += `${isWebGPU ? \"var clearcoatOut: clearcoatOutParams\" : \"clearcoatOutParams clearcoatOut\"};\r\n\r\n        #ifdef CLEARCOAT\r\n            clearcoatOut = clearcoatBlock(\r\n                ${worldPosVarName}.xyz\r\n                , vGeometricNormaClearCoatW\r\n                , viewDirectionW\r\n                , vClearCoatParams\r\n                , specularEnvironmentR0\r\n            #ifdef CLEARCOAT_TEXTURE\r\n                , vec2${state.fSuffix}(0.)\r\n            #endif\r\n            #ifdef CLEARCOAT_TINT\r\n                , vClearCoatTintParams\r\n                , ${tintAtDistance}\r\n                , ${isWebGPU ? \"uniforms.\" : \"\"}vClearCoatRefractionParams\r\n                #ifdef CLEARCOAT_TINT_TEXTURE\r\n                    , ${tintTexture}\r\n                #endif\r\n            #endif\r\n            #ifdef CLEARCOAT_BUMP\r\n                , vec2${state.fSuffix}(0., 1.)\r\n                , vec4${state.fSuffix}(${normalMapColor}, 0.)\r\n                , ${uv}\r\n                #if defined(${vTBNAvailable ? \"TANGENT\" : \"IGNORE\"}) && defined(NORMAL)\r\n                    , vTBN\r\n                #else\r\n                    , ${isWebGPU ? \"uniforms.\" : \"\"}vClearCoatTangentSpaceParams\r\n                #endif\r\n                #ifdef OBJECTSPACE_NORMALMAP\r\n                    , normalMatrix\r\n                #endif\r\n            #endif\r\n            #if defined(FORCENORMALFORWARD) && defined(NORMAL)\r\n                , faceNormal\r\n            #endif\r\n            #ifdef REFLECTION\r\n                , ${isWebGPU ? \"uniforms.\" : \"\"}${reflectionBlock?._vReflectionMicrosurfaceInfosName}\r\n                , ${reflectionBlock?._vReflectionInfosName}\r\n                , ${reflectionBlock?.reflectionColor}\r\n                , ${isWebGPU ? \"uniforms.\" : \"\"}vLightingIntensity\r\n                #ifdef ${reflectionBlock?._define3DName}\r\n                    , ${reflectionBlock?._cubeSamplerName}       \r\n                    ${isWebGPU ? `, ${reflectionBlock?._cubeSamplerName}Sampler` : \"\"}\r\n                #else\r\n                    , ${reflectionBlock?._2DSamplerName}       \r\n                    ${isWebGPU ? `, ${reflectionBlock?._2DSamplerName}Sampler` : \"\"}\r\n                #endif\r\n                #ifndef LODBASEDMICROSFURACE\r\n                    #ifdef ${reflectionBlock?._define3DName}\r\n                        , ${reflectionBlock?._cubeSamplerName}       \r\n                        ${isWebGPU ? `, ${reflectionBlock?._cubeSamplerName}Sampler` : \"\"}\r\n                        , ${reflectionBlock?._cubeSamplerName}\r\n                        ${isWebGPU ? `, ${reflectionBlock?._cubeSamplerName}Sampler` : \"\"}\r\n                    #else\r\n                        , ${reflectionBlock?._2DSamplerName}\r\n                        ${isWebGPU ? `, ${reflectionBlock?._2DSamplerName}Sampler` : \"\"}\r\n                        , ${reflectionBlock?._2DSamplerName}\r\n                        ${isWebGPU ? `, ${reflectionBlock?._2DSamplerName}Sampler` : \"\"}                        \r\n                    #endif\r\n                #endif\r\n            #endif\r\n            #if defined(CLEARCOAT_BUMP) || defined(TWOSIDEDLIGHTING)\r\n                , (${state._generateTernary(\"1.\", \"-1.\", isWebGPU ? \"fragmentInputs.frontFacing\" : \"gl_FrontFacing\")})\r\n            #endif\r\n            );\r\n        #else\r\n            clearcoatOut.specularEnvironmentR0 = specularEnvironmentR0;\r\n        #endif\\n`;\r\n\r\n        return code;\r\n    }\r\n\r\n    protected override _buildBlock(state: NodeMaterialBuildState) {\r\n        this._scene = state.sharedData.scene;\r\n\r\n        if (state.target === NodeMaterialBlockTargets.Fragment) {\r\n            state.sharedData.bindableBlocks.push(this);\r\n            state.sharedData.blocksWithDefines.push(this);\r\n\r\n            this._tangentCorrectionFactorName = state._getFreeDefineName(\"tangentCorrectionFactor\");\r\n            state._emitUniformFromString(this._tangentCorrectionFactorName, NodeMaterialBlockConnectionPointTypes.Float);\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    protected override _dumpPropertiesCode() {\r\n        let codeString = super._dumpPropertiesCode();\r\n\r\n        codeString += `${this._codeVariableName}.remapF0OnInterfaceChange = ${this.remapF0OnInterfaceChange};\\n`;\r\n\r\n        return codeString;\r\n    }\r\n\r\n    public override serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        serializationObject.remapF0OnInterfaceChange = this.remapF0OnInterfaceChange;\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public override _deserialize(serializationObject: any, scene: Scene, rootUrl: string) {\r\n        super._deserialize(serializationObject, scene, rootUrl);\r\n\r\n        this.remapF0OnInterfaceChange = serializationObject.remapF0OnInterfaceChange ?? true;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.ClearCoatBlock\", ClearCoatBlock);\r\n"], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAI1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAChF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AACjD,OAAO,EAAE,uCAAuC,EAAE,MAAM,+CAA+C,CAAC;AASxG,OAAO,EAAE,yBAAyB,EAAE,MAAM,wCAAwC,CAAC;AACnF,OAAO,EAAE,sBAAsB,EAA0B,MAAM,sCAAsC,CAAC;AACtG,OAAO,EAAE,QAAQ,EAAE,MAAM,sBAAsB,CAAC;;;;;;;;;;;AAM1C,MAAO,cAAe,uLAAQ,oBAAiB;IAIjD;;;OAGG,CACH,YAAmB,IAAY,CAAA;QAC3B,KAAK,CAAC,IAAI,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAP3C,IAAA,CAAA,4BAA4B,GAAG,EAAE,CAAC;QAwC1C;;WAEG,CAEI,IAAA,CAAA,wBAAwB,GAAY,IAAI,CAAC;QAnC5C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,IAAI,CAAC,aAAa,CAAC,WAAW,6MAAE,wCAAqC,CAAC,KAAK,EAAE,KAAK,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QACvH,IAAI,CAAC,aAAa,CAAC,WAAW,6MAAE,wCAAqC,CAAC,KAAK,EAAE,IAAI,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QACtH,IAAI,CAAC,aAAa,CAAC,mBAAmB,6MAAE,wCAAqC,CAAC,KAAK,EAAE,IAAI,EAAE,yNAAwB,CAAC,QAAQ,CAAC,CAAC;QAC9H,IAAI,CAAC,aAAa,CAAC,gBAAgB,6MAAE,wCAAqC,CAAC,MAAM,EAAE,IAAI,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAC5H,IAAI,CAAC,aAAa,CAAC,IAAI,6MAAE,wCAAqC,CAAC,OAAO,EAAE,IAAI,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QACjH,IAAI,CAAC,aAAa,CAAC,WAAW,6MAAE,wCAAqC,CAAC,MAAM,EAAE,IAAI,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QACvH,IAAI,CAAC,aAAa,CAAC,gBAAgB,6MAAE,wCAAqC,CAAC,KAAK,EAAE,IAAI,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAC3H,IAAI,CAAC,aAAa,CAAC,eAAe,6MAAE,wCAAqC,CAAC,KAAK,EAAE,IAAI,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAC1H,IAAI,CAAC,aAAa,CAAC,cAAc,4MAAE,yCAAqC,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACxF,IAAI,CAAC,aAAa,CAAC,aAAa,6MAAE,wCAAqC,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAC1F,IAAI,CAAC,WAAW,CAAC,0CAA0C,4MACvD,wCAAqC,CAAC,MAAM,8MAAG,wCAAqC,CAAC,OAAO,8MAAG,wCAAqC,CAAC,OAAO,CAC/I,CAAC;QACF,IAAI,CAAC,aAAa,CACd,KAAK,4MACL,yCAAqC,CAAC,MAAM,EAC5C,IAAI,gMACJ,2BAAwB,CAAC,iBAAiB,EAC1C,wMAAI,0CAAuC,CAAC,KAAK,EAAE,IAAI,EAAA,EAAA,8CAAA,KAA8C,sMAAQ,EAAE,UAAU,CAAC,CAC7H,CAAC;QAEF,IAAI,CAAC,cAAc,CACf,WAAW,6MACX,wCAAqC,CAAC,MAAM,gMAC5C,2BAAwB,CAAC,QAAQ,EACjC,wMAAI,0CAAuC,CAAC,WAAW,EAAE,IAAI,EAAA,EAAA,+CAAA,KAA+C,cAAc,EAAE,gBAAgB,CAAC,CAChJ,CAAC;IACN,CAAC;IAQD;;;OAGG,CACa,UAAU,CAAC,KAA6B,EAAA;QACpD,KAAK,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAC3C,KAAK,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,CAAC;QAC/C,KAAK,CAAC,oBAAoB,CAAC,sBAAsB,CAAC,CAAC;QACnD,KAAK,CAAC,oBAAoB,CAAC,4BAA4B,CAAC,CAAC;QACzD,KAAK,CAAC,oBAAoB,CAAC,8BAA8B,CAAC,CAAC;QAC3D,KAAK,CAAC,oBAAoB,CAAC,2BAA2B,CAAC,CAAC;IAC5D,CAAC;IAED;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,iBAAiB,GAAA;QACxB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,cAAc,GAAA;QACrB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,EAAE,GAAA;QACT,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,cAAc,GAAA;QACrB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,YAAY,GAAA;QACnB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,gEAAgE;IAChE,IAAW,GAAG,GAAA;QACV,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAEe,aAAa,GAAA;QACzB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;YAC9B,MAAM,cAAc,GAAG,8LAAI,aAAU,CAAC,qBAAqB,gMAAE,2BAAwB,CAAC,QAAQ,4MAAE,yCAAqC,CAAC,KAAK,CAAC,CAAC;YAC7I,cAAc,CAAC,KAAK,GAAG,CAAC,CAAC;YACzB,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACpD,CAAC;IACL,CAAC;IAEe,cAAc,CAAC,OAA4B,EAAA;QACvD,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QACpC,OAAO,CAAC,QAAQ,CAAC,mBAAmB,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QACnD,OAAO,CAAC,QAAQ,CAAC,0CAA0C,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QACzE,OAAO,CAAC,QAAQ,CAAC,gBAAgB,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,IAAI,IAAI,CAAC,aAAa,CAAC,WAAW,IAAI,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QAC1I,OAAO,CAAC,QAAQ,CAAC,gBAAgB,EAAE,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QAC1E,OAAO,CAAC,QAAQ,CACZ,sBAAsB,EACtB,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,iBAAkB,CAAC,KAAK,0LAAK,4BAAyB,CAAC,yBAAyB,CAAC,CAAC,CAAC,IAAI,EACnJ,IAAI,CACP,CAAC;QACF,OAAO,CAAC,QAAQ,CAAC,oBAAoB,EAAE,IAAI,CAAC,wBAAwB,EAAE,IAAI,CAAC,CAAC;IAChF,CAAC;IAEe,IAAI,CAAC,MAAc,EAAE,YAA0B,EAAE,IAAW,EAAA;QACxE,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;QAEvC,+BAA+B;QAC/B,MAAM,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,KAAK,yLAAI,4BAAyB,CAAC,yBAAyB,CAAC;QAEjI,MAAM,CAAC,GAAG,CAAC,GAAG,iBAAiB,CAAC;QAChC,MAAM,CAAC,GAAG,CAAC,GAAG,iBAAiB,CAAC;QAChC,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,4FAA4F;QAC5H,MAAM,GAAG,GAAG,CAAC,GAAG,iBAAiB,CAAC;QAElC,MAAM,CAAC,SAAS,CAAC,4BAA4B,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE9D,kCAAkC;QAClC,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,UAAwC,CAAC,CAAC,CAAC,IAAI,CAAC;QAChI,MAAM,oBAAoB,GAAG,YAAY,EAAE,eAAe,CAAC,WAAW,CAAC,CAAC,CAAE,YAAY,CAAC,eAAe,CAAC,cAAe,CAAC,UAAiC,CAAC,CAAC,CAAC,IAAI,CAAC;QAEhK,IAAI,IAAI,CAAC,MAAM,CAAC,uBAAuB,EAAE,CAAC;YACtC,MAAM,CAAC,SAAS,CAAC,8BAA8B,EAAE,oBAAoB,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,oBAAoB,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC7I,CAAC,MAAM,CAAC;YACJ,MAAM,CAAC,SAAS,CAAC,8BAA8B,EAAE,oBAAoB,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,oBAAoB,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC7I,CAAC;QAED,IAAI,IAAI,EAAE,CAAC;YACP,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,4BAA4B,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzG,CAAC;IACL,CAAC;IAEO,iBAAiB,CAAC,KAA6B,EAAE,oBAA4B,EAAE,kBAA0B,EAAA;QAC7G,IAAI,IAAI,GAAG,EAAE,CAAC;QAEd,MAAM,QAAQ,GAAG,CAAA,EAAA,EAAK,IAAI,CAAC,IAAI,EAAE,CAAC;QAClC,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QACvC,MAAM,QAAQ,GAAG,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,CAAC;QAE9D,IAAI,CAAC,QAAQ,EAAE,CAAC;YACZ,KAAK,CAAC,cAAc,CAAC,aAAa,EAAE,iDAAiD,CAAC,CAAC;QAC3F,CAAC;QAED,MAAM,oBAAoB,GAAG;YAAE,MAAM,EAAE,qBAAqB;YAAE,OAAO,EAAE,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,iBAAiB;QAAA,CAAE,CAAC;QAE3I,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;QACrB,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC;YAClB,KAAK,CAAC,iBAAiB,IAAI,CAAA;;kBAErB,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAA,GAAA,EAAM,GAAG,CAAC,sBAAsB,CAAA;;aAEtE,CAAC;QACN,CAAC,MAAM,IAAI,YAAY,CAAC,WAAW,EAAE,CAAC;YAClC,IAAI,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,WAAW,4MAAE,yCAAqC,CAAC,OAAO,CAAC,CAAA,aAAA,EAAgB,kBAAkB,CAAA,QAAA,CAAU,CAAC;YAC1I,IAAI,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,YAAY,4MAAE,yCAAqC,CAAC,OAAO,CAAC,CAAA,aAAA,EAAgB,YAAY,CAAC,sBAAsB,CAAA,QAAA,CAAU,CAAC;YAC5J,IAAI,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,cAAc,4MAAE,yCAAqC,CAAC,OAAO,CAAC,CAAA,kCAAA,EAAqC,IAAI,CAAC,4BAA4B,CAAA,GAAA,CAAK,CAAC;YAC5K,IAAI,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,WAAW,CAAA,GAAA,EAAM,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAA,wCAAA,CAA0C,CAAC;QAChI,CAAC;QAED,KAAK,CAAC,wBAAwB,CAAC,2BAA2B,EAAE,QAAQ,EAAE;YAClE,cAAc,EAAE;gBAAC,oBAAoB;aAAC;SACzC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,cAAA,EAAgB,CACT,MAAM,CAAC,sBAAsB,CAAC,KAA6B,EAAE,OAAiC,EAAA;QACjG,IAAI,IAAI,GAAG,EAAE,CAAC;QAEd,MAAM,SAAS,GAAG,OAAO,EAAE,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC;QACnG,MAAM,SAAS,GAAG,OAAO,EAAE,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC;QAEnG,MAAM,SAAS,GAAG,OAAO,EAAE,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAA,IAAA,EAAO,KAAK,CAAC,OAAO,CAAA,IAAA,CAAM,CAAC;QACzH,MAAM,aAAa,GAAG,OAAO,EAAE,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC;QAE/G,IAAI,IAAI,CAAA;;kBAEE,KAAK,CAAC,gBAAgB,CAAC,kBAAkB,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA,OAAA,EAAU,KAAK,CAAC,OAAO,CAAA,CAAA,EAAI,SAAS,CAAA,EAAA,EAAK,SAAS,CAAA;kBAC3I,KAAK,CAAC,gBAAgB,CAAC,sBAAsB,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA,OAAA,EAAU,KAAK,CAAC,OAAO,CAAA,CAAA,EAAI,SAAS,CAAA,EAAA,EAAK,aAAa,CAAA;qBAChJ,CAAC;QAEd,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;;;;OAUG,CACI,MAAM,CAAC,OAAO,CACjB,KAA6B,EAC7B,OAAiC,EACjC,eAA0C,EAC1C,eAAuB,EACvB,gBAAyB,EACzB,aAAsB,EACtB,kBAA0B,EAAA;QAE1B,IAAI,IAAI,GAAG,EAAE,CAAC;QAEd,MAAM,cAAc,GAAG,OAAO,EAAE,cAAc,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAA,IAAA,EAAO,KAAK,CAAC,OAAO,CAAA,IAAA,CAAM,CAAC;QACxI,MAAM,EAAE,GAAG,OAAO,EAAE,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAA,IAAA,EAAO,KAAK,CAAC,OAAO,CAAA,IAAA,CAAM,CAAC;QAEpG,MAAM,cAAc,GAAG,OAAO,EAAE,cAAc,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC;QAClH,MAAM,WAAW,GAAG,CAAA,IAAA,EAAO,KAAK,CAAC,OAAO,CAAA,IAAA,CAAM,CAAC;QAE/C,IAAI,OAAO,EAAE,CAAC;YACV,KAAK,CAAC,sBAAsB,CAAC,4BAA4B,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAC;YAC1G,KAAK,CAAC,sBAAsB,CAAC,8BAA8B,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAC;YAE5G,MAAM,aAAa,GAAG,OAAO,CAAC,WAAW,CAAC;YAC1C,IAAI,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,2BAA2B,4MAAE,yCAAqC,CAAC,OAAO,CAAC,CAAA,GAAA,EAAM,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,YAAY,GAAG,aAAa,CAAC,sBAAsB,GAAG,OAAO,CAAC,CAAC,CAAC,kBAAkB,CAAA,GAAA,CAAK,CAAC;QAC3O,CAAC,MAAM,CAAC;YACJ,IAAI,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,2BAA2B,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA,sBAAA,CAAwB,CAAC;QAC1I,CAAC;QAED,IAAI,gBAAgB,IAAI,OAAO,EAAE,CAAC;YAC9B,IAAI,IAAI,OAAO,CAAC,iBAAiB,CAAC,KAAK,EAAE,eAAe,EAAE,kBAAkB,CAAC,CAAC;YAC9E,aAAa,GAAG,OAAO,CAAC,YAAY,CAAC,WAAW,CAAC;QACrD,CAAC;QAED,MAAM,QAAQ,GAAG,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,CAAC;QAC9D,IAAI,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,sCAAsC,CAAC,CAAC,CAAC,iCAAiC,CAAA;;;;kBAItF,eAAe,CAAA;;;;;;wBAMT,KAAK,CAAC,OAAO,CAAA;;;;oBAIjB,cAAc,CAAA;oBACd,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAA;;wBAEvB,WAAW,CAAA;;;;wBAIX,KAAK,CAAC,OAAO,CAAA;wBACb,KAAK,CAAC,OAAO,CAAA,CAAA,EAAI,cAAc,CAAA;oBACnC,EAAE,CAAA;8BACQ,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAA;;;wBAG1C,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAA;;;;;;;;;;oBAU/B,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,GAAG,eAAe,EAAE,iCAAiC,CAAA;oBAChF,eAAe,EAAE,qBAAqB,CAAA;oBACtC,eAAe,EAAE,eAAe,CAAA;oBAChC,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAA;yBACtB,eAAe,EAAE,aAAa,CAAA;wBAC/B,eAAe,EAAE,gBAAgB,CAAA;sBACnC,QAAQ,CAAC,CAAC,CAAC,CAAA,EAAA,EAAK,eAAe,EAAE,gBAAgB,CAAA,OAAA,CAAS,CAAC,CAAC,CAAC,EAAE,CAAA;;wBAE7D,eAAe,EAAE,cAAc,CAAA;sBACjC,QAAQ,CAAC,CAAC,CAAC,CAAA,EAAA,EAAK,eAAe,EAAE,cAAc,CAAA,OAAA,CAAS,CAAC,CAAC,CAAC,EAAE,CAAA;;;6BAGtD,eAAe,EAAE,aAAa,CAAA;4BAC/B,eAAe,EAAE,gBAAgB,CAAA;0BACnC,QAAQ,CAAC,CAAC,CAAC,CAAA,EAAA,EAAK,eAAe,EAAE,gBAAgB,CAAA,OAAA,CAAS,CAAC,CAAC,CAAC,EAAE,CAAA;4BAC7D,eAAe,EAAE,gBAAgB,CAAA;0BACnC,QAAQ,CAAC,CAAC,CAAC,CAAA,EAAA,EAAK,eAAe,EAAE,gBAAgB,CAAA,OAAA,CAAS,CAAC,CAAC,CAAC,EAAE,CAAA;;4BAE7D,eAAe,EAAE,cAAc,CAAA;0BACjC,QAAQ,CAAC,CAAC,CAAC,CAAA,EAAA,EAAK,eAAe,EAAE,cAAc,CAAA,OAAA,CAAS,CAAC,CAAC,CAAC,EAAE,CAAA;4BAC3D,eAAe,EAAE,cAAc,CAAA;0BACjC,QAAQ,CAAC,CAAC,CAAC,CAAA,EAAA,EAAK,eAAe,EAAE,cAAc,CAAA,OAAA,CAAS,CAAC,CAAC,CAAC,EAAE,CAAA;;;;;qBAKlE,KAAK,CAAC,gBAAgB,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC,4BAA4B,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAA;;;;;iBAKnG,CAAC;QAEV,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,WAAW,CAAC,KAA6B,EAAA;QACxD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC;QAErC,IAAI,KAAK,CAAC,MAAM,mMAAK,2BAAwB,CAAC,QAAQ,EAAE,CAAC;YACrD,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,KAAK,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE9C,IAAI,CAAC,4BAA4B,GAAG,KAAK,CAAC,kBAAkB,CAAC,yBAAyB,CAAC,CAAC;YACxF,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,4BAA4B,6MAAE,wCAAqC,CAAC,KAAK,CAAC,CAAC;QACjH,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,mBAAmB,GAAA;QAClC,IAAI,UAAU,GAAG,KAAK,CAAC,mBAAmB,EAAE,CAAC;QAE7C,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,4BAAA,EAA+B,IAAI,CAAC,wBAAwB,CAAA,GAAA,CAAK,CAAC;QAEzG,OAAO,UAAU,CAAC;IACtB,CAAC;IAEe,SAAS,GAAA;QACrB,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,mBAAmB,CAAC,wBAAwB,GAAG,IAAI,CAAC,wBAAwB,CAAC;QAE7E,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEe,YAAY,CAAC,mBAAwB,EAAE,KAAY,EAAE,OAAe,EAAA;QAChF,KAAK,CAAC,YAAY,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAExD,IAAI,CAAC,wBAAwB,GAAG,mBAAmB,CAAC,wBAAwB,IAAI,IAAI,CAAC;IACzF,CAAC;CACJ;wJA3WU,aAAA,EAAA;KADN,+LAAA,AAAsB,EAAC,8BAA8B,EAAA,EAAA,kCAAA,KAAkC,UAAU,EAAE;QAAE,QAAQ,EAAE,IAAI;IAAA,CAAE,CAAC;gEACvE;6JA6WpD,gBAAA,AAAa,EAAC,wBAAwB,EAAE,cAAc,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 1137, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/iridescenceBlock.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Blocks/PBR/iridescenceBlock.ts"], "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialConnectionPointDirection } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport { InputBlock } from \"../Input/inputBlock\";\r\nimport { NodeMaterialConnectionPointCustomObject } from \"../../nodeMaterialConnectionPointCustomObject\";\r\nimport type { NodeMaterialDefines } from \"../../nodeMaterial\";\r\nimport type { Scene } from \"../../../../scene\";\r\nimport type { Nullable } from \"../../../../types\";\r\nimport { PBRIridescenceConfiguration } from \"../../../../Materials/PBR/pbrIridescenceConfiguration\";\r\nimport { ShaderLanguage } from \"core/Materials/shaderLanguage\";\r\n\r\n/**\r\n * Block used to implement the iridescence module of the PBR material\r\n */\r\nexport class IridescenceBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Create a new IridescenceBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this._isUnique = true;\r\n\r\n        this.registerInput(\"intensity\", NodeMaterialBlockConnectionPointTypes.Float, true, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"indexOfRefraction\", NodeMaterialBlockConnectionPointTypes.Float, true, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"thickness\", NodeMaterialBlockConnectionPointTypes.Float, true, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this.registerOutput(\r\n            \"iridescence\",\r\n            NodeMaterialBlockConnectionPointTypes.Object,\r\n            NodeMaterialBlockTargets.Fragment,\r\n            new NodeMaterialConnectionPointCustomObject(\"iridescence\", this, NodeMaterialConnectionPointDirection.Output, IridescenceBlock, \"IridescenceBlock\")\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Initialize the block and prepare the context for build\r\n     * @param state defines the state that will be used for the build\r\n     */\r\n    public override initialize(state: NodeMaterialBuildState) {\r\n        state._excludeVariableName(\"iridescenceOut\");\r\n        state._excludeVariableName(\"vIridescenceParams\");\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public override getClassName() {\r\n        return \"IridescenceBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the intensity input component\r\n     */\r\n    public get intensity(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the indexOfRefraction input component\r\n     */\r\n    public get indexOfRefraction(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the thickness input component\r\n     */\r\n    public get thickness(): NodeMaterialConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the iridescence object output component\r\n     */\r\n    public get iridescence(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    public override autoConfigure() {\r\n        if (!this.intensity.isConnected) {\r\n            const intensityInput = new InputBlock(\"Iridescence intensity\", NodeMaterialBlockTargets.Fragment, NodeMaterialBlockConnectionPointTypes.Float);\r\n            intensityInput.value = 1;\r\n            intensityInput.output.connectTo(this.intensity);\r\n\r\n            const indexOfRefractionInput = new InputBlock(\"Iridescence ior\", NodeMaterialBlockTargets.Fragment, NodeMaterialBlockConnectionPointTypes.Float);\r\n            indexOfRefractionInput.value = 1.3;\r\n            indexOfRefractionInput.output.connectTo(this.indexOfRefraction);\r\n\r\n            const thicknessInput = new InputBlock(\"Iridescence thickness\", NodeMaterialBlockTargets.Fragment, NodeMaterialBlockConnectionPointTypes.Float);\r\n            thicknessInput.value = 400;\r\n            thicknessInput.output.connectTo(this.thickness);\r\n        }\r\n    }\r\n\r\n    public override prepareDefines(defines: NodeMaterialDefines) {\r\n        defines.setValue(\"IRIDESCENCE\", true, true);\r\n        defines.setValue(\"IRIDESCENCE_TEXTURE\", false, true);\r\n        defines.setValue(\"IRIDESCENCE_THICKNESS_TEXTURE\", false, true);\r\n    }\r\n\r\n    /**\r\n     * Gets the main code of the block (fragment side)\r\n     * @param iridescenceBlock instance of a IridescenceBlock or null if the code must be generated without an active iridescence module\r\n     * @param state defines the build state\r\n     * @returns the shader code\r\n     */\r\n    public static GetCode(iridescenceBlock: Nullable<IridescenceBlock>, state: NodeMaterialBuildState): string {\r\n        let code = \"\";\r\n\r\n        const intensityName = iridescenceBlock?.intensity.isConnected ? iridescenceBlock.intensity.associatedVariableName : \"1.\";\r\n        const indexOfRefraction = iridescenceBlock?.indexOfRefraction.isConnected\r\n            ? iridescenceBlock.indexOfRefraction.associatedVariableName\r\n            : PBRIridescenceConfiguration._DefaultIndexOfRefraction;\r\n        const thickness = iridescenceBlock?.thickness.isConnected ? iridescenceBlock.thickness.associatedVariableName : PBRIridescenceConfiguration._DefaultMaximumThickness;\r\n\r\n        const isWebGPU = state.shaderLanguage === ShaderLanguage.WGSL;\r\n\r\n        code += `${isWebGPU ? \"var iridescenceOut: iridescenceOutParams\" : \"iridescenceOutParams iridescenceOut\"};\r\n\r\n        #ifdef IRIDESCENCE\r\n            iridescenceOut = iridescenceBlock(\r\n                vec4(${intensityName}, ${indexOfRefraction}, 1., ${thickness})\r\n                , NdotV\r\n                , specularEnvironmentR0\r\n                #ifdef CLEARCOAT\r\n                    , NdotVUnclamped\r\n                    , vClearCoatParams\r\n                #endif                \r\n            );\r\n\r\n            ${isWebGPU ? \"let\" : \"float\"} iridescenceIntensity = iridescenceOut.iridescenceIntensity;\r\n            specularEnvironmentR0 = iridescenceOut.specularEnvironmentR0;\r\n        #endif\\n`;\r\n\r\n        return code;\r\n    }\r\n\r\n    protected override _buildBlock(state: NodeMaterialBuildState) {\r\n        if (state.target === NodeMaterialBlockTargets.Fragment) {\r\n            state.sharedData.bindableBlocks.push(this);\r\n            state.sharedData.blocksWithDefines.push(this);\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    public override serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public override _deserialize(serializationObject: any, scene: Scene, rootUrl: string) {\r\n        super._deserialize(serializationObject, scene, rootUrl);\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.IridescenceBlock\", IridescenceBlock);\r\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAI1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAChF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AACjD,OAAO,EAAE,uCAAuC,EAAE,MAAM,+CAA+C,CAAC;AAIxG,OAAO,EAAE,2BAA2B,EAAE,MAAM,uDAAuD,CAAC;;;;;;;;AAM9F,MAAO,gBAAiB,uLAAQ,oBAAiB;IACnD;;;OAGG,CACH,YAAmB,IAAY,CAAA;QAC3B,KAAK,CAAC,IAAI,EAAE,yNAAwB,CAAC,QAAQ,CAAC,CAAC;QAE/C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,IAAI,CAAC,aAAa,CAAC,WAAW,6MAAE,wCAAqC,CAAC,KAAK,EAAE,IAAI,EAAE,yNAAwB,CAAC,QAAQ,CAAC,CAAC;QACtH,IAAI,CAAC,aAAa,CAAC,mBAAmB,6MAAE,wCAAqC,CAAC,KAAK,EAAE,IAAI,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAC9H,IAAI,CAAC,aAAa,CAAC,WAAW,6MAAE,wCAAqC,CAAC,KAAK,EAAE,IAAI,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAEtH,IAAI,CAAC,cAAc,CACf,aAAa,6MACb,wCAAqC,CAAC,MAAM,gMAC5C,2BAAwB,CAAC,QAAQ,EACjC,wMAAI,0CAAuC,CAAC,aAAa,EAAE,IAAI,EAAA,EAAA,+CAAA,KAA+C,gBAAgB,EAAE,kBAAkB,CAAC,CACtJ,CAAC;IACN,CAAC;IAED;;;OAGG,CACa,UAAU,CAAC,KAA6B,EAAA;QACpD,KAAK,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;QAC7C,KAAK,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;IACrD,CAAC;IAED;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,kBAAkB,CAAC;IAC9B,CAAC;IAED;;OAEG,CACH,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,iBAAiB,GAAA;QACxB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAEe,aAAa,GAAA;QACzB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;YAC9B,MAAM,cAAc,GAAG,8LAAI,aAAU,CAAC,uBAAuB,gMAAE,2BAAwB,CAAC,QAAQ,EAAE,mPAAqC,CAAC,KAAK,CAAC,CAAC;YAC/I,cAAc,CAAC,KAAK,GAAG,CAAC,CAAC;YACzB,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAEhD,MAAM,sBAAsB,GAAG,8LAAI,aAAU,CAAC,iBAAiB,EAAE,yNAAwB,CAAC,QAAQ,6MAAE,wCAAqC,CAAC,KAAK,CAAC,CAAC;YACjJ,sBAAsB,CAAC,KAAK,GAAG,GAAG,CAAC;YACnC,sBAAsB,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAEhE,MAAM,cAAc,GAAG,6LAAI,cAAU,CAAC,uBAAuB,gMAAE,2BAAwB,CAAC,QAAQ,6MAAE,wCAAqC,CAAC,KAAK,CAAC,CAAC;YAC/I,cAAc,CAAC,KAAK,GAAG,GAAG,CAAC;YAC3B,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACpD,CAAC;IACL,CAAC;IAEe,cAAc,CAAC,OAA4B,EAAA;QACvD,OAAO,CAAC,QAAQ,CAAC,aAAa,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAC5C,OAAO,CAAC,QAAQ,CAAC,qBAAqB,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QACrD,OAAO,CAAC,QAAQ,CAAC,+BAA+B,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;IACnE,CAAC;IAED;;;;;OAKG,CACI,MAAM,CAAC,OAAO,CAAC,gBAA4C,EAAE,KAA6B,EAAA;QAC7F,IAAI,IAAI,GAAG,EAAE,CAAC;QAEd,MAAM,aAAa,GAAG,gBAAgB,EAAE,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,gBAAgB,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC;QACzH,MAAM,iBAAiB,GAAG,gBAAgB,EAAE,iBAAiB,CAAC,WAAW,GACnE,gBAAgB,CAAC,iBAAiB,CAAC,sBAAsB,0LACzD,8BAA2B,CAAC,yBAAyB,CAAC;QAC5D,MAAM,SAAS,GAAG,gBAAgB,EAAE,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,gBAAgB,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC,wLAAC,8BAA2B,CAAC,wBAAwB,CAAC;QAErK,MAAM,QAAQ,GAAG,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,CAAC;QAE9D,IAAI,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,0CAA0C,CAAC,CAAC,CAAC,qCAAqC,CAAA;;;;uBAIzF,aAAa,CAAA,EAAA,EAAK,iBAAiB,CAAA,MAAA,EAAS,SAAS,CAAA;;;;;;;;;cAS9D,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAA;;iBAEvB,CAAC;QAEV,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,WAAW,CAAC,KAA6B,EAAA;QACxD,IAAI,KAAK,CAAC,MAAM,mMAAK,2BAAwB,CAAC,QAAQ,EAAE,CAAC;YACrD,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,KAAK,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClD,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEe,SAAS,GAAA;QACrB,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEe,YAAY,CAAC,mBAAwB,EAAE,KAAY,EAAE,OAAe,EAAA;QAChF,KAAK,CAAC,YAAY,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IAC5D,CAAC;CACJ;6JAED,gBAAA,AAAa,EAAC,0BAA0B,EAAE,gBAAgB,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 1266, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/refractionBlock.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Blocks/PBR/refractionBlock.ts"], "sourcesContent": ["import { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialConnectionPointDirection } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterial, NodeMaterialDefines } from \"../../nodeMaterial\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport { InputBlock } from \"../Input/inputBlock\";\r\nimport { NodeMaterialConnectionPointCustomObject } from \"../../nodeMaterialConnectionPointCustomObject\";\r\nimport type { Nullable } from \"../../../../types\";\r\nimport type { BaseTexture } from \"../../../Textures/baseTexture\";\r\nimport type { Mesh } from \"../../../../Meshes/mesh\";\r\nimport type { Effect } from \"../../../effect\";\r\nimport { editableInPropertyPage, PropertyTypeForEdition } from \"../../../../Decorators/nodeDecorator\";\r\nimport type { Scene } from \"../../../../scene\";\r\nimport { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { CubeTexture } from \"../../../Textures/cubeTexture\";\r\nimport { Texture } from \"../../../Textures/texture\";\r\nimport { NodeMaterialSystemValues } from \"../../Enums/nodeMaterialSystemValues\";\r\nimport { ShaderLanguage } from \"core/Materials/shaderLanguage\";\r\n\r\n/**\r\n * Block used to implement the refraction part of the sub surface module of the PBR material\r\n */\r\nexport class RefractionBlock extends NodeMaterialBlock {\r\n    /** @internal */\r\n    public _define3DName: string;\r\n    /** @internal */\r\n    public _refractionMatrixName: string;\r\n    /** @internal */\r\n    public _defineLODRefractionAlpha: string;\r\n    /** @internal */\r\n    public _defineLinearSpecularRefraction: string;\r\n    /** @internal */\r\n    public _defineOppositeZ: string;\r\n    /** @internal */\r\n    public _cubeSamplerName: string;\r\n    /** @internal */\r\n    public _2DSamplerName: string;\r\n    /** @internal */\r\n    public _vRefractionMicrosurfaceInfosName: string;\r\n    /** @internal */\r\n    public _vRefractionInfosName: string;\r\n    /** @internal */\r\n    public _vRefractionFilteringInfoName: string;\r\n\r\n    private _scene: Scene;\r\n\r\n    /**\r\n     * The properties below are set by the main PBR block prior to calling methods of this class.\r\n     * This is to avoid having to add them as inputs here whereas they are already inputs of the main block, so already known.\r\n     * It's less burden on the user side in the editor part.\r\n     */\r\n\r\n    /** @internal */\r\n    public viewConnectionPoint: NodeMaterialConnectionPoint;\r\n\r\n    /** @internal */\r\n    public indexOfRefractionConnectionPoint: NodeMaterialConnectionPoint;\r\n\r\n    /**\r\n     * This parameters will make the material used its opacity to control how much it is refracting against not.\r\n     * Materials half opaque for instance using refraction could benefit from this control.\r\n     */\r\n    @editableInPropertyPage(\"Link refraction to transparency\", PropertyTypeForEdition.Boolean, \"ADVANCED\", { embedded: true, notifiers: { update: true } })\r\n    public linkRefractionWithTransparency: boolean = false;\r\n\r\n    /**\r\n     * Controls if refraction needs to be inverted on Y. This could be useful for procedural texture.\r\n     */\r\n    @editableInPropertyPage(\"Invert refraction Y\", PropertyTypeForEdition.Boolean, \"ADVANCED\", { embedded: true, notifiers: { update: true } })\r\n    public invertRefractionY: boolean = false;\r\n\r\n    /**\r\n     * Controls if refraction needs to be inverted on Y. This could be useful for procedural texture.\r\n     */\r\n    @editableInPropertyPage(\"Use thickness as depth\", PropertyTypeForEdition.Boolean, \"ADVANCED\", { embedded: true, notifiers: { update: true } })\r\n    public useThicknessAsDepth: boolean = false;\r\n\r\n    /**\r\n     * Gets or sets the texture associated with the node\r\n     */\r\n    public texture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * Create a new RefractionBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this._isUnique = true;\r\n\r\n        this.registerInput(\"intensity\", NodeMaterialBlockConnectionPointTypes.Float, false, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"tintAtDistance\", NodeMaterialBlockConnectionPointTypes.Float, true, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"volumeIndexOfRefraction\", NodeMaterialBlockConnectionPointTypes.Float, true, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this.registerOutput(\r\n            \"refraction\",\r\n            NodeMaterialBlockConnectionPointTypes.Object,\r\n            NodeMaterialBlockTargets.Fragment,\r\n            new NodeMaterialConnectionPointCustomObject(\"refraction\", this, NodeMaterialConnectionPointDirection.Output, RefractionBlock, \"RefractionBlock\")\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Initialize the block and prepare the context for build\r\n     * @param state defines the state that will be used for the build\r\n     */\r\n    public override initialize(state: NodeMaterialBuildState) {\r\n        state._excludeVariableName(\"vRefractionPosition\");\r\n        state._excludeVariableName(\"vRefractionSize\");\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public override getClassName() {\r\n        return \"RefractionBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the intensity input component\r\n     */\r\n    public get intensity(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the tint at distance input component\r\n     */\r\n    public get tintAtDistance(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the volume index of refraction input component\r\n     */\r\n    public get volumeIndexOfRefraction(): NodeMaterialConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the view input component\r\n     */\r\n    public get view(): NodeMaterialConnectionPoint {\r\n        return this.viewConnectionPoint;\r\n    }\r\n\r\n    /**\r\n     * Gets the refraction object output component\r\n     */\r\n    public get refraction(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Returns true if the block has a texture\r\n     */\r\n    public get hasTexture(): boolean {\r\n        return !!this._getTexture();\r\n    }\r\n\r\n    protected _getTexture(): Nullable<BaseTexture> {\r\n        if (this.texture) {\r\n            return this.texture;\r\n        }\r\n\r\n        return this._scene.environmentTexture;\r\n    }\r\n\r\n    public override autoConfigure(material: NodeMaterial, additionalFilteringInfo: (node: NodeMaterialBlock) => boolean = () => true) {\r\n        if (!this.intensity.isConnected) {\r\n            const intensityInput = new InputBlock(\"Refraction intensity\", NodeMaterialBlockTargets.Fragment, NodeMaterialBlockConnectionPointTypes.Float);\r\n            intensityInput.value = 1;\r\n            intensityInput.output.connectTo(this.intensity);\r\n        }\r\n\r\n        if (this.view && !this.view.isConnected) {\r\n            let viewInput = material.getInputBlockByPredicate((b) => b.systemValue === NodeMaterialSystemValues.View && additionalFilteringInfo(b));\r\n\r\n            if (!viewInput) {\r\n                viewInput = new InputBlock(\"view\");\r\n                viewInput.setAsSystemValue(NodeMaterialSystemValues.View);\r\n            }\r\n            viewInput.output.connectTo(this.view);\r\n        }\r\n    }\r\n\r\n    public override prepareDefines(defines: NodeMaterialDefines) {\r\n        const refractionTexture = this._getTexture();\r\n        const refraction = refractionTexture && refractionTexture.getTextureMatrix;\r\n\r\n        defines.setValue(\"SS_REFRACTION\", refraction, true);\r\n\r\n        if (!refraction) {\r\n            return;\r\n        }\r\n\r\n        defines.setValue(this._define3DName, refractionTexture.isCube, true);\r\n        defines.setValue(this._defineLODRefractionAlpha, refractionTexture.lodLevelInAlpha, true);\r\n        defines.setValue(this._defineLinearSpecularRefraction, refractionTexture.linearSpecularLOD, true);\r\n        defines.setValue(this._defineOppositeZ, this._scene.useRightHandedSystem && refractionTexture.isCube ? !refractionTexture.invertZ : refractionTexture.invertZ, true);\r\n\r\n        defines.setValue(\"SS_LINKREFRACTIONTOTRANSPARENCY\", this.linkRefractionWithTransparency, true);\r\n        defines.setValue(\"SS_GAMMAREFRACTION\", refractionTexture.gammaSpace, true);\r\n        defines.setValue(\"SS_RGBDREFRACTION\", refractionTexture.isRGBD, true);\r\n        defines.setValue(\"SS_USE_LOCAL_REFRACTIONMAP_CUBIC\", (<any>refractionTexture).boundingBoxSize ? true : false, true);\r\n        defines.setValue(\"SS_USE_THICKNESS_AS_DEPTH\", this.useThicknessAsDepth, true);\r\n    }\r\n\r\n    public override isReady() {\r\n        const texture = this._getTexture();\r\n\r\n        if (texture && !texture.isReadyOrNotBlocking()) {\r\n            return false;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    public override bind(effect: Effect, nodeMaterial: NodeMaterial, mesh?: Mesh) {\r\n        super.bind(effect, nodeMaterial, mesh);\r\n\r\n        const refractionTexture = this._getTexture();\r\n\r\n        if (!refractionTexture) {\r\n            return;\r\n        }\r\n\r\n        if (refractionTexture.isCube) {\r\n            effect.setTexture(this._cubeSamplerName, refractionTexture);\r\n        } else {\r\n            effect.setTexture(this._2DSamplerName, refractionTexture);\r\n        }\r\n\r\n        effect.setMatrix(this._refractionMatrixName, refractionTexture.getRefractionTextureMatrix());\r\n\r\n        let depth = 1.0;\r\n        if (!refractionTexture.isCube) {\r\n            if ((<any>refractionTexture).depth) {\r\n                depth = (<any>refractionTexture).depth;\r\n            }\r\n        }\r\n\r\n        const indexOfRefraction = this.volumeIndexOfRefraction.connectInputBlock?.value ?? this.indexOfRefractionConnectionPoint.connectInputBlock?.value ?? 1.5;\r\n\r\n        effect.setFloat4(this._vRefractionInfosName, refractionTexture.level, 1 / indexOfRefraction, depth, this.invertRefractionY ? -1 : 1);\r\n\r\n        effect.setFloat4(\r\n            this._vRefractionMicrosurfaceInfosName,\r\n            refractionTexture.getSize().width,\r\n            refractionTexture.lodGenerationScale,\r\n            refractionTexture.lodGenerationOffset,\r\n            1 / indexOfRefraction\r\n        );\r\n\r\n        const width = refractionTexture.getSize().width;\r\n\r\n        effect.setFloat2(this._vRefractionFilteringInfoName, width, Math.log2(width));\r\n\r\n        if ((<any>refractionTexture).boundingBoxSize) {\r\n            const cubeTexture = <CubeTexture>refractionTexture;\r\n            effect.setVector3(\"vRefractionPosition\", cubeTexture.boundingBoxPosition);\r\n            effect.setVector3(\"vRefractionSize\", cubeTexture.boundingBoxSize);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the main code of the block (fragment side)\r\n     * @param state current state of the node material building\r\n     * @returns the shader code\r\n     */\r\n    public getCode(state: NodeMaterialBuildState): string {\r\n        const code = \"\";\r\n\r\n        state.sharedData.blockingBlocks.push(this);\r\n        state.sharedData.textureBlocks.push(this);\r\n\r\n        // Samplers\r\n        this._cubeSamplerName = state._getFreeVariableName(this.name + \"CubeSampler\");\r\n        state.samplers.push(this._cubeSamplerName);\r\n\r\n        this._2DSamplerName = state._getFreeVariableName(this.name + \"2DSampler\");\r\n        state.samplers.push(this._2DSamplerName);\r\n\r\n        this._define3DName = state._getFreeDefineName(\"SS_REFRACTIONMAP_3D\");\r\n        const refractionTexture = this._getTexture();\r\n\r\n        if (refractionTexture) {\r\n            state._samplerDeclaration += `#ifdef ${this._define3DName}\\n`;\r\n            state._emitCubeSampler(this._cubeSamplerName, undefined, true);\r\n            state._samplerDeclaration += `#else\\n`;\r\n            state._emit2DSampler(this._2DSamplerName, undefined, true);\r\n            state._samplerDeclaration += `#endif\\n`;\r\n        }\r\n\r\n        // Fragment\r\n        state.sharedData.blocksWithDefines.push(this);\r\n        state.sharedData.bindableBlocks.push(this);\r\n\r\n        this._defineLODRefractionAlpha = state._getFreeDefineName(\"SS_LODINREFRACTIONALPHA\");\r\n        this._defineLinearSpecularRefraction = state._getFreeDefineName(\"SS_LINEARSPECULARREFRACTION\");\r\n        this._defineOppositeZ = state._getFreeDefineName(\"SS_REFRACTIONMAP_OPPOSITEZ\");\r\n\r\n        this._refractionMatrixName = state._getFreeVariableName(\"refractionMatrix\");\r\n\r\n        state._emitUniformFromString(this._refractionMatrixName, NodeMaterialBlockConnectionPointTypes.Matrix);\r\n\r\n        if (state.shaderLanguage !== ShaderLanguage.WGSL) {\r\n            state._emitFunction(\r\n                \"sampleRefraction\",\r\n                `\r\n                #ifdef ${this._define3DName}\r\n                    #define sampleRefraction(s, c) textureCube(s, c)\r\n                #else\r\n                    #define sampleRefraction(s, c) texture2D(s, c)\r\n                #endif\\n`,\r\n                `//${this.name}`\r\n            );\r\n\r\n            state._emitFunction(\r\n                \"sampleRefractionLod\",\r\n                `\r\n                #ifdef ${this._define3DName}\r\n                    #define sampleRefractionLod(s, c, l) textureCubeLodEXT(s, c, l)\r\n                #else\r\n                    #define sampleRefractionLod(s, c, l) texture2DLodEXT(s, c, l)\r\n                #endif\\n`,\r\n                `//${this.name}`\r\n            );\r\n        }\r\n\r\n        this._vRefractionMicrosurfaceInfosName = state._getFreeVariableName(\"vRefractionMicrosurfaceInfos\");\r\n\r\n        state._emitUniformFromString(this._vRefractionMicrosurfaceInfosName, NodeMaterialBlockConnectionPointTypes.Vector4);\r\n\r\n        this._vRefractionInfosName = state._getFreeVariableName(\"vRefractionInfos\");\r\n\r\n        state._emitUniformFromString(this._vRefractionInfosName, NodeMaterialBlockConnectionPointTypes.Vector4);\r\n\r\n        this._vRefractionFilteringInfoName = state._getFreeVariableName(\"vRefractionFilteringInfo\");\r\n\r\n        state._emitUniformFromString(this._vRefractionFilteringInfoName, NodeMaterialBlockConnectionPointTypes.Vector2);\r\n\r\n        state._emitUniformFromString(\"vRefractionPosition\", NodeMaterialBlockConnectionPointTypes.Vector3);\r\n        state._emitUniformFromString(\"vRefractionSize\", NodeMaterialBlockConnectionPointTypes.Vector3);\r\n\r\n        return code;\r\n    }\r\n\r\n    protected override _buildBlock(state: NodeMaterialBuildState) {\r\n        this._scene = state.sharedData.scene;\r\n\r\n        return this;\r\n    }\r\n\r\n    protected override _dumpPropertiesCode() {\r\n        let codeString = super._dumpPropertiesCode();\r\n\r\n        if (this.texture) {\r\n            if (this.texture.isCube) {\r\n                codeString = `${this._codeVariableName}.texture = new BABYLON.CubeTexture(\"${this.texture.name}\");\\n`;\r\n            } else {\r\n                codeString = `${this._codeVariableName}.texture = new BABYLON.Texture(\"${this.texture.name}\");\\n`;\r\n            }\r\n            codeString += `${this._codeVariableName}.texture.coordinatesMode = ${this.texture.coordinatesMode};\\n`;\r\n        }\r\n\r\n        codeString += `${this._codeVariableName}.linkRefractionWithTransparency = ${this.linkRefractionWithTransparency};\\n`;\r\n        codeString += `${this._codeVariableName}.invertRefractionY = ${this.invertRefractionY};\\n`;\r\n        codeString += `${this._codeVariableName}.useThicknessAsDepth = ${this.useThicknessAsDepth};\\n`;\r\n\r\n        return codeString;\r\n    }\r\n\r\n    public override serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        if (this.texture && !this.texture.isRenderTarget) {\r\n            serializationObject.texture = this.texture.serialize();\r\n        }\r\n\r\n        serializationObject.linkRefractionWithTransparency = this.linkRefractionWithTransparency;\r\n        serializationObject.invertRefractionY = this.invertRefractionY;\r\n        serializationObject.useThicknessAsDepth = this.useThicknessAsDepth;\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public override _deserialize(serializationObject: any, scene: Scene, rootUrl: string) {\r\n        super._deserialize(serializationObject, scene, rootUrl);\r\n\r\n        if (serializationObject.texture) {\r\n            rootUrl = serializationObject.texture.url.indexOf(\"data:\") === 0 ? \"\" : rootUrl;\r\n            if (serializationObject.texture.isCube) {\r\n                this.texture = CubeTexture.Parse(serializationObject.texture, scene, rootUrl);\r\n            } else {\r\n                this.texture = Texture.Parse(serializationObject.texture, scene, rootUrl);\r\n            }\r\n        }\r\n\r\n        this.linkRefractionWithTransparency = serializationObject.linkRefractionWithTransparency;\r\n        this.invertRefractionY = serializationObject.invertRefractionY;\r\n        this.useThicknessAsDepth = !!serializationObject.useThicknessAsDepth;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.RefractionBlock\", RefractionBlock);\r\n"], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAI1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAEhF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AACjD,OAAO,EAAE,uCAAuC,EAAE,MAAM,+CAA+C,CAAC;AAKxG,OAAO,EAAE,sBAAsB,EAA0B,MAAM,sCAAsC,CAAC;AAEtG,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,+BAA+B,CAAC;AAC5D,OAAO,EAAE,OAAO,EAAE,MAAM,2BAA2B,CAAC;AACpD,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;;;;;;;;;;;;AAM1E,MAAO,eAAgB,uLAAQ,oBAAiB;IA4DlD;;;OAGG,CACH,YAAmB,IAAY,CAAA;QAC3B,KAAK,CAAC,IAAI,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QA7BnD;;;WAGG,CAEI,IAAA,CAAA,8BAA8B,GAAY,KAAK,CAAC;QAEvD;;WAEG,CAEI,IAAA,CAAA,iBAAiB,GAAY,KAAK,CAAC;QAE1C;;WAEG,CAEI,IAAA,CAAA,mBAAmB,GAAY,KAAK,CAAC;QAcxC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,IAAI,CAAC,aAAa,CAAC,WAAW,6MAAE,wCAAqC,CAAC,KAAK,EAAE,KAAK,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QACvH,IAAI,CAAC,aAAa,CAAC,gBAAgB,6MAAE,wCAAqC,CAAC,KAAK,EAAE,IAAI,EAAE,yNAAwB,CAAC,QAAQ,CAAC,CAAC;QAC3H,IAAI,CAAC,aAAa,CAAC,yBAAyB,6MAAE,wCAAqC,CAAC,KAAK,EAAE,IAAI,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAEpI,IAAI,CAAC,cAAc,CACf,YAAY,6MACZ,wCAAqC,CAAC,MAAM,gMAC5C,2BAAwB,CAAC,QAAQ,EACjC,wMAAI,0CAAuC,CAAC,YAAY,EAAE,IAAI,EAAA,EAAA,+CAAA,KAA+C,eAAe,EAAE,iBAAiB,CAAC,CACnJ,CAAC;IACN,CAAC;IAED;;;OAGG,CACa,UAAU,CAAC,KAA6B,EAAA;QACpD,KAAK,CAAC,oBAAoB,CAAC,qBAAqB,CAAC,CAAC;QAClD,KAAK,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;IAClD,CAAC;IAED;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IAED;;OAEG,CACH,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,cAAc,GAAA;QACrB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,uBAAuB,GAAA;QAC9B,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,IAAI,GAAA;QACX,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAED;;OAEG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;IAChC,CAAC;IAES,WAAW,GAAA;QACjB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,OAAO,IAAI,CAAC,OAAO,CAAC;QACxB,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC;IAC1C,CAAC;IAEe,aAAa,CAAC,QAAsB,EAAE,0BAAgE,GAAG,CAAG,CAAD,GAAK,EAAA;QAC5H,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;YAC9B,MAAM,cAAc,GAAG,8LAAI,aAAU,CAAC,sBAAsB,gMAAE,2BAAwB,CAAC,QAAQ,6MAAE,wCAAqC,CAAC,KAAK,CAAC,CAAC;YAC9I,cAAc,CAAC,KAAK,GAAG,CAAC,CAAC;YACzB,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtC,IAAI,SAAS,GAAG,QAAQ,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,CAAG,CAAC,AAAF,CAAG,WAAW,mMAAK,2BAAwB,CAAC,IAAI,IAAI,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;YAExI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACb,SAAS,GAAG,8LAAI,aAAU,CAAC,MAAM,CAAC,CAAC;gBACnC,SAAS,CAAC,gBAAgB,+LAAC,2BAAwB,CAAC,IAAI,CAAC,CAAC;YAC9D,CAAC;YACD,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1C,CAAC;IACL,CAAC;IAEe,cAAc,CAAC,OAA4B,EAAA;QACvD,MAAM,iBAAiB,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAC7C,MAAM,UAAU,GAAG,iBAAiB,IAAI,iBAAiB,CAAC,gBAAgB,CAAC;QAE3E,OAAO,CAAC,QAAQ,CAAC,eAAe,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;QAEpD,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,OAAO;QACX,CAAC;QAED,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACrE,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,yBAAyB,EAAE,iBAAiB,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;QAC1F,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,+BAA+B,EAAE,iBAAiB,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;QAClG,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB,IAAI,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAErK,OAAO,CAAC,QAAQ,CAAC,iCAAiC,EAAE,IAAI,CAAC,8BAA8B,EAAE,IAAI,CAAC,CAAC;QAC/F,OAAO,CAAC,QAAQ,CAAC,oBAAoB,EAAE,iBAAiB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAC3E,OAAO,CAAC,QAAQ,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACtE,OAAO,CAAC,QAAQ,CAAC,kCAAkC,EAAQ,iBAAkB,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACpH,OAAO,CAAC,QAAQ,CAAC,2BAA2B,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;IAClF,CAAC;IAEe,OAAO,GAAA;QACnB,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAEnC,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,EAAE,CAAC;YAC7C,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEe,IAAI,CAAC,MAAc,EAAE,YAA0B,EAAE,IAAW,EAAA;QACxE,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;QAEvC,MAAM,iBAAiB,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAE7C,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACrB,OAAO;QACX,CAAC;QAED,IAAI,iBAAiB,CAAC,MAAM,EAAE,CAAC;YAC3B,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,EAAE,iBAAiB,CAAC,CAAC;QAChE,CAAC,MAAM,CAAC;YACJ,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;QAC9D,CAAC;QAED,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,qBAAqB,EAAE,iBAAiB,CAAC,0BAA0B,EAAE,CAAC,CAAC;QAE7F,IAAI,KAAK,GAAG,GAAG,CAAC;QAChB,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC;YAC5B,IAAU,iBAAkB,CAAC,KAAK,EAAE,CAAC;gBACjC,KAAK,GAAS,iBAAkB,CAAC,KAAK,CAAC;YAC3C,CAAC;QACL,CAAC;QAED,MAAM,iBAAiB,GAAG,IAAI,CAAC,uBAAuB,CAAC,iBAAiB,EAAE,KAAK,IAAI,IAAI,CAAC,gCAAgC,CAAC,iBAAiB,EAAE,KAAK,IAAI,GAAG,CAAC;QAEzJ,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,qBAAqB,EAAE,iBAAiB,CAAC,KAAK,EAAE,CAAC,GAAG,iBAAiB,EAAE,KAAK,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAErI,MAAM,CAAC,SAAS,CACZ,IAAI,CAAC,iCAAiC,EACtC,iBAAiB,CAAC,OAAO,EAAE,CAAC,KAAK,EACjC,iBAAiB,CAAC,kBAAkB,EACpC,iBAAiB,CAAC,mBAAmB,EACrC,CAAC,GAAG,iBAAiB,CACxB,CAAC;QAEF,MAAM,KAAK,GAAG,iBAAiB,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC;QAEhD,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,6BAA6B,EAAE,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;QAE9E,IAAU,iBAAkB,CAAC,eAAe,EAAE,CAAC;YAC3C,MAAM,WAAW,GAAgB,iBAAiB,CAAC;YACnD,MAAM,CAAC,UAAU,CAAC,qBAAqB,EAAE,WAAW,CAAC,mBAAmB,CAAC,CAAC;YAC1E,MAAM,CAAC,UAAU,CAAC,iBAAiB,EAAE,WAAW,CAAC,eAAe,CAAC,CAAC;QACtE,CAAC;IACL,CAAC;IAED;;;;OAIG,CACI,OAAO,CAAC,KAA6B,EAAA;QACxC,MAAM,IAAI,GAAG,EAAE,CAAC;QAEhB,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3C,KAAK,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE1C,WAAW;QACX,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,GAAG,aAAa,CAAC,CAAC;QAC9E,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAE3C,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC,CAAC;QAC1E,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAEzC,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,CAAC;QACrE,MAAM,iBAAiB,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAE7C,IAAI,iBAAiB,EAAE,CAAC;YACpB,KAAK,CAAC,mBAAmB,IAAI,CAAA,OAAA,EAAU,IAAI,CAAC,aAAa,CAAA,EAAA,CAAI,CAAC;YAC9D,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;YAC/D,KAAK,CAAC,mBAAmB,IAAI,CAAA,OAAA,CAAS,CAAC;YACvC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;YAC3D,KAAK,CAAC,mBAAmB,IAAI,CAAA,QAAA,CAAU,CAAC;QAC5C,CAAC;QAED,WAAW;QACX,KAAK,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9C,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE3C,IAAI,CAAC,yBAAyB,GAAG,KAAK,CAAC,kBAAkB,CAAC,yBAAyB,CAAC,CAAC;QACrF,IAAI,CAAC,+BAA+B,GAAG,KAAK,CAAC,kBAAkB,CAAC,6BAA6B,CAAC,CAAC;QAC/F,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,kBAAkB,CAAC,4BAA4B,CAAC,CAAC;QAE/E,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,CAAC;QAE5E,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,qBAAqB,6MAAE,wCAAqC,CAAC,MAAM,CAAC,CAAC;QAEvG,IAAI,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;YAC/C,KAAK,CAAC,aAAa,CACf,kBAAkB,EAClB,CAAA;yBACS,IAAI,CAAC,aAAa,CAAA;;;;yBAIlB,EACT,CAAA,EAAA,EAAK,IAAI,CAAC,IAAI,EAAE,CACnB,CAAC;YAEF,KAAK,CAAC,aAAa,CACf,qBAAqB,EACrB,CAAA;yBACS,IAAI,CAAC,aAAa,CAAA;;;;yBAIlB,EACT,CAAA,EAAA,EAAK,IAAI,CAAC,IAAI,EAAE,CACnB,CAAC;QACN,CAAC;QAED,IAAI,CAAC,iCAAiC,GAAG,KAAK,CAAC,oBAAoB,CAAC,8BAA8B,CAAC,CAAC;QAEpG,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,iCAAiC,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAC;QAEpH,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,CAAC;QAE5E,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,qBAAqB,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAC;QAExG,IAAI,CAAC,6BAA6B,GAAG,KAAK,CAAC,oBAAoB,CAAC,0BAA0B,CAAC,CAAC;QAE5F,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,6BAA6B,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAC;QAEhH,KAAK,CAAC,sBAAsB,CAAC,qBAAqB,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAC;QACnG,KAAK,CAAC,sBAAsB,CAAC,iBAAiB,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAC;QAE/F,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,WAAW,CAAC,KAA6B,EAAA;QACxD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC;QAErC,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,mBAAmB,GAAA;QAClC,IAAI,UAAU,GAAG,KAAK,CAAC,mBAAmB,EAAE,CAAC;QAE7C,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;gBACtB,UAAU,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAA,oCAAA,EAAuC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAA,KAAA,CAAO,CAAC;YAC1G,CAAC,MAAM,CAAC;gBACJ,UAAU,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAA,gCAAA,EAAmC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAA,KAAA,CAAO,CAAC;YACtG,CAAC;YACD,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,2BAAA,EAA8B,IAAI,CAAC,OAAO,CAAC,eAAe,CAAA,GAAA,CAAK,CAAC;QAC3G,CAAC;QAED,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,kCAAA,EAAqC,IAAI,CAAC,8BAA8B,CAAA,GAAA,CAAK,CAAC;QACrH,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,qBAAA,EAAwB,IAAI,CAAC,iBAAiB,CAAA,GAAA,CAAK,CAAC;QAC3F,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,uBAAA,EAA0B,IAAI,CAAC,mBAAmB,CAAA,GAAA,CAAK,CAAC;QAE/F,OAAO,UAAU,CAAC;IACtB,CAAC;IAEe,SAAS,GAAA;QACrB,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;YAC/C,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;QAC3D,CAAC;QAED,mBAAmB,CAAC,8BAA8B,GAAG,IAAI,CAAC,8BAA8B,CAAC;QACzF,mBAAmB,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAC/D,mBAAmB,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;QAEnE,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEe,YAAY,CAAC,mBAAwB,EAAE,KAAY,EAAE,OAAe,EAAA;QAChF,KAAK,CAAC,YAAY,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAExD,IAAI,mBAAmB,CAAC,OAAO,EAAE,CAAC;YAC9B,OAAO,GAAG,mBAAmB,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;YAChF,IAAI,mBAAmB,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;gBACrC,IAAI,CAAC,OAAO,+KAAG,cAAW,CAAC,KAAK,CAAC,mBAAmB,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;YAClF,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,OAAO,2KAAG,UAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;YAC9E,CAAC;QACL,CAAC;QAED,IAAI,CAAC,8BAA8B,GAAG,mBAAmB,CAAC,8BAA8B,CAAC;QACzF,IAAI,CAAC,iBAAiB,GAAG,mBAAmB,CAAC,iBAAiB,CAAC;QAC/D,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC,mBAAmB,CAAC,mBAAmB,CAAC;IACzE,CAAC;CACJ;wJAtVU,aAAA,EAAA;2KADN,yBAAA,AAAsB,EAAC,iCAAiC,EAAA,EAAA,kCAAA,KAAkC,UAAU,EAAE;QAAE,QAAQ,EAAE,IAAI;QAAE,SAAS,EAAE;YAAE,MAAM,EAAE,IAAI;QAAA,CAAE;IAAA,CAAE,CAAC;uEAChG;wJAMhD,aAAA,EAAA;2KADN,yBAAA,AAAsB,EAAC,qBAAqB,EAAA,EAAA,kCAAA,KAAkC,UAAU,EAAE;QAAE,QAAQ,EAAE,IAAI;QAAE,SAAS,EAAE;YAAE,MAAM,EAAE,IAAI;QAAA,CAAE;IAAA,CAAE,CAAC;0DACjG;wJAMnC,aAAA,EAAA;2KADN,yBAAA,AAAsB,EAAC,wBAAwB,EAAA,EAAA,kCAAA,KAAkC,UAAU,EAAE;QAAE,QAAQ,EAAE,IAAI;QAAE,SAAS,EAAE;YAAE,MAAM,EAAE,IAAI;QAAA,CAAE;IAAA,CAAE,CAAC;4DAClG;6JA4UhD,gBAAA,AAAa,EAAC,yBAAyB,EAAE,eAAe,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 1557, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/subSurfaceBlock.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Blocks/PBR/subSurfaceBlock.ts"], "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialConnectionPointDirection } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport { InputBlock } from \"../Input/inputBlock\";\r\nimport { NodeMaterialConnectionPointCustomObject } from \"../../nodeMaterialConnectionPointCustomObject\";\r\nimport type { NodeMaterialDefines } from \"../../nodeMaterial\";\r\nimport type { ReflectionBlock } from \"./reflectionBlock\";\r\nimport type { Nullable } from \"../../../../types\";\r\nimport { RefractionBlock } from \"./refractionBlock\";\r\nimport { ShaderLanguage } from \"core/Materials/shaderLanguage\";\r\nimport { editableInPropertyPage, PropertyTypeForEdition } from \"../../../../Decorators/nodeDecorator\";\r\nimport { PBRSubSurfaceConfiguration } from \"core/Materials/PBR/pbrSubSurfaceConfiguration\";\r\n\r\n/**\r\n * Block used to implement the sub surface module of the PBR material\r\n */\r\nexport class SubSurfaceBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Create a new SubSurfaceBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this._isUnique = true;\r\n\r\n        this.registerInput(\"thickness\", NodeMaterialBlockConnectionPointTypes.Float, false, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"tintColor\", NodeMaterialBlockConnectionPointTypes.Color3, true, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"translucencyIntensity\", NodeMaterialBlockConnectionPointTypes.Float, true, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"translucencyDiffusionDist\", NodeMaterialBlockConnectionPointTypes.Color3, true, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\r\n            \"refraction\",\r\n            NodeMaterialBlockConnectionPointTypes.Object,\r\n            true,\r\n            NodeMaterialBlockTargets.Fragment,\r\n            new NodeMaterialConnectionPointCustomObject(\"refraction\", this, NodeMaterialConnectionPointDirection.Input, RefractionBlock, \"RefractionBlock\")\r\n        );\r\n        this.registerInput(\"dispersion\", NodeMaterialBlockConnectionPointTypes.Float, true, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this.registerOutput(\r\n            \"subsurface\",\r\n            NodeMaterialBlockConnectionPointTypes.Object,\r\n            NodeMaterialBlockTargets.Fragment,\r\n            new NodeMaterialConnectionPointCustomObject(\"subsurface\", this, NodeMaterialConnectionPointDirection.Output, SubSurfaceBlock, \"SubSurfaceBlock\")\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Set it to true if your rendering in 8.0+ is different from that in 7 when you use sub-surface properties (transmission, refraction, etc.)\r\n     */\r\n    @editableInPropertyPage(\"Apply albedo after sub-surface\", PropertyTypeForEdition.Boolean, \"ADVANCED\")\r\n    public applyAlbedoAfterSubSurface: boolean = PBRSubSurfaceConfiguration.DEFAULT_APPLY_ALBEDO_AFTERSUBSURFACE;\r\n\r\n    /**\r\n     * Initialize the block and prepare the context for build\r\n     * @param state defines the state that will be used for the build\r\n     */\r\n    public override initialize(state: NodeMaterialBuildState) {\r\n        state._excludeVariableName(\"subSurfaceOut\");\r\n        state._excludeVariableName(\"vThicknessParam\");\r\n        state._excludeVariableName(\"vTintColor\");\r\n        state._excludeVariableName(\"vTranslucencyColor\");\r\n        state._excludeVariableName(\"vSubSurfaceIntensity\");\r\n        state._excludeVariableName(\"dispersion\");\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public override getClassName() {\r\n        return \"SubSurfaceBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the thickness component\r\n     */\r\n    public get thickness(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the tint color input component\r\n     */\r\n    public get tintColor(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the translucency intensity input component\r\n     */\r\n    public get translucencyIntensity(): NodeMaterialConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the translucency diffusion distance input component\r\n     */\r\n    public get translucencyDiffusionDist(): NodeMaterialConnectionPoint {\r\n        return this._inputs[3];\r\n    }\r\n\r\n    /**\r\n     * Gets the refraction object parameters\r\n     */\r\n    public get refraction(): NodeMaterialConnectionPoint {\r\n        return this._inputs[4];\r\n    }\r\n\r\n    /**\r\n     * Gets the dispersion input component\r\n     */\r\n    public get dispersion(): NodeMaterialConnectionPoint {\r\n        return this._inputs[5];\r\n    }\r\n\r\n    /**\r\n     * Gets the sub surface object output component\r\n     */\r\n    public get subsurface(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    public override autoConfigure() {\r\n        if (!this.thickness.isConnected) {\r\n            const thicknessInput = new InputBlock(\"SubSurface thickness\", NodeMaterialBlockTargets.Fragment, NodeMaterialBlockConnectionPointTypes.Float);\r\n            thicknessInput.value = 0;\r\n            thicknessInput.output.connectTo(this.thickness);\r\n        }\r\n    }\r\n\r\n    public override prepareDefines(defines: NodeMaterialDefines) {\r\n        const translucencyEnabled = this.translucencyDiffusionDist.isConnected || this.translucencyIntensity.isConnected;\r\n\r\n        defines.setValue(\"SUBSURFACE\", translucencyEnabled || this.refraction.isConnected, true);\r\n        defines.setValue(\"SS_TRANSLUCENCY\", translucencyEnabled, true);\r\n        defines.setValue(\"SS_THICKNESSANDMASK_TEXTURE\", false, true);\r\n        defines.setValue(\"SS_REFRACTIONINTENSITY_TEXTURE\", false, true);\r\n        defines.setValue(\"SS_TRANSLUCENCYINTENSITY_TEXTURE\", false, true);\r\n        defines.setValue(\"SS_USE_GLTF_TEXTURES\", false, true);\r\n        defines.setValue(\"SS_DISPERSION\", this.dispersion.isConnected, true);\r\n        defines.setValue(\"SS_APPLY_ALBEDO_AFTER_SUBSURFACE\", this.applyAlbedoAfterSubSurface, true);\r\n    }\r\n\r\n    /**\r\n     * Gets the main code of the block (fragment side)\r\n     * @param state current state of the node material building\r\n     * @param ssBlock instance of a SubSurfaceBlock or null if the code must be generated without an active sub surface module\r\n     * @param reflectionBlock instance of a ReflectionBlock null if the code must be generated without an active reflection module\r\n     * @param worldPosVarName name of the variable holding the world position\r\n     * @returns the shader code\r\n     */\r\n    public static GetCode(state: NodeMaterialBuildState, ssBlock: Nullable<SubSurfaceBlock>, reflectionBlock: Nullable<ReflectionBlock>, worldPosVarName: string): string {\r\n        let code = \"\";\r\n\r\n        const thickness = ssBlock?.thickness.isConnected ? ssBlock.thickness.associatedVariableName : \"0.\";\r\n        const tintColor = ssBlock?.tintColor.isConnected ? ssBlock.tintColor.associatedVariableName : \"vec3(1.)\";\r\n        const translucencyIntensity = ssBlock?.translucencyIntensity.isConnected ? ssBlock?.translucencyIntensity.associatedVariableName : \"1.\";\r\n        const translucencyDiffusionDistance = ssBlock?.translucencyDiffusionDist.isConnected ? ssBlock?.translucencyDiffusionDist.associatedVariableName : \"vec3(1.)\";\r\n\r\n        const refractionBlock: Nullable<RefractionBlock> = (ssBlock?.refraction.isConnected ? ssBlock?.refraction.connectedPoint?.ownerBlock : null) as Nullable<RefractionBlock>;\r\n\r\n        const refractionTintAtDistance = refractionBlock?.tintAtDistance.isConnected ? refractionBlock.tintAtDistance.associatedVariableName : \"1.\";\r\n        const refractionIntensity = refractionBlock?.intensity.isConnected ? refractionBlock.intensity.associatedVariableName : \"1.\";\r\n        const refractionView = refractionBlock?.view.isConnected ? refractionBlock.view.associatedVariableName : \"\";\r\n\r\n        const dispersion = ssBlock?.dispersion.isConnected ? ssBlock?.dispersion.associatedVariableName : \"0.0\";\r\n        const isWebGPU = state.shaderLanguage === ShaderLanguage.WGSL;\r\n\r\n        code += refractionBlock?.getCode(state) ?? \"\";\r\n\r\n        code += `${isWebGPU ? \"var subSurfaceOut: subSurfaceOutParams\" : \"subSurfaceOutParams subSurfaceOut\"};\r\n\r\n        #ifdef SUBSURFACE\r\n            ${state._declareLocalVar(\"vThicknessParam\", NodeMaterialBlockConnectionPointTypes.Vector2)} = vec2${state.fSuffix}(0., ${thickness});\r\n            ${state._declareLocalVar(\"vTintColor\", NodeMaterialBlockConnectionPointTypes.Vector4)} = vec4${state.fSuffix}(${tintColor}, ${refractionTintAtDistance});\r\n            ${state._declareLocalVar(\"vSubSurfaceIntensity\", NodeMaterialBlockConnectionPointTypes.Vector3)} = vec3(${refractionIntensity}, ${translucencyIntensity}, 0.);\r\n            ${state._declareLocalVar(\"dispersion\", NodeMaterialBlockConnectionPointTypes.Float)} = ${dispersion};\r\n            subSurfaceOut = subSurfaceBlock(\r\n                vSubSurfaceIntensity\r\n                , vThicknessParam\r\n                , vTintColor\r\n                , normalW\r\n            #ifdef LEGACY_SPECULAR_ENERGY_CONSERVATION\r\n        `;\r\n\r\n        code += isWebGPU\r\n            ? `, vec3f(max(colorSpecularEnvironmentReflectance.r, max(colorSpecularEnvironmentReflectance.g, colorSpecularEnvironmentReflectance.b)))/n`\r\n            : `, vec3(max(colorSpecularEnvironmentReflectance.r, max(colorSpecularEnvironmentReflectance.g, colorSpecularEnvironmentReflectance.b)))/n`;\r\n\r\n        code += `#else\r\n                , baseSpecularEnvironmentReflectance\r\n            #endif\r\n            #ifdef SS_THICKNESSANDMASK_TEXTURE\r\n                , vec4${state.fSuffix}(0.)\r\n            #endif\r\n            #ifdef REFLECTION\r\n                #ifdef SS_TRANSLUCENCY\r\n                    , ${(isWebGPU ? \"uniforms.\" : \"\") + reflectionBlock?._reflectionMatrixName}\r\n                    #ifdef USESPHERICALFROMREFLECTIONMAP\r\n                        #if !defined(NORMAL) || !defined(USESPHERICALINVERTEX)\r\n                            , reflectionOut.irradianceVector\r\n                        #endif\r\n                        #if defined(REALTIME_FILTERING)\r\n                            , ${reflectionBlock?._cubeSamplerName}\r\n                            ${isWebGPU ? `, ${reflectionBlock?._cubeSamplerName}Sampler` : \"\"}\r\n                            , ${reflectionBlock?._vReflectionFilteringInfoName}\r\n                        #endif\r\n                        #endif\r\n                    #ifdef USEIRRADIANCEMAP\r\n                        , irradianceSampler\r\n                        ${isWebGPU ? `, irradianceSamplerSampler` : \"\"}\r\n                    #endif\r\n                #endif\r\n            #endif\r\n            #if defined(SS_REFRACTION) || defined(SS_TRANSLUCENCY)\r\n                , surfaceAlbedo\r\n            #endif\r\n            #ifdef SS_REFRACTION\r\n                , ${worldPosVarName}.xyz\r\n                , viewDirectionW\r\n                , ${refractionView}\r\n                , ${(isWebGPU ? \"uniforms.\" : \"\") + (refractionBlock?._vRefractionInfosName ?? \"\")}\r\n                , ${(isWebGPU ? \"uniforms.\" : \"\") + (refractionBlock?._refractionMatrixName ?? \"\")}\r\n                , ${(isWebGPU ? \"uniforms.\" : \"\") + (refractionBlock?._vRefractionMicrosurfaceInfosName ?? \"\")}\r\n                , ${isWebGPU ? \"uniforms.\" : \"\"}vLightingIntensity\r\n                #ifdef SS_LINKREFRACTIONTOTRANSPARENCY\r\n                    , alpha\r\n                #endif\r\n                #ifdef ${refractionBlock?._defineLODRefractionAlpha ?? \"IGNORE\"}\r\n                    , NdotVUnclamped\r\n                #endif\r\n                #ifdef ${refractionBlock?._defineLinearSpecularRefraction ?? \"IGNORE\"}\r\n                    , roughness\r\n                #endif\r\n                , alphaG\r\n                #ifdef ${refractionBlock?._define3DName ?? \"IGNORE\"}\r\n                    , ${refractionBlock?._cubeSamplerName ?? \"\"}\r\n                    ${isWebGPU ? `, ${refractionBlock?._cubeSamplerName}Sampler` : \"\"}\r\n                #else\r\n                    , ${refractionBlock?._2DSamplerName ?? \"\"}\r\n                    ${isWebGPU ? `, ${refractionBlock?._2DSamplerName}Sampler` : \"\"}\r\n                #endif\r\n                #ifndef LODBASEDMICROSFURACE\r\n                    #ifdef ${refractionBlock?._define3DName ?? \"IGNORE\"}\r\n                        , ${refractionBlock?._cubeSamplerName ?? \"\"}                        \r\n                        ${isWebGPU ? `, ${refractionBlock?._cubeSamplerName}Sampler` : \"\"}\r\n                        , ${refractionBlock?._cubeSamplerName ?? \"\"}                        \r\n                        ${isWebGPU ? `, ${refractionBlock?._cubeSamplerName}Sampler` : \"\"}\r\n                    #else\r\n                        , ${refractionBlock?._2DSamplerName ?? \"\"}\r\n                        ${isWebGPU ? `, ${refractionBlock?._2DSamplerName}Sampler` : \"\"}\r\n                        , ${refractionBlock?._2DSamplerName ?? \"\"}\r\n                        ${isWebGPU ? `, ${refractionBlock?._2DSamplerName}Sampler` : \"\"}\r\n                    #endif\r\n                #endif\r\n                #ifdef ANISOTROPIC\r\n                    , anisotropicOut\r\n                #endif\r\n                #ifdef REALTIME_FILTERING\r\n                    , ${refractionBlock?._vRefractionFilteringInfoName ?? \"\"}\r\n                #endif\r\n                #ifdef SS_USE_LOCAL_REFRACTIONMAP_CUBIC\r\n                    , vRefractionPosition\r\n                    , vRefractionSize\r\n                #endif\r\n                #ifdef SS_DISPERSION\r\n                    , dispersion\r\n                #endif\r\n            #endif\r\n            #ifdef SS_TRANSLUCENCY\r\n                , ${translucencyDiffusionDistance}\r\n                , vTintColor\r\n                #ifdef SS_TRANSLUCENCYCOLOR_TEXTURE\r\n                    , vec4${state.fSuffix}(0.)\r\n                #endif\r\n            #endif                \r\n            );\r\n\r\n            #ifdef SS_REFRACTION\r\n                surfaceAlbedo = subSurfaceOut.surfaceAlbedo;\r\n                #ifdef SS_LINKREFRACTIONTOTRANSPARENCY\r\n                    alpha = subSurfaceOut.alpha;\r\n                #endif\r\n            #endif\r\n        #else\r\n            subSurfaceOut.specularEnvironmentReflectance = colorSpecularEnvironmentReflectance;\r\n        #endif\\n`;\r\n\r\n        return code;\r\n    }\r\n\r\n    protected override _buildBlock(state: NodeMaterialBuildState) {\r\n        if (state.target === NodeMaterialBlockTargets.Fragment) {\r\n            state.sharedData.blocksWithDefines.push(this);\r\n        }\r\n\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.SubSurfaceBlock\", SubSurfaceBlock);\r\n"], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAI1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAChF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AACjD,OAAO,EAAE,uCAAuC,EAAE,MAAM,+CAA+C,CAAC;AAIxG,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAEpD,OAAO,EAAE,sBAAsB,EAA0B,MAAM,sCAAsC,CAAC;AACtG,OAAO,EAAE,0BAA0B,EAAE,mDAAsD;;;;;;;;;;;AAKrF,MAAO,eAAgB,uLAAQ,oBAAiB;IAClD;;;OAGG,CACH,YAAmB,IAAY,CAAA;QAC3B,KAAK,CAAC,IAAI,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAyBnD;;WAEG,CAEI,IAAA,CAAA,0BAA0B,yLAAY,6BAA0B,CAAC,oCAAoC,CAAC;QA3BzG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,IAAI,CAAC,aAAa,CAAC,WAAW,6MAAE,wCAAqC,CAAC,KAAK,EAAE,KAAK,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QACvH,IAAI,CAAC,aAAa,CAAC,WAAW,6MAAE,wCAAqC,CAAC,MAAM,EAAE,IAAI,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QACvH,IAAI,CAAC,aAAa,CAAC,uBAAuB,6MAAE,wCAAqC,CAAC,KAAK,EAAE,IAAI,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAClI,IAAI,CAAC,aAAa,CAAC,2BAA2B,6MAAE,wCAAqC,CAAC,MAAM,EAAE,IAAI,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QACvI,IAAI,CAAC,aAAa,CACd,YAAY,6MACZ,wCAAqC,CAAC,MAAM,EAC5C,IAAI,gMACJ,2BAAwB,CAAC,QAAQ,EACjC,wMAAI,0CAAuC,CAAC,YAAY,EAAE,IAAI,EAAA,EAAA,8CAAA,kMAA8C,kBAAe,EAAE,iBAAiB,CAAC,CAClJ,CAAC;QACF,IAAI,CAAC,aAAa,CAAC,YAAY,6MAAE,wCAAqC,CAAC,KAAK,EAAE,IAAI,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAEvH,IAAI,CAAC,cAAc,CACf,YAAY,6MACZ,wCAAqC,CAAC,MAAM,gMAC5C,2BAAwB,CAAC,QAAQ,EACjC,wMAAI,0CAAuC,CAAC,YAAY,EAAE,IAAI,EAAA,EAAA,+CAAA,KAA+C,eAAe,EAAE,iBAAiB,CAAC,CACnJ,CAAC;IACN,CAAC;IAQD;;;OAGG,CACa,UAAU,CAAC,KAA6B,EAAA;QACpD,KAAK,CAAC,oBAAoB,CAAC,eAAe,CAAC,CAAC;QAC5C,KAAK,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;QAC9C,KAAK,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;QACzC,KAAK,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;QACjD,KAAK,CAAC,oBAAoB,CAAC,sBAAsB,CAAC,CAAC;QACnD,KAAK,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;IAC7C,CAAC;IAED;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IAED;;OAEG,CACH,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,qBAAqB,GAAA;QAC5B,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,yBAAyB,GAAA;QAChC,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAEe,aAAa,GAAA;QACzB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;YAC9B,MAAM,cAAc,GAAG,8LAAI,aAAU,CAAC,sBAAsB,EAAE,yNAAwB,CAAC,QAAQ,6MAAE,wCAAqC,CAAC,KAAK,CAAC,CAAC;YAC9I,cAAc,CAAC,KAAK,GAAG,CAAC,CAAC;YACzB,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACpD,CAAC;IACL,CAAC;IAEe,cAAc,CAAC,OAA4B,EAAA;QACvD,MAAM,mBAAmB,GAAG,IAAI,CAAC,yBAAyB,CAAC,WAAW,IAAI,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC;QAEjH,OAAO,CAAC,QAAQ,CAAC,YAAY,EAAE,mBAAmB,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QACzF,OAAO,CAAC,QAAQ,CAAC,iBAAiB,EAAE,mBAAmB,EAAE,IAAI,CAAC,CAAC;QAC/D,OAAO,CAAC,QAAQ,CAAC,6BAA6B,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAC7D,OAAO,CAAC,QAAQ,CAAC,gCAAgC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAChE,OAAO,CAAC,QAAQ,CAAC,kCAAkC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAClE,OAAO,CAAC,QAAQ,CAAC,sBAAsB,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QACtD,OAAO,CAAC,QAAQ,CAAC,eAAe,EAAE,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QACrE,OAAO,CAAC,QAAQ,CAAC,kCAAkC,EAAE,IAAI,CAAC,0BAA0B,EAAE,IAAI,CAAC,CAAC;IAChG,CAAC;IAED;;;;;;;OAOG,CACI,MAAM,CAAC,OAAO,CAAC,KAA6B,EAAE,OAAkC,EAAE,eAA0C,EAAE,eAAuB,EAAA;QACxJ,IAAI,IAAI,GAAG,EAAE,CAAC;QAEd,MAAM,SAAS,GAAG,OAAO,EAAE,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC;QACnG,MAAM,SAAS,GAAG,OAAO,EAAE,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAAC,UAAU,CAAC;QACzG,MAAM,qBAAqB,GAAG,OAAO,EAAE,qBAAqB,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,EAAE,qBAAqB,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC;QACxI,MAAM,6BAA6B,GAAG,OAAO,EAAE,yBAAyB,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,EAAE,yBAAyB,CAAC,sBAAsB,CAAC,CAAC,CAAC,UAAU,CAAC;QAE9J,MAAM,eAAe,GAA8B,AAAC,OAAO,EAAE,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,EAAE,UAAU,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC,CAAC,IAAI,CAA8B,CAAC;QAE1K,MAAM,wBAAwB,GAAG,eAAe,EAAE,cAAc,CAAC,WAAW,CAAC,CAAC,CAAC,eAAe,CAAC,cAAc,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC;QAC5I,MAAM,mBAAmB,GAAG,eAAe,EAAE,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,eAAe,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC;QAC7H,MAAM,cAAc,GAAG,eAAe,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,EAAE,CAAC;QAE5G,MAAM,UAAU,GAAG,OAAO,EAAE,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,EAAE,UAAU,CAAC,sBAAsB,CAAC,CAAC,CAAC,KAAK,CAAC;QACxG,MAAM,QAAQ,GAAG,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,CAAC;QAE9D,IAAI,IAAI,eAAe,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;QAE9C,IAAI,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,wCAAwC,CAAC,CAAC,CAAC,mCAAmC,CAAA;;;cAG9F,KAAK,CAAC,gBAAgB,CAAC,iBAAiB,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA,OAAA,EAAU,KAAK,CAAC,OAAO,CAAA,KAAA,EAAQ,SAAS,CAAA;cAChI,KAAK,CAAC,gBAAgB,CAAC,YAAY,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA,OAAA,EAAU,KAAK,CAAC,OAAO,CAAA,CAAA,EAAI,SAAS,CAAA,EAAA,EAAK,wBAAwB,CAAA;cACpJ,KAAK,CAAC,gBAAgB,CAAC,sBAAsB,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA,QAAA,EAAW,mBAAmB,CAAA,EAAA,EAAK,qBAAqB,CAAA;cACrJ,KAAK,CAAC,gBAAgB,CAAC,YAAY,EAAE,mPAAqC,CAAC,KAAK,CAAC,CAAA,GAAA,EAAM,UAAU,CAAA;;;;;;;SAOtG,CAAC;QAEF,IAAI,IAAI,QAAQ,GACV,CAAA,wIAAA,CAA0I,GAC1I,CAAA,uIAAA,CAAyI,CAAC;QAEhJ,IAAI,IAAI,CAAA;;;;wBAIQ,KAAK,CAAC,OAAO,CAAA;;;;wBAIb,CAAC,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,eAAe,EAAE,qBAAqB,CAAA;;;;;;gCAM9D,eAAe,EAAE,gBAAgB,CAAA;8BACnC,QAAQ,CAAC,CAAC,CAAC,CAAA,EAAA,EAAK,eAAe,EAAE,gBAAgB,CAAA,OAAA,CAAS,CAAC,CAAC,CAAC,EAAE,CAAA;gCAC7D,eAAe,EAAE,6BAA6B,CAAA;;;;;0BAKpD,QAAQ,CAAC,CAAC,CAAC,CAAA,0BAAA,CAA4B,CAAC,CAAC,CAAC,EAAE,CAAA;;;;;;;;oBAQlD,eAAe,CAAA;;oBAEf,cAAc,CAAA;oBACd,CAAC,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,eAAe,EAAE,qBAAqB,IAAI,EAAE,CAAC,CAAA;oBAC9E,CAAC,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,eAAe,EAAE,qBAAqB,IAAI,EAAE,CAAC,CAAA;oBAC9E,CAAC,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,eAAe,EAAE,iCAAiC,IAAI,EAAE,CAAC,CAAA;oBAC1F,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAA;;;;yBAItB,eAAe,EAAE,yBAAyB,IAAI,QAAQ,CAAA;;;yBAGtD,eAAe,EAAE,+BAA+B,IAAI,QAAQ,CAAA;;;;yBAI5D,eAAe,EAAE,aAAa,IAAI,QAAQ,CAAA;wBAC3C,eAAe,EAAE,gBAAgB,IAAI,EAAE,CAAA;sBACzC,QAAQ,CAAC,CAAC,CAAC,CAAA,EAAA,EAAK,eAAe,EAAE,gBAAgB,CAAA,OAAA,CAAS,CAAC,CAAC,CAAC,EAAE,CAAA;;wBAE7D,eAAe,EAAE,cAAc,IAAI,EAAE,CAAA;sBACvC,QAAQ,CAAC,CAAC,CAAC,CAAA,EAAA,EAAK,eAAe,EAAE,cAAc,CAAA,OAAA,CAAS,CAAC,CAAC,CAAC,EAAE,CAAA;;;6BAGtD,eAAe,EAAE,aAAa,IAAI,QAAQ,CAAA;4BAC3C,eAAe,EAAE,gBAAgB,IAAI,EAAE,CAAA;0BACzC,QAAQ,CAAC,CAAC,CAAC,CAAA,EAAA,EAAK,eAAe,EAAE,gBAAgB,CAAA,OAAA,CAAS,CAAC,CAAC,CAAC,EAAE,CAAA;4BAC7D,eAAe,EAAE,gBAAgB,IAAI,EAAE,CAAA;0BACzC,QAAQ,CAAC,CAAC,CAAC,CAAA,EAAA,EAAK,eAAe,EAAE,gBAAgB,CAAA,OAAA,CAAS,CAAC,CAAC,CAAC,EAAE,CAAA;;4BAE7D,eAAe,EAAE,cAAc,IAAI,EAAE,CAAA;0BACvC,QAAQ,CAAC,CAAC,CAAC,CAAA,EAAA,EAAK,eAAe,EAAE,cAAc,CAAA,OAAA,CAAS,CAAC,CAAC,CAAC,EAAE,CAAA;4BAC3D,eAAe,EAAE,cAAc,IAAI,EAAE,CAAA;0BACvC,QAAQ,CAAC,CAAC,CAAC,CAAA,EAAA,EAAK,eAAe,EAAE,cAAc,CAAA,OAAA,CAAS,CAAC,CAAC,CAAC,EAAE,CAAA;;;;;;;wBAO/D,eAAe,EAAE,6BAA6B,IAAI,EAAE,CAAA;;;;;;;;;;;oBAWxD,6BAA6B,CAAA;;;4BAGrB,KAAK,CAAC,OAAO,CAAA;;;;;;;;;;;;;iBAaxB,CAAC;QAEV,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,WAAW,CAAC,KAA6B,EAAA;QACxD,IAAI,KAAK,CAAC,MAAM,mMAAK,2BAAwB,CAAC,QAAQ,EAAE,CAAC;YACrD,KAAK,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClD,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;wJAxPU,aAAA,EAAA;2KADN,yBAAA,AAAsB,EAAC,gCAAgC,EAAA,EAAA,kCAAA,KAAkC,UAAU,CAAC;mEACQ;6JA0PjH,gBAAA,AAAa,EAAC,yBAAyB,EAAE,eAAe,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 1818, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/pbrMetallicRoughnessBlock.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Blocks/PBR/pbrMetallicRoughnessBlock.ts"], "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialConnectionPointDirection } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterial, NodeMaterialDefines } from \"../../nodeMaterial\";\r\nimport { NodeMaterialSystemValues } from \"../../Enums/nodeMaterialSystemValues\";\r\nimport { InputBlock } from \"../Input/inputBlock\";\r\nimport type { Light } from \"../../../../Lights/light\";\r\nimport type { Nullable } from \"../../../../types\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport type { AbstractMesh } from \"../../../../Meshes/abstractMesh\";\r\nimport type { Effect } from \"../../../effect\";\r\nimport type { Mesh } from \"../../../../Meshes/mesh\";\r\nimport { PBRBaseMaterial } from \"../../../PBR/pbrBaseMaterial\";\r\nimport type { Scene } from \"../../../../scene\";\r\nimport { editableInPropertyPage, PropertyTypeForEdition } from \"../../../../Decorators/nodeDecorator\";\r\nimport { NodeMaterialConnectionPointCustomObject } from \"../../nodeMaterialConnectionPointCustomObject\";\r\nimport { SheenBlock } from \"./sheenBlock\";\r\nimport type { BaseTexture } from \"../../../Textures/baseTexture\";\r\nimport { GetEnvironmentBRDFTexture } from \"../../../../Misc/brdfTextureTools\";\r\nimport { MaterialFlags } from \"../../../materialFlags\";\r\nimport { AnisotropyBlock } from \"./anisotropyBlock\";\r\nimport { ReflectionBlock } from \"./reflectionBlock\";\r\nimport { ClearCoatBlock } from \"./clearCoatBlock\";\r\nimport { IridescenceBlock } from \"./iridescenceBlock\";\r\nimport { SubSurfaceBlock } from \"./subSurfaceBlock\";\r\nimport type { RefractionBlock } from \"./refractionBlock\";\r\nimport type { PerturbNormalBlock } from \"../Fragment/perturbNormalBlock\";\r\nimport { Constants } from \"../../../../Engines/constants\";\r\nimport { Color3 } from \"../../../../Maths/math.color\";\r\nimport { Logger } from \"core/Misc/logger\";\r\nimport {\r\n    BindLight,\r\n    BindLights,\r\n    PrepareDefinesForLight,\r\n    PrepareDefinesForLights,\r\n    PrepareDefinesForMultiview,\r\n    PrepareUniformsAndSamplersForLight,\r\n} from \"../../../materialHelper.functions\";\r\nimport { ShaderLanguage } from \"core/Materials/shaderLanguage\";\r\n\r\nconst MapOutputToVariable: { [name: string]: [string, string] } = {\r\n    ambientClr: [\"finalAmbient\", \"\"],\r\n    diffuseDir: [\"finalDiffuse\", \"\"],\r\n    specularDir: [\"finalSpecularScaled\", \"!defined(UNLIT) && defined(SPECULARTERM)\"],\r\n    clearcoatDir: [\"finalClearCoatScaled\", \"!defined(UNLIT) && defined(CLEARCOAT)\"],\r\n    sheenDir: [\"finalSheenScaled\", \"!defined(UNLIT) && defined(SHEEN)\"],\r\n    diffuseInd: [\"finalIrradiance\", \"!defined(UNLIT) && defined(REFLECTION)\"],\r\n    specularInd: [\"finalRadianceScaled\", \"!defined(UNLIT) && defined(REFLECTION)\"],\r\n    clearcoatInd: [\"clearcoatOut.finalClearCoatRadianceScaled\", \"!defined(UNLIT) && defined(REFLECTION) && defined(CLEARCOAT)\"],\r\n    sheenInd: [\"sheenOut.finalSheenRadianceScaled\", \"!defined(UNLIT) && defined(REFLECTION) && defined(SHEEN) && defined(ENVIRONMENTBRDF)\"],\r\n    refraction: [\"subSurfaceOut.finalRefraction\", \"!defined(UNLIT) && defined(SS_REFRACTION)\"],\r\n    lighting: [\"finalColor.rgb\", \"\"],\r\n    shadow: [\"aggShadow\", \"\"],\r\n    alpha: [\"alpha\", \"\"],\r\n};\r\n\r\n/**\r\n * Block used to implement the PBR metallic/roughness model\r\n * @see https://playground.babylonjs.com/#D8AK3Z#80\r\n */\r\nexport class PBRMetallicRoughnessBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Gets or sets the light associated with this block\r\n     */\r\n    public light: Nullable<Light>;\r\n\r\n    private static _OnGenerateOnlyFragmentCodeChanged(block: NodeMaterialBlock, _propertyName: string): boolean {\r\n        const that = block as PBRMetallicRoughnessBlock;\r\n\r\n        if (that.worldPosition.isConnected || that.worldNormal.isConnected) {\r\n            that.generateOnlyFragmentCode = !that.generateOnlyFragmentCode;\r\n            Logger.Error(\"The worldPosition and worldNormal inputs must not be connected to be able to switch!\");\r\n            return false;\r\n        }\r\n\r\n        that._setTarget();\r\n\r\n        return true;\r\n    }\r\n\r\n    private _setTarget(): void {\r\n        this._setInitialTarget(this.generateOnlyFragmentCode ? NodeMaterialBlockTargets.Fragment : NodeMaterialBlockTargets.VertexAndFragment);\r\n        this.getInputByName(\"worldPosition\")!.target = this.generateOnlyFragmentCode ? NodeMaterialBlockTargets.Fragment : NodeMaterialBlockTargets.Vertex;\r\n        this.getInputByName(\"worldNormal\")!.target = this.generateOnlyFragmentCode ? NodeMaterialBlockTargets.Fragment : NodeMaterialBlockTargets.Vertex;\r\n    }\r\n\r\n    private _lightId: number;\r\n    private _scene: Scene;\r\n    private _environmentBRDFTexture: Nullable<BaseTexture> = null;\r\n    private _environmentBrdfSamplerName: string;\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private _vNormalWName: string;\r\n    private _invertNormalName: string;\r\n    private _metallicReflectanceColor: Color3 = Color3.White();\r\n    private _metallicF0Factor = 1;\r\n    private _vMetallicReflectanceFactorsName: string;\r\n    private _baseDiffuseRoughnessName: string;\r\n\r\n    /**\r\n     * Create a new ReflectionBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.VertexAndFragment);\r\n\r\n        this._isUnique = true;\r\n\r\n        this.registerInput(\"worldPosition\", NodeMaterialBlockConnectionPointTypes.Vector4, false, NodeMaterialBlockTargets.Vertex);\r\n        this.registerInput(\"worldNormal\", NodeMaterialBlockConnectionPointTypes.Vector4, false, NodeMaterialBlockTargets.Vertex);\r\n        this.registerInput(\"view\", NodeMaterialBlockConnectionPointTypes.Matrix, false);\r\n        this.registerInput(\"cameraPosition\", NodeMaterialBlockConnectionPointTypes.Vector3, false, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"perturbedNormal\", NodeMaterialBlockConnectionPointTypes.Vector4, true, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"baseColor\", NodeMaterialBlockConnectionPointTypes.Color3, true, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"metallic\", NodeMaterialBlockConnectionPointTypes.Float, false, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"roughness\", NodeMaterialBlockConnectionPointTypes.Float, false, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"ambientOcc\", NodeMaterialBlockConnectionPointTypes.Float, true, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"opacity\", NodeMaterialBlockConnectionPointTypes.Float, true, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"indexOfRefraction\", NodeMaterialBlockConnectionPointTypes.Float, true, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"ambientColor\", NodeMaterialBlockConnectionPointTypes.Color3, true, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\r\n            \"reflection\",\r\n            NodeMaterialBlockConnectionPointTypes.Object,\r\n            true,\r\n            NodeMaterialBlockTargets.Fragment,\r\n            new NodeMaterialConnectionPointCustomObject(\"reflection\", this, NodeMaterialConnectionPointDirection.Input, ReflectionBlock, \"ReflectionBlock\")\r\n        );\r\n        this.registerInput(\r\n            \"clearcoat\",\r\n            NodeMaterialBlockConnectionPointTypes.Object,\r\n            true,\r\n            NodeMaterialBlockTargets.Fragment,\r\n            new NodeMaterialConnectionPointCustomObject(\"clearcoat\", this, NodeMaterialConnectionPointDirection.Input, ClearCoatBlock, \"ClearCoatBlock\")\r\n        );\r\n        this.registerInput(\r\n            \"sheen\",\r\n            NodeMaterialBlockConnectionPointTypes.Object,\r\n            true,\r\n            NodeMaterialBlockTargets.Fragment,\r\n            new NodeMaterialConnectionPointCustomObject(\"sheen\", this, NodeMaterialConnectionPointDirection.Input, SheenBlock, \"SheenBlock\")\r\n        );\r\n        this.registerInput(\r\n            \"subsurface\",\r\n            NodeMaterialBlockConnectionPointTypes.Object,\r\n            true,\r\n            NodeMaterialBlockTargets.Fragment,\r\n            new NodeMaterialConnectionPointCustomObject(\"subsurface\", this, NodeMaterialConnectionPointDirection.Input, SubSurfaceBlock, \"SubSurfaceBlock\")\r\n        );\r\n        this.registerInput(\r\n            \"anisotropy\",\r\n            NodeMaterialBlockConnectionPointTypes.Object,\r\n            true,\r\n            NodeMaterialBlockTargets.Fragment,\r\n            new NodeMaterialConnectionPointCustomObject(\"anisotropy\", this, NodeMaterialConnectionPointDirection.Input, AnisotropyBlock, \"AnisotropyBlock\")\r\n        );\r\n        this.registerInput(\r\n            \"iridescence\",\r\n            NodeMaterialBlockConnectionPointTypes.Object,\r\n            true,\r\n            NodeMaterialBlockTargets.Fragment,\r\n            new NodeMaterialConnectionPointCustomObject(\"iridescence\", this, NodeMaterialConnectionPointDirection.Input, IridescenceBlock, \"IridescenceBlock\")\r\n        );\r\n\r\n        this.registerOutput(\"ambientClr\", NodeMaterialBlockConnectionPointTypes.Color3, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"diffuseDir\", NodeMaterialBlockConnectionPointTypes.Color3, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"specularDir\", NodeMaterialBlockConnectionPointTypes.Color3, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"clearcoatDir\", NodeMaterialBlockConnectionPointTypes.Color3, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"sheenDir\", NodeMaterialBlockConnectionPointTypes.Color3, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"diffuseInd\", NodeMaterialBlockConnectionPointTypes.Color3, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"specularInd\", NodeMaterialBlockConnectionPointTypes.Color3, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"clearcoatInd\", NodeMaterialBlockConnectionPointTypes.Color3, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"sheenInd\", NodeMaterialBlockConnectionPointTypes.Color3, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"refraction\", NodeMaterialBlockConnectionPointTypes.Color3, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"lighting\", NodeMaterialBlockConnectionPointTypes.Color3, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"shadow\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"alpha\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Fragment);\r\n    }\r\n\r\n    /**\r\n     * Intensity of the direct lights e.g. the four lights available in your scene.\r\n     * This impacts both the direct diffuse and specular highlights.\r\n     */\r\n    @editableInPropertyPage(\"Direct lights\", PropertyTypeForEdition.Float, \"INTENSITY\", { min: 0, max: 1, notifiers: { update: true } })\r\n    public directIntensity: number = 1.0;\r\n\r\n    /**\r\n     * Intensity of the environment e.g. how much the environment will light the object\r\n     * either through harmonics for rough material or through the reflection for shiny ones.\r\n     */\r\n    @editableInPropertyPage(\"Environment lights\", PropertyTypeForEdition.Float, \"INTENSITY\", { min: 0, max: 1, notifiers: { update: true } })\r\n    public environmentIntensity: number = 1.0;\r\n\r\n    /**\r\n     * This is a special control allowing the reduction of the specular highlights coming from the\r\n     * four lights of the scene. Those highlights may not be needed in full environment lighting.\r\n     */\r\n    @editableInPropertyPage(\"Specular highlights\", PropertyTypeForEdition.Float, \"INTENSITY\", { min: 0, max: 1, notifiers: { update: true } })\r\n    public specularIntensity: number = 1.0;\r\n\r\n    /**\r\n     * Defines the  falloff type used in this material.\r\n     * It by default is Physical.\r\n     */\r\n    @editableInPropertyPage(\"Light falloff\", PropertyTypeForEdition.List, \"LIGHTING & COLORS\", {\r\n        notifiers: { update: true },\r\n        options: [\r\n            { label: \"Physical\", value: PBRBaseMaterial.LIGHTFALLOFF_PHYSICAL },\r\n            { label: \"GLTF\", value: PBRBaseMaterial.LIGHTFALLOFF_GLTF },\r\n            { label: \"Standard\", value: PBRBaseMaterial.LIGHTFALLOFF_STANDARD },\r\n        ],\r\n    })\r\n    public lightFalloff = 0;\r\n\r\n    /**\r\n     * Specifies that alpha test should be used\r\n     */\r\n    @editableInPropertyPage(\"Alpha Testing\", PropertyTypeForEdition.Boolean, \"OPACITY\")\r\n    public useAlphaTest: boolean = false;\r\n\r\n    /**\r\n     * Defines the alpha limits in alpha test mode.\r\n     */\r\n    @editableInPropertyPage(\"Alpha CutOff\", PropertyTypeForEdition.Float, \"OPACITY\", { min: 0, max: 1, notifiers: { update: true } })\r\n    public alphaTestCutoff: number = 0.5;\r\n\r\n    /**\r\n     * Specifies that alpha blending should be used\r\n     */\r\n    @editableInPropertyPage(\"Alpha blending\", PropertyTypeForEdition.Boolean, \"OPACITY\")\r\n    public useAlphaBlending: boolean = false;\r\n\r\n    /**\r\n     * Specifies that the material will keeps the reflection highlights over a transparent surface (only the most luminous ones).\r\n     * A car glass is a good example of that. When the street lights reflects on it you can not see what is behind.\r\n     */\r\n    @editableInPropertyPage(\"Radiance over alpha\", PropertyTypeForEdition.Boolean, \"RENDERING\", { notifiers: { update: true } })\r\n    public useRadianceOverAlpha: boolean = true;\r\n\r\n    /**\r\n     * Specifies that the material will keeps the specular highlights over a transparent surface (only the most luminous ones).\r\n     * A car glass is a good example of that. When sun reflects on it you can not see what is behind.\r\n     */\r\n    @editableInPropertyPage(\"Specular over alpha\", PropertyTypeForEdition.Boolean, \"RENDERING\", { notifiers: { update: true } })\r\n    public useSpecularOverAlpha: boolean = true;\r\n\r\n    /**\r\n     * Enables specular anti aliasing in the PBR shader.\r\n     * It will both interacts on the Geometry for analytical and IBL lighting.\r\n     * It also prefilter the roughness map based on the bump values.\r\n     */\r\n    @editableInPropertyPage(\"Specular anti-aliasing\", PropertyTypeForEdition.Boolean, \"RENDERING\", { notifiers: { update: true } })\r\n    public enableSpecularAntiAliasing: boolean = false;\r\n\r\n    /**\r\n     * Enables realtime filtering on the texture.\r\n     */\r\n    @editableInPropertyPage(\"Realtime filtering\", PropertyTypeForEdition.Boolean, \"RENDERING\", { notifiers: { update: true } })\r\n    public realTimeFiltering: boolean = false;\r\n\r\n    /**\r\n     * Quality switch for realtime filtering\r\n     */\r\n    @editableInPropertyPage(\"Realtime filtering quality\", PropertyTypeForEdition.List, \"RENDERING\", {\r\n        notifiers: { update: true },\r\n        options: [\r\n            { label: \"Low\", value: Constants.TEXTURE_FILTERING_QUALITY_LOW },\r\n            { label: \"Medium\", value: Constants.TEXTURE_FILTERING_QUALITY_MEDIUM },\r\n            { label: \"High\", value: Constants.TEXTURE_FILTERING_QUALITY_HIGH },\r\n        ],\r\n    })\r\n    public realTimeFilteringQuality = Constants.TEXTURE_FILTERING_QUALITY_LOW;\r\n\r\n    /**\r\n     * Base Diffuse Model\r\n     */\r\n    @editableInPropertyPage(\"Diffuse Model\", PropertyTypeForEdition.List, \"RENDERING\", {\r\n        notifiers: { update: true },\r\n        options: [\r\n            { label: \"Lambert\", value: Constants.MATERIAL_DIFFUSE_MODEL_LAMBERT },\r\n            { label: \"Burley\", value: Constants.MATERIAL_DIFFUSE_MODEL_BURLEY },\r\n            { label: \"Oren-Nayar\", value: Constants.MATERIAL_DIFFUSE_MODEL_E_OREN_NAYAR },\r\n            { label: \"Legacy\", value: Constants.MATERIAL_DIFFUSE_MODEL_LEGACY },\r\n        ],\r\n    })\r\n    public baseDiffuseModel = Constants.MATERIAL_DIFFUSE_MODEL_E_OREN_NAYAR;\r\n\r\n    /**\r\n     * Defines if the material uses energy conservation.\r\n     */\r\n    @editableInPropertyPage(\"Energy Conservation\", PropertyTypeForEdition.Boolean, \"ADVANCED\", { notifiers: { update: true } })\r\n    public useEnergyConservation: boolean = true;\r\n\r\n    /**\r\n     * This parameters will enable/disable radiance occlusion by preventing the radiance to lit\r\n     * too much the area relying on ambient texture to define their ambient occlusion.\r\n     */\r\n    @editableInPropertyPage(\"Radiance occlusion\", PropertyTypeForEdition.Boolean, \"ADVANCED\", { notifiers: { update: true } })\r\n    public useRadianceOcclusion: boolean = true;\r\n\r\n    /**\r\n     * This parameters will enable/disable Horizon occlusion to prevent normal maps to look shiny when the normal\r\n     * makes the reflect vector face the model (under horizon).\r\n     */\r\n    @editableInPropertyPage(\"Horizon occlusion\", PropertyTypeForEdition.Boolean, \"ADVANCED\", { notifiers: { update: true } })\r\n    public useHorizonOcclusion: boolean = true;\r\n\r\n    /**\r\n     * If set to true, no lighting calculations will be applied.\r\n     */\r\n    @editableInPropertyPage(\"Unlit\", PropertyTypeForEdition.Boolean, \"ADVANCED\", { notifiers: { update: true } })\r\n    public unlit: boolean = false;\r\n\r\n    /**\r\n     * Force normal to face away from face.\r\n     */\r\n    @editableInPropertyPage(\"Force normal forward\", PropertyTypeForEdition.Boolean, \"ADVANCED\", { notifiers: { update: true } })\r\n    public forceNormalForward: boolean = false;\r\n\r\n    /** Indicates that no code should be generated in the vertex shader. Can be useful in some specific circumstances (like when doing ray marching for eg) */\r\n    @editableInPropertyPage(\"Generate only fragment code\", PropertyTypeForEdition.Boolean, \"ADVANCED\", {\r\n        notifiers: { rebuild: true, update: true, onValidation: PBRMetallicRoughnessBlock._OnGenerateOnlyFragmentCodeChanged },\r\n    })\r\n    public generateOnlyFragmentCode = false;\r\n\r\n    /**\r\n     * Defines the material debug mode.\r\n     * It helps seeing only some components of the material while troubleshooting.\r\n     */\r\n    @editableInPropertyPage(\"Debug mode\", PropertyTypeForEdition.List, \"DEBUG\", {\r\n        notifiers: { update: true },\r\n        options: [\r\n            { label: \"None\", value: 0 },\r\n            // Geometry\r\n            { label: \"Normalized position\", value: 1 },\r\n            { label: \"Normals\", value: 2 },\r\n            { label: \"Tangents\", value: 3 },\r\n            { label: \"Bitangents\", value: 4 },\r\n            { label: \"Bump Normals\", value: 5 },\r\n            //{ label: \"UV1\", value: 6 },\r\n            //{ label: \"UV2\", value: 7 },\r\n            { label: \"ClearCoat Normals\", value: 8 },\r\n            { label: \"ClearCoat Tangents\", value: 9 },\r\n            { label: \"ClearCoat Bitangents\", value: 10 },\r\n            { label: \"Anisotropic Normals\", value: 11 },\r\n            { label: \"Anisotropic Tangents\", value: 12 },\r\n            { label: \"Anisotropic Bitangents\", value: 13 },\r\n            // Maps\r\n            //{ label: \"Emissive Map\", value: 23 },\r\n            //{ label: \"Light Map\", value: 24 },\r\n            // Env\r\n            { label: \"Env Refraction\", value: 40 },\r\n            { label: \"Env Reflection\", value: 41 },\r\n            { label: \"Env Clear Coat\", value: 42 },\r\n            // Lighting\r\n            { label: \"Direct Diffuse\", value: 50 },\r\n            { label: \"Direct Specular\", value: 51 },\r\n            { label: \"Direct Clear Coat\", value: 52 },\r\n            { label: \"Direct Sheen\", value: 53 },\r\n            { label: \"Env Irradiance\", value: 54 },\r\n            // Lighting Params\r\n            { label: \"Surface Albedo\", value: 60 },\r\n            { label: \"Reflectance 0\", value: 61 },\r\n            { label: \"Metallic\", value: 62 },\r\n            { label: \"Metallic F0\", value: 71 },\r\n            { label: \"Roughness\", value: 63 },\r\n            { label: \"AlphaG\", value: 64 },\r\n            { label: \"NdotV\", value: 65 },\r\n            { label: \"ClearCoat Color\", value: 66 },\r\n            { label: \"ClearCoat Roughness\", value: 67 },\r\n            { label: \"ClearCoat NdotV\", value: 68 },\r\n            { label: \"Transmittance\", value: 69 },\r\n            { label: \"Refraction Transmittance\", value: 70 },\r\n            // Misc\r\n            { label: \"SEO\", value: 80 },\r\n            { label: \"EHO\", value: 81 },\r\n            { label: \"Energy Factor\", value: 82 },\r\n            { label: \"Specular Reflectance\", value: 83 },\r\n            { label: \"Clear Coat Reflectance\", value: 84 },\r\n            { label: \"Sheen Reflectance\", value: 85 },\r\n            { label: \"Luminance Over Alpha\", value: 86 },\r\n            { label: \"Alpha\", value: 87 },\r\n            { label: \"Albedo color\", value: 88 },\r\n            { label: \"Ambient occlusion color\", value: 89 },\r\n        ],\r\n    })\r\n    public debugMode = 0;\r\n\r\n    /**\r\n     * Specify from where on screen the debug mode should start.\r\n     * The value goes from -1 (full screen) to 1 (not visible)\r\n     * It helps with side by side comparison against the final render\r\n     * This defaults to 0\r\n     */\r\n    @editableInPropertyPage(\"Split position\", PropertyTypeForEdition.Float, \"DEBUG\", { min: -1, max: 1, notifiers: { update: true } })\r\n    public debugLimit = 0;\r\n\r\n    /**\r\n     * As the default viewing range might not be enough (if the ambient is really small for instance)\r\n     * You can use the factor to better multiply the final value.\r\n     */\r\n    @editableInPropertyPage(\"Output factor\", PropertyTypeForEdition.Float, \"DEBUG\", { min: 0, max: 5, notifiers: { update: true } })\r\n    public debugFactor = 1;\r\n\r\n    /**\r\n     * Initialize the block and prepare the context for build\r\n     * @param state defines the state that will be used for the build\r\n     */\r\n    public override initialize(state: NodeMaterialBuildState) {\r\n        state._excludeVariableName(\"vLightingIntensity\");\r\n\r\n        state._excludeVariableName(\"geometricNormalW\");\r\n        state._excludeVariableName(\"normalW\");\r\n        state._excludeVariableName(\"faceNormal\");\r\n\r\n        state._excludeVariableName(\"albedoOpacityOut\");\r\n        state._excludeVariableName(\"surfaceAlbedo\");\r\n        state._excludeVariableName(\"alpha\");\r\n\r\n        state._excludeVariableName(\"aoOut\");\r\n\r\n        state._excludeVariableName(\"baseColor\");\r\n        state._excludeVariableName(\"reflectivityOut\");\r\n        state._excludeVariableName(\"microSurface\");\r\n        state._excludeVariableName(\"roughness\");\r\n        state._excludeVariableName(\"vReflectivityColor\");\r\n\r\n        state._excludeVariableName(\"NdotVUnclamped\");\r\n        state._excludeVariableName(\"NdotV\");\r\n        state._excludeVariableName(\"alphaG\");\r\n        state._excludeVariableName(\"AARoughnessFactors\");\r\n        state._excludeVariableName(\"environmentBrdf\");\r\n        state._excludeVariableName(\"ambientMonochrome\");\r\n        state._excludeVariableName(\"seo\");\r\n        state._excludeVariableName(\"eho\");\r\n\r\n        state._excludeVariableName(\"environmentRadiance\");\r\n        state._excludeVariableName(\"irradianceVector\");\r\n        state._excludeVariableName(\"environmentIrradiance\");\r\n\r\n        state._excludeVariableName(\"diffuseBase\");\r\n        state._excludeVariableName(\"specularBase\");\r\n        state._excludeVariableName(\"preInfo\");\r\n        state._excludeVariableName(\"info\");\r\n        state._excludeVariableName(\"shadow\");\r\n\r\n        state._excludeVariableName(\"finalDiffuse\");\r\n        state._excludeVariableName(\"finalAmbient\");\r\n        state._excludeVariableName(\"ambientOcclusionForDirectDiffuse\");\r\n\r\n        state._excludeVariableName(\"finalColor\");\r\n\r\n        state._excludeVariableName(\"vClipSpacePosition\");\r\n        state._excludeVariableName(\"vDebugMode\");\r\n\r\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n        this._initShaderSourceAsync(state.shaderLanguage);\r\n    }\r\n\r\n    private async _initShaderSourceAsync(shaderLanguage: ShaderLanguage) {\r\n        this._codeIsReady = false;\r\n        if (shaderLanguage === ShaderLanguage.WGSL) {\r\n            await Promise.all([import(\"../../../../ShadersWGSL/pbr.vertex\"), import(\"../../../../ShadersWGSL/pbr.fragment\")]);\r\n        } else {\r\n            await Promise.all([import(\"../../../../Shaders/pbr.vertex\"), import(\"../../../../Shaders/pbr.fragment\")]);\r\n        }\r\n\r\n        this._codeIsReady = true;\r\n        this.onCodeIsReadyObservable.notifyObservers(this);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public override getClassName() {\r\n        return \"PBRMetallicRoughnessBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the world position input component\r\n     */\r\n    public get worldPosition(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the world normal input component\r\n     */\r\n    public get worldNormal(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the view matrix parameter\r\n     */\r\n    public get view(): NodeMaterialConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the camera position input component\r\n     */\r\n    public get cameraPosition(): NodeMaterialConnectionPoint {\r\n        return this._inputs[3];\r\n    }\r\n\r\n    /**\r\n     * Gets the perturbed normal input component\r\n     */\r\n    public get perturbedNormal(): NodeMaterialConnectionPoint {\r\n        return this._inputs[4];\r\n    }\r\n\r\n    /**\r\n     * Gets the base color input component\r\n     */\r\n    public get baseColor(): NodeMaterialConnectionPoint {\r\n        return this._inputs[5];\r\n    }\r\n\r\n    /**\r\n     * Gets the metallic input component\r\n     */\r\n    public get metallic(): NodeMaterialConnectionPoint {\r\n        return this._inputs[6];\r\n    }\r\n\r\n    /**\r\n     * Gets the roughness input component\r\n     */\r\n    public get roughness(): NodeMaterialConnectionPoint {\r\n        return this._inputs[7];\r\n    }\r\n\r\n    /**\r\n     * Gets the ambient occlusion input component\r\n     */\r\n    public get ambientOcc(): NodeMaterialConnectionPoint {\r\n        return this._inputs[8];\r\n    }\r\n\r\n    /**\r\n     * Gets the opacity input component\r\n     */\r\n    public get opacity(): NodeMaterialConnectionPoint {\r\n        return this._inputs[9];\r\n    }\r\n\r\n    /**\r\n     * Gets the index of refraction input component\r\n     */\r\n    public get indexOfRefraction(): NodeMaterialConnectionPoint {\r\n        return this._inputs[10];\r\n    }\r\n\r\n    /**\r\n     * Gets the ambient color input component\r\n     */\r\n    public get ambientColor(): NodeMaterialConnectionPoint {\r\n        return this._inputs[11];\r\n    }\r\n\r\n    /**\r\n     * Gets the reflection object parameters\r\n     */\r\n    public get reflection(): NodeMaterialConnectionPoint {\r\n        return this._inputs[12];\r\n    }\r\n\r\n    /**\r\n     * Gets the clear coat object parameters\r\n     */\r\n    public get clearcoat(): NodeMaterialConnectionPoint {\r\n        return this._inputs[13];\r\n    }\r\n\r\n    /**\r\n     * Gets the sheen object parameters\r\n     */\r\n    public get sheen(): NodeMaterialConnectionPoint {\r\n        return this._inputs[14];\r\n    }\r\n\r\n    /**\r\n     * Gets the sub surface object parameters\r\n     */\r\n    public get subsurface(): NodeMaterialConnectionPoint {\r\n        return this._inputs[15];\r\n    }\r\n\r\n    /**\r\n     * Gets the anisotropy object parameters\r\n     */\r\n    public get anisotropy(): NodeMaterialConnectionPoint {\r\n        return this._inputs[16];\r\n    }\r\n\r\n    /**\r\n     * Gets the iridescence object parameters\r\n     */\r\n    public get iridescence(): NodeMaterialConnectionPoint {\r\n        return this._inputs[17];\r\n    }\r\n\r\n    /**\r\n     * Gets the ambient output component\r\n     */\r\n    public get ambientClr(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the diffuse output component\r\n     */\r\n    public get diffuseDir(): NodeMaterialConnectionPoint {\r\n        return this._outputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the specular output component\r\n     */\r\n    public get specularDir(): NodeMaterialConnectionPoint {\r\n        return this._outputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the clear coat output component\r\n     */\r\n    public get clearcoatDir(): NodeMaterialConnectionPoint {\r\n        return this._outputs[3];\r\n    }\r\n\r\n    /**\r\n     * Gets the sheen output component\r\n     */\r\n    public get sheenDir(): NodeMaterialConnectionPoint {\r\n        return this._outputs[4];\r\n    }\r\n\r\n    /**\r\n     * Gets the indirect diffuse output component\r\n     */\r\n    public get diffuseInd(): NodeMaterialConnectionPoint {\r\n        return this._outputs[5];\r\n    }\r\n\r\n    /**\r\n     * Gets the indirect specular output component\r\n     */\r\n    public get specularInd(): NodeMaterialConnectionPoint {\r\n        return this._outputs[6];\r\n    }\r\n\r\n    /**\r\n     * Gets the indirect clear coat output component\r\n     */\r\n    public get clearcoatInd(): NodeMaterialConnectionPoint {\r\n        return this._outputs[7];\r\n    }\r\n\r\n    /**\r\n     * Gets the indirect sheen output component\r\n     */\r\n    public get sheenInd(): NodeMaterialConnectionPoint {\r\n        return this._outputs[8];\r\n    }\r\n\r\n    /**\r\n     * Gets the refraction output component\r\n     */\r\n    public get refraction(): NodeMaterialConnectionPoint {\r\n        return this._outputs[9];\r\n    }\r\n\r\n    /**\r\n     * Gets the global lighting output component\r\n     */\r\n    public get lighting(): NodeMaterialConnectionPoint {\r\n        return this._outputs[10];\r\n    }\r\n\r\n    /**\r\n     * Gets the shadow output component\r\n     */\r\n    public get shadow(): NodeMaterialConnectionPoint {\r\n        return this._outputs[11];\r\n    }\r\n\r\n    /**\r\n     * Gets the alpha output component\r\n     */\r\n    public get alpha(): NodeMaterialConnectionPoint {\r\n        return this._outputs[12];\r\n    }\r\n\r\n    public override autoConfigure(material: NodeMaterial, additionalFilteringInfo: (node: NodeMaterialBlock) => boolean = () => true) {\r\n        if (!this.cameraPosition.isConnected) {\r\n            let cameraPositionInput = material.getInputBlockByPredicate((b) => b.systemValue === NodeMaterialSystemValues.CameraPosition && additionalFilteringInfo(b));\r\n\r\n            if (!cameraPositionInput) {\r\n                cameraPositionInput = new InputBlock(\"cameraPosition\");\r\n                cameraPositionInput.setAsSystemValue(NodeMaterialSystemValues.CameraPosition);\r\n            }\r\n            cameraPositionInput.output.connectTo(this.cameraPosition);\r\n        }\r\n\r\n        if (!this.view.isConnected) {\r\n            let viewInput = material.getInputBlockByPredicate((b) => b.systemValue === NodeMaterialSystemValues.View && additionalFilteringInfo(b));\r\n\r\n            if (!viewInput) {\r\n                viewInput = new InputBlock(\"view\");\r\n                viewInput.setAsSystemValue(NodeMaterialSystemValues.View);\r\n            }\r\n            viewInput.output.connectTo(this.view);\r\n        }\r\n    }\r\n\r\n    public override prepareDefines(defines: NodeMaterialDefines, nodeMaterial: NodeMaterial, mesh?: AbstractMesh) {\r\n        if (!mesh) {\r\n            return;\r\n        }\r\n\r\n        // General\r\n        defines.setValue(\"PBR\", true);\r\n        defines.setValue(\"METALLICWORKFLOW\", true);\r\n        defines.setValue(\"DEBUGMODE\", this.debugMode, true);\r\n        defines.setValue(\"DEBUGMODE_FORCERETURN\", true);\r\n        defines.setValue(\"NORMALXYSCALE\", true);\r\n        defines.setValue(\"BUMP\", this.perturbedNormal.isConnected, true);\r\n        defines.setValue(\"LODBASEDMICROSFURACE\", this._scene.getEngine().getCaps().textureLOD);\r\n\r\n        // Albedo & Opacity\r\n        defines.setValue(\"ALBEDO\", false, true);\r\n        defines.setValue(\"OPACITY\", this.opacity.isConnected, true);\r\n\r\n        // Ambient occlusion\r\n        defines.setValue(\"AMBIENT\", true, true);\r\n        defines.setValue(\"AMBIENTINGRAYSCALE\", false, true);\r\n\r\n        // Reflectivity\r\n        defines.setValue(\"REFLECTIVITY\", false, true);\r\n        defines.setValue(\"AOSTOREINMETALMAPRED\", false, true);\r\n        defines.setValue(\"METALLNESSSTOREINMETALMAPBLUE\", false, true);\r\n        defines.setValue(\"ROUGHNESSSTOREINMETALMAPALPHA\", false, true);\r\n        defines.setValue(\"ROUGHNESSSTOREINMETALMAPGREEN\", false, true);\r\n\r\n        // Lighting & colors\r\n        if (this.lightFalloff === PBRBaseMaterial.LIGHTFALLOFF_STANDARD) {\r\n            defines.setValue(\"USEPHYSICALLIGHTFALLOFF\", false);\r\n            defines.setValue(\"USEGLTFLIGHTFALLOFF\", false);\r\n        } else if (this.lightFalloff === PBRBaseMaterial.LIGHTFALLOFF_GLTF) {\r\n            defines.setValue(\"USEPHYSICALLIGHTFALLOFF\", false);\r\n            defines.setValue(\"USEGLTFLIGHTFALLOFF\", true);\r\n        } else {\r\n            defines.setValue(\"USEPHYSICALLIGHTFALLOFF\", true);\r\n            defines.setValue(\"USEGLTFLIGHTFALLOFF\", false);\r\n        }\r\n\r\n        // Transparency\r\n        const alphaTestCutOffString = this.alphaTestCutoff.toString();\r\n\r\n        defines.setValue(\"ALPHABLEND\", this.useAlphaBlending, true);\r\n        defines.setValue(\"ALPHAFROMALBEDO\", false, true);\r\n        defines.setValue(\"ALPHATEST\", this.useAlphaTest, true);\r\n        defines.setValue(\"ALPHATESTVALUE\", alphaTestCutOffString.indexOf(\".\") < 0 ? alphaTestCutOffString + \".\" : alphaTestCutOffString, true);\r\n        defines.setValue(\"OPACITYRGB\", false, true);\r\n\r\n        // Rendering\r\n        defines.setValue(\"RADIANCEOVERALPHA\", this.useRadianceOverAlpha, true);\r\n        defines.setValue(\"SPECULAROVERALPHA\", this.useSpecularOverAlpha, true);\r\n        defines.setValue(\"SPECULARAA\", this._scene.getEngine().getCaps().standardDerivatives && this.enableSpecularAntiAliasing, true);\r\n        defines.setValue(\"REALTIME_FILTERING\", this.realTimeFiltering, true);\r\n\r\n        const scene = mesh.getScene();\r\n        const engine = scene.getEngine();\r\n\r\n        if (engine._features.needTypeSuffixInShaderConstants) {\r\n            defines.setValue(\"NUM_SAMPLES\", this.realTimeFilteringQuality + \"u\", true);\r\n        } else {\r\n            defines.setValue(\"NUM_SAMPLES\", \"\" + this.realTimeFilteringQuality, true);\r\n        }\r\n\r\n        defines.setValue(\"BASE_DIFFUSE_MODEL\", this.baseDiffuseModel, true);\r\n\r\n        // Advanced\r\n        defines.setValue(\"BRDF_V_HEIGHT_CORRELATED\", true);\r\n        defines.setValue(\"LEGACY_SPECULAR_ENERGY_CONSERVATION\", true);\r\n        defines.setValue(\"MS_BRDF_ENERGY_CONSERVATION\", this.useEnergyConservation, true);\r\n        defines.setValue(\"RADIANCEOCCLUSION\", this.useRadianceOcclusion, true);\r\n        defines.setValue(\"HORIZONOCCLUSION\", this.useHorizonOcclusion, true);\r\n        defines.setValue(\"UNLIT\", this.unlit, true);\r\n        defines.setValue(\"FORCENORMALFORWARD\", this.forceNormalForward, true);\r\n\r\n        if (this._environmentBRDFTexture && MaterialFlags.ReflectionTextureEnabled) {\r\n            defines.setValue(\"ENVIRONMENTBRDF\", true);\r\n            defines.setValue(\"ENVIRONMENTBRDF_RGBD\", this._environmentBRDFTexture.isRGBD, true);\r\n        } else {\r\n            defines.setValue(\"ENVIRONMENTBRDF\", false);\r\n            defines.setValue(\"ENVIRONMENTBRDF_RGBD\", false);\r\n        }\r\n\r\n        if (defines._areImageProcessingDirty && nodeMaterial.imageProcessingConfiguration) {\r\n            nodeMaterial.imageProcessingConfiguration.prepareDefines(defines);\r\n        }\r\n\r\n        if (!defines._areLightsDirty) {\r\n            return;\r\n        }\r\n\r\n        if (!this.light) {\r\n            // Lights\r\n            PrepareDefinesForLights(scene, mesh, defines, true, nodeMaterial.maxSimultaneousLights);\r\n            defines._needNormals = true;\r\n\r\n            // Multiview\r\n            PrepareDefinesForMultiview(scene, defines);\r\n        } else {\r\n            const state = {\r\n                needNormals: false,\r\n                needRebuild: false,\r\n                lightmapMode: false,\r\n                shadowEnabled: false,\r\n                specularEnabled: false,\r\n            };\r\n\r\n            PrepareDefinesForLight(scene, mesh, this.light, this._lightId, defines, true, state);\r\n\r\n            if (state.needRebuild) {\r\n                defines.rebuild();\r\n            }\r\n        }\r\n    }\r\n\r\n    public override updateUniformsAndSamples(state: NodeMaterialBuildState, nodeMaterial: NodeMaterial, defines: NodeMaterialDefines, uniformBuffers: string[]) {\r\n        for (let lightIndex = 0; lightIndex < nodeMaterial.maxSimultaneousLights; lightIndex++) {\r\n            if (!defines[\"LIGHT\" + lightIndex]) {\r\n                break;\r\n            }\r\n            const onlyUpdateBuffersList = state.uniforms.indexOf(\"vLightData\" + lightIndex) >= 0;\r\n            PrepareUniformsAndSamplersForLight(\r\n                lightIndex,\r\n                state.uniforms,\r\n                state.samplers,\r\n                defines[\"PROJECTEDLIGHTTEXTURE\" + lightIndex],\r\n                uniformBuffers,\r\n                onlyUpdateBuffersList,\r\n                defines[\"IESLIGHTTEXTURE\" + lightIndex]\r\n            );\r\n        }\r\n    }\r\n\r\n    public override isReady(mesh: AbstractMesh, nodeMaterial: NodeMaterial, defines: NodeMaterialDefines) {\r\n        if (this._environmentBRDFTexture && !this._environmentBRDFTexture.isReady()) {\r\n            return false;\r\n        }\r\n\r\n        if (defines._areImageProcessingDirty && nodeMaterial.imageProcessingConfiguration) {\r\n            if (!nodeMaterial.imageProcessingConfiguration.isReady()) {\r\n                return false;\r\n            }\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    public override bind(effect: Effect, nodeMaterial: NodeMaterial, mesh?: Mesh) {\r\n        if (!mesh) {\r\n            return;\r\n        }\r\n\r\n        const scene = mesh.getScene();\r\n\r\n        if (!this.light) {\r\n            BindLights(scene, mesh, effect, true, nodeMaterial.maxSimultaneousLights);\r\n        } else {\r\n            BindLight(this.light, this._lightId, scene, effect, true);\r\n        }\r\n\r\n        effect.setTexture(this._environmentBrdfSamplerName, this._environmentBRDFTexture);\r\n\r\n        effect.setFloat2(\"vDebugMode\", this.debugLimit, this.debugFactor);\r\n\r\n        const ambientScene = this._scene.ambientColor;\r\n\r\n        if (ambientScene) {\r\n            effect.setColor3(\"ambientFromScene\", ambientScene);\r\n        }\r\n\r\n        const invertNormal = scene.useRightHandedSystem === (scene._mirroredCameraPosition != null);\r\n\r\n        effect.setFloat(this._invertNormalName, invertNormal ? -1 : 1);\r\n\r\n        effect.setFloat4(\"vLightingIntensity\", this.directIntensity, 1, this.environmentIntensity * this._scene.environmentIntensity, this.specularIntensity);\r\n\r\n        // reflectivity bindings\r\n        const metallicF90 = this._metallicF0Factor;\r\n\r\n        effect.setColor4(this._vMetallicReflectanceFactorsName, this._metallicReflectanceColor, metallicF90);\r\n\r\n        if (nodeMaterial.imageProcessingConfiguration) {\r\n            nodeMaterial.imageProcessingConfiguration.bind(effect);\r\n        }\r\n    }\r\n\r\n    private _injectVertexCode(state: NodeMaterialBuildState) {\r\n        const worldPos = this.worldPosition;\r\n        const worldNormal = this.worldNormal;\r\n        const comments = `//${this.name}`;\r\n        const isWebGPU = state.shaderLanguage === ShaderLanguage.WGSL;\r\n\r\n        // Declaration\r\n        if (!this.light) {\r\n            // Emit for all lights\r\n            state._emitFunctionFromInclude(state.supportUniformBuffers ? \"lightVxUboDeclaration\" : \"lightVxFragmentDeclaration\", comments, {\r\n                repeatKey: \"maxSimultaneousLights\",\r\n            });\r\n            this._lightId = 0;\r\n\r\n            state.sharedData.dynamicUniformBlocks.push(this);\r\n        } else {\r\n            this._lightId = (state.counters[\"lightCounter\"] !== undefined ? state.counters[\"lightCounter\"] : -1) + 1;\r\n            state.counters[\"lightCounter\"] = this._lightId;\r\n\r\n            state._emitFunctionFromInclude(\r\n                state.supportUniformBuffers ? \"lightVxUboDeclaration\" : \"lightVxFragmentDeclaration\",\r\n                comments,\r\n                {\r\n                    replaceStrings: [{ search: /{X}/g, replace: this._lightId.toString() }],\r\n                },\r\n                this._lightId.toString()\r\n            );\r\n        }\r\n\r\n        // Inject code in vertex\r\n        const worldPosVaryingName = \"v_\" + worldPos.associatedVariableName;\r\n        if (state._emitVaryingFromString(worldPosVaryingName, NodeMaterialBlockConnectionPointTypes.Vector4)) {\r\n            state.compilationString += (isWebGPU ? \"vertexOutputs.\" : \"\") + `${worldPosVaryingName} = ${worldPos.associatedVariableName};\\n`;\r\n        }\r\n\r\n        const worldNormalVaryingName = \"v_\" + worldNormal.associatedVariableName;\r\n        if (state._emitVaryingFromString(worldNormalVaryingName, NodeMaterialBlockConnectionPointTypes.Vector4)) {\r\n            state.compilationString += (isWebGPU ? \"vertexOutputs.\" : \"\") + `${worldNormalVaryingName} = ${worldNormal.associatedVariableName};\\n`;\r\n        }\r\n\r\n        const reflectionBlock = this.reflection.isConnected ? (this.reflection.connectedPoint?.ownerBlock as ReflectionBlock) : null;\r\n\r\n        if (reflectionBlock) {\r\n            reflectionBlock.viewConnectionPoint = this.view;\r\n        }\r\n\r\n        state.compilationString += reflectionBlock?.handleVertexSide(state) ?? \"\";\r\n\r\n        if (state._emitVaryingFromString(\"vClipSpacePosition\", NodeMaterialBlockConnectionPointTypes.Vector4, \"defined(IGNORE) || DEBUGMODE > 0\")) {\r\n            state._injectAtEnd += `#if DEBUGMODE > 0\\n`;\r\n            state._injectAtEnd += (isWebGPU ? \"vertexOutputs.\" : \"\") + `vClipSpacePosition = ${isWebGPU ? \"vertexOutputs.position\" : \"gl_Position\"};\\n`;\r\n            state._injectAtEnd += `#endif\\n`;\r\n        }\r\n\r\n        if (this.light) {\r\n            state.compilationString += state._emitCodeFromInclude(\"shadowsVertex\", comments, {\r\n                replaceStrings: [\r\n                    { search: /{X}/g, replace: this._lightId.toString() },\r\n                    { search: /worldPos/g, replace: worldPos.associatedVariableName },\r\n                ],\r\n            });\r\n        } else {\r\n            state.compilationString += `${state._declareLocalVar(\"worldPos\", NodeMaterialBlockConnectionPointTypes.Vector4)} = ${worldPos.associatedVariableName};\\n`;\r\n            if (this.view.isConnected) {\r\n                state.compilationString += `${state._declareLocalVar(\"view\", NodeMaterialBlockConnectionPointTypes.Matrix)} = ${this.view.associatedVariableName};\\n`;\r\n            }\r\n            state.compilationString += state._emitCodeFromInclude(\"shadowsVertex\", comments, {\r\n                repeatKey: \"maxSimultaneousLights\",\r\n            });\r\n        }\r\n    }\r\n\r\n    private _getAlbedoOpacityCode(state: NodeMaterialBuildState): string {\r\n        const isWebGPU = state.shaderLanguage === ShaderLanguage.WGSL;\r\n\r\n        let code = isWebGPU ? \"var albedoOpacityOut: albedoOpacityOutParams;\\n\" : `albedoOpacityOutParams albedoOpacityOut;\\n`;\r\n\r\n        const albedoColor = this.baseColor.isConnected ? this.baseColor.associatedVariableName : \"vec3(1.)\";\r\n        const opacity = this.opacity.isConnected ? this.opacity.associatedVariableName : \"1.\";\r\n\r\n        code += `albedoOpacityOut = albedoOpacityBlock(\r\n                vec4${state.fSuffix}(${albedoColor}, 1.)\r\n            #ifdef ALBEDO\r\n                ,vec4${state.fSuffix}(1.)\r\n                ,vec2${state.fSuffix}(1., 1.)\r\n            #endif\r\n                ,1. /* Base Weight */\r\n            #ifdef OPACITY\r\n                ,vec4${state.fSuffix}(${opacity})\r\n                ,vec2${state.fSuffix}(1., 1.)\r\n            #endif\r\n            );\r\n\r\n            ${state._declareLocalVar(\"surfaceAlbedo\", NodeMaterialBlockConnectionPointTypes.Vector3)} = albedoOpacityOut.surfaceAlbedo;\r\n            ${state._declareLocalVar(\"alpha\", NodeMaterialBlockConnectionPointTypes.Float)} = albedoOpacityOut.alpha;\\n`;\r\n\r\n        return code;\r\n    }\r\n\r\n    private _getAmbientOcclusionCode(state: NodeMaterialBuildState): string {\r\n        const isWebGPU = state.shaderLanguage === ShaderLanguage.WGSL;\r\n        let code = isWebGPU ? \"var aoOut: ambientOcclusionOutParams;\\n\" : `ambientOcclusionOutParams aoOut;\\n`;\r\n\r\n        const ao = this.ambientOcc.isConnected ? this.ambientOcc.associatedVariableName : \"1.\";\r\n\r\n        code += `aoOut = ambientOcclusionBlock(\r\n            #ifdef AMBIENT\r\n                vec3${state.fSuffix}(${ao}),\r\n                vec4${state.fSuffix}(0., 1.0, 1.0, 0.)\r\n            #endif\r\n            );\\n`;\r\n\r\n        return code;\r\n    }\r\n\r\n    private _getReflectivityCode(state: NodeMaterialBuildState): string {\r\n        const isWebGPU = state.shaderLanguage === ShaderLanguage.WGSL;\r\n        let code = isWebGPU ? \"var reflectivityOut: reflectivityOutParams;\\n\" : `reflectivityOutParams reflectivityOut;\\n`;\r\n        const aoIntensity = \"1.\";\r\n\r\n        this._vMetallicReflectanceFactorsName = state._getFreeVariableName(\"vMetallicReflectanceFactors\");\r\n        state._emitUniformFromString(this._vMetallicReflectanceFactorsName, NodeMaterialBlockConnectionPointTypes.Vector4);\r\n\r\n        this._baseDiffuseRoughnessName = state._getFreeVariableName(\"baseDiffuseRoughness\");\r\n        state._emitUniformFromString(this._baseDiffuseRoughnessName, NodeMaterialBlockConnectionPointTypes.Float);\r\n\r\n        const outsideIOR = 1; // consider air as clear coat and other layers would remap in the shader.\r\n        const ior = this.indexOfRefraction.connectInputBlock?.value ?? 1.5;\r\n        // Based of the schlick fresnel approximation model\r\n        // for dielectrics.\r\n        const f0 = Math.pow((ior - outsideIOR) / (ior + outsideIOR), 2);\r\n\r\n        code += `${state._declareLocalVar(\"baseColor\", NodeMaterialBlockConnectionPointTypes.Vector3)} = surfaceAlbedo;\r\n            ${isWebGPU ? \"let\" : `vec4${state.fSuffix}`} vReflectivityColor = vec4${state.fSuffix}(${this.metallic.associatedVariableName}, ${this.roughness.associatedVariableName}, ${this.indexOfRefraction.associatedVariableName || \"1.5\"}, ${f0});\r\n            reflectivityOut = reflectivityBlock(\r\n                vReflectivityColor\r\n            #ifdef METALLICWORKFLOW\r\n                , surfaceAlbedo\r\n                , ${(isWebGPU ? \"uniforms.\" : \"\") + this._vMetallicReflectanceFactorsName}\r\n            #endif\r\n                , ${(isWebGPU ? \"uniforms.\" : \"\") + this._baseDiffuseRoughnessName}\r\n            #ifdef BASE_DIFFUSE_ROUGHNESS\r\n                , 0.\r\n                , vec2${state.fSuffix}(0., 0.)\r\n            #endif\r\n            #ifdef REFLECTIVITY\r\n                , vec3${state.fSuffix}(0., 0., ${aoIntensity})\r\n                , vec4${state.fSuffix}(1.)\r\n            #endif\r\n            #if defined(METALLICWORKFLOW) && defined(REFLECTIVITY)  && defined(AOSTOREINMETALMAPRED)\r\n                , aoOut.ambientOcclusionColor\r\n            #endif\r\n            #ifdef MICROSURFACEMAP\r\n                , microSurfaceTexel <== not handled!\r\n            #endif\r\n            );\r\n\r\n            ${state._declareLocalVar(\"microSurface\", NodeMaterialBlockConnectionPointTypes.Float)} = reflectivityOut.microSurface;\r\n            ${state._declareLocalVar(\"roughness\", NodeMaterialBlockConnectionPointTypes.Float)} = reflectivityOut.roughness;\r\n            ${state._declareLocalVar(\"diffuseRoughness\", NodeMaterialBlockConnectionPointTypes.Float)} = reflectivityOut.diffuseRoughness;\r\n\r\n            #ifdef METALLICWORKFLOW\r\n                surfaceAlbedo = reflectivityOut.surfaceAlbedo;\r\n            #endif\r\n            #if defined(METALLICWORKFLOW) && defined(REFLECTIVITY) && defined(AOSTOREINMETALMAPRED)\r\n                aoOut.ambientOcclusionColor = reflectivityOut.ambientOcclusionColor;\r\n            #endif\\n`;\r\n\r\n        return code;\r\n    }\r\n\r\n    protected override _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        this._scene = state.sharedData.scene;\r\n        const isWebGPU = state.shaderLanguage === ShaderLanguage.WGSL;\r\n\r\n        if (!this._environmentBRDFTexture) {\r\n            this._environmentBRDFTexture = GetEnvironmentBRDFTexture(this._scene);\r\n        }\r\n\r\n        const reflectionBlock = this.reflection.isConnected ? (this.reflection.connectedPoint?.ownerBlock as ReflectionBlock) : null;\r\n\r\n        if (reflectionBlock) {\r\n            // Need those variables to be setup when calling _injectVertexCode\r\n            reflectionBlock.worldPositionConnectionPoint = this.worldPosition;\r\n            reflectionBlock.cameraPositionConnectionPoint = this.cameraPosition;\r\n            reflectionBlock.worldNormalConnectionPoint = this.worldNormal;\r\n            reflectionBlock.viewConnectionPoint = this.view;\r\n        }\r\n\r\n        if (state.target !== NodeMaterialBlockTargets.Fragment) {\r\n            // Vertex\r\n            this._injectVertexCode(state);\r\n\r\n            return this;\r\n        }\r\n\r\n        // Fragment\r\n        state.sharedData.forcedBindableBlocks.push(this);\r\n        state.sharedData.blocksWithDefines.push(this);\r\n        state.sharedData.blockingBlocks.push(this);\r\n        if (this.generateOnlyFragmentCode) {\r\n            state.sharedData.dynamicUniformBlocks.push(this);\r\n        }\r\n\r\n        const comments = `//${this.name}`;\r\n        const normalShading = this.perturbedNormal;\r\n\r\n        let worldPosVarName = this.worldPosition.associatedVariableName;\r\n        let worldNormalVarName = this.worldNormal.associatedVariableName;\r\n        if (this.generateOnlyFragmentCode) {\r\n            worldPosVarName = state._getFreeVariableName(\"globalWorldPos\");\r\n            state._emitFunction(\r\n                \"pbr_globalworldpos\",\r\n                isWebGPU ? `var<private> ${worldPosVarName}:vec3${state.fSuffix};\\n` : `vec3${state.fSuffix} ${worldPosVarName};\\n`,\r\n                comments\r\n            );\r\n            state.compilationString += `${worldPosVarName} = ${this.worldPosition.associatedVariableName}.xyz;\\n`;\r\n\r\n            worldNormalVarName = state._getFreeVariableName(\"globalWorldNormal\");\r\n            state._emitFunction(\r\n                \"pbr_globalworldnorm\",\r\n                isWebGPU ? `var<private> ${worldNormalVarName}:vec4${state.fSuffix};\\n` : `vec4${state.fSuffix} ${worldNormalVarName};\\n`,\r\n                comments\r\n            );\r\n            state.compilationString += `${worldNormalVarName} = ${this.worldNormal.associatedVariableName};\\n`;\r\n\r\n            state.compilationString += state._emitCodeFromInclude(\"shadowsVertex\", comments, {\r\n                repeatKey: \"maxSimultaneousLights\",\r\n                substitutionVars: this.generateOnlyFragmentCode ? `worldPos,${this.worldPosition.associatedVariableName}` : undefined,\r\n            });\r\n\r\n            state.compilationString += `#if DEBUGMODE > 0\\n`;\r\n            state.compilationString += `${state._declareLocalVar(\"vClipSpacePosition\", NodeMaterialBlockConnectionPointTypes.Vector4)} = vec4${state.fSuffix}((vec2${state.fSuffix}(${isWebGPU ? \"fragmentInputs.position\" : \"gl_FragCoord.xy\"}) / vec2${state.fSuffix}(1.0)) * 2.0 - 1.0, 0.0, 1.0);\\n`;\r\n            state.compilationString += `#endif\\n`;\r\n        } else {\r\n            worldPosVarName = (isWebGPU ? \"input.\" : \"\") + \"v_\" + worldPosVarName;\r\n            worldNormalVarName = (isWebGPU ? \"input.\" : \"\") + \"v_\" + worldNormalVarName;\r\n        }\r\n\r\n        this._environmentBrdfSamplerName = state._getFreeVariableName(\"environmentBrdfSampler\");\r\n\r\n        state._emit2DSampler(this._environmentBrdfSamplerName);\r\n\r\n        state.sharedData.hints.needAlphaBlending = state.sharedData.hints.needAlphaBlending || this.useAlphaBlending;\r\n        state.sharedData.hints.needAlphaTesting = state.sharedData.hints.needAlphaTesting || this.useAlphaTest;\r\n\r\n        state._emitExtension(\"lod\", \"#extension GL_EXT_shader_texture_lod : enable\", \"defined(LODBASEDMICROSFURACE)\");\r\n        state._emitExtension(\"derivatives\", \"#extension GL_OES_standard_derivatives : enable\");\r\n\r\n        state._emitUniformFromString(\"vDebugMode\", NodeMaterialBlockConnectionPointTypes.Vector2, \"defined(IGNORE) || DEBUGMODE > 0\");\r\n        state._emitUniformFromString(\"ambientFromScene\", NodeMaterialBlockConnectionPointTypes.Vector3);\r\n\r\n        // Image processing uniforms\r\n        state.uniforms.push(\"exposureLinear\");\r\n        state.uniforms.push(\"contrast\");\r\n        state.uniforms.push(\"vInverseScreenSize\");\r\n        state.uniforms.push(\"vignetteSettings1\");\r\n        state.uniforms.push(\"vignetteSettings2\");\r\n        state.uniforms.push(\"vCameraColorCurveNegative\");\r\n        state.uniforms.push(\"vCameraColorCurveNeutral\");\r\n        state.uniforms.push(\"vCameraColorCurvePositive\");\r\n        state.uniforms.push(\"txColorTransform\");\r\n        state.uniforms.push(\"colorTransformSettings\");\r\n        state.uniforms.push(\"ditherIntensity\");\r\n\r\n        //\r\n        // Includes\r\n        //\r\n        if (!this.light) {\r\n            // Emit for all lights\r\n            state._emitFunctionFromInclude(state.supportUniformBuffers ? \"lightUboDeclaration\" : \"lightFragmentDeclaration\", comments, {\r\n                repeatKey: \"maxSimultaneousLights\",\r\n                substitutionVars: this.generateOnlyFragmentCode ? \"varying,\" : undefined,\r\n            });\r\n        } else {\r\n            state._emitFunctionFromInclude(\r\n                state.supportUniformBuffers ? \"lightUboDeclaration\" : \"lightFragmentDeclaration\",\r\n                comments,\r\n                {\r\n                    replaceStrings: [{ search: /{X}/g, replace: this._lightId.toString() }],\r\n                },\r\n                this._lightId.toString()\r\n            );\r\n        }\r\n\r\n        state._emitFunctionFromInclude(\"helperFunctions\", comments);\r\n        state._emitFunctionFromInclude(\"importanceSampling\", comments);\r\n        state._emitFunctionFromInclude(\"pbrHelperFunctions\", comments);\r\n        state._emitFunctionFromInclude(\"imageProcessingDeclaration\", comments);\r\n        state._emitFunctionFromInclude(\"imageProcessingFunctions\", comments);\r\n\r\n        state._emitFunctionFromInclude(\"shadowsFragmentFunctions\", comments);\r\n\r\n        state._emitFunctionFromInclude(\"pbrDirectLightingSetupFunctions\", comments);\r\n\r\n        state._emitFunctionFromInclude(\"pbrDirectLightingFalloffFunctions\", comments);\r\n        state._emitFunctionFromInclude(\"pbrBRDFFunctions\", comments, {\r\n            replaceStrings: [{ search: /REFLECTIONMAP_SKYBOX/g, replace: reflectionBlock?._defineSkyboxName ?? \"REFLECTIONMAP_SKYBOX\" }],\r\n        });\r\n        state._emitFunctionFromInclude(\"hdrFilteringFunctions\", comments);\r\n\r\n        state._emitFunctionFromInclude(\"pbrDirectLightingFunctions\", comments);\r\n\r\n        state._emitFunctionFromInclude(\"pbrIBLFunctions\", comments);\r\n\r\n        state._emitFunctionFromInclude(\"pbrBlockAlbedoOpacity\", comments);\r\n        state._emitFunctionFromInclude(\"pbrBlockReflectivity\", comments);\r\n        state._emitFunctionFromInclude(\"pbrBlockAmbientOcclusion\", comments);\r\n        state._emitFunctionFromInclude(\"pbrBlockAlphaFresnel\", comments);\r\n        state._emitFunctionFromInclude(\"pbrBlockAnisotropic\", comments);\r\n\r\n        //\r\n        // code\r\n        //\r\n\r\n        state._emitUniformFromString(\"vLightingIntensity\", NodeMaterialBlockConnectionPointTypes.Vector4);\r\n\r\n        if (reflectionBlock?.generateOnlyFragmentCode) {\r\n            state.compilationString += reflectionBlock.handleVertexSide(state);\r\n        }\r\n\r\n        // _____________________________ Geometry Information ____________________________\r\n        this._vNormalWName = state._getFreeVariableName(\"vNormalW\");\r\n\r\n        state.compilationString += `${state._declareLocalVar(this._vNormalWName, NodeMaterialBlockConnectionPointTypes.Vector4)} = normalize(${worldNormalVarName});\\n`;\r\n\r\n        if (state._registerTempVariable(\"viewDirectionW\")) {\r\n            state.compilationString += `${state._declareLocalVar(\"viewDirectionW\", NodeMaterialBlockConnectionPointTypes.Vector3)} = normalize(${this.cameraPosition.associatedVariableName} - ${worldPosVarName}.xyz);\\n`;\r\n        }\r\n\r\n        state.compilationString += `${state._declareLocalVar(\"geometricNormalW\", NodeMaterialBlockConnectionPointTypes.Vector3)} = ${this._vNormalWName}.xyz;\\n`;\r\n\r\n        state.compilationString += `${state._declareLocalVar(\"normalW\", NodeMaterialBlockConnectionPointTypes.Vector3)} = ${normalShading.isConnected ? \"normalize(\" + normalShading.associatedVariableName + \".xyz)\" : \"geometricNormalW\"};\\n`;\r\n\r\n        this._invertNormalName = state._getFreeVariableName(\"invertNormal\");\r\n\r\n        state._emitUniformFromString(this._invertNormalName, NodeMaterialBlockConnectionPointTypes.Float);\r\n\r\n        state.compilationString += state._emitCodeFromInclude(\"pbrBlockNormalFinal\", comments, {\r\n            replaceStrings: [\r\n                { search: /vPositionW/g, replace: worldPosVarName + \".xyz\" },\r\n                { search: /vEyePosition.w/g, replace: this._invertNormalName },\r\n            ],\r\n        });\r\n\r\n        // _____________________________ Albedo & Opacity ______________________________\r\n        state.compilationString += this._getAlbedoOpacityCode(state);\r\n\r\n        state.compilationString += state._emitCodeFromInclude(\"depthPrePass\", comments);\r\n\r\n        // _____________________________ AO  _______________________________\r\n        state.compilationString += this._getAmbientOcclusionCode(state);\r\n\r\n        state.compilationString += state._emitCodeFromInclude(\"pbrBlockLightmapInit\", comments);\r\n\r\n        // _____________________________ UNLIT  _______________________________\r\n        state.compilationString += `#ifdef UNLIT\r\n                ${state._declareLocalVar(\"diffuseBase\", NodeMaterialBlockConnectionPointTypes.Vector3)} = vec3${state.fSuffix}(1., 1., 1.);\r\n            #else\\n`;\r\n\r\n        // _____________________________ Reflectivity _______________________________\r\n        state.compilationString += this._getReflectivityCode(state);\r\n\r\n        // _____________________________ Geometry info _________________________________\r\n        state.compilationString += state._emitCodeFromInclude(\"pbrBlockGeometryInfo\", comments, {\r\n            replaceStrings: [\r\n                { search: /REFLECTIONMAP_SKYBOX/g, replace: reflectionBlock?._defineSkyboxName ?? \"REFLECTIONMAP_SKYBOX\" },\r\n                { search: /REFLECTIONMAP_3D/g, replace: reflectionBlock?._define3DName ?? \"REFLECTIONMAP_3D\" },\r\n            ],\r\n        });\r\n\r\n        // _____________________________ Anisotropy _______________________________________\r\n        const anisotropyBlock = this.anisotropy.isConnected ? (this.anisotropy.connectedPoint?.ownerBlock as AnisotropyBlock) : null;\r\n\r\n        if (anisotropyBlock) {\r\n            anisotropyBlock.worldPositionConnectionPoint = this.worldPosition;\r\n            anisotropyBlock.worldNormalConnectionPoint = this.worldNormal;\r\n\r\n            state.compilationString += anisotropyBlock.getCode(state, !this.perturbedNormal.isConnected);\r\n        }\r\n\r\n        // _____________________________ Reflection _______________________________________\r\n        if (reflectionBlock && reflectionBlock.hasTexture) {\r\n            state.compilationString += reflectionBlock.getCode(state, anisotropyBlock ? \"anisotropicOut.anisotropicNormal\" : \"normalW\");\r\n        }\r\n\r\n        state._emitFunctionFromInclude(\"pbrBlockReflection\", comments, {\r\n            replaceStrings: [\r\n                { search: /computeReflectionCoords/g, replace: \"computeReflectionCoordsPBR\" },\r\n                { search: /REFLECTIONMAP_3D/g, replace: reflectionBlock?._define3DName ?? \"REFLECTIONMAP_3D\" },\r\n                { search: /REFLECTIONMAP_OPPOSITEZ/g, replace: reflectionBlock?._defineOppositeZ ?? \"REFLECTIONMAP_OPPOSITEZ\" },\r\n                { search: /REFLECTIONMAP_PROJECTION/g, replace: reflectionBlock?._defineProjectionName ?? \"REFLECTIONMAP_PROJECTION\" },\r\n                { search: /REFLECTIONMAP_SKYBOX/g, replace: reflectionBlock?._defineSkyboxName ?? \"REFLECTIONMAP_SKYBOX\" },\r\n                { search: /LODINREFLECTIONALPHA/g, replace: reflectionBlock?._defineLODReflectionAlpha ?? \"LODINREFLECTIONALPHA\" },\r\n                { search: /LINEARSPECULARREFLECTION/g, replace: reflectionBlock?._defineLinearSpecularReflection ?? \"LINEARSPECULARREFLECTION\" },\r\n                { search: /vReflectionFilteringInfo/g, replace: reflectionBlock?._vReflectionFilteringInfoName ?? \"vReflectionFilteringInfo\" },\r\n            ],\r\n        });\r\n\r\n        // ___________________ Compute Reflectance aka R0 F0 info _________________________\r\n        state.compilationString += state._emitCodeFromInclude(\"pbrBlockReflectance0\", comments, {\r\n            replaceStrings: [{ search: /metallicReflectanceFactors/g, replace: (isWebGPU ? \"uniforms.\" : \"\") + this._vMetallicReflectanceFactorsName }],\r\n        });\r\n        // ________________________________ Sheen ______________________________\r\n        const sheenBlock = this.sheen.isConnected ? (this.sheen.connectedPoint?.ownerBlock as SheenBlock) : null;\r\n\r\n        if (sheenBlock) {\r\n            state.compilationString += sheenBlock.getCode(reflectionBlock, state);\r\n        }\r\n\r\n        state._emitFunctionFromInclude(\"pbrBlockSheen\", comments, {\r\n            replaceStrings: [\r\n                { search: /REFLECTIONMAP_3D/g, replace: reflectionBlock?._define3DName ?? \"REFLECTIONMAP_3D\" },\r\n                { search: /REFLECTIONMAP_SKYBOX/g, replace: reflectionBlock?._defineSkyboxName ?? \"REFLECTIONMAP_SKYBOX\" },\r\n                { search: /LODINREFLECTIONALPHA/g, replace: reflectionBlock?._defineLODReflectionAlpha ?? \"LODINREFLECTIONALPHA\" },\r\n                { search: /LINEARSPECULARREFLECTION/g, replace: reflectionBlock?._defineLinearSpecularReflection ?? \"LINEARSPECULARREFLECTION\" },\r\n            ],\r\n        });\r\n\r\n        // ____________________ Clear Coat Initialization Code _____________________\r\n        const clearcoatBlock = this.clearcoat.isConnected ? (this.clearcoat.connectedPoint?.ownerBlock as ClearCoatBlock) : null;\r\n\r\n        state.compilationString += ClearCoatBlock._GetInitializationCode(state, clearcoatBlock);\r\n\r\n        // _____________________________ Iridescence _______________________________\r\n        const iridescenceBlock = this.iridescence.isConnected ? (this.iridescence.connectedPoint?.ownerBlock as IridescenceBlock) : null;\r\n        state.compilationString += IridescenceBlock.GetCode(iridescenceBlock, state);\r\n\r\n        state._emitFunctionFromInclude(\"pbrBlockIridescence\", comments, {\r\n            replaceStrings: [],\r\n        });\r\n\r\n        // _____________________________ Clear Coat ____________________________\r\n        const generateTBNSpace = !this.perturbedNormal.isConnected && !this.anisotropy.isConnected;\r\n        const isTangentConnectedToPerturbNormal =\r\n            this.perturbedNormal.isConnected && (this.perturbedNormal.connectedPoint?.ownerBlock as PerturbNormalBlock).worldTangent?.isConnected;\r\n        const isTangentConnectedToAnisotropy = this.anisotropy.isConnected && (this.anisotropy.connectedPoint?.ownerBlock as AnisotropyBlock).worldTangent.isConnected;\r\n        let vTBNAvailable = isTangentConnectedToPerturbNormal || (!this.perturbedNormal.isConnected && isTangentConnectedToAnisotropy);\r\n\r\n        state.compilationString += ClearCoatBlock.GetCode(state, clearcoatBlock, reflectionBlock, worldPosVarName, generateTBNSpace, vTBNAvailable, worldNormalVarName);\r\n\r\n        if (generateTBNSpace) {\r\n            vTBNAvailable = clearcoatBlock?.worldTangent.isConnected ?? false;\r\n        }\r\n\r\n        state._emitFunctionFromInclude(\"pbrBlockClearcoat\", comments, {\r\n            replaceStrings: [\r\n                { search: /computeReflectionCoords/g, replace: \"computeReflectionCoordsPBR\" },\r\n                { search: /REFLECTIONMAP_3D/g, replace: reflectionBlock?._define3DName ?? \"REFLECTIONMAP_3D\" },\r\n                { search: /REFLECTIONMAP_OPPOSITEZ/g, replace: reflectionBlock?._defineOppositeZ ?? \"REFLECTIONMAP_OPPOSITEZ\" },\r\n                { search: /REFLECTIONMAP_PROJECTION/g, replace: reflectionBlock?._defineProjectionName ?? \"REFLECTIONMAP_PROJECTION\" },\r\n                { search: /REFLECTIONMAP_SKYBOX/g, replace: reflectionBlock?._defineSkyboxName ?? \"REFLECTIONMAP_SKYBOX\" },\r\n                { search: /LODINREFLECTIONALPHA/g, replace: reflectionBlock?._defineLODReflectionAlpha ?? \"LODINREFLECTIONALPHA\" },\r\n                { search: /LINEARSPECULARREFLECTION/g, replace: reflectionBlock?._defineLinearSpecularReflection ?? \"LINEARSPECULARREFLECTION\" },\r\n                { search: /defined\\(TANGENT\\)/g, replace: vTBNAvailable ? \"defined(TANGENT)\" : \"defined(IGNORE)\" },\r\n            ],\r\n        });\r\n\r\n        // _________________________ Specular Environment Reflectance __________________________\r\n        state.compilationString += state._emitCodeFromInclude(\"pbrBlockReflectance\", comments, {\r\n            replaceStrings: [\r\n                { search: /REFLECTIONMAP_SKYBOX/g, replace: reflectionBlock?._defineSkyboxName ?? \"REFLECTIONMAP_SKYBOX\" },\r\n                { search: /REFLECTIONMAP_3D/g, replace: reflectionBlock?._define3DName ?? \"REFLECTIONMAP_3D\" },\r\n                { search: /uniforms\\.vReflectivityColor/g, replace: \"vReflectivityColor\" },\r\n            ],\r\n        });\r\n\r\n        // ___________________________________ SubSurface ______________________________________\r\n        const subsurfaceBlock = this.subsurface.isConnected ? (this.subsurface.connectedPoint?.ownerBlock as SubSurfaceBlock) : null;\r\n        const refractionBlock = this.subsurface.isConnected\r\n            ? ((this.subsurface.connectedPoint?.ownerBlock as SubSurfaceBlock).refraction.connectedPoint?.ownerBlock as RefractionBlock)\r\n            : null;\r\n\r\n        if (refractionBlock) {\r\n            refractionBlock.viewConnectionPoint = this.view;\r\n            refractionBlock.indexOfRefractionConnectionPoint = this.indexOfRefraction;\r\n        }\r\n\r\n        state.compilationString += SubSurfaceBlock.GetCode(state, subsurfaceBlock, reflectionBlock, worldPosVarName);\r\n\r\n        state._emitFunctionFromInclude(\"pbrBlockSubSurface\", comments, {\r\n            replaceStrings: [\r\n                { search: /REFLECTIONMAP_3D/g, replace: reflectionBlock?._define3DName ?? \"REFLECTIONMAP_3D\" },\r\n                { search: /REFLECTIONMAP_OPPOSITEZ/g, replace: reflectionBlock?._defineOppositeZ ?? \"REFLECTIONMAP_OPPOSITEZ\" },\r\n                { search: /REFLECTIONMAP_PROJECTION/g, replace: reflectionBlock?._defineProjectionName ?? \"REFLECTIONMAP_PROJECTION\" },\r\n                { search: /SS_REFRACTIONMAP_3D/g, replace: refractionBlock?._define3DName ?? \"SS_REFRACTIONMAP_3D\" },\r\n                { search: /SS_LODINREFRACTIONALPHA/g, replace: refractionBlock?._defineLODRefractionAlpha ?? \"SS_LODINREFRACTIONALPHA\" },\r\n                { search: /SS_LINEARSPECULARREFRACTION/g, replace: refractionBlock?._defineLinearSpecularRefraction ?? \"SS_LINEARSPECULARREFRACTION\" },\r\n                { search: /SS_REFRACTIONMAP_OPPOSITEZ/g, replace: refractionBlock?._defineOppositeZ ?? \"SS_REFRACTIONMAP_OPPOSITEZ\" },\r\n            ],\r\n        });\r\n\r\n        // _____________________________ Direct Lighting Info __________________________________\r\n        state.compilationString += state._emitCodeFromInclude(\"pbrBlockDirectLighting\", comments);\r\n\r\n        if (this.light) {\r\n            state.compilationString += state._emitCodeFromInclude(\"lightFragment\", comments, {\r\n                replaceStrings: [\r\n                    { search: /{X}/g, replace: this._lightId.toString() },\r\n                    { search: new RegExp(`${isWebGPU ? \"fragmentInputs.\" : \"\"}vPositionW`, \"g\"), replace: worldPosVarName + \".xyz\" },\r\n                    { search: /uniforms\\.vReflectivityColor/g, replace: \"vReflectivityColor\" },\r\n                ],\r\n            });\r\n        } else {\r\n            state.compilationString += state._emitCodeFromInclude(\"lightFragment\", comments, {\r\n                repeatKey: \"maxSimultaneousLights\",\r\n                substitutionVars: `${isWebGPU ? \"fragmentInputs.\" : \"\"}vPositionW,${worldPosVarName}.xyz,uniforms.vReflectivityColor,vReflectivityColor`,\r\n            });\r\n        }\r\n\r\n        // _____________________________ Compute Final Lit Components ________________________\r\n        state.compilationString += state._emitCodeFromInclude(\"pbrBlockFinalLitComponents\", comments);\r\n\r\n        // _____________________________ UNLIT (2) ________________________\r\n        state.compilationString += `#endif\\n`; // UNLIT\r\n\r\n        // _____________________________ Compute Final Unlit Components ________________________\r\n        const aoColor = this.ambientColor.isConnected ? this.ambientColor.associatedVariableName : `vec3${state.fSuffix}(0., 0., 0.)`;\r\n\r\n        let aoDirectLightIntensity = PBRBaseMaterial.DEFAULT_AO_ON_ANALYTICAL_LIGHTS.toString();\r\n\r\n        if (aoDirectLightIntensity.indexOf(\".\") === -1) {\r\n            aoDirectLightIntensity += \".\";\r\n        }\r\n\r\n        let replaceStrings = [\r\n            { search: /vec3 finalEmissive[\\s\\S]*?finalEmissive\\*=vLightingIntensity\\.y;/g, replace: \"\" },\r\n            { search: new RegExp(`${isWebGPU ? \"uniforms.\" : \"\"}vAmbientColor`, \"g\"), replace: aoColor + ` * ${isWebGPU ? \"uniforms.\" : \"\"}ambientFromScene` },\r\n            { search: new RegExp(`${isWebGPU ? \"uniforms.\" : \"\"}vAmbientInfos.w`, \"g\"), replace: aoDirectLightIntensity },\r\n        ];\r\n\r\n        if (isWebGPU) {\r\n            replaceStrings[0] = { search: /var finalEmissive[\\s\\S]*?finalEmissive\\*=uniforms.vLightingIntensity\\.y;/g, replace: \"\" };\r\n        }\r\n\r\n        state.compilationString += state._emitCodeFromInclude(\"pbrBlockFinalUnlitComponents\", comments, {\r\n            replaceStrings: replaceStrings,\r\n        });\r\n\r\n        // _____________________________ Output Final Color Composition ________________________\r\n        state.compilationString += state._emitCodeFromInclude(\"pbrBlockFinalColorComposition\", comments, {\r\n            replaceStrings: [{ search: /finalEmissive/g, replace: `vec3${state.fSuffix}(0.)` }],\r\n        });\r\n\r\n        // _____________________________ Apply image processing ________________________\r\n        if (isWebGPU) {\r\n            replaceStrings = [{ search: /mesh.visibility/g, replace: \"1.\" }];\r\n        } else {\r\n            replaceStrings = [{ search: /visibility/g, replace: \"1.\" }];\r\n        }\r\n\r\n        state.compilationString += state._emitCodeFromInclude(\"pbrBlockImageProcessing\", comments, {\r\n            replaceStrings: replaceStrings,\r\n        });\r\n\r\n        // _____________________________ Generate debug code ________________________\r\n\r\n        const colorOutput = isWebGPU ? \"fragmentOutputs.color\" : \"gl_FragColor\";\r\n        replaceStrings = [\r\n            { search: new RegExp(`${isWebGPU ? \"fragmentInputs.\" : \"\"}vNormalW`, \"g\"), replace: this._vNormalWName },\r\n            { search: new RegExp(`${isWebGPU ? \"fragmentInputs.\" : \"\"}vPositionW`, \"g\"), replace: worldPosVarName },\r\n            { search: /uniforms\\.vReflectivityColor/g, replace: \"vReflectivityColor\" },\r\n            {\r\n                search: /albedoTexture\\.rgb;/g,\r\n                replace: `vec3${state.fSuffix}(1.);\\n${colorOutput}.rgb = toGammaSpace(${colorOutput}.rgb);\\n`,\r\n            },\r\n        ];\r\n        state.compilationString += state._emitCodeFromInclude(\"pbrDebug\", comments, {\r\n            replaceStrings: replaceStrings,\r\n        });\r\n\r\n        // _____________________________ Generate end points ________________________\r\n        for (const output of this._outputs) {\r\n            if (output.hasEndpoints) {\r\n                const remap = MapOutputToVariable[output.name];\r\n                if (remap) {\r\n                    const [varName, conditions] = remap;\r\n                    if (conditions) {\r\n                        state.compilationString += `#if ${conditions}\\n`;\r\n                    }\r\n                    state.compilationString += `${state._declareOutput(output)} = ${varName};\\n`;\r\n                    if (conditions) {\r\n                        state.compilationString += `#else\\n`;\r\n                        state.compilationString += `${state._declareOutput(output)} = vec3${state.fSuffix}(0.);\\n`;\r\n                        state.compilationString += `#endif\\n`;\r\n                    }\r\n                } else {\r\n                    state.sharedData.raiseBuildError(`There's no remapping for the ${output.name} end point! No code generated`);\r\n                }\r\n            }\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    protected override _dumpPropertiesCode() {\r\n        let codeString = super._dumpPropertiesCode();\r\n\r\n        codeString += `${this._codeVariableName}.lightFalloff = ${this.lightFalloff};\\n`;\r\n        codeString += `${this._codeVariableName}.useAlphaTest = ${this.useAlphaTest};\\n`;\r\n        codeString += `${this._codeVariableName}.alphaTestCutoff = ${this.alphaTestCutoff};\\n`;\r\n        codeString += `${this._codeVariableName}.useAlphaBlending = ${this.useAlphaBlending};\\n`;\r\n        codeString += `${this._codeVariableName}.useRadianceOverAlpha = ${this.useRadianceOverAlpha};\\n`;\r\n        codeString += `${this._codeVariableName}.useSpecularOverAlpha = ${this.useSpecularOverAlpha};\\n`;\r\n        codeString += `${this._codeVariableName}.enableSpecularAntiAliasing = ${this.enableSpecularAntiAliasing};\\n`;\r\n        codeString += `${this._codeVariableName}.realTimeFiltering = ${this.realTimeFiltering};\\n`;\r\n        codeString += `${this._codeVariableName}.realTimeFilteringQuality = ${this.realTimeFilteringQuality};\\n`;\r\n        codeString += `${this._codeVariableName}.useEnergyConservation = ${this.useEnergyConservation};\\n`;\r\n        codeString += `${this._codeVariableName}.useRadianceOcclusion = ${this.useRadianceOcclusion};\\n`;\r\n        codeString += `${this._codeVariableName}.useHorizonOcclusion = ${this.useHorizonOcclusion};\\n`;\r\n        codeString += `${this._codeVariableName}.unlit = ${this.unlit};\\n`;\r\n        codeString += `${this._codeVariableName}.forceNormalForward = ${this.forceNormalForward};\\n`;\r\n        codeString += `${this._codeVariableName}.debugMode = ${this.debugMode};\\n`;\r\n        codeString += `${this._codeVariableName}.debugLimit = ${this.debugLimit};\\n`;\r\n        codeString += `${this._codeVariableName}.debugFactor = ${this.debugFactor};\\n`;\r\n\r\n        return codeString;\r\n    }\r\n\r\n    public override serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        if (this.light) {\r\n            serializationObject.lightId = this.light.id;\r\n        }\r\n\r\n        serializationObject.lightFalloff = this.lightFalloff;\r\n        serializationObject.useAlphaTest = this.useAlphaTest;\r\n        serializationObject.alphaTestCutoff = this.alphaTestCutoff;\r\n        serializationObject.useAlphaBlending = this.useAlphaBlending;\r\n        serializationObject.useRadianceOverAlpha = this.useRadianceOverAlpha;\r\n        serializationObject.useSpecularOverAlpha = this.useSpecularOverAlpha;\r\n        serializationObject.enableSpecularAntiAliasing = this.enableSpecularAntiAliasing;\r\n        serializationObject.realTimeFiltering = this.realTimeFiltering;\r\n        serializationObject.realTimeFilteringQuality = this.realTimeFilteringQuality;\r\n        serializationObject.useEnergyConservation = this.useEnergyConservation;\r\n        serializationObject.useRadianceOcclusion = this.useRadianceOcclusion;\r\n        serializationObject.useHorizonOcclusion = this.useHorizonOcclusion;\r\n        serializationObject.unlit = this.unlit;\r\n        serializationObject.forceNormalForward = this.forceNormalForward;\r\n        serializationObject.debugMode = this.debugMode;\r\n        serializationObject.debugLimit = this.debugLimit;\r\n        serializationObject.debugFactor = this.debugFactor;\r\n        serializationObject.generateOnlyFragmentCode = this.generateOnlyFragmentCode;\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public override _deserialize(serializationObject: any, scene: Scene, rootUrl: string) {\r\n        super._deserialize(serializationObject, scene, rootUrl);\r\n\r\n        if (serializationObject.lightId) {\r\n            this.light = scene.getLightById(serializationObject.lightId);\r\n        }\r\n\r\n        this.lightFalloff = serializationObject.lightFalloff ?? 0;\r\n        this.useAlphaTest = serializationObject.useAlphaTest;\r\n        this.alphaTestCutoff = serializationObject.alphaTestCutoff;\r\n        this.useAlphaBlending = serializationObject.useAlphaBlending;\r\n        this.useRadianceOverAlpha = serializationObject.useRadianceOverAlpha;\r\n        this.useSpecularOverAlpha = serializationObject.useSpecularOverAlpha;\r\n        this.enableSpecularAntiAliasing = serializationObject.enableSpecularAntiAliasing;\r\n        this.realTimeFiltering = !!serializationObject.realTimeFiltering;\r\n        this.realTimeFilteringQuality = serializationObject.realTimeFilteringQuality ?? Constants.TEXTURE_FILTERING_QUALITY_LOW;\r\n        this.useEnergyConservation = serializationObject.useEnergyConservation;\r\n        this.useRadianceOcclusion = serializationObject.useRadianceOcclusion;\r\n        this.useHorizonOcclusion = serializationObject.useHorizonOcclusion;\r\n        this.unlit = serializationObject.unlit;\r\n        this.forceNormalForward = !!serializationObject.forceNormalForward;\r\n        this.debugMode = serializationObject.debugMode;\r\n        this.debugLimit = serializationObject.debugLimit;\r\n        this.debugFactor = serializationObject.debugFactor;\r\n        this.generateOnlyFragmentCode = !!serializationObject.generateOnlyFragmentCode;\r\n\r\n        this._setTarget();\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.PBRMetallicRoughnessBlock\", PBRMetallicRoughnessBlock);\r\n"], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAI1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAEhF,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAChF,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AAGjD,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAI3D,OAAO,EAAE,eAAe,EAAE,MAAM,8BAA8B,CAAC;AAE/D,OAAO,EAAE,sBAAsB,EAA0B,MAAM,sCAAsC,CAAC;AACtG,OAAO,EAAE,uCAAuC,EAAE,MAAM,+CAA+C,CAAC;AACxG,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAE1C,OAAO,EAAE,yBAAyB,EAAE,MAAM,mCAAmC,CAAC;AAC9E,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AACvD,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AACpD,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AACpD,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAClD,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AACtD,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAIpD,OAAO,EAAE,MAAM,EAAE,MAAM,8BAA8B,CAAC;AACtD,OAAO,EAAE,MAAM,EAAE,mCAAyB;AAC1C,OAAO,EACH,SAAS,EACT,UAAU,EACV,sBAAsB,EACtB,uBAAuB,EACvB,0BAA0B,EAC1B,kCAAkC,GACrC,MAAM,mCAAmC,CAAC;;;;;;;;;;;;;;;;;;;;;;AAG3C,MAAM,mBAAmB,GAAyC;IAC9D,UAAU,EAAE;QAAC,cAAc;QAAE,EAAE;KAAC;IAChC,UAAU,EAAE;QAAC,cAAc;QAAE,EAAE;KAAC;IAChC,WAAW,EAAE;QAAC,qBAAqB;QAAE,0CAA0C;KAAC;IAChF,YAAY,EAAE;QAAC,sBAAsB;QAAE,uCAAuC;KAAC;IAC/E,QAAQ,EAAE;QAAC,kBAAkB;QAAE,mCAAmC;KAAC;IACnE,UAAU,EAAE;QAAC,iBAAiB;QAAE,wCAAwC;KAAC;IACzE,WAAW,EAAE;QAAC,qBAAqB;QAAE,wCAAwC;KAAC;IAC9E,YAAY,EAAE;QAAC,2CAA2C;QAAE,8DAA8D;KAAC;IAC3H,QAAQ,EAAE;QAAC,mCAAmC;QAAE,sFAAsF;KAAC;IACvI,UAAU,EAAE;QAAC,+BAA+B;QAAE,2CAA2C;KAAC;IAC1F,QAAQ,EAAE;QAAC,gBAAgB;QAAE,EAAE;KAAC;IAChC,MAAM,EAAE;QAAC,WAAW;QAAE,EAAE;KAAC;IACzB,KAAK,EAAE;QAAC,OAAO;QAAE,EAAE;KAAC;CACvB,CAAC;AAMI,MAAO,yBAA0B,uLAAQ,oBAAiB;IAMpD,MAAM,CAAC,kCAAkC,CAAC,KAAwB,EAAE,aAAqB,EAAA;QAC7F,MAAM,IAAI,GAAG,KAAkC,CAAC;QAEhD,IAAI,IAAI,CAAC,aAAa,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;YACjE,IAAI,CAAC,wBAAwB,GAAG,CAAC,IAAI,CAAC,wBAAwB,CAAC;iKAC/D,UAAM,CAAC,KAAK,CAAC,sFAAsF,CAAC,CAAC;YACrG,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,UAAU,EAAE,CAAC;QAElB,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,UAAU,GAAA;QACd,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,+LAAC,2BAAwB,CAAC,QAAQ,CAAC,CAAC,+LAAC,2BAAwB,CAAC,iBAAiB,CAAC,CAAC;QACvI,IAAI,CAAC,cAAc,CAAC,eAAe,CAAE,CAAC,MAAM,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,+LAAC,2BAAwB,CAAC,QAAQ,CAAC,CAAC,+LAAC,2BAAwB,CAAC,MAAM,CAAC;QACnJ,IAAI,CAAC,cAAc,CAAC,aAAa,CAAE,CAAC,MAAM,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,+LAAC,2BAAwB,CAAC,QAAQ,CAAC,CAAC,CAAC,yNAAwB,CAAC,MAAM,CAAC;IACrJ,CAAC;IAcD;;;OAGG,CACH,YAAmB,IAAY,CAAA;QAC3B,KAAK,CAAC,IAAI,gMAAE,2BAAwB,CAAC,iBAAiB,CAAC,CAAC;QAfpD,IAAA,CAAA,uBAAuB,GAA0B,IAAI,CAAC;QAKtD,IAAA,CAAA,yBAAyB,iKAAW,SAAM,CAAC,KAAK,EAAE,CAAC;QACnD,IAAA,CAAA,iBAAiB,GAAG,CAAC,CAAC;QAmF9B;;;WAGG,CAEI,IAAA,CAAA,eAAe,GAAW,GAAG,CAAC;QAErC;;;WAGG,CAEI,IAAA,CAAA,oBAAoB,GAAW,GAAG,CAAC;QAE1C;;;WAGG,CAEI,IAAA,CAAA,iBAAiB,GAAW,GAAG,CAAC;QAEvC;;;WAGG,CASI,IAAA,CAAA,YAAY,GAAG,CAAC,CAAC;QAExB;;WAEG,CAEI,IAAA,CAAA,YAAY,GAAY,KAAK,CAAC;QAErC;;WAEG,CAEI,IAAA,CAAA,eAAe,GAAW,GAAG,CAAC;QAErC;;WAEG,CAEI,IAAA,CAAA,gBAAgB,GAAY,KAAK,CAAC;QAEzC;;;WAGG,CAEI,IAAA,CAAA,oBAAoB,GAAY,IAAI,CAAC;QAE5C;;;WAGG,CAEI,IAAA,CAAA,oBAAoB,GAAY,IAAI,CAAC;QAE5C;;;;WAIG,CAEI,IAAA,CAAA,0BAA0B,GAAY,KAAK,CAAC;QAEnD;;WAEG,CAEI,IAAA,CAAA,iBAAiB,GAAY,KAAK,CAAC;QAE1C;;WAEG,CASI,IAAA,CAAA,wBAAwB,GAAG,SAAS,CAAC,6BAA6B,CAAC;QAE1E;;WAEG,CAUI,IAAA,CAAA,gBAAgB,GAAG,SAAS,CAAC,mCAAmC,CAAC;QAExE;;WAEG,CAEI,IAAA,CAAA,qBAAqB,GAAY,IAAI,CAAC;QAE7C;;;WAGG,CAEI,IAAA,CAAA,oBAAoB,GAAY,IAAI,CAAC;QAE5C;;;WAGG,CAEI,IAAA,CAAA,mBAAmB,GAAY,IAAI,CAAC;QAE3C;;WAEG,CAEI,IAAA,CAAA,KAAK,GAAY,KAAK,CAAC;QAE9B;;WAEG,CAEI,IAAA,CAAA,kBAAkB,GAAY,KAAK,CAAC;QAE3C,wJAAA,EAA0J,CAInJ,IAAA,CAAA,wBAAwB,GAAG,KAAK,CAAC;QAExC;;;WAGG,CA0DI,IAAA,CAAA,SAAS,GAAG,CAAC,CAAC;QAErB;;;;;WAKG,CAEI,IAAA,CAAA,UAAU,GAAG,CAAC,CAAC;QAEtB;;;WAGG,CAEI,IAAA,CAAA,WAAW,GAAG,CAAC,CAAC;QAvSnB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,IAAI,CAAC,aAAa,CAAC,eAAe,4MAAE,yCAAqC,CAAC,OAAO,EAAE,KAAK,gMAAE,2BAAwB,CAAC,MAAM,CAAC,CAAC;QAC3H,IAAI,CAAC,aAAa,CAAC,aAAa,6MAAE,wCAAqC,CAAC,OAAO,EAAE,KAAK,gMAAE,2BAAwB,CAAC,MAAM,CAAC,CAAC;QACzH,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,mPAAqC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAChF,IAAI,CAAC,aAAa,CAAC,gBAAgB,4MAAE,yCAAqC,CAAC,OAAO,EAAE,KAAK,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAC9H,IAAI,CAAC,aAAa,CAAC,iBAAiB,6MAAE,wCAAqC,CAAC,OAAO,EAAE,IAAI,EAAE,yNAAwB,CAAC,QAAQ,CAAC,CAAC;QAC9H,IAAI,CAAC,aAAa,CAAC,WAAW,6MAAE,wCAAqC,CAAC,MAAM,EAAE,IAAI,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QACvH,IAAI,CAAC,aAAa,CAAC,UAAU,6MAAE,wCAAqC,CAAC,KAAK,EAAE,KAAK,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QACtH,IAAI,CAAC,aAAa,CAAC,WAAW,6MAAE,wCAAqC,CAAC,KAAK,EAAE,KAAK,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QACvH,IAAI,CAAC,aAAa,CAAC,YAAY,6MAAE,wCAAqC,CAAC,KAAK,EAAE,IAAI,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QACvH,IAAI,CAAC,aAAa,CAAC,SAAS,6MAAE,wCAAqC,CAAC,KAAK,EAAE,IAAI,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QACpH,IAAI,CAAC,aAAa,CAAC,mBAAmB,EAAE,mPAAqC,CAAC,KAAK,EAAE,IAAI,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAC9H,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,mPAAqC,CAAC,MAAM,EAAE,IAAI,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAC1H,IAAI,CAAC,aAAa,CACd,YAAY,6MACZ,wCAAqC,CAAC,MAAM,EAC5C,IAAI,gMACJ,2BAAwB,CAAC,QAAQ,EACjC,IAAI,8OAAuC,CAAC,YAAY,EAAE,IAAI,EAAA,EAAA,8CAAA,kMAA8C,kBAAe,EAAE,iBAAiB,CAAC,CAClJ,CAAC;QACF,IAAI,CAAC,aAAa,CACd,WAAW,6MACX,wCAAqC,CAAC,MAAM,EAC5C,IAAI,gMACJ,2BAAwB,CAAC,QAAQ,EACjC,wMAAI,0CAAuC,CAAC,WAAW,EAAE,IAAI,EAAA,EAAA,8CAAA,KAA8C,6MAAc,EAAE,gBAAgB,CAAC,CAC/I,CAAC;QACF,IAAI,CAAC,aAAa,CACd,OAAO,6MACP,wCAAqC,CAAC,MAAM,EAC5C,IAAI,gMACJ,2BAAwB,CAAC,QAAQ,EACjC,wMAAI,0CAAuC,CAAC,OAAO,EAAE,IAAI,EAAA,EAAA,8CAAA,6LAA8C,aAAU,EAAE,YAAY,CAAC,CACnI,CAAC;QACF,IAAI,CAAC,aAAa,CACd,YAAY,6MACZ,wCAAqC,CAAC,MAAM,EAC5C,IAAI,gMACJ,2BAAwB,CAAC,QAAQ,EACjC,wMAAI,0CAAuC,CAAC,YAAY,EAAE,IAAI,EAAA,EAAA,8CAAA,kMAA8C,kBAAe,EAAE,iBAAiB,CAAC,CAClJ,CAAC;QACF,IAAI,CAAC,aAAa,CACd,YAAY,6MACZ,wCAAqC,CAAC,MAAM,EAC5C,IAAI,gMACJ,2BAAwB,CAAC,QAAQ,EACjC,IAAI,8OAAuC,CAAC,YAAY,EAAE,IAAI,EAAA,EAAA,8CAAA,kMAA8C,kBAAe,EAAE,iBAAiB,CAAC,CAClJ,CAAC;QACF,IAAI,CAAC,aAAa,CACd,aAAa,6MACb,wCAAqC,CAAC,MAAM,EAC5C,IAAI,gMACJ,2BAAwB,CAAC,QAAQ,EACjC,IAAI,8OAAuC,CAAC,aAAa,EAAE,IAAI,EAAA,EAAA,8CAAA,mMAA8C,mBAAgB,EAAE,kBAAkB,CAAC,CACrJ,CAAC;QAEF,IAAI,CAAC,cAAc,CAAC,YAAY,6MAAE,wCAAqC,CAAC,MAAM,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QACnH,IAAI,CAAC,cAAc,CAAC,YAAY,6MAAE,wCAAqC,CAAC,MAAM,EAAE,yNAAwB,CAAC,QAAQ,CAAC,CAAC;QACnH,IAAI,CAAC,cAAc,CAAC,aAAa,6MAAE,wCAAqC,CAAC,MAAM,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QACpH,IAAI,CAAC,cAAc,CAAC,cAAc,6MAAE,wCAAqC,CAAC,MAAM,EAAE,yNAAwB,CAAC,QAAQ,CAAC,CAAC;QACrH,IAAI,CAAC,cAAc,CAAC,UAAU,6MAAE,wCAAqC,CAAC,MAAM,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QACjH,IAAI,CAAC,cAAc,CAAC,YAAY,6MAAE,wCAAqC,CAAC,MAAM,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QACnH,IAAI,CAAC,cAAc,CAAC,aAAa,6MAAE,wCAAqC,CAAC,MAAM,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QACpH,IAAI,CAAC,cAAc,CAAC,cAAc,6MAAE,wCAAqC,CAAC,MAAM,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QACrH,IAAI,CAAC,cAAc,CAAC,UAAU,6MAAE,wCAAqC,CAAC,MAAM,EAAE,yNAAwB,CAAC,QAAQ,CAAC,CAAC;QACjH,IAAI,CAAC,cAAc,CAAC,YAAY,6MAAE,wCAAqC,CAAC,MAAM,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QACnH,IAAI,CAAC,cAAc,CAAC,UAAU,6MAAE,wCAAqC,CAAC,MAAM,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QACjH,IAAI,CAAC,cAAc,CAAC,QAAQ,6MAAE,wCAAqC,CAAC,KAAK,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAC9G,IAAI,CAAC,cAAc,CAAC,OAAO,6MAAE,wCAAqC,CAAC,KAAK,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;IACjH,CAAC;IAmOD;;;OAGG,CACa,UAAU,CAAC,KAA6B,EAAA;QACpD,KAAK,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;QAEjD,KAAK,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,CAAC;QAC/C,KAAK,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;QACtC,KAAK,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;QAEzC,KAAK,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,CAAC;QAC/C,KAAK,CAAC,oBAAoB,CAAC,eAAe,CAAC,CAAC;QAC5C,KAAK,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAEpC,KAAK,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAEpC,KAAK,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;QACxC,KAAK,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;QAC9C,KAAK,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAC3C,KAAK,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;QACxC,KAAK,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;QAEjD,KAAK,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;QAC7C,KAAK,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QACpC,KAAK,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QACrC,KAAK,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;QACjD,KAAK,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;QAC9C,KAAK,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,CAAC;QAChD,KAAK,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QAClC,KAAK,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QAElC,KAAK,CAAC,oBAAoB,CAAC,qBAAqB,CAAC,CAAC;QAClD,KAAK,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,CAAC;QAC/C,KAAK,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,CAAC;QAEpD,KAAK,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;QAC1C,KAAK,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAC3C,KAAK,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;QACtC,KAAK,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;QACnC,KAAK,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QAErC,KAAK,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAC3C,KAAK,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAC3C,KAAK,CAAC,oBAAoB,CAAC,kCAAkC,CAAC,CAAC;QAE/D,KAAK,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;QAEzC,KAAK,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;QACjD,KAAK,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;QAEzC,mEAAmE;QACnE,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;IACtD,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,cAA8B,EAAA;QAC/D,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,cAAc,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;YACzC,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,oCAAoC,CAAC,EAAE,MAAM,CAAC,sCAAsC,CAAC,CAAC,CAAC,CAAC;;;aAAA;QACtH,CAAC,MAAM,CAAC;YACJ,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,gCAAgC,CAAC,EAAE,MAAM,CAAC,kCAAkC,CAAC,CAAC,CAAC,CAAC;;;aAAA;QAC9G,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACvD,CAAC;IAED;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,2BAA2B,CAAC;IACvC,CAAC;IAED;;OAEG,CACH,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,IAAI,GAAA;QACX,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,cAAc,GAAA;QACrB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,eAAe,GAAA;QACtB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,OAAO,GAAA;QACd,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,iBAAiB,GAAA;QACxB,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,YAAY,GAAA;QACnB,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,KAAK,GAAA;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,YAAY,GAAA;QACnB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,YAAY,GAAA;QACnB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG,CACH,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG,CACH,IAAW,KAAK,GAAA;QACZ,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IAC7B,CAAC;IAEe,aAAa,CAAC,QAAsB,EAAE,0BAAgE,GAAG,CAAG,CAAD,GAAK,EAAA;QAC5H,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC;YACnC,IAAI,mBAAmB,GAAG,QAAQ,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,WAAW,mMAAK,2BAAwB,CAAC,cAAc,IAAI,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;YAE5J,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBACvB,mBAAmB,GAAG,6LAAI,cAAU,CAAC,gBAAgB,CAAC,CAAC;gBACvD,mBAAmB,CAAC,gBAAgB,+LAAC,2BAAwB,CAAC,cAAc,CAAC,CAAC;YAClF,CAAC;YACD,mBAAmB,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACzB,IAAI,SAAS,GAAG,QAAQ,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,WAAW,mMAAK,2BAAwB,CAAC,IAAI,IAAI,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;YAExI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACb,SAAS,GAAG,8LAAI,aAAU,CAAC,MAAM,CAAC,CAAC;gBACnC,SAAS,CAAC,gBAAgB,+LAAC,2BAAwB,CAAC,IAAI,CAAC,CAAC;YAC9D,CAAC;YACD,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1C,CAAC;IACL,CAAC;IAEe,cAAc,CAAC,OAA4B,EAAE,YAA0B,EAAE,IAAmB,EAAA;QACxG,IAAI,CAAC,IAAI,EAAE,CAAC;YACR,OAAO;QACX,CAAC;QAED,UAAU;QACV,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAC9B,OAAO,CAAC,QAAQ,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC;QAC3C,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QACpD,OAAO,CAAC,QAAQ,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAC;QAChD,OAAO,CAAC,QAAQ,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;QACxC,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QACjE,OAAO,CAAC,QAAQ,CAAC,sBAAsB,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,CAAC,UAAU,CAAC,CAAC;QAEvF,mBAAmB;QACnB,OAAO,CAAC,QAAQ,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QACxC,OAAO,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QAE5D,oBAAoB;QACpB,OAAO,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QACxC,OAAO,CAAC,QAAQ,CAAC,oBAAoB,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAEpD,eAAe;QACf,OAAO,CAAC,QAAQ,CAAC,cAAc,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAC9C,OAAO,CAAC,QAAQ,CAAC,sBAAsB,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QACtD,OAAO,CAAC,QAAQ,CAAC,+BAA+B,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAC/D,OAAO,CAAC,QAAQ,CAAC,+BAA+B,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAC/D,OAAO,CAAC,QAAQ,CAAC,+BAA+B,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAE/D,oBAAoB;QACpB,IAAI,IAAI,CAAC,YAAY,gLAAK,kBAAe,CAAC,qBAAqB,EAAE,CAAC;YAC9D,OAAO,CAAC,QAAQ,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YACnD,OAAO,CAAC,QAAQ,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;QACnD,CAAC,MAAM,IAAI,IAAI,CAAC,YAAY,gLAAK,kBAAe,CAAC,iBAAiB,EAAE,CAAC;YACjE,OAAO,CAAC,QAAQ,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YACnD,OAAO,CAAC,QAAQ,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC;QAClD,CAAC,MAAM,CAAC;YACJ,OAAO,CAAC,QAAQ,CAAC,yBAAyB,EAAE,IAAI,CAAC,CAAC;YAClD,OAAO,CAAC,QAAQ,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;QACnD,CAAC;QAED,eAAe;QACf,MAAM,qBAAqB,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC;QAE9D,OAAO,CAAC,QAAQ,CAAC,YAAY,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;QAC5D,OAAO,CAAC,QAAQ,CAAC,iBAAiB,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QACjD,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;QACvD,OAAO,CAAC,QAAQ,CAAC,gBAAgB,EAAE,qBAAqB,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,qBAAqB,GAAG,GAAG,CAAC,CAAC,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC;QACvI,OAAO,CAAC,QAAQ,CAAC,YAAY,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAE5C,YAAY;QACZ,OAAO,CAAC,QAAQ,CAAC,mBAAmB,EAAE,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;QACvE,OAAO,CAAC,QAAQ,CAAC,mBAAmB,EAAE,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;QACvE,OAAO,CAAC,QAAQ,CAAC,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,CAAC,mBAAmB,IAAI,IAAI,CAAC,0BAA0B,EAAE,IAAI,CAAC,CAAC;QAC/H,OAAO,CAAC,QAAQ,CAAC,oBAAoB,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;QAErE,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAEjC,IAAI,MAAM,CAAC,SAAS,CAAC,+BAA+B,EAAE,CAAC;YACnD,OAAO,CAAC,QAAQ,CAAC,aAAa,EAAE,IAAI,CAAC,wBAAwB,GAAG,GAAG,EAAE,IAAI,CAAC,CAAC;QAC/E,CAAC,MAAM,CAAC;YACJ,OAAO,CAAC,QAAQ,CAAC,aAAa,EAAE,EAAE,GAAG,IAAI,CAAC,wBAAwB,EAAE,IAAI,CAAC,CAAC;QAC9E,CAAC;QAED,OAAO,CAAC,QAAQ,CAAC,oBAAoB,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;QAEpE,WAAW;QACX,OAAO,CAAC,QAAQ,CAAC,0BAA0B,EAAE,IAAI,CAAC,CAAC;QACnD,OAAO,CAAC,QAAQ,CAAC,qCAAqC,EAAE,IAAI,CAAC,CAAC;QAC9D,OAAO,CAAC,QAAQ,CAAC,6BAA6B,EAAE,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC;QAClF,OAAO,CAAC,QAAQ,CAAC,mBAAmB,EAAE,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;QACvE,OAAO,CAAC,QAAQ,CAAC,kBAAkB,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;QACrE,OAAO,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAC5C,OAAO,CAAC,QAAQ,CAAC,oBAAoB,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC;QAEtE,IAAI,IAAI,CAAC,uBAAuB,sKAAI,gBAAa,CAAC,wBAAwB,EAAE,CAAC;YACzE,OAAO,CAAC,QAAQ,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;YAC1C,OAAO,CAAC,QAAQ,CAAC,sBAAsB,EAAE,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACxF,CAAC,MAAM,CAAC;YACJ,OAAO,CAAC,QAAQ,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;YAC3C,OAAO,CAAC,QAAQ,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,OAAO,CAAC,wBAAwB,IAAI,YAAY,CAAC,4BAA4B,EAAE,CAAC;YAChF,YAAY,CAAC,4BAA4B,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;YAC3B,OAAO;QACX,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YACd,SAAS;gMACT,0BAAuB,AAAvB,EAAwB,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,YAAY,CAAC,qBAAqB,CAAC,CAAC;YACxF,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;YAE5B,YAAY;+LACZ,8BAA0B,AAA1B,EAA2B,KAAK,EAAE,OAAO,CAAC,CAAC;QAC/C,CAAC,MAAM,CAAC;YACJ,MAAM,KAAK,GAAG;gBACV,WAAW,EAAE,KAAK;gBAClB,WAAW,EAAE,KAAK;gBAClB,YAAY,EAAE,KAAK;gBACnB,aAAa,EAAE,KAAK;gBACpB,eAAe,EAAE,KAAK;aACzB,CAAC;YAEF,6MAAA,AAAsB,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YAErF,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC;gBACpB,OAAO,CAAC,OAAO,EAAE,CAAC;YACtB,CAAC;QACL,CAAC;IACL,CAAC;IAEe,wBAAwB,CAAC,KAA6B,EAAE,YAA0B,EAAE,OAA4B,EAAE,cAAwB,EAAA;QACtJ,IAAK,IAAI,UAAU,GAAG,CAAC,EAAE,UAAU,GAAG,YAAY,CAAC,qBAAqB,EAAE,UAAU,EAAE,CAAE,CAAC;YACrF,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,UAAU,CAAC,EAAE,CAAC;gBACjC,MAAM;YACV,CAAC;YACD,MAAM,qBAAqB,GAAG,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;gMACrF,qCAAA,AAAkC,EAC9B,UAAU,EACV,KAAK,CAAC,QAAQ,EACd,KAAK,CAAC,QAAQ,EACd,OAAO,CAAC,uBAAuB,GAAG,UAAU,CAAC,EAC7C,cAAc,EACd,qBAAqB,EACrB,OAAO,CAAC,iBAAiB,GAAG,UAAU,CAAC,CAC1C,CAAC;QACN,CAAC;IACL,CAAC;IAEe,OAAO,CAAC,IAAkB,EAAE,YAA0B,EAAE,OAA4B,EAAA;QAChG,IAAI,IAAI,CAAC,uBAAuB,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,EAAE,CAAC;YAC1E,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,OAAO,CAAC,wBAAwB,IAAI,YAAY,CAAC,4BAA4B,EAAE,CAAC;YAChF,IAAI,CAAC,YAAY,CAAC,4BAA4B,CAAC,OAAO,EAAE,EAAE,CAAC;gBACvD,OAAO,KAAK,CAAC;YACjB,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEe,IAAI,CAAC,MAAc,EAAE,YAA0B,EAAE,IAAW,EAAA;QACxE,IAAI,CAAC,IAAI,EAAE,CAAC;YACR,OAAO;QACX,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gMACd,aAAA,AAAU,EAAC,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,YAAY,CAAC,qBAAqB,CAAC,CAAC;QAC9E,CAAC,MAAM,CAAC;gMACJ,YAAA,AAAS,EAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;QAC9D,CAAC;QAED,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,2BAA2B,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;QAElF,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAElE,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;QAE9C,IAAI,YAAY,EAAE,CAAC;YACf,MAAM,CAAC,SAAS,CAAC,kBAAkB,EAAE,YAAY,CAAC,CAAC;QACvD,CAAC;QAED,MAAM,YAAY,GAAG,KAAK,CAAC,oBAAoB,KAAK,CAAC,KAAK,CAAC,uBAAuB,IAAI,IAAI,CAAC,CAAC;QAE5F,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE/D,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC,EAAE,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAEtJ,wBAAwB;QACxB,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAE3C,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,gCAAgC,EAAE,IAAI,CAAC,yBAAyB,EAAE,WAAW,CAAC,CAAC;QAErG,IAAI,YAAY,CAAC,4BAA4B,EAAE,CAAC;YAC5C,YAAY,CAAC,4BAA4B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC3D,CAAC;IACL,CAAC;IAEO,iBAAiB,CAAC,KAA6B,EAAA;QACnD,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC;QACpC,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,MAAM,QAAQ,GAAG,CAAA,EAAA,EAAK,IAAI,CAAC,IAAI,EAAE,CAAC;QAClC,MAAM,QAAQ,GAAG,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,CAAC;QAE9D,cAAc;QACd,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YACd,sBAAsB;YACtB,KAAK,CAAC,wBAAwB,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC,4BAA4B,EAAE,QAAQ,EAAE;gBAC3H,SAAS,EAAE,uBAAuB;aACrC,CAAC,CAAC;YACH,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;YAElB,KAAK,CAAC,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrD,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,QAAQ,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,cAAc,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACzG,KAAK,CAAC,QAAQ,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;YAE/C,KAAK,CAAC,wBAAwB,CAC1B,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC,4BAA4B,EACpF,QAAQ,EACR;gBACI,cAAc,EAAE;oBAAC;wBAAE,MAAM,EAAE,MAAM;wBAAE,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;oBAAA,CAAE;iBAAC;aAC1E,EACD,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAC3B,CAAC;QACN,CAAC;QAED,wBAAwB;QACxB,MAAM,mBAAmB,GAAG,IAAI,GAAG,QAAQ,CAAC,sBAAsB,CAAC;QACnE,IAAI,KAAK,CAAC,sBAAsB,CAAC,mBAAmB,6MAAE,wCAAqC,CAAC,OAAO,CAAC,EAAE,CAAC;YACnG,KAAK,CAAC,iBAAiB,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,mBAAmB,CAAA,GAAA,EAAM,QAAQ,CAAC,sBAAsB,CAAA,GAAA,CAAK,CAAC;QACrI,CAAC;QAED,MAAM,sBAAsB,GAAG,IAAI,GAAG,WAAW,CAAC,sBAAsB,CAAC;QACzE,IAAI,KAAK,CAAC,sBAAsB,CAAC,sBAAsB,6MAAE,wCAAqC,CAAC,OAAO,CAAC,EAAE,CAAC;YACtG,KAAK,CAAC,iBAAiB,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,sBAAsB,CAAA,GAAA,EAAM,WAAW,CAAC,sBAAsB,CAAA,GAAA,CAAK,CAAC;QAC3I,CAAC;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAE,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,UAA8B,CAAC,CAAC,CAAC,IAAI,CAAC;QAE7H,IAAI,eAAe,EAAE,CAAC;YAClB,eAAe,CAAC,mBAAmB,GAAG,IAAI,CAAC,IAAI,CAAC;QACpD,CAAC;QAED,KAAK,CAAC,iBAAiB,IAAI,eAAe,EAAE,gBAAgB,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;QAE1E,IAAI,KAAK,CAAC,sBAAsB,CAAC,oBAAoB,6MAAE,wCAAqC,CAAC,OAAO,EAAE,kCAAkC,CAAC,EAAE,CAAC;YACxI,KAAK,CAAC,YAAY,IAAI,CAAA,mBAAA,CAAqB,CAAC;YAC5C,KAAK,CAAC,YAAY,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAA,qBAAA,EAAwB,QAAQ,CAAC,CAAC,CAAC,wBAAwB,CAAC,CAAC,CAAC,aAAa,CAAA,GAAA,CAAK,CAAC;YAC5I,KAAK,CAAC,YAAY,IAAI,CAAA,QAAA,CAAU,CAAC;QACrC,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,eAAe,EAAE,QAAQ,EAAE;gBAC7E,cAAc,EAAE;oBACZ;wBAAE,MAAM,EAAE,MAAM;wBAAE,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;oBAAA,CAAE;oBACrD;wBAAE,MAAM,EAAE,WAAW;wBAAE,OAAO,EAAE,QAAQ,CAAC,sBAAsB;oBAAA,CAAE;iBACpE;aACJ,CAAC,CAAC;QACP,CAAC,MAAM,CAAC;YACJ,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,UAAU,EAAE,mPAAqC,CAAC,OAAO,CAAC,CAAA,GAAA,EAAM,QAAQ,CAAC,sBAAsB,CAAA,GAAA,CAAK,CAAC;YAC1J,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;gBACxB,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,MAAM,6MAAE,wCAAqC,CAAC,MAAM,CAAC,CAAA,GAAA,EAAM,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAA,GAAA,CAAK,CAAC;YAC1J,CAAC;YACD,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,eAAe,EAAE,QAAQ,EAAE;gBAC7E,SAAS,EAAE,uBAAuB;aACrC,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAEO,qBAAqB,CAAC,KAA6B,EAAA;QACvD,MAAM,QAAQ,GAAG,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,CAAC;QAE9D,IAAI,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,iDAAiD,CAAC,CAAC,CAAC,CAAA,0CAAA,CAA4C,CAAC;QAEvH,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAAC,UAAU,CAAC;QACpG,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC;QAEtF,IAAI,IAAI,CAAA;sBACM,KAAK,CAAC,OAAO,CAAA,CAAA,EAAI,WAAW,CAAA;;uBAE3B,KAAK,CAAC,OAAO,CAAA;uBACb,KAAK,CAAC,OAAO,CAAA;;;;uBAIb,KAAK,CAAC,OAAO,CAAA,CAAA,EAAI,OAAO,CAAA;uBACxB,KAAK,CAAC,OAAO,CAAA;;;;cAItB,KAAK,CAAC,gBAAgB,CAAC,eAAe,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA;cACtF,KAAK,CAAC,gBAAgB,CAAC,OAAO,6MAAE,wCAAqC,CAAC,KAAK,CAAC,CAAA,4BAAA,CAA8B,CAAC;QAEjH,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,wBAAwB,CAAC,KAA6B,EAAA;QAC1D,MAAM,QAAQ,GAAG,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,CAAC;QAC9D,IAAI,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,yCAAyC,CAAC,CAAC,CAAC,CAAA,kCAAA,CAAoC,CAAC;QAEvG,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC;QAEvF,IAAI,IAAI,CAAA;;sBAEM,KAAK,CAAC,OAAO,CAAA,CAAA,EAAI,EAAE,CAAA;sBACnB,KAAK,CAAC,OAAO,CAAA;;iBAElB,CAAC;QAEV,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,oBAAoB,CAAC,KAA6B,EAAA;QACtD,MAAM,QAAQ,GAAG,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,CAAC;QAC9D,IAAI,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,+CAA+C,CAAC,CAAC,CAAC,CAAA,wCAAA,CAA0C,CAAC;QACnH,MAAM,WAAW,GAAG,IAAI,CAAC;QAEzB,IAAI,CAAC,gCAAgC,GAAG,KAAK,CAAC,oBAAoB,CAAC,6BAA6B,CAAC,CAAC;QAClG,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,gCAAgC,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAC;QAEnH,IAAI,CAAC,yBAAyB,GAAG,KAAK,CAAC,oBAAoB,CAAC,sBAAsB,CAAC,CAAC;QACpF,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,yBAAyB,6MAAE,wCAAqC,CAAC,KAAK,CAAC,CAAC;QAE1G,MAAM,UAAU,GAAG,CAAC,CAAC,CAAC,yEAAyE;QAC/F,MAAM,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,KAAK,IAAI,GAAG,CAAC;QACnE,mDAAmD;QACnD,mBAAmB;QACnB,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,GAAG,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC;QAEhE,IAAI,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,WAAW,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA;cACvF,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA,IAAA,EAAO,KAAK,CAAC,OAAO,EAAE,CAAA,0BAAA,EAA6B,KAAK,CAAC,OAAO,CAAA,CAAA,EAAI,IAAI,CAAC,QAAQ,CAAC,sBAAsB,CAAA,EAAA,EAAK,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAA,EAAA,EAAK,IAAI,CAAC,iBAAiB,CAAC,sBAAsB,IAAI,KAAK,CAAA,EAAA,EAAK,EAAE,CAAA;;;;;oBAKjO,CAAC,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gCAAgC,CAAA;;oBAErE,CAAC,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,yBAAyB,CAAA;;;wBAG1D,KAAK,CAAC,OAAO,CAAA;;;wBAGb,KAAK,CAAC,OAAO,CAAA,SAAA,EAAY,WAAW,CAAA;wBACpC,KAAK,CAAC,OAAO,CAAA;;;;;;;;;;cAUvB,KAAK,CAAC,gBAAgB,CAAC,cAAc,6MAAE,wCAAqC,CAAC,KAAK,CAAC,CAAA;cACnF,KAAK,CAAC,gBAAgB,CAAC,WAAW,EAAE,mPAAqC,CAAC,KAAK,CAAC,CAAA;cAChF,KAAK,CAAC,gBAAgB,CAAC,kBAAkB,6MAAE,wCAAqC,CAAC,KAAK,CAAC,CAAA;;;;;;;qBAOhF,CAAC;QAEd,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,WAAW,CAAC,KAA6B,EAAA;QACxD,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC;QACrC,MAAM,QAAQ,GAAG,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,CAAC;QAE9D,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAChC,IAAI,CAAC,uBAAuB,uKAAG,4BAAA,AAAyB,EAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC1E,CAAC;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAE,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,UAA8B,CAAC,CAAC,CAAC,IAAI,CAAC;QAE7H,IAAI,eAAe,EAAE,CAAC;YAClB,kEAAkE;YAClE,eAAe,CAAC,4BAA4B,GAAG,IAAI,CAAC,aAAa,CAAC;YAClE,eAAe,CAAC,6BAA6B,GAAG,IAAI,CAAC,cAAc,CAAC;YACpE,eAAe,CAAC,0BAA0B,GAAG,IAAI,CAAC,WAAW,CAAC;YAC9D,eAAe,CAAC,mBAAmB,GAAG,IAAI,CAAC,IAAI,CAAC;QACpD,CAAC;QAED,IAAI,KAAK,CAAC,MAAM,mMAAK,2BAAwB,CAAC,QAAQ,EAAE,CAAC;YACrD,SAAS;YACT,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAE9B,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,WAAW;QACX,KAAK,CAAC,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjD,KAAK,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9C,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3C,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAChC,KAAK,CAAC,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrD,CAAC;QAED,MAAM,QAAQ,GAAG,CAAA,EAAA,EAAK,IAAI,CAAC,IAAI,EAAE,CAAC;QAClC,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC;QAE3C,IAAI,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAC;QAChE,IAAI,kBAAkB,GAAG,IAAI,CAAC,WAAW,CAAC,sBAAsB,CAAC;QACjE,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAChC,eAAe,GAAG,KAAK,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;YAC/D,KAAK,CAAC,aAAa,CACf,oBAAoB,EACpB,QAAQ,CAAC,CAAC,CAAC,CAAA,aAAA,EAAgB,eAAe,CAAA,KAAA,EAAQ,KAAK,CAAC,OAAO,CAAA,GAAA,CAAK,CAAC,CAAC,CAAC,CAAA,IAAA,EAAO,KAAK,CAAC,OAAO,CAAA,CAAA,EAAI,eAAe,CAAA,GAAA,CAAK,EACnH,QAAQ,CACX,CAAC;YACF,KAAK,CAAC,iBAAiB,IAAI,GAAG,eAAe,CAAA,GAAA,EAAM,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAA,OAAA,CAAS,CAAC;YAEtG,kBAAkB,GAAG,KAAK,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,CAAC;YACrE,KAAK,CAAC,aAAa,CACf,qBAAqB,EACrB,QAAQ,CAAC,CAAC,CAAC,CAAA,aAAA,EAAgB,kBAAkB,CAAA,KAAA,EAAQ,KAAK,CAAC,OAAO,CAAA,GAAA,CAAK,CAAC,CAAC,CAAC,CAAA,IAAA,EAAO,KAAK,CAAC,OAAO,CAAA,CAAA,EAAI,kBAAkB,CAAA,GAAA,CAAK,EACzH,QAAQ,CACX,CAAC;YACF,KAAK,CAAC,iBAAiB,IAAI,GAAG,kBAAkB,CAAA,GAAA,EAAM,IAAI,CAAC,WAAW,CAAC,sBAAsB,CAAA,GAAA,CAAK,CAAC;YAEnG,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,eAAe,EAAE,QAAQ,EAAE;gBAC7E,SAAS,EAAE,uBAAuB;gBAClC,gBAAgB,EAAE,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAA,SAAA,EAAY,IAAI,CAAC,aAAa,CAAC,sBAAsB,EAAE,CAAC,CAAC,CAAC,SAAS;aACxH,CAAC,CAAC;YAEH,KAAK,CAAC,iBAAiB,IAAI,CAAA,mBAAA,CAAqB,CAAC;YACjD,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,oBAAoB,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA,OAAA,EAAU,KAAK,CAAC,OAAO,CAAA,MAAA,EAAS,KAAK,CAAC,OAAO,CAAA,CAAA,EAAI,QAAQ,CAAC,CAAC,CAAC,yBAAyB,CAAC,CAAC,CAAC,iBAAiB,CAAA,QAAA,EAAW,KAAK,CAAC,OAAO,CAAA,gCAAA,CAAkC,CAAC;YAC7R,KAAK,CAAC,iBAAiB,IAAI,CAAA,QAAA,CAAU,CAAC;QAC1C,CAAC,MAAM,CAAC;YACJ,eAAe,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,eAAe,CAAC;YACtE,kBAAkB,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,kBAAkB,CAAC;QAChF,CAAC;QAED,IAAI,CAAC,2BAA2B,GAAG,KAAK,CAAC,oBAAoB,CAAC,wBAAwB,CAAC,CAAC;QAExF,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAEvD,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,iBAAiB,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,gBAAgB,CAAC;QAC7G,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,gBAAgB,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,gBAAgB,IAAI,IAAI,CAAC,YAAY,CAAC;QAEvG,KAAK,CAAC,cAAc,CAAC,KAAK,EAAE,+CAA+C,EAAE,+BAA+B,CAAC,CAAC;QAC9G,KAAK,CAAC,cAAc,CAAC,aAAa,EAAE,iDAAiD,CAAC,CAAC;QAEvF,KAAK,CAAC,sBAAsB,CAAC,YAAY,4MAAE,yCAAqC,CAAC,OAAO,EAAE,kCAAkC,CAAC,CAAC;QAC9H,KAAK,CAAC,sBAAsB,CAAC,kBAAkB,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAC;QAEhG,4BAA4B;QAC5B,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACtC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAChC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAC1C,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACzC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACzC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QACjD,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAChD,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QACjD,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACxC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAC9C,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAEvC,EAAE;QACF,WAAW;QACX,EAAE;QACF,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YACd,sBAAsB;YACtB,KAAK,CAAC,wBAAwB,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,0BAA0B,EAAE,QAAQ,EAAE;gBACvH,SAAS,EAAE,uBAAuB;gBAClC,gBAAgB,EAAE,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS;aAC3E,CAAC,CAAC;QACP,CAAC,MAAM,CAAC;YACJ,KAAK,CAAC,wBAAwB,CAC1B,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,0BAA0B,EAChF,QAAQ,EACR;gBACI,cAAc,EAAE;oBAAC;wBAAE,MAAM,EAAE,MAAM;wBAAE,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;oBAAA,CAAE;iBAAC;aAC1E,EACD,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAC3B,CAAC;QACN,CAAC;QAED,KAAK,CAAC,wBAAwB,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC;QAC5D,KAAK,CAAC,wBAAwB,CAAC,oBAAoB,EAAE,QAAQ,CAAC,CAAC;QAC/D,KAAK,CAAC,wBAAwB,CAAC,oBAAoB,EAAE,QAAQ,CAAC,CAAC;QAC/D,KAAK,CAAC,wBAAwB,CAAC,4BAA4B,EAAE,QAAQ,CAAC,CAAC;QACvE,KAAK,CAAC,wBAAwB,CAAC,0BAA0B,EAAE,QAAQ,CAAC,CAAC;QAErE,KAAK,CAAC,wBAAwB,CAAC,0BAA0B,EAAE,QAAQ,CAAC,CAAC;QAErE,KAAK,CAAC,wBAAwB,CAAC,iCAAiC,EAAE,QAAQ,CAAC,CAAC;QAE5E,KAAK,CAAC,wBAAwB,CAAC,mCAAmC,EAAE,QAAQ,CAAC,CAAC;QAC9E,KAAK,CAAC,wBAAwB,CAAC,kBAAkB,EAAE,QAAQ,EAAE;YACzD,cAAc,EAAE;gBAAC;oBAAE,MAAM,EAAE,uBAAuB;oBAAE,OAAO,EAAE,eAAe,EAAE,iBAAiB,IAAI,sBAAsB;gBAAA,CAAE;aAAC;SAC/H,CAAC,CAAC;QACH,KAAK,CAAC,wBAAwB,CAAC,uBAAuB,EAAE,QAAQ,CAAC,CAAC;QAElE,KAAK,CAAC,wBAAwB,CAAC,4BAA4B,EAAE,QAAQ,CAAC,CAAC;QAEvE,KAAK,CAAC,wBAAwB,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC;QAE5D,KAAK,CAAC,wBAAwB,CAAC,uBAAuB,EAAE,QAAQ,CAAC,CAAC;QAClE,KAAK,CAAC,wBAAwB,CAAC,sBAAsB,EAAE,QAAQ,CAAC,CAAC;QACjE,KAAK,CAAC,wBAAwB,CAAC,0BAA0B,EAAE,QAAQ,CAAC,CAAC;QACrE,KAAK,CAAC,wBAAwB,CAAC,sBAAsB,EAAE,QAAQ,CAAC,CAAC;QACjE,KAAK,CAAC,wBAAwB,CAAC,qBAAqB,EAAE,QAAQ,CAAC,CAAC;QAEhE,EAAE;QACF,OAAO;QACP,EAAE;QAEF,KAAK,CAAC,sBAAsB,CAAC,oBAAoB,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAC;QAElG,IAAI,eAAe,EAAE,wBAAwB,EAAE,CAAC;YAC5C,KAAK,CAAC,iBAAiB,IAAI,eAAe,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QACvE,CAAC;QAED,kFAAkF;QAClF,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;QAE5D,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA,aAAA,EAAgB,kBAAkB,CAAA,IAAA,CAAM,CAAC;QAEhK,IAAI,KAAK,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,EAAE,CAAC;YAChD,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,gBAAgB,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA,aAAA,EAAgB,IAAI,CAAC,cAAc,CAAC,sBAAsB,CAAA,GAAA,EAAM,eAAe,CAAA,QAAA,CAAU,CAAC;QACnN,CAAC;QAED,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,kBAAkB,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA,GAAA,EAAM,IAAI,CAAC,aAAa,CAAA,OAAA,CAAS,CAAC;QAEzJ,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,SAAS,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA,GAAA,EAAM,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,YAAY,GAAG,aAAa,CAAC,sBAAsB,GAAG,OAAO,CAAC,CAAC,CAAC,kBAAkB,CAAA,GAAA,CAAK,CAAC;QAExO,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAEpE,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,iBAAiB,6MAAE,wCAAqC,CAAC,KAAK,CAAC,CAAC;QAElG,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,qBAAqB,EAAE,QAAQ,EAAE;YACnF,cAAc,EAAE;gBACZ;oBAAE,MAAM,EAAE,aAAa;oBAAE,OAAO,EAAE,eAAe,GAAG,MAAM;gBAAA,CAAE;gBAC5D;oBAAE,MAAM,EAAE,iBAAiB;oBAAE,OAAO,EAAE,IAAI,CAAC,iBAAiB;gBAAA,CAAE;aACjE;SACJ,CAAC,CAAC;QAEH,gFAAgF;QAChF,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;QAE7D,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;QAEhF,oEAAoE;QACpE,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;QAEhE,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,sBAAsB,EAAE,QAAQ,CAAC,CAAC;QAExF,uEAAuE;QACvE,KAAK,CAAC,iBAAiB,IAAI,CAAA;kBACjB,KAAK,CAAC,gBAAgB,CAAC,aAAa,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA,OAAA,EAAU,KAAK,CAAC,OAAO,CAAA;oBACzG,CAAC;QAEb,6EAA6E;QAC7E,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QAE5D,gFAAgF;QAChF,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,sBAAsB,EAAE,QAAQ,EAAE;YACpF,cAAc,EAAE;gBACZ;oBAAE,MAAM,EAAE,uBAAuB;oBAAE,OAAO,EAAE,eAAe,EAAE,iBAAiB,IAAI,sBAAsB;gBAAA,CAAE;gBAC1G;oBAAE,MAAM,EAAE,mBAAmB;oBAAE,OAAO,EAAE,eAAe,EAAE,aAAa,IAAI,kBAAkB;gBAAA,CAAE;aACjG;SACJ,CAAC,CAAC;QAEH,mFAAmF;QACnF,MAAM,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAE,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,UAA8B,CAAC,CAAC,CAAC,IAAI,CAAC;QAE7H,IAAI,eAAe,EAAE,CAAC;YAClB,eAAe,CAAC,4BAA4B,GAAG,IAAI,CAAC,aAAa,CAAC;YAClE,eAAe,CAAC,0BAA0B,GAAG,IAAI,CAAC,WAAW,CAAC;YAE9D,KAAK,CAAC,iBAAiB,IAAI,eAAe,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;QACjG,CAAC;QAED,mFAAmF;QACnF,IAAI,eAAe,IAAI,eAAe,CAAC,UAAU,EAAE,CAAC;YAChD,KAAK,CAAC,iBAAiB,IAAI,eAAe,CAAC,OAAO,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC,CAAC,kCAAkC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;QAChI,CAAC;QAED,KAAK,CAAC,wBAAwB,CAAC,oBAAoB,EAAE,QAAQ,EAAE;YAC3D,cAAc,EAAE;gBACZ;oBAAE,MAAM,EAAE,0BAA0B;oBAAE,OAAO,EAAE,4BAA4B;gBAAA,CAAE;gBAC7E;oBAAE,MAAM,EAAE,mBAAmB;oBAAE,OAAO,EAAE,eAAe,EAAE,aAAa,IAAI,kBAAkB;gBAAA,CAAE;gBAC9F;oBAAE,MAAM,EAAE,0BAA0B;oBAAE,OAAO,EAAE,eAAe,EAAE,gBAAgB,IAAI,yBAAyB;gBAAA,CAAE;gBAC/G;oBAAE,MAAM,EAAE,2BAA2B;oBAAE,OAAO,EAAE,eAAe,EAAE,qBAAqB,IAAI,0BAA0B;gBAAA,CAAE;gBACtH;oBAAE,MAAM,EAAE,uBAAuB;oBAAE,OAAO,EAAE,eAAe,EAAE,iBAAiB,IAAI,sBAAsB;gBAAA,CAAE;gBAC1G;oBAAE,MAAM,EAAE,uBAAuB;oBAAE,OAAO,EAAE,eAAe,EAAE,yBAAyB,IAAI,sBAAsB;gBAAA,CAAE;gBAClH;oBAAE,MAAM,EAAE,2BAA2B;oBAAE,OAAO,EAAE,eAAe,EAAE,+BAA+B,IAAI,0BAA0B;gBAAA,CAAE;gBAChI;oBAAE,MAAM,EAAE,2BAA2B;oBAAE,OAAO,EAAE,eAAe,EAAE,6BAA6B,IAAI,0BAA0B;gBAAA,CAAE;aACjI;SACJ,CAAC,CAAC;QAEH,mFAAmF;QACnF,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,sBAAsB,EAAE,QAAQ,EAAE;YACpF,cAAc,EAAE;gBAAC;oBAAE,MAAM,EAAE,6BAA6B;oBAAE,OAAO,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gCAAgC;gBAAA,CAAE;aAAC;SAC9I,CAAC,CAAC;QACH,wEAAwE;QACxE,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAE,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,UAAyB,CAAC,CAAC,CAAC,IAAI,CAAC;QAEzG,IAAI,UAAU,EAAE,CAAC;YACb,KAAK,CAAC,iBAAiB,IAAI,UAAU,CAAC,OAAO,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;QAC1E,CAAC;QAED,KAAK,CAAC,wBAAwB,CAAC,eAAe,EAAE,QAAQ,EAAE;YACtD,cAAc,EAAE;gBACZ;oBAAE,MAAM,EAAE,mBAAmB;oBAAE,OAAO,EAAE,eAAe,EAAE,aAAa,IAAI,kBAAkB;gBAAA,CAAE;gBAC9F;oBAAE,MAAM,EAAE,uBAAuB;oBAAE,OAAO,EAAE,eAAe,EAAE,iBAAiB,IAAI,sBAAsB;gBAAA,CAAE;gBAC1G;oBAAE,MAAM,EAAE,uBAAuB;oBAAE,OAAO,EAAE,eAAe,EAAE,yBAAyB,IAAI,sBAAsB;gBAAA,CAAE;gBAClH;oBAAE,MAAM,EAAE,2BAA2B;oBAAE,OAAO,EAAE,eAAe,EAAE,+BAA+B,IAAI,0BAA0B;gBAAA,CAAE;aACnI;SACJ,CAAC,CAAC;QAEH,4EAA4E;QAC5E,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAE,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,UAA6B,CAAC,CAAC,CAAC,IAAI,CAAC;QAEzH,KAAK,CAAC,iBAAiB,gMAAI,iBAAc,CAAC,sBAAsB,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;QAExF,4EAA4E;QAC5E,MAAM,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,CAAE,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,UAA+B,CAAC,CAAC,CAAC,IAAI,CAAC;QACjI,KAAK,CAAC,iBAAiB,kMAAI,mBAAgB,CAAC,OAAO,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;QAE7E,KAAK,CAAC,wBAAwB,CAAC,qBAAqB,EAAE,QAAQ,EAAE;YAC5D,cAAc,EAAE,EAAE;SACrB,CAAC,CAAC;QAEH,wEAAwE;QACxE,MAAM,gBAAgB,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC;QAC3F,MAAM,iCAAiC,GACnC,IAAI,CAAC,eAAe,CAAC,WAAW,IAAI,AAAC,IAAI,CAAC,eAAe,CAAC,cAAc,EAAE,UAAiC,CAAC,AAAD,YAAa,EAAE,WAAW,CAAC;QAC1I,MAAM,8BAA8B,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,UAA8B,CAAA,CAAC,YAAY,CAAC,WAAW,CAAC;QAC/J,IAAI,aAAa,GAAG,iCAAiC,IAAK,AAAD,CAAE,IAAI,CAAC,eAAe,CAAC,WAAW,IAAI,8BAA8B,CAAC,CAAC;QAE/H,KAAK,CAAC,iBAAiB,gMAAI,iBAAc,CAAC,OAAO,CAAC,KAAK,EAAE,cAAc,EAAE,eAAe,EAAE,eAAe,EAAE,gBAAgB,EAAE,aAAa,EAAE,kBAAkB,CAAC,CAAC;QAEhK,IAAI,gBAAgB,EAAE,CAAC;YACnB,aAAa,GAAG,cAAc,EAAE,YAAY,CAAC,WAAW,IAAI,KAAK,CAAC;QACtE,CAAC;QAED,KAAK,CAAC,wBAAwB,CAAC,mBAAmB,EAAE,QAAQ,EAAE;YAC1D,cAAc,EAAE;gBACZ;oBAAE,MAAM,EAAE,0BAA0B;oBAAE,OAAO,EAAE,4BAA4B;gBAAA,CAAE;gBAC7E;oBAAE,MAAM,EAAE,mBAAmB;oBAAE,OAAO,EAAE,eAAe,EAAE,aAAa,IAAI,kBAAkB;gBAAA,CAAE;gBAC9F;oBAAE,MAAM,EAAE,0BAA0B;oBAAE,OAAO,EAAE,eAAe,EAAE,gBAAgB,IAAI,yBAAyB;gBAAA,CAAE;gBAC/G;oBAAE,MAAM,EAAE,2BAA2B;oBAAE,OAAO,EAAE,eAAe,EAAE,qBAAqB,IAAI,0BAA0B;gBAAA,CAAE;gBACtH;oBAAE,MAAM,EAAE,uBAAuB;oBAAE,OAAO,EAAE,eAAe,EAAE,iBAAiB,IAAI,sBAAsB;gBAAA,CAAE;gBAC1G;oBAAE,MAAM,EAAE,uBAAuB;oBAAE,OAAO,EAAE,eAAe,EAAE,yBAAyB,IAAI,sBAAsB;gBAAA,CAAE;gBAClH;oBAAE,MAAM,EAAE,2BAA2B;oBAAE,OAAO,EAAE,eAAe,EAAE,+BAA+B,IAAI,0BAA0B;gBAAA,CAAE;gBAChI;oBAAE,MAAM,EAAE,qBAAqB;oBAAE,OAAO,EAAE,aAAa,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,iBAAiB;gBAAA,CAAE;aACrG;SACJ,CAAC,CAAC;QAEH,wFAAwF;QACxF,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,qBAAqB,EAAE,QAAQ,EAAE;YACnF,cAAc,EAAE;gBACZ;oBAAE,MAAM,EAAE,uBAAuB;oBAAE,OAAO,EAAE,eAAe,EAAE,iBAAiB,IAAI,sBAAsB;gBAAA,CAAE;gBAC1G;oBAAE,MAAM,EAAE,mBAAmB;oBAAE,OAAO,EAAE,eAAe,EAAE,aAAa,IAAI,kBAAkB;gBAAA,CAAE;gBAC9F;oBAAE,MAAM,EAAE,+BAA+B;oBAAE,OAAO,EAAE,oBAAoB;gBAAA,CAAE;aAC7E;SACJ,CAAC,CAAC;QAEH,wFAAwF;QACxF,MAAM,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAE,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,UAA8B,CAAC,CAAC,CAAC,IAAI,CAAC;QAC7H,MAAM,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,GAC5C,AAAC,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,UAA8B,CAAA,AAAC,UAAU,CAAC,cAAc,EAAE,UAA8B,GAC1H,IAAI,CAAC;QAEX,IAAI,eAAe,EAAE,CAAC;YAClB,eAAe,CAAC,mBAAmB,GAAG,IAAI,CAAC,IAAI,CAAC;YAChD,eAAe,CAAC,gCAAgC,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAC9E,CAAC;QAED,KAAK,CAAC,iBAAiB,IAAI,+MAAe,CAAC,OAAO,CAAC,KAAK,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe,CAAC,CAAC;QAE7G,KAAK,CAAC,wBAAwB,CAAC,oBAAoB,EAAE,QAAQ,EAAE;YAC3D,cAAc,EAAE;gBACZ;oBAAE,MAAM,EAAE,mBAAmB;oBAAE,OAAO,EAAE,eAAe,EAAE,aAAa,IAAI,kBAAkB;gBAAA,CAAE;gBAC9F;oBAAE,MAAM,EAAE,0BAA0B;oBAAE,OAAO,EAAE,eAAe,EAAE,gBAAgB,IAAI,yBAAyB;gBAAA,CAAE;gBAC/G;oBAAE,MAAM,EAAE,2BAA2B;oBAAE,OAAO,EAAE,eAAe,EAAE,qBAAqB,IAAI,0BAA0B;gBAAA,CAAE;gBACtH;oBAAE,MAAM,EAAE,sBAAsB;oBAAE,OAAO,EAAE,eAAe,EAAE,aAAa,IAAI,qBAAqB;gBAAA,CAAE;gBACpG;oBAAE,MAAM,EAAE,0BAA0B;oBAAE,OAAO,EAAE,eAAe,EAAE,yBAAyB,IAAI,yBAAyB;gBAAA,CAAE;gBACxH;oBAAE,MAAM,EAAE,8BAA8B;oBAAE,OAAO,EAAE,eAAe,EAAE,+BAA+B,IAAI,6BAA6B;gBAAA,CAAE;gBACtI;oBAAE,MAAM,EAAE,6BAA6B;oBAAE,OAAO,EAAE,eAAe,EAAE,gBAAgB,IAAI,4BAA4B;gBAAA,CAAE;aACxH;SACJ,CAAC,CAAC;QAEH,wFAAwF;QACxF,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,wBAAwB,EAAE,QAAQ,CAAC,CAAC;QAE1F,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,eAAe,EAAE,QAAQ,EAAE;gBAC7E,cAAc,EAAE;oBACZ;wBAAE,MAAM,EAAE,MAAM;wBAAE,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;oBAAA,CAAE;oBACrD;wBAAE,MAAM,EAAE,IAAI,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE,CAAA,UAAA,CAAY,EAAE,GAAG,CAAC;wBAAE,OAAO,EAAE,eAAe,GAAG,MAAM;oBAAA,CAAE;oBAChH;wBAAE,MAAM,EAAE,+BAA+B;wBAAE,OAAO,EAAE,oBAAoB;oBAAA,CAAE;iBAC7E;aACJ,CAAC,CAAC;QACP,CAAC,MAAM,CAAC;YACJ,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,eAAe,EAAE,QAAQ,EAAE;gBAC7E,SAAS,EAAE,uBAAuB;gBAClC,gBAAgB,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE,CAAA,WAAA,EAAc,eAAe,CAAA,mDAAA,CAAqD;aAC3I,CAAC,CAAC;QACP,CAAC;QAED,sFAAsF;QACtF,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,4BAA4B,EAAE,QAAQ,CAAC,CAAC;QAE9F,mEAAmE;QACnE,KAAK,CAAC,iBAAiB,IAAI,CAAA,QAAA,CAAU,CAAC,CAAC,QAAQ;QAE/C,wFAAwF;QACxF,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAA,IAAA,EAAO,KAAK,CAAC,OAAO,CAAA,YAAA,CAAc,CAAC;QAE9H,IAAI,sBAAsB,8KAAG,kBAAe,CAAC,+BAA+B,CAAC,QAAQ,EAAE,CAAC;QAExF,IAAI,sBAAsB,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YAC7C,sBAAsB,IAAI,GAAG,CAAC;QAClC,CAAC;QAED,IAAI,cAAc,GAAG;YACjB;gBAAE,MAAM,EAAE,mEAAmE;gBAAE,OAAO,EAAE,EAAE;YAAA,CAAE;YAC5F;gBAAE,MAAM,EAAE,IAAI,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAA,aAAA,CAAe,EAAE,GAAG,CAAC;gBAAE,OAAO,EAAE,OAAO,GAAG,CAAA,GAAA,EAAM,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAA,gBAAA,CAAkB;YAAA,CAAE;YAClJ;gBAAE,MAAM,EAAE,IAAI,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAA,eAAA,CAAiB,EAAE,GAAG,CAAC;gBAAE,OAAO,EAAE,sBAAsB;YAAA,CAAE;SAChH,CAAC;QAEF,IAAI,QAAQ,EAAE,CAAC;YACX,cAAc,CAAC,CAAC,CAAC,GAAG;gBAAE,MAAM,EAAE,2EAA2E;gBAAE,OAAO,EAAE,EAAE;YAAA,CAAE,CAAC;QAC7H,CAAC;QAED,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,8BAA8B,EAAE,QAAQ,EAAE;YAC5F,cAAc,EAAE,cAAc;SACjC,CAAC,CAAC;QAEH,wFAAwF;QACxF,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,+BAA+B,EAAE,QAAQ,EAAE;YAC7F,cAAc,EAAE;gBAAC;oBAAE,MAAM,EAAE,gBAAgB;oBAAE,OAAO,EAAE,CAAA,IAAA,EAAO,KAAK,CAAC,OAAO,CAAA,IAAA,CAAM;gBAAA,CAAE;aAAC;SACtF,CAAC,CAAC;QAEH,gFAAgF;QAChF,IAAI,QAAQ,EAAE,CAAC;YACX,cAAc,GAAG;gBAAC;oBAAE,MAAM,EAAE,kBAAkB;oBAAE,OAAO,EAAE,IAAI;gBAAA,CAAE;aAAC,CAAC;QACrE,CAAC,MAAM,CAAC;YACJ,cAAc,GAAG;gBAAC;oBAAE,MAAM,EAAE,aAAa;oBAAE,OAAO,EAAE,IAAI;gBAAA,CAAE;aAAC,CAAC;QAChE,CAAC;QAED,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,yBAAyB,EAAE,QAAQ,EAAE;YACvF,cAAc,EAAE,cAAc;SACjC,CAAC,CAAC;QAEH,6EAA6E;QAE7E,MAAM,WAAW,GAAG,QAAQ,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC,cAAc,CAAC;QACxE,cAAc,GAAG;YACb;gBAAE,MAAM,EAAE,IAAI,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE,CAAA,QAAA,CAAU,EAAE,GAAG,CAAC;gBAAE,OAAO,EAAE,IAAI,CAAC,aAAa;YAAA,CAAE;YACxG;gBAAE,MAAM,EAAE,IAAI,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE,CAAA,UAAA,CAAY,EAAE,GAAG,CAAC;gBAAE,OAAO,EAAE,eAAe;YAAA,CAAE;YACvG;gBAAE,MAAM,EAAE,+BAA+B;gBAAE,OAAO,EAAE,oBAAoB;YAAA,CAAE;YAC1E;gBACI,MAAM,EAAE,sBAAsB;gBAC9B,OAAO,EAAE,CAAA,IAAA,EAAO,KAAK,CAAC,OAAO,CAAA,OAAA,EAAU,WAAW,CAAA,oBAAA,EAAuB,WAAW,CAAA,QAAA,CAAU;aACjG;SACJ,CAAC;QACF,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,UAAU,EAAE,QAAQ,EAAE;YACxE,cAAc,EAAE,cAAc;SACjC,CAAC,CAAC;QAEH,6EAA6E;QAC7E,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAE,CAAC;YACjC,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;gBACtB,MAAM,KAAK,GAAG,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBAC/C,IAAI,KAAK,EAAE,CAAC;oBACR,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,KAAK,CAAC;oBACpC,IAAI,UAAU,EAAE,CAAC;wBACb,KAAK,CAAC,iBAAiB,IAAI,CAAA,IAAA,EAAO,UAAU,CAAA,EAAA,CAAI,CAAC;oBACrD,CAAC;oBACD,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA,GAAA,EAAM,OAAO,CAAA,GAAA,CAAK,CAAC;oBAC7E,IAAI,UAAU,EAAE,CAAC;wBACb,KAAK,CAAC,iBAAiB,IAAI,CAAA,OAAA,CAAS,CAAC;wBACrC,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA,OAAA,EAAU,KAAK,CAAC,OAAO,CAAA,OAAA,CAAS,CAAC;wBAC3F,KAAK,CAAC,iBAAiB,IAAI,CAAA,QAAA,CAAU,CAAC;oBAC1C,CAAC;gBACL,CAAC,MAAM,CAAC;oBACJ,KAAK,CAAC,UAAU,CAAC,eAAe,CAAC,CAAA,6BAAA,EAAgC,MAAM,CAAC,IAAI,CAAA,6BAAA,CAA+B,CAAC,CAAC;gBACjH,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,mBAAmB,GAAA;QAClC,IAAI,UAAU,GAAG,KAAK,CAAC,mBAAmB,EAAE,CAAC;QAE7C,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,gBAAA,EAAmB,IAAI,CAAC,YAAY,CAAA,GAAA,CAAK,CAAC;QACjF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,gBAAA,EAAmB,IAAI,CAAC,YAAY,CAAA,GAAA,CAAK,CAAC;QACjF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,mBAAA,EAAsB,IAAI,CAAC,eAAe,CAAA,GAAA,CAAK,CAAC;QACvF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,oBAAA,EAAuB,IAAI,CAAC,gBAAgB,CAAA,GAAA,CAAK,CAAC;QACzF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,wBAAA,EAA2B,IAAI,CAAC,oBAAoB,CAAA,GAAA,CAAK,CAAC;QACjG,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,wBAAA,EAA2B,IAAI,CAAC,oBAAoB,CAAA,GAAA,CAAK,CAAC;QACjG,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,8BAAA,EAAiC,IAAI,CAAC,0BAA0B,CAAA,GAAA,CAAK,CAAC;QAC7G,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,qBAAA,EAAwB,IAAI,CAAC,iBAAiB,CAAA,GAAA,CAAK,CAAC;QAC3F,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,4BAAA,EAA+B,IAAI,CAAC,wBAAwB,CAAA,GAAA,CAAK,CAAC;QACzG,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,yBAAA,EAA4B,IAAI,CAAC,qBAAqB,CAAA,GAAA,CAAK,CAAC;QACnG,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,wBAAA,EAA2B,IAAI,CAAC,oBAAoB,CAAA,GAAA,CAAK,CAAC;QACjG,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,uBAAA,EAA0B,IAAI,CAAC,mBAAmB,CAAA,GAAA,CAAK,CAAC;QAC/F,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,SAAA,EAAY,IAAI,CAAC,KAAK,CAAA,GAAA,CAAK,CAAC;QACnE,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,sBAAA,EAAyB,IAAI,CAAC,kBAAkB,CAAA,GAAA,CAAK,CAAC;QAC7F,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,aAAA,EAAgB,IAAI,CAAC,SAAS,CAAA,GAAA,CAAK,CAAC;QAC3E,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,cAAA,EAAiB,IAAI,CAAC,UAAU,CAAA,GAAA,CAAK,CAAC;QAC7E,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,eAAA,EAAkB,IAAI,CAAC,WAAW,CAAA,GAAA,CAAK,CAAC;QAE/E,OAAO,UAAU,CAAC;IACtB,CAAC;IAEe,SAAS,GAAA;QACrB,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;QAChD,CAAC;QAED,mBAAmB,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QACrD,mBAAmB,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QACrD,mBAAmB,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;QAC3D,mBAAmB,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAC7D,mBAAmB,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC;QACrE,mBAAmB,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC;QACrE,mBAAmB,CAAC,0BAA0B,GAAG,IAAI,CAAC,0BAA0B,CAAC;QACjF,mBAAmB,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAC/D,mBAAmB,CAAC,wBAAwB,GAAG,IAAI,CAAC,wBAAwB,CAAC;QAC7E,mBAAmB,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC;QACvE,mBAAmB,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC;QACrE,mBAAmB,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;QACnE,mBAAmB,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACvC,mBAAmB,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QACjE,mBAAmB,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAC/C,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACjD,mBAAmB,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACnD,mBAAmB,CAAC,wBAAwB,GAAG,IAAI,CAAC,wBAAwB,CAAC;QAE7E,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEe,YAAY,CAAC,mBAAwB,EAAE,KAAY,EAAE,OAAe,EAAA;QAChF,KAAK,CAAC,YAAY,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAExD,IAAI,mBAAmB,CAAC,OAAO,EAAE,CAAC;YAC9B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,YAAY,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;QACjE,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,mBAAmB,CAAC,YAAY,IAAI,CAAC,CAAC;QAC1D,IAAI,CAAC,YAAY,GAAG,mBAAmB,CAAC,YAAY,CAAC;QACrD,IAAI,CAAC,eAAe,GAAG,mBAAmB,CAAC,eAAe,CAAC;QAC3D,IAAI,CAAC,gBAAgB,GAAG,mBAAmB,CAAC,gBAAgB,CAAC;QAC7D,IAAI,CAAC,oBAAoB,GAAG,mBAAmB,CAAC,oBAAoB,CAAC;QACrE,IAAI,CAAC,oBAAoB,GAAG,mBAAmB,CAAC,oBAAoB,CAAC;QACrE,IAAI,CAAC,0BAA0B,GAAG,mBAAmB,CAAC,0BAA0B,CAAC;QACjF,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,mBAAmB,CAAC,iBAAiB,CAAC;QACjE,IAAI,CAAC,wBAAwB,GAAG,mBAAmB,CAAC,wBAAwB,IAAI,SAAS,CAAC,6BAA6B,CAAC;QACxH,IAAI,CAAC,qBAAqB,GAAG,mBAAmB,CAAC,qBAAqB,CAAC;QACvE,IAAI,CAAC,oBAAoB,GAAG,mBAAmB,CAAC,oBAAoB,CAAC;QACrE,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC,mBAAmB,CAAC;QACnE,IAAI,CAAC,KAAK,GAAG,mBAAmB,CAAC,KAAK,CAAC;QACvC,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC,mBAAmB,CAAC,kBAAkB,CAAC;QACnE,IAAI,CAAC,SAAS,GAAG,mBAAmB,CAAC,SAAS,CAAC;QAC/C,IAAI,CAAC,UAAU,GAAG,mBAAmB,CAAC,UAAU,CAAC;QACjD,IAAI,CAAC,WAAW,GAAG,mBAAmB,CAAC,WAAW,CAAC;QACnD,IAAI,CAAC,wBAAwB,GAAG,CAAC,CAAC,mBAAmB,CAAC,wBAAwB,CAAC;QAE/E,IAAI,CAAC,UAAU,EAAE,CAAC;IACtB,CAAC;CACJ;uJAv3CU,cAAA,EAAA;2KADN,yBAAA,AAAsB,EAAC,eAAe,EAAA,EAAA,gCAAA,KAAgC,WAAW,EAAE;QAAE,GAAG,EAAE,CAAC;QAAE,GAAG,EAAE,CAAC;QAAE,SAAS,EAAE;YAAE,MAAM,EAAE,IAAI;QAAA,CAAE;IAAA,CAAE,CAAC;kEAC/F;wJAO9B,aAAA,EAAA;2KADN,yBAAA,AAAsB,EAAC,oBAAoB,EAAA,EAAA,gCAAA,KAAgC,WAAW,EAAE;QAAE,GAAG,EAAE,CAAC;QAAE,GAAG,EAAE,CAAC;QAAE,SAAS,EAAE;YAAE,MAAM,EAAE,IAAI;QAAA,CAAE;IAAA,CAAE,CAAC;uEAC/F;wJAOnC,aAAA,EAAA;0KADN,0BAAA,AAAsB,EAAC,qBAAqB,EAAA,EAAA,gCAAA,KAAgC,WAAW,EAAE;QAAE,GAAG,EAAE,CAAC;QAAE,GAAG,EAAE,CAAC;QAAE,SAAS,EAAE;YAAE,MAAM,EAAE,IAAI;QAAA,CAAE;IAAA,CAAE,CAAC;oEACnG;CAchC,oKAAA,EAAA;2KARN,yBAAA,AAAsB,EAAC,eAAe,EAAA,EAAA,+BAAA,KAA+B,mBAAmB,EAAE;QACvF,SAAS,EAAE;YAAE,MAAM,EAAE,IAAI;QAAA,CAAE;QAC3B,OAAO,EAAE;YACL;gBAAE,KAAK,EAAE,UAAU;gBAAE,KAAK,6KAAE,kBAAe,CAAC,qBAAqB;YAAA,CAAE;YACnE;gBAAE,KAAK,EAAE,MAAM;gBAAE,KAAK,6KAAE,kBAAe,CAAC,iBAAiB;YAAA,CAAE;YAC3D;gBAAE,KAAK,EAAE,UAAU;gBAAE,KAAK,6KAAE,kBAAe,CAAC,qBAAqB;YAAA,CAAE;SACtE;KACJ,CAAC;+DACsB;CAMjB,oKAAA,EAAA;2KADN,yBAAA,AAAsB,EAAC,eAAe,EAAA,EAAA,kCAAA,KAAkC,SAAS,CAAC;+DAC9C;wJAM9B,aAAA,EAAA;KADN,+LAAA,AAAsB,EAAC,cAAc,EAAA,EAAA,gCAAA,KAAgC,SAAS,EAAE;QAAE,GAAG,EAAE,CAAC;QAAE,GAAG,EAAE,CAAC;QAAE,SAAS,EAAE;YAAE,MAAM,EAAE,IAAI;QAAA,CAAE;IAAA,CAAE,CAAC;kEAC5F;uJAM9B,cAAA,EAAA;2KADN,yBAAA,AAAsB,EAAC,gBAAgB,EAAA,EAAA,kCAAA,KAAkC,SAAS,CAAC;mEAC3C;IAOlC,iKAAA,EAAA;2KADN,yBAAA,AAAsB,EAAC,qBAAqB,EAAA,EAAA,kCAAA,KAAkC,WAAW,EAAE;QAAE,SAAS,EAAE;YAAE,MAAM,EAAE,IAAI;QAAA,CAAE;IAAA,CAAE,CAAC;uEAChF;wJAOrC,aAAA,EAAA;QADN,4LAAA,AAAsB,EAAC,qBAAqB,EAAA,EAAA,kCAAA,KAAkC,WAAW,EAAE;QAAE,SAAS,EAAE;YAAE,MAAM,EAAE,IAAI;QAAA,CAAE;IAAA,CAAE,CAAC;uEAChF;CAQrC,oKAAA,EAAA;2KADN,yBAAA,AAAsB,EAAC,wBAAwB,EAAA,EAAA,kCAAA,KAAkC,WAAW,EAAE;QAAE,SAAS,EAAE;YAAE,MAAM,EAAE,IAAI;QAAA,CAAE;IAAA,CAAE,CAAC;6EAC5E;wJAM5C,aAAA,EAAA;2KADN,yBAAsB,AAAtB,EAAuB,oBAAoB,EAAA,EAAA,kCAAA,KAAkC,WAAW,EAAE;QAAE,SAAS,EAAE;YAAE,MAAM,EAAE,IAAI;QAAA,CAAE;IAAA,CAAE,CAAC;oEACjF;wJAanC,aAAA,EAAA;2KARN,yBAAA,AAAsB,EAAC,4BAA4B,EAAA,EAAA,+BAAA,KAA+B,WAAW,EAAE;QAC5F,SAAS,EAAE;YAAE,MAAM,EAAE,IAAI;QAAA,CAAE;QAC3B,OAAO,EAAE;YACL;gBAAE,KAAK,EAAE,KAAK;gBAAE,KAAK,EAAE;YAAA,OAAS,CAAC,6BAA6B,EAAE;YAChE;gBAAE,KAAK,EAAE,QAAQ;gBAAE,KAAK,EAAE;YAAA,MAAS,CAAC,gCAAgC,EAAE;YACtE;gBAAE,KAAK,EAAE,MAAM;gBAAE,KAAK,EAAE;YAAA,MAAS,CAAC,8BAA8B,EAAE;SACrE;KACJ,CAAC;2EACwE;IAcnE,iKAAA,EAAA;2KATN,yBAAA,AAAsB,EAAC,eAAe,EAAA,EAAA,+BAAA,KAA+B,WAAW,EAAE;QAC/E,SAAS,EAAE;YAAE,MAAM,EAAE,IAAI;QAAA,CAAE;QAC3B,OAAO,EAAE;YACL;gBAAE,KAAK,EAAE,SAAS;gBAAE,KAAK,EAAE;YAAA,OAAS,CAAC,8BAA8B,EAAE;YACrE;gBAAE,KAAK,EAAE,QAAQ;gBAAE,KAAK,EAAE;YAAA,OAAS,CAAC,6BAA6B,EAAE;YACnE;gBAAE,KAAK,EAAE,YAAY;gBAAE,KAAK,EAAE;YAAA,OAAS,CAAC,mCAAmC,EAAE;YAC7E;gBAAE,KAAK,EAAE,QAAQ;gBAAE,KAAK,EAAE;YAAA,OAAS,CAAC,6BAA6B,EAAE;SACtE;KACJ,CAAC;mEACsE;wJAMjE,aAAA,EAAA;IADN,gMAAA,AAAsB,EAAC,qBAAqB,EAAA,EAAA,kCAAA,KAAkC,UAAU,EAAE;QAAE,SAAS,EAAE;YAAE,MAAM,EAAE,IAAI;QAAA,CAAE;IAAA,CAAE,CAAC;wEAC9E;wJAOtC,aAAA,EAAA;2KADN,yBAAA,AAAsB,EAAC,oBAAoB,EAAA,EAAA,kCAAA,KAAkC,UAAU,EAAE;QAAE,SAAS,EAAE;YAAE,MAAM,EAAE,IAAI;QAAA,CAAE;IAAA,CAAE,CAAC;uEAC9E;wJAOrC,aAAA,EAAA;IADN,gMAAA,AAAsB,EAAC,mBAAmB,EAAA,EAAA,kCAAA,KAAkC,UAAU,EAAE;QAAE,SAAS,EAAE;YAAE,MAAM,EAAE,IAAI;QAAA,CAAE;IAAA,CAAE,CAAC;sEAC9E;uJAMpC,cAAA,EAAA;2KADN,yBAAA,AAAsB,EAAC,OAAO,EAAA,EAAA,kCAAA,KAAkC,UAAU,EAAE;QAAE,SAAS,EAAE;YAAE,MAAM,EAAE,IAAI;QAAA,CAAE;IAAA,CAAE,CAAC;wDAC/E;wJAMvB,aAAA,EAAA;2KADN,yBAAA,AAAsB,EAAC,sBAAsB,EAAA,EAAA,kCAAA,KAAkC,UAAU,EAAE;QAAE,SAAS,EAAE;YAAE,MAAM,EAAE,IAAI;QAAA,CAAE;IAAA,CAAE,CAAC;qEACjF;IAMpC,iKAAA,EAAA;2KAHN,yBAAA,AAAsB,EAAC,6BAA6B,EAAA,EAAA,kCAAA,KAAkC,UAAU,EAAE;QAC/F,SAAS,EAAE;YAAE,OAAO,EAAE,IAAI;YAAE,MAAM,EAAE,IAAI;YAAE,YAAY,EAAE,yBAAyB,CAAC,kCAAkC;QAAA,CAAE;KACzH,CAAC;2EACsC;AA+DjC,qKAAA,EAAA;2KAzDN,yBAAA,AAAsB,EAAC,YAAY,EAAA,EAAA,+BAAA,KAA+B,OAAO,EAAE;QACxE,SAAS,EAAE;YAAE,MAAM,EAAE,IAAI;QAAA,CAAE;QAC3B,OAAO,EAAE;YACL;gBAAE,KAAK,EAAE,MAAM;gBAAE,KAAK,EAAE,CAAC;YAAA,CAAE;YAC3B,WAAW;YACX;gBAAE,KAAK,EAAE,qBAAqB;gBAAE,KAAK,EAAE,CAAC;YAAA,CAAE;YAC1C;gBAAE,KAAK,EAAE,SAAS;gBAAE,KAAK,EAAE,CAAC;YAAA,CAAE;YAC9B;gBAAE,KAAK,EAAE,UAAU;gBAAE,KAAK,EAAE,CAAC;YAAA,CAAE;YAC/B;gBAAE,KAAK,EAAE,YAAY;gBAAE,KAAK,EAAE,CAAC;YAAA,CAAE;YACjC;gBAAE,KAAK,EAAE,cAAc;gBAAE,KAAK,EAAE,CAAC;YAAA,CAAE;YACnC,6BAA6B;YAC7B,6BAA6B;YAC7B;gBAAE,KAAK,EAAE,mBAAmB;gBAAE,KAAK,EAAE,CAAC;YAAA,CAAE;YACxC;gBAAE,KAAK,EAAE,oBAAoB;gBAAE,KAAK,EAAE,CAAC;YAAA,CAAE;YACzC;gBAAE,KAAK,EAAE,sBAAsB;gBAAE,KAAK,EAAE,EAAE;YAAA,CAAE;YAC5C;gBAAE,KAAK,EAAE,qBAAqB;gBAAE,KAAK,EAAE,EAAE;YAAA,CAAE;YAC3C;gBAAE,KAAK,EAAE,sBAAsB;gBAAE,KAAK,EAAE,EAAE;YAAA,CAAE;YAC5C;gBAAE,KAAK,EAAE,wBAAwB;gBAAE,KAAK,EAAE,EAAE;YAAA,CAAE;YAC9C,OAAO;YACP,uCAAuC;YACvC,oCAAoC;YACpC,MAAM;YACN;gBAAE,KAAK,EAAE,gBAAgB;gBAAE,KAAK,EAAE,EAAE;YAAA,CAAE;YACtC;gBAAE,KAAK,EAAE,gBAAgB;gBAAE,KAAK,EAAE,EAAE;YAAA,CAAE;YACtC;gBAAE,KAAK,EAAE,gBAAgB;gBAAE,KAAK,EAAE,EAAE;YAAA,CAAE;YACtC,WAAW;YACX;gBAAE,KAAK,EAAE,gBAAgB;gBAAE,KAAK,EAAE,EAAE;YAAA,CAAE;YACtC;gBAAE,KAAK,EAAE,iBAAiB;gBAAE,KAAK,EAAE,EAAE;YAAA,CAAE;YACvC;gBAAE,KAAK,EAAE,mBAAmB;gBAAE,KAAK,EAAE,EAAE;YAAA,CAAE;YACzC;gBAAE,KAAK,EAAE,cAAc;gBAAE,KAAK,EAAE,EAAE;YAAA,CAAE;YACpC;gBAAE,KAAK,EAAE,gBAAgB;gBAAE,KAAK,EAAE,EAAE;YAAA,CAAE;YACtC,kBAAkB;YAClB;gBAAE,KAAK,EAAE,gBAAgB;gBAAE,KAAK,EAAE,EAAE;YAAA,CAAE;YACtC;gBAAE,KAAK,EAAE,eAAe;gBAAE,KAAK,EAAE,EAAE;YAAA,CAAE;YACrC;gBAAE,KAAK,EAAE,UAAU;gBAAE,KAAK,EAAE,EAAE;YAAA,CAAE;YAChC;gBAAE,KAAK,EAAE,aAAa;gBAAE,KAAK,EAAE,EAAE;YAAA,CAAE;YACnC;gBAAE,KAAK,EAAE,WAAW;gBAAE,KAAK,EAAE,EAAE;YAAA,CAAE;YACjC;gBAAE,KAAK,EAAE,QAAQ;gBAAE,KAAK,EAAE,EAAE;YAAA,CAAE;YAC9B;gBAAE,KAAK,EAAE,OAAO;gBAAE,KAAK,EAAE,EAAE;YAAA,CAAE;YAC7B;gBAAE,KAAK,EAAE,iBAAiB;gBAAE,KAAK,EAAE,EAAE;YAAA,CAAE;YACvC;gBAAE,KAAK,EAAE,qBAAqB;gBAAE,KAAK,EAAE,EAAE;YAAA,CAAE;YAC3C;gBAAE,KAAK,EAAE,iBAAiB;gBAAE,KAAK,EAAE,EAAE;YAAA,CAAE;YACvC;gBAAE,KAAK,EAAE,eAAe;gBAAE,KAAK,EAAE,EAAE;YAAA,CAAE;YACrC;gBAAE,KAAK,EAAE,0BAA0B;gBAAE,KAAK,EAAE,EAAE;YAAA,CAAE;YAChD,OAAO;YACP;gBAAE,KAAK,EAAE,KAAK;gBAAE,KAAK,EAAE,EAAE;YAAA,CAAE;YAC3B;gBAAE,KAAK,EAAE,KAAK;gBAAE,KAAK,EAAE,EAAE;YAAA,CAAE;YAC3B;gBAAE,KAAK,EAAE,eAAe;gBAAE,KAAK,EAAE,EAAE;YAAA,CAAE;YACrC;gBAAE,KAAK,EAAE,sBAAsB;gBAAE,KAAK,EAAE,EAAE;YAAA,CAAE;YAC5C;gBAAE,KAAK,EAAE,wBAAwB;gBAAE,KAAK,EAAE,EAAE;YAAA,CAAE;YAC9C;gBAAE,KAAK,EAAE,mBAAmB;gBAAE,KAAK,EAAE,EAAE;YAAA,CAAE;YACzC;gBAAE,KAAK,EAAE,sBAAsB;gBAAE,KAAK,EAAE,EAAE;YAAA,CAAE;YAC5C;gBAAE,KAAK,EAAE,OAAO;gBAAE,KAAK,EAAE,EAAE;YAAA,CAAE;YAC7B;gBAAE,KAAK,EAAE,cAAc;gBAAE,KAAK,EAAE,EAAE;YAAA,CAAE;YACpC;gBAAE,KAAK,EAAE,yBAAyB;gBAAE,KAAK,EAAE,EAAE;YAAA,CAAE;SAClD;KACJ,CAAC;4DACmB;CASd,oKAAA,EAAA;2KADN,yBAAA,AAAsB,EAAC,gBAAgB,EAAA,EAAA,gCAAA,KAAgC,OAAO,EAAE;QAAE,GAAG,EAAE,CAAC,CAAC;QAAE,GAAG,EAAE,CAAC;QAAE,SAAS,EAAE;YAAE,MAAM,EAAE,IAAI;QAAA,CAAE;IAAA,CAAE,CAAC;6DAC5G;wJAOf,aAAA,EAAA;2KADN,yBAAA,AAAsB,EAAC,eAAe,EAAA,EAAA,gCAAA,KAAgC,OAAO,EAAE;QAAE,GAAG,EAAE,CAAC;QAAE,GAAG,EAAE,CAAC;QAAE,SAAS,EAAE;YAAE,MAAM,EAAE,IAAI;QAAA,CAAE;IAAA,CAAE,CAAC;8DACzG;6JA+pC3B,gBAAA,AAAa,EAAC,mCAAmC,EAAE,yBAAyB,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 3535, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/PBR/index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Blocks/PBR/index.ts"], "sourcesContent": ["export * from \"./pbrMetallicRoughnessBlock\";\r\nexport * from \"./sheenBlock\";\r\nexport * from \"./anisotropyBlock\";\r\nexport * from \"./reflectionBlock\";\r\nexport * from \"./clearCoatBlock\";\r\nexport * from \"./refractionBlock\";\r\nexport * from \"./subSurfaceBlock\";\r\nexport * from \"./iridescenceBlock\";\r\n"], "names": [], "mappings": ";AAAA,cAAc,6BAA6B,CAAC;AAC5C,cAAc,cAAc,CAAC;AAC7B,cAAc,mBAAmB,CAAC;AAClC,cAAc,mBAAmB,CAAC;AAClC,cAAc,kBAAkB,CAAC;AACjC,cAAc,mBAAmB,CAAC;AAClC,cAAc,mBAAmB,CAAC;AAClC,cAAc,oBAAoB,CAAC", "debugId": null}}]}