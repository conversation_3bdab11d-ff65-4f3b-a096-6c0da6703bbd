{"version": 3, "file": "computeEffect.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Compute/computeEffect.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAExC,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAEhD,OAAO,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAC/E,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,uCAAuC,CAAC;AAEzF,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AAKrD,OAAO,EAAE,kBAAkB,EAAE,+BAA8B;AAoD3D;;GAEG;AACH,MAAM,OAAO,aAAa;IAgEtB;;;;;;OAMG;IACH,YAAY,QAAqC,EAAE,OAAsC,EAAE,MAAsB,EAAE,GAAG,GAAG,EAAE;QA5D3H;;WAEG;QACI,YAAO,GAAW,EAAE,CAAC;QAC5B;;WAEG;QACI,eAAU,GAA8C,IAAI,CAAC;QACpE;;WAEG;QACI,YAAO,GAA8D,IAAI,CAAC;QACjF;;WAEG;QACI,aAAQ,GAAG,CAAC,CAAC;QACpB;;;WAGG;QACI,wBAAmB,GAAG,IAAI,UAAU,EAAiB,CAAC;QAC7D;;WAEG;QACI,sBAAiB,GAAG,IAAI,UAAU,EAAiB,CAAC;QAC3D;;WAEG;QACI,qBAAgB,GAAG,IAAI,UAAU,EAAiB,CAAC;QAE1D;;;WAGG;QACI,wBAAmB,GAAG,KAAK,CAAC;QAG3B,aAAQ,GAAG,KAAK,CAAC;QACjB,sBAAiB,GAAG,EAAE,CAAC;QAC/B,gBAAgB;QACT,SAAI,GAAW,EAAE,CAAC;QACjB,+BAA0B,GAAW,EAAE,CAAC;QAChD,gBAAgB;QACT,qBAAgB,GAAsC,IAAI,CAAC;QAClE,gBAAgB;QACT,uBAAkB,GAAW,EAAE,CAAC;QAC/B,0BAAqB,GAAW,EAAE,CAAC;QAEnC,oBAAe,+BAAuB;QAa1C,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC;QACrB,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;QAEhB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,QAAQ,GAAG,aAAa,CAAC,aAAa,EAAE,CAAC;QAE9C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,EAAE,CAAC;QACrC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAC/B,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;QACrC,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,UAAU,IAAI,MAAM,CAAC;QAEhD,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACtE,IAAI,CAAC,iBAAiB,GAAG,WAAW,CAAC,oBAAoB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAChF,IAAI,CAAC,mBAAmB,GAAG,WAAW,CAAC,uBAAuB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAErF,IAAI,aAAwD,CAAC;QAE7D,MAAM,YAAY,GAAG,mBAAmB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;QAEnF,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;YAC/B,aAAa,GAAG,QAAQ,CAAC;QAC7B,CAAC;aAAM,IAAI,QAAQ,CAAC,aAAa,EAAE,CAAC;YAChC,aAAa,GAAG,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC;QACvD,CAAC;aAAM,IAAI,QAAQ,CAAC,cAAc,EAAE,CAAC;YACjC,aAAa,GAAG,YAAY,EAAE,cAAc,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,QAAQ,CAAC,cAAc,CAAC;QACrG,CAAC;aAAM,CAAC;YACJ,aAAa,GAAG,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC;QACjD,CAAC;QAED,MAAM,gBAAgB,GAAwB;YAC1C,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;YACjC,eAAe,EAAE,SAAS;YAC1B,UAAU,EAAE,KAAK;YACjB,4BAA4B,EAAE,KAAK;YACnC,SAAS,EAAE,IAAI;YACf,sBAAsB,EAAE,IAAI,CAAC,OAAO,CAAC,sBAAsB;YAC3D,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;YACzC,oBAAoB,EAAE,IAAI,CAAC,mBAAmB;YAC9C,OAAO,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,GAAG,CAAC,CAAC,QAAQ,EAAE;YAChD,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,kBAAkB;YAC7C,iBAAiB,EAAE,IAAI;YACvB,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;YAC7C,qBAAqB,EAAE,IAAI,CAAC,OAAO,CAAC,qBAAqB;YACzD,wBAAwB,EAAE,CAAC,UAAkB,EAAE,IAAY,EAAE,OAAkB,EAAE,EAAE;gBAC/E,IAAI,CAAC,OAAO,EAAE,CAAC;oBACX,OAAO,IAAI,CAAC;gBAChB,CAAC;gBACD,kDAAkD;gBAClD,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;oBAC3B,MAAM,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;oBACvE,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;oBAClC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;wBACrB,MAAM,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;wBACrB,MAAM,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;wBACvB,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;4BACvD,IAAI,GAAG,SAAS,GAAG,MAAM,KAAK,KAAK,GAAG,IAAI,CAAC;wBAC/C,CAAC;oBACL,CAAC;gBACL,CAAC;gBACD,OAAO,IAAI,CAAC;YAChB,CAAC;SACJ,CAAC;QAEF,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,WAAW,EAAE,EAAE;YAC3D,UAAU,CAAC,gBAAgB,CAAC,CAAC;YAC7B,UAAU,CACN,WAAW,EACX,gBAAgB,EAChB,CAAC,mBAAmB,EAAE,EAAE;gBACpB,IAAI,CAAC,qBAAqB,GAAG,WAAW,CAAC;gBACzC,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;oBAC3B,mBAAmB,GAAG,OAAO,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;gBACxE,CAAC;gBACD,MAAM,YAAY,GAAG,QAAQ,CAAC,mBAAmB,EAAE,EAAE,EAAE,gBAAgB,CAAC,CAAC;gBACzE,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAC1D,CAAC,EACD,IAAI,CAAC,OAAO,CACf,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,aAAa,CAAC,oBAA4B,EAAE,QAAa;QAC7D,IAAI,QAAQ,EAAE,CAAC;YACX,MAAM,OAAO,GAAG,QAAQ,CAAC,cAAc,IAAI,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC;YAEhG,IAAI,CAAC,kBAAkB,GAAG,gCAAgC,GAAG,OAAO,GAAG,IAAI,GAAG,oBAAoB,CAAC;QACvG,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,kBAAkB,GAAG,oBAAoB,CAAC;QACnD,CAAC;QACD,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,IAAW,GAAG;QACV,OAAO,IAAI,CAAC,IAAI,CAAC;IACrB,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,IAAI,CAAC;YACD,OAAO,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACnC,CAAC;QAAC,MAAM,CAAC;YACL,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;IAEO,gBAAgB;QACpB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;QACzC,CAAC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED;;;OAGG;IACI,kBAAkB;QACrB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;;OAGG;IACI,mBAAmB;QACtB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED;;;OAGG;IACI,mBAAmB,CAAC,IAAqC;QAC5D,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC;YACjB,IAAI,CAAC,IAAI,CAAC,CAAC;YACX,OAAO;QACX,CAAC;QAED,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;YACpC,IAAI,CAAC,MAAM,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;YAC1D,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAC7B,CAAC;IACL,CAAC;IAEO,aAAa,CAAC,uBAA0D;QAC5E,kBAAkB,CACd,GAAG,EAAE,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAC7B,GAAG,EAAE;YACD,8CAA8C;QAClD,CAAC,EACD,CAAC,CAAC,EAAE,EAAE;YACF,IAAI,CAAC,yBAAyB,CAAC,CAAC,EAAE,uBAAuB,CAAC,CAAC;QAC/D,CAAC,EACD,SAAS,EACT,SAAS,EACT,KAAK,CACR,CAAC;IACN,CAAC;IAEO,WAAW,CAAC,MAAW,EAAE,GAAW,EAAE,WAAmB,EAAE,QAA6B;QAC5F,IAAI,OAAO,WAAW,KAAK,WAAW,EAAE,CAAC;YACrC,gBAAgB;YAChB,IAAI,MAAM,YAAY,WAAW,EAAE,CAAC;gBAChC,MAAM,UAAU,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC;gBAC7C,QAAQ,CAAC,UAAU,CAAC,CAAC;gBACrB,OAAO;YACX,CAAC;QACL,CAAC;QAED,kBAAkB;QAClB,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,SAAS,EAAE,CAAC;YACvC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9B,OAAO;QACX,CAAC;QAED,mBAAmB;QACnB,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,SAAS,EAAE,CAAC;YACvC,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YACtD,QAAQ,CAAC,YAAY,CAAC,CAAC;YACvB,OAAO;QACX,CAAC;QAED,sBAAsB;QACtB,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ,CAAC,EAAE,CAAC;YAC7C,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ,CAAC,CAAC,CAAC;YACrD,OAAO;QACX,CAAC;QAED,IAAI,WAAW,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,WAAW,GAAG,QAAQ,CAAC,EAAE,CAAC;YACpE,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,WAAW,GAAG,QAAQ,CAAC,CAAC,CAAC;YAC7D,OAAO;QACX,CAAC;QAED,IAAI,SAAS,CAAC;QAEd,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;YACxE,SAAS,GAAG,MAAM,CAAC;QACvB,CAAC;aAAM,CAAC;YACJ,SAAS,GAAG,IAAI,CAAC,iBAAiB,GAAG,MAAM,CAAC;QAChD,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS,GAAG,GAAG,GAAG,GAAG,CAAC,WAAW,EAAE,GAAG,KAAK,EAAE,QAAQ,CAAC,CAAC;IAClF,CAAC;IAED;;OAEG;IACH,IAAW,iBAAiB;QACxB,OAAO,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,EAAE,qBAAqB,EAAE,IAAI,IAAI,CAAC,kBAAkB,CAAC,CAAC;IAC3J,CAAC;IAED;;OAEG;IACH,IAAW,oBAAoB;QAC3B,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACtC,CAAC;IAED;;;OAGG;IACI,cAAc;QACjB,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAE7B,MAAM,uBAAuB,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAEtD,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QAEtB,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;YAE5B,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC,4BAA4B,EAAE,CAAC;YAC9D,IAAI,CAAC,gBAAgB,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC;YAExC,MAAM,CAAC,8BAA8B,CACjC,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,EAC3F,IAAI,CAAC,qBAAqB,EAC1B,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,EAChD,IAAI,CAAC,WAAW,CACnB,CAAC;YAEF,MAAM,CAAC,kCAAkC,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,QAA8C,EAAE,EAAE;gBAChH,IAAI,QAAQ,IAAI,QAAQ,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC;oBACrC,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EAAE,uBAAuB,CAAC,CAAC;oBAClE,OAAO;gBACX,CAAC;gBACD,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;gBAC5B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;gBACrB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;oBAClB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;gBAC1B,CAAC;gBACD,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBAC/C,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;gBAEjC,IAAI,uBAAuB,EAAE,CAAC;oBAC1B,IAAI,CAAC,SAAS,EAAE,CAAC,6BAA6B,CAAC,uBAAuB,CAAC,CAAC;gBAC5E,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,IAAI,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;gBAChC,IAAI,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC;YAChD,CAAC;QACL,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,IAAI,CAAC,yBAAyB,CAAC,CAAC,EAAE,uBAAuB,CAAC,CAAC;QAC/D,CAAC;IACL,CAAC;IAEO,yBAAyB,CAAC,CAAsC,EAAE,0BAA6D,IAAI;QACvI,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;QAE5B,MAAM,CAAC,KAAK,CAAC,mCAAmC,CAAC,CAAC;QAClD,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,aAAa,CAAC,+BAA+B,EAAE,CAAC;YAChD,MAAM,IAAI,GAAG,IAAI,CAAC,gBAAgB,EAAE,qBAAqB,EAAE,CAAC;YAC5D,IAAI,IAAI,EAAE,CAAC;gBACP,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;gBAC9B,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACvB,CAAC;QACL,CAAC;QAED,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE,CAAC;YACxB,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;YAC3B,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACrD,CAAC;aAAM,CAAC;YACJ,KAAK,MAAM,OAAO,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC;gBAC/B,IAAI,GAAG,GAAG,EAAE,CAAC;gBACb,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;oBAC7B,GAAG,IAAI,OAAO,GAAG,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;gBACzC,CAAC;gBACD,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;oBAC/B,GAAG,IAAI,SAAS,GAAG,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;gBAC7C,CAAC;gBACD,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;oBAC/B,GAAG,IAAI,SAAS,GAAG,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;gBAC7C,CAAC;gBACD,GAAG,IAAI,OAAO,CAAC,IAAI,GAAG,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;gBAE1C,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBACzB,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC;gBACnC,CAAC;gBACD,IAAI,CAAC,iBAAiB,IAAI,GAAG,CAAC;gBAC9B,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACtB,CAAC;QACL,CAAC;QAED,IAAI,uBAAuB,EAAE,CAAC;YAC1B,IAAI,CAAC,gBAAgB,GAAG,uBAAuB,CAAC;YAChD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACzB,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC/C,CAAC;QACD,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;IAED;;QAEI;IACG,OAAO;QACV,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;QACpC,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,cAAc,CAAC,IAAY,EAAE,aAAqB;QAC5D,WAAW,CAAC,eAAe,6BAAqB,CAAC,GAAG,IAAI,eAAe,CAAC,GAAG,aAAa,CAAC;IAC7F,CAAC;;AA7ac,2BAAa,GAAG,CAAC,AAAJ,CAAK;AAEjC;;GAEG;AACW,6CAA+B,GAAG,IAAI,AAAP,CAAQ", "sourcesContent": ["import { Logger } from \"../Misc/logger\";\r\nimport type { Nullable } from \"../types\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport type { IComputePipelineContext } from \"./IComputePipelineContext\";\r\nimport { GetDOMTextContent, IsWindowObjectExist } from \"../Misc/domManagement\";\r\nimport { Finalize, Initialize, PreProcess } from \"../Engines/Processors/shaderProcessor\";\r\nimport type { _IProcessingOptions } from \"../Engines/Processors/shaderProcessingOptions\";\r\nimport { ShaderStore } from \"../Engines/shaderStore\";\r\nimport { ShaderLanguage } from \"../Materials/shaderLanguage\";\r\n\r\nimport type { AbstractEngine } from \"../Engines/abstractEngine\";\r\nimport type { ComputeCompilationMessages } from \"../Engines/Extensions/engine.computeShader\";\r\nimport { _RetryWithInterval } from \"core/Misc/timingTools\";\r\n\r\n/**\r\n * Defines the route to the shader code. The priority is as follows:\r\n *  * object: `{ computeSource: \"compute shader code string\"}` for directly passing the shader code\r\n *  * object: `{ computeElement: \"vertexShaderCode\" }`, used with shader code in script tags\r\n *  * object: `{ compute: \"custom\" }`, used with `Effect.ShadersStore[\"customVertexShader\"]` and `Effect.ShadersStore[\"customFragmentShader\"]`\r\n *  * string: `\"./COMMON_NAME\"`, used with external files COMMON_NAME.vertex.fx and COMMON_NAME.fragment.fx in index.html folder.\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport type IComputeShaderPath = {\r\n    /**\r\n     * Directly pass the shader code\r\n     */\r\n    computeSource?: string;\r\n    /**\r\n     * Used with Effect.ShadersStore. If the `vertex` is set to `\"custom`, then\r\n     * Babylon.js will read from Effect.ShadersStore[\"customVertexShader\"]\r\n     */\r\n    compute?: string;\r\n    /**\r\n     * Used with shader code in script tags\r\n     */\r\n    computeElement?: string;\r\n};\r\n\r\n/**\r\n * Options to be used when creating a compute effect.\r\n */\r\nexport interface IComputeEffectCreationOptions {\r\n    /**\r\n     * Define statements that will be set in the shader.\r\n     */\r\n    defines: any;\r\n    /**\r\n     * The name of the entry point in the shader source (default: \"main\")\r\n     */\r\n    entryPoint?: string;\r\n    /**\r\n     * Callback that will be called when the shader is compiled.\r\n     */\r\n    onCompiled: Nullable<(effect: ComputeEffect) => void>;\r\n    /**\r\n     * Callback that will be called if an error occurs during shader compilation.\r\n     */\r\n    onError: Nullable<(effect: ComputeEffect, errors: string) => void>;\r\n    /**\r\n     * If provided, will be called with the shader code so that this code can be updated before it is compiled by the GPU\r\n     */\r\n    processFinalCode?: Nullable<(code: string) => string>;\r\n}\r\n\r\n/**\r\n * Effect wrapping a compute shader and let execute (dispatch) the shader\r\n */\r\nexport class ComputeEffect {\r\n    private static _UniqueIdSeed = 0;\r\n\r\n    /**\r\n     * Enable logging of the shader code when a compilation error occurs\r\n     */\r\n    public static LogShaderCodeOnCompilationError = true;\r\n    /**\r\n     * Name of the effect.\r\n     */\r\n    public name: IComputeShaderPath | string;\r\n    /**\r\n     * String container all the define statements that should be set on the shader.\r\n     */\r\n    public defines: string = \"\";\r\n    /**\r\n     * Callback that will be called when the shader is compiled.\r\n     */\r\n    public onCompiled: Nullable<(effect: ComputeEffect) => void> = null;\r\n    /**\r\n     * Callback that will be called if an error occurs during shader compilation.\r\n     */\r\n    public onError: Nullable<(effect: ComputeEffect, errors: string) => void> = null;\r\n    /**\r\n     * Unique ID of the effect.\r\n     */\r\n    public uniqueId = 0;\r\n    /**\r\n     * Observable that will be called when the shader is compiled.\r\n     * It is recommended to use executeWhenCompile() or to make sure that scene.isReady() is called to get this observable raised.\r\n     */\r\n    public onCompileObservable = new Observable<ComputeEffect>();\r\n    /**\r\n     * Observable that will be called if an error occurs during shader compilation.\r\n     */\r\n    public onErrorObservable = new Observable<ComputeEffect>();\r\n    /**\r\n     * Observable that will be called when effect is bound.\r\n     */\r\n    public onBindObservable = new Observable<ComputeEffect>();\r\n\r\n    /**\r\n     * @internal\r\n     * Specifies if the effect was previously ready\r\n     */\r\n    public _wasPreviouslyReady = false;\r\n\r\n    private _engine: AbstractEngine;\r\n    private _isReady = false;\r\n    private _compilationError = \"\";\r\n    /** @internal */\r\n    public _key: string = \"\";\r\n    private _computeSourceCodeOverride: string = \"\";\r\n    /** @internal */\r\n    public _pipelineContext: Nullable<IComputePipelineContext> = null;\r\n    /** @internal */\r\n    public _computeSourceCode: string = \"\";\r\n    private _rawComputeSourceCode: string = \"\";\r\n    private _entryPoint: string;\r\n    private _shaderLanguage = ShaderLanguage.WGSL;\r\n    private _shaderStore: { [key: string]: string };\r\n    private _shaderRepository: string;\r\n    private _includeShaderStore: { [key: string]: string };\r\n\r\n    /**\r\n     * Creates a compute effect that can be used to execute a compute shader\r\n     * @param baseName Name of the effect\r\n     * @param options Set of all options to create the effect\r\n     * @param engine The engine the effect is created for\r\n     * @param key Effect Key identifying uniquely compiled shader variants\r\n     */\r\n    constructor(baseName: IComputeShaderPath | string, options: IComputeEffectCreationOptions, engine: AbstractEngine, key = \"\") {\r\n        this.name = baseName;\r\n        this._key = key;\r\n\r\n        this._engine = engine;\r\n        this.uniqueId = ComputeEffect._UniqueIdSeed++;\r\n\r\n        this.defines = options.defines ?? \"\";\r\n        this.onError = options.onError;\r\n        this.onCompiled = options.onCompiled;\r\n        this._entryPoint = options.entryPoint ?? \"main\";\r\n\r\n        this._shaderStore = ShaderStore.GetShadersStore(this._shaderLanguage);\r\n        this._shaderRepository = ShaderStore.GetShadersRepository(this._shaderLanguage);\r\n        this._includeShaderStore = ShaderStore.GetIncludesShadersStore(this._shaderLanguage);\r\n\r\n        let computeSource: IComputeShaderPath | HTMLElement | string;\r\n\r\n        const hostDocument = IsWindowObjectExist() ? this._engine.getHostDocument() : null;\r\n\r\n        if (typeof baseName === \"string\") {\r\n            computeSource = baseName;\r\n        } else if (baseName.computeSource) {\r\n            computeSource = \"source:\" + baseName.computeSource;\r\n        } else if (baseName.computeElement) {\r\n            computeSource = hostDocument?.getElementById(baseName.computeElement) || baseName.computeElement;\r\n        } else {\r\n            computeSource = baseName.compute || baseName;\r\n        }\r\n\r\n        const processorOptions: _IProcessingOptions = {\r\n            defines: this.defines.split(\"\\n\"),\r\n            indexParameters: undefined,\r\n            isFragment: false,\r\n            shouldUseHighPrecisionShader: false,\r\n            processor: null,\r\n            supportsUniformBuffers: this._engine.supportsUniformBuffers,\r\n            shadersRepository: this._shaderRepository,\r\n            includesShadersStore: this._includeShaderStore,\r\n            version: (this._engine.version * 100).toString(),\r\n            platformName: this._engine.shaderPlatformName,\r\n            processingContext: null,\r\n            isNDCHalfZRange: this._engine.isNDCHalfZRange,\r\n            useReverseDepthBuffer: this._engine.useReverseDepthBuffer,\r\n            processCodeAfterIncludes: (shaderType: string, code: string, defines?: string[]) => {\r\n                if (!defines) {\r\n                    return code;\r\n                }\r\n                // We need to convert #define key value to a const\r\n                for (const define of defines) {\r\n                    const keyValue = define.replace(\"#define\", \"\").replace(\";\", \"\").trim();\r\n                    const split = keyValue.split(\" \");\r\n                    if (split.length === 2) {\r\n                        const key = split[0];\r\n                        const value = split[1];\r\n                        if (!isNaN(parseInt(value)) || !isNaN(parseFloat(value))) {\r\n                            code = `const ${key} = ${value};\\n` + code;\r\n                        }\r\n                    }\r\n                }\r\n                return code;\r\n            },\r\n        };\r\n\r\n        this._loadShader(computeSource, \"Compute\", \"\", (computeCode) => {\r\n            Initialize(processorOptions);\r\n            PreProcess(\r\n                computeCode,\r\n                processorOptions,\r\n                (migratedComputeCode) => {\r\n                    this._rawComputeSourceCode = computeCode;\r\n                    if (options.processFinalCode) {\r\n                        migratedComputeCode = options.processFinalCode(migratedComputeCode);\r\n                    }\r\n                    const finalShaders = Finalize(migratedComputeCode, \"\", processorOptions);\r\n                    this._useFinalCode(finalShaders.vertexCode, baseName);\r\n                },\r\n                this._engine\r\n            );\r\n        });\r\n    }\r\n\r\n    private _useFinalCode(migratedCommputeCode: string, baseName: any) {\r\n        if (baseName) {\r\n            const compute = baseName.computeElement || baseName.compute || baseName.spectorName || baseName;\r\n\r\n            this._computeSourceCode = \"//#define SHADER_NAME compute:\" + compute + \"\\n\" + migratedCommputeCode;\r\n        } else {\r\n            this._computeSourceCode = migratedCommputeCode;\r\n        }\r\n        this._prepareEffect();\r\n    }\r\n\r\n    /**\r\n     * Unique key for this effect\r\n     */\r\n    public get key(): string {\r\n        return this._key;\r\n    }\r\n\r\n    /**\r\n     * If the effect has been compiled and prepared.\r\n     * @returns if the effect is compiled and prepared.\r\n     */\r\n    public isReady(): boolean {\r\n        try {\r\n            return this._isReadyInternal();\r\n        } catch {\r\n            return false;\r\n        }\r\n    }\r\n\r\n    private _isReadyInternal(): boolean {\r\n        if (this._isReady) {\r\n            return true;\r\n        }\r\n        if (this._pipelineContext) {\r\n            return this._pipelineContext.isReady;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * The engine the effect was initialized with.\r\n     * @returns the engine.\r\n     */\r\n    public getEngine(): AbstractEngine {\r\n        return this._engine;\r\n    }\r\n\r\n    /**\r\n     * The pipeline context for this effect\r\n     * @returns the associated pipeline context\r\n     */\r\n    public getPipelineContext(): Nullable<IComputePipelineContext> {\r\n        return this._pipelineContext;\r\n    }\r\n\r\n    /**\r\n     * The error from the last compilation.\r\n     * @returns the error string.\r\n     */\r\n    public getCompilationError(): string {\r\n        return this._compilationError;\r\n    }\r\n\r\n    /**\r\n     * Adds a callback to the onCompiled observable and call the callback immediately if already ready.\r\n     * @param func The callback to be used.\r\n     */\r\n    public executeWhenCompiled(func: (effect: ComputeEffect) => void): void {\r\n        if (this.isReady()) {\r\n            func(this);\r\n            return;\r\n        }\r\n\r\n        this.onCompileObservable.add((effect) => {\r\n            func(effect);\r\n        });\r\n\r\n        if (!this._pipelineContext || this._pipelineContext.isAsync) {\r\n            this._checkIsReady(null);\r\n        }\r\n    }\r\n\r\n    private _checkIsReady(previousPipelineContext: Nullable<IComputePipelineContext>) {\r\n        _RetryWithInterval(\r\n            () => this._isReadyInternal(),\r\n            () => {\r\n                // no-op, all work is done in _isReadyInternal\r\n            },\r\n            (e) => {\r\n                this._processCompilationErrors(e, previousPipelineContext);\r\n            },\r\n            undefined,\r\n            undefined,\r\n            false\r\n        );\r\n    }\r\n\r\n    private _loadShader(shader: any, key: string, optionalKey: string, callback: (data: any) => void): void {\r\n        if (typeof HTMLElement !== \"undefined\") {\r\n            // DOM element ?\r\n            if (shader instanceof HTMLElement) {\r\n                const shaderCode = GetDOMTextContent(shader);\r\n                callback(shaderCode);\r\n                return;\r\n            }\r\n        }\r\n\r\n        // Direct source ?\r\n        if (shader.substring(0, 7) === \"source:\") {\r\n            callback(shader.substring(7));\r\n            return;\r\n        }\r\n\r\n        // Base64 encoded ?\r\n        if (shader.substring(0, 7) === \"base64:\") {\r\n            const shaderBinary = window.atob(shader.substring(7));\r\n            callback(shaderBinary);\r\n            return;\r\n        }\r\n\r\n        // Is in local store ?\r\n        if (this._shaderStore[shader + key + \"Shader\"]) {\r\n            callback(this._shaderStore[shader + key + \"Shader\"]);\r\n            return;\r\n        }\r\n\r\n        if (optionalKey && this._shaderStore[shader + optionalKey + \"Shader\"]) {\r\n            callback(this._shaderStore[shader + optionalKey + \"Shader\"]);\r\n            return;\r\n        }\r\n\r\n        let shaderUrl;\r\n\r\n        if (shader[0] === \".\" || shader[0] === \"/\" || shader.indexOf(\"http\") > -1) {\r\n            shaderUrl = shader;\r\n        } else {\r\n            shaderUrl = this._shaderRepository + shader;\r\n        }\r\n\r\n        this._engine._loadFile(shaderUrl + \".\" + key.toLowerCase() + \".fx\", callback);\r\n    }\r\n\r\n    /**\r\n     * Gets the compute shader source code of this effect\r\n     */\r\n    public get computeSourceCode(): string {\r\n        return this._computeSourceCodeOverride ? this._computeSourceCodeOverride : (this._pipelineContext?._getComputeShaderCode() ?? this._computeSourceCode);\r\n    }\r\n\r\n    /**\r\n     * Gets the compute shader source code before it has been processed by the preprocessor\r\n     */\r\n    public get rawComputeSourceCode(): string {\r\n        return this._rawComputeSourceCode;\r\n    }\r\n\r\n    /**\r\n     * Prepares the effect\r\n     * @internal\r\n     */\r\n    public _prepareEffect() {\r\n        const defines = this.defines;\r\n\r\n        const previousPipelineContext = this._pipelineContext;\r\n\r\n        this._isReady = false;\r\n\r\n        try {\r\n            const engine = this._engine;\r\n\r\n            this._pipelineContext = engine.createComputePipelineContext();\r\n            this._pipelineContext._name = this._key;\r\n\r\n            engine._prepareComputePipelineContext(\r\n                this._pipelineContext,\r\n                this._computeSourceCodeOverride ? this._computeSourceCodeOverride : this._computeSourceCode,\r\n                this._rawComputeSourceCode,\r\n                this._computeSourceCodeOverride ? null : defines,\r\n                this._entryPoint\r\n            );\r\n\r\n            engine._executeWhenComputeStateIsCompiled(this._pipelineContext, (messages: Nullable<ComputeCompilationMessages>) => {\r\n                if (messages && messages.numErrors > 0) {\r\n                    this._processCompilationErrors(messages, previousPipelineContext);\r\n                    return;\r\n                }\r\n                this._compilationError = \"\";\r\n                this._isReady = true;\r\n                if (this.onCompiled) {\r\n                    this.onCompiled(this);\r\n                }\r\n                this.onCompileObservable.notifyObservers(this);\r\n                this.onCompileObservable.clear();\r\n\r\n                if (previousPipelineContext) {\r\n                    this.getEngine()._deleteComputePipelineContext(previousPipelineContext);\r\n                }\r\n            });\r\n\r\n            if (this._pipelineContext.isAsync) {\r\n                this._checkIsReady(previousPipelineContext);\r\n            }\r\n        } catch (e) {\r\n            this._processCompilationErrors(e, previousPipelineContext);\r\n        }\r\n    }\r\n\r\n    private _processCompilationErrors(e: ComputeCompilationMessages | string, previousPipelineContext: Nullable<IComputePipelineContext> = null) {\r\n        this._compilationError = \"\";\r\n\r\n        Logger.Error(\"Unable to compile compute effect:\");\r\n        if (this.defines) {\r\n            Logger.Error(\"Defines:\\n\" + this.defines);\r\n        }\r\n\r\n        if (ComputeEffect.LogShaderCodeOnCompilationError) {\r\n            const code = this._pipelineContext?._getComputeShaderCode();\r\n            if (code) {\r\n                Logger.Error(\"Compute code:\");\r\n                Logger.Error(code);\r\n            }\r\n        }\r\n\r\n        if (typeof e === \"string\") {\r\n            this._compilationError = e;\r\n            Logger.Error(\"Error: \" + this._compilationError);\r\n        } else {\r\n            for (const message of e.messages) {\r\n                let msg = \"\";\r\n                if (message.line !== undefined) {\r\n                    msg += \"Line \" + message.line + \", \";\r\n                }\r\n                if (message.offset !== undefined) {\r\n                    msg += \"Offset \" + message.offset + \", \";\r\n                }\r\n                if (message.length !== undefined) {\r\n                    msg += \"Length \" + message.length + \", \";\r\n                }\r\n                msg += message.type + \": \" + message.text;\r\n\r\n                if (this._compilationError) {\r\n                    this._compilationError += \"\\n\";\r\n                }\r\n                this._compilationError += msg;\r\n                Logger.Error(msg);\r\n            }\r\n        }\r\n\r\n        if (previousPipelineContext) {\r\n            this._pipelineContext = previousPipelineContext;\r\n            this._isReady = true;\r\n        }\r\n\r\n        if (this.onError) {\r\n            this.onError(this, this._compilationError);\r\n        }\r\n        this.onErrorObservable.notifyObservers(this);\r\n    }\r\n\r\n    /**\r\n     * Release all associated resources.\r\n     **/\r\n    public dispose() {\r\n        if (this._pipelineContext) {\r\n            this._pipelineContext.dispose();\r\n        }\r\n        this._engine._releaseComputeEffect(this);\r\n    }\r\n\r\n    /**\r\n     * This function will add a new compute shader to the shader store\r\n     * @param name the name of the shader\r\n     * @param computeShader compute shader content\r\n     */\r\n    public static RegisterShader(name: string, computeShader: string) {\r\n        ShaderStore.GetShadersStore(ShaderLanguage.WGSL)[`${name}ComputeShader`] = computeShader;\r\n    }\r\n}\r\n"]}