{"version": 3, "file": "webAudioStaticSound.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/AudioV2/webAudio/webAudioStaticSound.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,WAAW,EAAE,MAAM,8BAA8B,CAAC;AAE3D,OAAO,EAAE,iBAAiB,EAAE,MAAM,oCAAoC,CAAC;AAEvE,OAAO,EAAE,oBAAoB,EAAE,MAAM,sCAAsC,CAAC;AAC5E,OAAO,EAAE,uBAAuB,EAAE,MAAM,qDAAqD,CAAC;AAE9F,OAAO,EAAE,YAAY,EAAE,MAAM,4CAA4C,CAAC;AAC1E,OAAO,EAAE,SAAS,EAAE,mBAAmB,EAAE,MAAM,eAAe,CAAC;AAE/D,OAAO,EAAE,2BAA2B,EAAE,MAAM,yCAAyC,CAAC;AACtF,OAAO,EAAE,4BAA4B,EAAE,MAAM,wCAAwC,CAAC;AACtF,OAAO,EAAE,gBAAgB,EAAE,MAAM,iCAAiC,CAAC;AAMnE,gBAAgB;AAChB,MAAM,OAAO,oBAAqB,SAAQ,WAAW;IAgBjD,gBAAgB;IAChB,YAAmB,IAAY,EAAE,MAAuB,EAAE,OAAqC;QAC3F,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAhBhB,aAAQ,GAA+B,IAAI,CAAC;QACnC,uBAAkB,GAAY,IAAI,CAAC;QACnC,0BAAqB,GAAW,CAAC,CAAC;QAC3C,YAAO,GAA2B,IAAI,CAAC;QAe3C,IAAI,OAAO,OAAO,CAAC,iBAAiB,KAAK,SAAS,EAAE,CAAC;YACjD,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,iBAAiB,CAAC;QACxD,CAAC;QAED,IAAI,OAAO,OAAO,CAAC,oBAAoB,KAAK,QAAQ,EAAE,CAAC;YACnD,IAAI,CAAC,qBAAqB,GAAG,OAAO,CAAC,oBAAoB,CAAC;QAC9D,CAAC;QAED,IAAI,CAAC,QAAQ,GAAG;YACZ,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,KAAK;YACnC,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,CAAC;YAC/B,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,KAAK;YAC3B,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,CAAC;YAC7B,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,CAAC;YACjC,YAAY,EAAE,OAAO,CAAC,YAAY,IAAI,QAAQ;YAC9C,KAAK,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC;YACzB,YAAY,EAAE,OAAO,CAAC,YAAY,IAAI,CAAC;YACvC,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,CAAC;SACxC,CAAC;QAEF,IAAI,CAAC,SAAS,GAAG,IAAI,oBAAoB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAC9D,CAAC;IAED,gBAAgB;IACT,KAAK,CAAC,UAAU,CAAC,MAA6B,EAAE,OAAqC;QACxF,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;QAE/C,IAAI,MAAM,YAAY,0BAA0B,EAAE,CAAC;YAC/C,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QAC1B,CAAC;aAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,MAAM,YAAY,WAAW,IAAI,MAAM,YAAY,WAAW,EAAE,CAAC;YAC/H,IAAI,CAAC,OAAO,GAAG,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,MAAM,EAAE,OAAO,CAAC,CAA+B,CAAC;QAC7G,CAAC;QAED,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACjB,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QACjC,CAAC;aAAM,IAAI,OAAO,CAAC,iBAAiB,KAAK,KAAK,EAAE,CAAC;YAC7C,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;YACjC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;QAC7C,CAAC;QAED,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAExC,IAAI,uBAAuB,CAAC,OAAO,CAAC,EAAE,CAAC;YACnC,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAChC,CAAC;QAED,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;YACnB,IAAI,CAAC,IAAI,EAAE,CAAC;QAChB,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAED,gBAAgB;IAChB,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,gBAAgB;IAChB,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;IAClC,CAAC;IAED,gBAAgB;IAChB,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;IACnC,CAAC;IAED,gBAAgB;IAChB,IAAoB,OAAO;QACvB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,OAAO,IAAI,CAAC,QAAQ,CAAC;QACzB,CAAC;QACD,OAAO,IAAI,CAAC,oBAAoB,EAAE,CAAC;IACvC,CAAC;IAED,gBAAgB;IAChB,IAAoB,MAAM;QACtB,OAAO,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IAC7E,CAAC;IAED,gBAAgB;IACA,KAAK,CAAC,UAAU,CAAC,UAAuD,IAAI;QACxF,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAErI,KAAK,CAAC,MAAM,GAAG,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;QAE9D,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,gBAAgB;IACA,OAAO;QACnB,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC;QACzB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QAErB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QAEpB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QAEzB,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAED,gBAAgB;IACT,YAAY;QACf,OAAO,sBAAsB,CAAC;IAClC,CAAC;IAES,eAAe;QACrB,OAAO,IAAI,4BAA4B,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IACjE,CAAC;IAEkB,QAAQ,CAAC,IAAqB;QAC7C,MAAM,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEvC,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,wFAAwF;QACxF,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACzC,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,WAAW,CAAC,IAAqB;QAChD,MAAM,YAAY,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAE7C,IAAI,CAAC,YAAY,EAAE,CAAC;YAChB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5C,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,oBAAoB;QACxB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACjB,IAAI,CAAC,QAAQ,GAAG,IAAI,gBAAgB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAC9G,CAAC;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;;AAEc,8BAAS,GAAG,KAAM,SAAQ,4BAA4B;IAGjE,IAAc,gBAAgB;QAC1B,OAAO,IAAI,CAAC,MAAM,CAAC,gBAAgB,IAAI,IAAI,CAAC;IAChD,CAAC;IAED,IAAc,cAAc;QACxB,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,IAAI,CAAC;IAC9C,CAAC;CACJ,AAVuB,CAUtB;AAGN,gBAAgB;AAChB,MAAM,OAAO,0BAA2B,SAAQ,iBAAiB;IAO7D,gBAAgB;IAChB,YAAmB,MAAuB;QACtC,KAAK,CAAC,MAAM,CAAC,CAAC;IAClB,CAAC;IAEM,KAAK,CAAC,UAAU,CAAC,MAA6B,EAAE,OAA2C;QAC9F,IAAI,MAAM,YAAY,WAAW,EAAE,CAAC;YAChC,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;QAC/B,CAAC;aAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;YACpC,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QACzC,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YAC/B,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,OAAO,CAAC,cAAc,IAAI,KAAK,CAAC,CAAC;QAC3E,CAAC;aAAM,IAAI,MAAM,YAAY,WAAW,EAAE,CAAC;YACvC,MAAM,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC;QACjD,CAAC;IACL,CAAC;IAED,gBAAgB;IAChB,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC;IAC9C,CAAC;IAED,gBAAgB;IAChB,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;IACtC,CAAC;IAED,gBAAgB;IAChB,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;IACpC,CAAC;IAED,gBAAgB;IAChB,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC;IACxC,CAAC;IAED,gBAAgB;IACA,KAAK,CAAC,UAA6D,IAAI;QACnF,MAAM,WAAW,GAAG,IAAI,WAAW,CAAC;YAChC,MAAM,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM;YAChC,gBAAgB,EAAE,IAAI,CAAC,YAAY,CAAC,gBAAgB;YACpD,UAAU,EAAE,IAAI,CAAC,YAAY,CAAC,UAAU;SAC3C,CAAC,CAAC;QAEH,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1D,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACtE,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,0BAA0B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC3D,MAAM,CAAC,YAAY,GAAG,WAAW,CAAC;QAClC,MAAM,CAAC,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;QAEvD,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,KAAK,CAAC,yBAAyB,CAAC,WAAwB;QAC5D,IAAI,CAAC,YAAY,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;IACrF,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,GAAW;QACvC,GAAG,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;QACrB,MAAM,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC;IACjF,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,IAAc,EAAE,cAAuB;QACpE,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;YACrB,IAAI,cAAc,EAAE,CAAC;gBACjB,4CAA4C;gBAC5C,MAAM,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;YACtC,CAAC;iBAAM,CAAC;gBACJ,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;gBAC/C,MAAM,MAAM,GAAG,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;gBAC9B,IAAI,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC9C,IAAI,CAAC;wBACD,4CAA4C;wBAC5C,MAAM,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;oBACtC,CAAC;oBAAC,MAAM,CAAC;wBACL,IAAI,MAAM,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;4BAC9B,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;wBAC1C,CAAC;oBACL,CAAC;gBACL,CAAC;YACL,CAAC;YAED,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpB,MAAM;YACV,CAAC;QACL,CAAC;IACL,CAAC;CACJ;AAED,gBAAgB;AAChB,MAAM,4BAA6B,SAAQ,oBAAoB;IAe3D,YAAmB,KAA2B,EAAE,OAAoC;QAChF,KAAK,CAAC,KAAK,CAAC,CAAC;QAfT,oBAAe,GAAW,CAAC,CAAC;QAC5B,qBAAgB,GAAW,CAAC,CAAC;QAC7B,iBAAY,GAAY,KAAK,CAAC;QAC9B,WAAM,GAA0C,IAAI,CAAC;QACrD,kBAAa,GAA0C,IAAI,CAAC;QAC5D,gBAAW,GAAoC,IAAI,CAAC;QAoMlD,aAAQ,GAAG,GAAG,EAAE;YACtB,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;YAEzB,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAC7C,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC7B,CAAC,CAAC;QAwCM,0BAAqB,GAAG,GAAG,EAAE;YACjC,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;gBAClC,OAAO;YACX,CAAC;YAED,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,gCAAwB,EAAE,CAAC;gBAC3D,IAAI,CAAC,IAAI,EAAE,CAAC;YAChB,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,cAAc,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAClF,CAAC,CAAC;QA/OE,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QAExB,IAAI,CAAC,WAAW,GAAG,IAAI,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;QACrD,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAED,gBAAgB;IACA,OAAO;QACnB,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC;QACvB,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,CAAC;QAE9B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QAExB,IAAI,CAAC,IAAI,EAAE,CAAC;QAEZ,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzB,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,cAAc,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;IAClF,CAAC;IAED,gBAAgB;IAChB,IAAW,WAAW;QAClB,IAAI,IAAI,CAAC,MAAM,+BAAuB,EAAE,CAAC;YACrC,OAAO,CAAC,CAAC;QACb,CAAC;QAED,MAAM,kBAAkB,GAAG,IAAI,CAAC,MAAM,8BAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC;QAClH,OAAO,IAAI,CAAC,gBAAgB,GAAG,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;IAClF,CAAC;IAED,IAAW,WAAW,CAAC,KAAa;QAChC,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,gCAAwB,IAAI,IAAI,CAAC,MAAM,+BAAuB,CAAC;QAE1F,IAAI,OAAO,EAAE,CAAC;YACV,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC7B,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,WAAW,GAAG,KAAK,CAAC;QAElC,IAAI,OAAO,EAAE,CAAC;YACV,IAAI,CAAC,IAAI,EAAE,CAAC;QAChB,CAAC;IACL,CAAC;IAED,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,gBAAgB;IAChB,IAAW,KAAK,CAAC,KAAa;QAC1B,IAAI,CAAC,MAAM,EAAE,cAAc,CAAC,KAAK,CAAC,CAAC;IACvC,CAAC;IAED,gBAAgB;IAChB,IAAW,YAAY,CAAC,KAAa;QACjC,IAAI,CAAC,aAAa,EAAE,cAAc,CAAC,KAAK,CAAC,CAAC;IAC9C,CAAC;IAED,gBAAgB;IAChB,IAAW,SAAS;QAChB,IAAI,IAAI,CAAC,MAAM,+BAAuB,EAAE,CAAC;YACrC,OAAO,CAAC,CAAC;QACb,CAAC;QAED,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED,gBAAgB;IACT,YAAY;QACf,OAAO,8BAA8B,CAAC;IAC1C,CAAC;IAED,gBAAgB;IACT,IAAI,CAAC,UAA4C,EAAE;QACtD,IAAI,IAAI,CAAC,MAAM,+BAAuB,EAAE,CAAC;YACrC,OAAO;QACX,CAAC;QAED,IAAI,OAAO,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACjC,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QAC9C,CAAC;QACD,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YAC7B,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QACtC,CAAC;QACD,IAAI,OAAO,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;YAClC,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QAChD,CAAC;QACD,IAAI,OAAO,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;YAChC,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAC5C,CAAC;QACD,IAAI,OAAO,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;YACpC,IAAI,CAAC,QAAQ,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACpD,CAAC;QAED,IAAI,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;QAE5C,IAAI,IAAI,CAAC,MAAM,8BAAsB,EAAE,CAAC;YACpC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC;YAChC,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;QAC/C,CAAC;QAED,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,CAAC,CAAC;QAEzE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC;QAElD,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;YAClC,IAAI,CAAC,SAAS,4BAAoB,CAAC;YACnC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,IAAI,CAAC,eAAe,EAAE,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;QAChI,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;YAC5B,IAAI,CAAC,SAAS,6BAAqB,CAAC;YACpC,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,GAAG,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACvE,CAAC;IACL,CAAC;IAED,gBAAgB;IACT,KAAK;QACR,IAAI,IAAI,CAAC,MAAM,8BAAsB,EAAE,CAAC;YACpC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,SAAS,2BAAmB,CAAC;QAClC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC;QAExE,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC;QACzB,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC7B,CAAC;IAED,gBAAgB;IACT,MAAM;QACT,IAAI,IAAI,CAAC,MAAM,8BAAsB,EAAE,CAAC;YACpC,IAAI,CAAC,IAAI,EAAE,CAAC;QAChB,CAAC;IACL,CAAC;IAED,gBAAgB;IACT,IAAI,CAAC,UAA4C,EAAE;QACtD,IAAI,IAAI,CAAC,MAAM,+BAAuB,EAAE,CAAC;YACrC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,SAAS,4BAAoB,CAAC;QAEnC,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,CAAC,CAAC;QACzE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAEvC,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,cAAc,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;IAClF,CAAC;IAEkB,QAAQ,CAAC,IAAuB;QAC/C,MAAM,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEvC,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,gGAAgG;QAChG,IAAI,IAAI,YAAY,oBAAoB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACvD,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACrC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAC7B,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,WAAW,CAAC,IAAuB;QAClD,MAAM,YAAY,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAE7C,IAAI,CAAC,YAAY,EAAE,CAAC;YAChB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,YAAY,oBAAoB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACvD,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACxC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC9B,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IASO,iBAAiB;QACrB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACpB,OAAO;QACX,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;YACtD,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;QACzC,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC9C,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAE7D,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IAC5B,CAAC;IAEO,eAAe;QACnB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACpB,IAAI,CAAC,WAAW,GAAG,IAAI,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC;YAErH,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;YAC1E,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAE3C,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC9B,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;YACtC,CAAC;YAED,IAAI,CAAC,MAAM,GAAG,IAAI,2BAA2B,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YACpF,IAAI,CAAC,aAAa,GAAG,IAAI,2BAA2B,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;QACrG,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC;QAC9B,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;QACtC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QAC/B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;QACrC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;QACzC,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;IACvD,CAAC;CAaJ", "sourcesContent": ["import type { Nullable } from \"../../types\";\nimport type { AbstractAudioNode } from \"../abstractAudio/abstractAudioNode\";\nimport type { IStaticSoundCloneOptions, IStaticSoundOptions, IStaticSoundPlayOptions, IStaticSoundStopOptions, IStaticSoundStoredOptions } from \"../abstractAudio/staticSound\";\nimport { StaticSound } from \"../abstractAudio/staticSound\";\nimport type { IStaticSoundBufferCloneOptions, IStaticSoundBufferOptions } from \"../abstractAudio/staticSoundBuffer\";\nimport { StaticSoundBuffer } from \"../abstractAudio/staticSoundBuffer\";\nimport type { IStaticSoundInstanceOptions } from \"../abstractAudio/staticSoundInstance\";\nimport { _StaticSoundInstance } from \"../abstractAudio/staticSoundInstance\";\nimport { _HasSpatialAudioOptions } from \"../abstractAudio/subProperties/abstractSpatialAudio\";\nimport type { _SpatialAudio } from \"../abstractAudio/subProperties/spatialAudio\";\nimport { _StereoAudio } from \"../abstractAudio/subProperties/stereoAudio\";\nimport { _CleanUrl, _FileExtensionRegex } from \"../audioUtils\";\nimport { SoundState } from \"../soundState\";\nimport { _WebAudioParameterComponent } from \"./components/webAudioParameterComponent\";\nimport { _WebAudioBusAndSoundSubGraph } from \"./subNodes/webAudioBusAndSoundSubGraph\";\nimport { _SpatialWebAudio } from \"./subProperties/spatialWebAudio\";\nimport type { _WebAudioEngine } from \"./webAudioEngine\";\nimport type { IWebAudioInNode, IWebAudioOutNode, IWebAudioSuperNode } from \"./webAudioNode\";\n\ntype StaticSoundSourceType = ArrayBuffer | AudioBuffer | StaticSoundBuffer | string | string[];\n\n/** @internal */\nexport class _WebAudioStaticSound extends StaticSound implements IWebAudioSuperNode {\n    private _buffer: _WebAudioStaticSoundBuffer;\n    private _spatial: Nullable<_SpatialWebAudio> = null;\n    private readonly _spatialAutoUpdate: boolean = true;\n    private readonly _spatialMinUpdateTime: number = 0;\n    private _stereo: Nullable<_StereoAudio> = null;\n\n    protected override readonly _options: IStaticSoundStoredOptions;\n    protected _subGraph: _WebAudioBusAndSoundSubGraph;\n\n    /** @internal */\n    public _audioContext: AudioContext | OfflineAudioContext;\n\n    /** @internal */\n    public override readonly engine: _WebAudioEngine;\n\n    /** @internal */\n    public constructor(name: string, engine: _WebAudioEngine, options: Partial<IStaticSoundOptions>) {\n        super(name, engine);\n\n        if (typeof options.spatialAutoUpdate === \"boolean\") {\n            this._spatialAutoUpdate = options.spatialAutoUpdate;\n        }\n\n        if (typeof options.spatialMinUpdateTime === \"number\") {\n            this._spatialMinUpdateTime = options.spatialMinUpdateTime;\n        }\n\n        this._options = {\n            autoplay: options.autoplay ?? false,\n            duration: options.duration ?? 0,\n            loop: options.loop ?? false,\n            loopEnd: options.loopEnd ?? 0,\n            loopStart: options.loopStart ?? 0,\n            maxInstances: options.maxInstances ?? Infinity,\n            pitch: options.pitch ?? 0,\n            playbackRate: options.playbackRate ?? 1,\n            startOffset: options.startOffset ?? 0,\n        };\n\n        this._subGraph = new _WebAudioStaticSound._SubGraph(this);\n    }\n\n    /** @internal */\n    public async _initAsync(source: StaticSoundSourceType, options: Partial<IStaticSoundOptions>): Promise<void> {\n        this._audioContext = this.engine._audioContext;\n\n        if (source instanceof _WebAudioStaticSoundBuffer) {\n            this._buffer = source;\n        } else if (typeof source === \"string\" || Array.isArray(source) || source instanceof ArrayBuffer || source instanceof AudioBuffer) {\n            this._buffer = (await this.engine.createSoundBufferAsync(source, options)) as _WebAudioStaticSoundBuffer;\n        }\n\n        if (options.outBus) {\n            this.outBus = options.outBus;\n        } else if (options.outBusAutoDefault !== false) {\n            await this.engine.isReadyPromise;\n            this.outBus = this.engine.defaultMainBus;\n        }\n\n        await this._subGraph.initAsync(options);\n\n        if (_HasSpatialAudioOptions(options)) {\n            this._initSpatialProperty();\n        }\n\n        if (options.autoplay) {\n            this.play();\n        }\n\n        this.engine._addNode(this);\n    }\n\n    /** @internal */\n    public get buffer(): _WebAudioStaticSoundBuffer {\n        return this._buffer;\n    }\n\n    /** @internal */\n    public get _inNode() {\n        return this._subGraph._inNode;\n    }\n\n    /** @internal */\n    public get _outNode() {\n        return this._subGraph._outNode;\n    }\n\n    /** @internal */\n    public override get spatial(): _SpatialAudio {\n        if (this._spatial) {\n            return this._spatial;\n        }\n        return this._initSpatialProperty();\n    }\n\n    /** @internal */\n    public override get stereo(): _StereoAudio {\n        return this._stereo ?? (this._stereo = new _StereoAudio(this._subGraph));\n    }\n\n    /** @internal */\n    public override async cloneAsync(options: Nullable<Partial<IStaticSoundCloneOptions>> = null): Promise<StaticSound> {\n        const clone = await this.engine.createSoundAsync(this.name, options?.cloneBuffer ? this.buffer.clone() : this.buffer, this._options);\n\n        clone.outBus = options?.outBus ? options.outBus : this.outBus;\n\n        return clone;\n    }\n\n    /** @internal */\n    public override dispose(): void {\n        super.dispose();\n\n        this._spatial?.dispose();\n        this._spatial = null;\n\n        this._stereo = null;\n\n        this._subGraph.dispose();\n\n        this.engine._removeNode(this);\n    }\n\n    /** @internal */\n    public getClassName(): string {\n        return \"_WebAudioStaticSound\";\n    }\n\n    protected _createInstance(): _WebAudioStaticSoundInstance {\n        return new _WebAudioStaticSoundInstance(this, this._options);\n    }\n\n    protected override _connect(node: IWebAudioInNode): boolean {\n        const connected = super._connect(node);\n\n        if (!connected) {\n            return false;\n        }\n\n        // If the wrapped node is not available now, it will be connected later by the subgraph.\n        if (node._inNode) {\n            this._outNode?.connect(node._inNode);\n        }\n\n        return true;\n    }\n\n    protected override _disconnect(node: IWebAudioInNode): boolean {\n        const disconnected = super._disconnect(node);\n\n        if (!disconnected) {\n            return false;\n        }\n\n        if (node._inNode) {\n            this._outNode?.disconnect(node._inNode);\n        }\n\n        return true;\n    }\n\n    private _initSpatialProperty(): _SpatialAudio {\n        if (!this._spatial) {\n            this._spatial = new _SpatialWebAudio(this._subGraph, this._spatialAutoUpdate, this._spatialMinUpdateTime);\n        }\n\n        return this._spatial;\n    }\n\n    private static _SubGraph = class extends _WebAudioBusAndSoundSubGraph {\n        protected override _owner: _WebAudioStaticSound;\n\n        protected get _downstreamNodes(): Nullable<Set<AbstractAudioNode>> {\n            return this._owner._downstreamNodes ?? null;\n        }\n\n        protected get _upstreamNodes(): Nullable<Set<AbstractAudioNode>> {\n            return this._owner._upstreamNodes ?? null;\n        }\n    };\n}\n\n/** @internal */\nexport class _WebAudioStaticSoundBuffer extends StaticSoundBuffer {\n    /** @internal */\n    public _audioBuffer: AudioBuffer;\n\n    /** @internal */\n    public override readonly engine: _WebAudioEngine;\n\n    /** @internal */\n    public constructor(engine: _WebAudioEngine) {\n        super(engine);\n    }\n\n    public async _initAsync(source: StaticSoundSourceType, options: Partial<IStaticSoundBufferOptions>): Promise<void> {\n        if (source instanceof AudioBuffer) {\n            this._audioBuffer = source;\n        } else if (typeof source === \"string\") {\n            await this._initFromUrlAsync(source);\n        } else if (Array.isArray(source)) {\n            await this._initFromUrlsAsync(source, options.skipCodecCheck ?? false);\n        } else if (source instanceof ArrayBuffer) {\n            await this._initFromArrayBufferAsync(source);\n        }\n    }\n\n    /** @internal */\n    public get channelCount(): number {\n        return this._audioBuffer.numberOfChannels;\n    }\n\n    /** @internal */\n    public get duration(): number {\n        return this._audioBuffer.duration;\n    }\n\n    /** @internal */\n    public get length(): number {\n        return this._audioBuffer.length;\n    }\n\n    /** @internal */\n    public get sampleRate(): number {\n        return this._audioBuffer.sampleRate;\n    }\n\n    /** @internal */\n    public override clone(options: Nullable<Partial<IStaticSoundBufferCloneOptions>> = null): StaticSoundBuffer {\n        const audioBuffer = new AudioBuffer({\n            length: this._audioBuffer.length,\n            numberOfChannels: this._audioBuffer.numberOfChannels,\n            sampleRate: this._audioBuffer.sampleRate,\n        });\n\n        for (let i = 0; i < this._audioBuffer.numberOfChannels; i++) {\n            audioBuffer.copyToChannel(this._audioBuffer.getChannelData(i), i);\n        }\n\n        const buffer = new _WebAudioStaticSoundBuffer(this.engine);\n        buffer._audioBuffer = audioBuffer;\n        buffer.name = options?.name ? options.name : this.name;\n\n        return buffer;\n    }\n\n    private async _initFromArrayBufferAsync(arrayBuffer: ArrayBuffer): Promise<void> {\n        this._audioBuffer = await this.engine._audioContext.decodeAudioData(arrayBuffer);\n    }\n\n    private async _initFromUrlAsync(url: string): Promise<void> {\n        url = _CleanUrl(url);\n        await this._initFromArrayBufferAsync(await (await fetch(url)).arrayBuffer());\n    }\n\n    private async _initFromUrlsAsync(urls: string[], skipCodecCheck: boolean): Promise<void> {\n        for (const url of urls) {\n            if (skipCodecCheck) {\n                // eslint-disable-next-line no-await-in-loop\n                await this._initFromUrlAsync(url);\n            } else {\n                const matches = url.match(_FileExtensionRegex);\n                const format = matches?.at(1);\n                if (format && this.engine.isFormatValid(format)) {\n                    try {\n                        // eslint-disable-next-line no-await-in-loop\n                        await this._initFromUrlAsync(url);\n                    } catch {\n                        if (format && 0 < format.length) {\n                            this.engine.flagInvalidFormat(format);\n                        }\n                    }\n                }\n            }\n\n            if (this._audioBuffer) {\n                break;\n            }\n        }\n    }\n}\n\n/** @internal */\nclass _WebAudioStaticSoundInstance extends _StaticSoundInstance implements IWebAudioOutNode {\n    private _enginePlayTime: number = 0;\n    private _enginePauseTime: number = 0;\n    private _isConnected: boolean = false;\n    private _pitch: Nullable<_WebAudioParameterComponent> = null;\n    private _playbackRate: Nullable<_WebAudioParameterComponent> = null;\n    private _sourceNode: Nullable<AudioBufferSourceNode> = null;\n    private _volumeNode: GainNode;\n\n    protected override readonly _options: IStaticSoundInstanceOptions;\n    protected override _sound: _WebAudioStaticSound;\n\n    /** @internal */\n    public override readonly engine: _WebAudioEngine;\n\n    public constructor(sound: _WebAudioStaticSound, options: IStaticSoundInstanceOptions) {\n        super(sound);\n\n        this._options = options;\n\n        this._volumeNode = new GainNode(sound._audioContext);\n        this._initSourceNode();\n    }\n\n    /** @internal */\n    public override dispose(): void {\n        super.dispose();\n\n        this._pitch?.dispose();\n        this._playbackRate?.dispose();\n\n        this._sourceNode = null;\n\n        this.stop();\n\n        this._deinitSourceNode();\n\n        this.engine.stateChangedObservable.removeCallback(this._onEngineStateChanged);\n    }\n\n    /** @internal */\n    public get currentTime(): number {\n        if (this._state === SoundState.Stopped) {\n            return 0;\n        }\n\n        const timeSinceLastStart = this._state === SoundState.Paused ? 0 : this.engine.currentTime - this._enginePlayTime;\n        return this._enginePauseTime + timeSinceLastStart + this._options.startOffset;\n    }\n\n    public set currentTime(value: number) {\n        const restart = this._state === SoundState.Starting || this._state === SoundState.Started;\n\n        if (restart) {\n            this.stop();\n            this._deinitSourceNode();\n        }\n\n        this._options.startOffset = value;\n\n        if (restart) {\n            this.play();\n        }\n    }\n\n    public get _outNode(): Nullable<AudioNode> {\n        return this._volumeNode;\n    }\n\n    /** @internal */\n    public set pitch(value: number) {\n        this._pitch?.setTargetValue(value);\n    }\n\n    /** @internal */\n    public set playbackRate(value: number) {\n        this._playbackRate?.setTargetValue(value);\n    }\n\n    /** @internal */\n    public get startTime(): number {\n        if (this._state === SoundState.Stopped) {\n            return 0;\n        }\n\n        return this._enginePlayTime;\n    }\n\n    /** @internal */\n    public getClassName(): string {\n        return \"_WebAudioStaticSoundInstance\";\n    }\n\n    /** @internal */\n    public play(options: Partial<IStaticSoundPlayOptions> = {}): void {\n        if (this._state === SoundState.Started) {\n            return;\n        }\n\n        if (options.duration !== undefined) {\n            this._options.duration = options.duration;\n        }\n        if (options.loop !== undefined) {\n            this._options.loop = options.loop;\n        }\n        if (options.loopStart !== undefined) {\n            this._options.loopStart = options.loopStart;\n        }\n        if (options.loopEnd !== undefined) {\n            this._options.loopEnd = options.loopEnd;\n        }\n        if (options.startOffset !== undefined) {\n            this._options.startOffset = options.startOffset;\n        }\n\n        let startOffset = this._options.startOffset;\n\n        if (this._state === SoundState.Paused) {\n            startOffset += this.currentTime;\n            startOffset %= this._sound.buffer.duration;\n        }\n\n        this._enginePlayTime = this.engine.currentTime + (options.waitTime ?? 0);\n\n        this._volumeNode.gain.value = options.volume ?? 1;\n\n        this._initSourceNode();\n\n        if (this.engine.state === \"running\") {\n            this._setState(SoundState.Started);\n            this._sourceNode?.start(this._enginePlayTime, startOffset, this._options.duration > 0 ? this._options.duration : undefined);\n        } else if (this._options.loop) {\n            this._setState(SoundState.Starting);\n            this.engine.stateChangedObservable.add(this._onEngineStateChanged);\n        }\n    }\n\n    /** @internal */\n    public pause(): void {\n        if (this._state === SoundState.Paused) {\n            return;\n        }\n\n        this._setState(SoundState.Paused);\n        this._enginePauseTime += this.engine.currentTime - this._enginePlayTime;\n\n        this._sourceNode?.stop();\n        this._deinitSourceNode();\n    }\n\n    /** @internal */\n    public resume(): void {\n        if (this._state === SoundState.Paused) {\n            this.play();\n        }\n    }\n\n    /** @internal */\n    public stop(options: Partial<IStaticSoundStopOptions> = {}): void {\n        if (this._state === SoundState.Stopped) {\n            return;\n        }\n\n        this._setState(SoundState.Stopped);\n\n        const engineStopTime = this.engine.currentTime + (options.waitTime ?? 0);\n        this._sourceNode?.stop(engineStopTime);\n\n        this.engine.stateChangedObservable.removeCallback(this._onEngineStateChanged);\n    }\n\n    protected override _connect(node: AbstractAudioNode): boolean {\n        const connected = super._connect(node);\n\n        if (!connected) {\n            return false;\n        }\n\n        // If the wrapped node is not available now, it will be connected later by the sound's subgraph.\n        if (node instanceof _WebAudioStaticSound && node._inNode) {\n            this._outNode?.connect(node._inNode);\n            this._isConnected = true;\n        }\n\n        return true;\n    }\n\n    protected override _disconnect(node: AbstractAudioNode): boolean {\n        const disconnected = super._disconnect(node);\n\n        if (!disconnected) {\n            return false;\n        }\n\n        if (node instanceof _WebAudioStaticSound && node._inNode) {\n            this._outNode?.disconnect(node._inNode);\n            this._isConnected = false;\n        }\n\n        return true;\n    }\n\n    protected _onEnded = () => {\n        this._enginePlayTime = 0;\n\n        this.onEndedObservable.notifyObservers(this);\n        this._deinitSourceNode();\n    };\n\n    private _deinitSourceNode(): void {\n        if (!this._sourceNode) {\n            return;\n        }\n\n        if (this._isConnected && !this._disconnect(this._sound)) {\n            throw new Error(\"Disconnect failed\");\n        }\n\n        this._sourceNode.disconnect(this._volumeNode);\n        this._sourceNode.removeEventListener(\"ended\", this._onEnded);\n\n        this._sourceNode = null;\n    }\n\n    private _initSourceNode(): void {\n        if (!this._sourceNode) {\n            this._sourceNode = new AudioBufferSourceNode(this._sound._audioContext, { buffer: this._sound.buffer._audioBuffer });\n\n            this._sourceNode.addEventListener(\"ended\", this._onEnded, { once: true });\n            this._sourceNode.connect(this._volumeNode);\n\n            if (!this._connect(this._sound)) {\n                throw new Error(\"Connect failed\");\n            }\n\n            this._pitch = new _WebAudioParameterComponent(this.engine, this._sourceNode.detune);\n            this._playbackRate = new _WebAudioParameterComponent(this.engine, this._sourceNode.playbackRate);\n        }\n\n        const node = this._sourceNode;\n        node.detune.value = this._sound.pitch;\n        node.loop = this._options.loop;\n        node.loopEnd = this._options.loopEnd;\n        node.loopStart = this._options.loopStart;\n        node.playbackRate.value = this._sound.playbackRate;\n    }\n\n    private _onEngineStateChanged = () => {\n        if (this.engine.state !== \"running\") {\n            return;\n        }\n\n        if (this._options.loop && this.state === SoundState.Starting) {\n            this.play();\n        }\n\n        this.engine.stateChangedObservable.removeCallback(this._onEngineStateChanged);\n    };\n}\n"]}